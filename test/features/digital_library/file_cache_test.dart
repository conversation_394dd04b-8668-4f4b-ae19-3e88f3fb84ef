import 'package:flutter_test/flutter_test.dart';
import 'package:scholara_student/features/digital_library/services/file_cache_service.dart';

void main() {
  group('CacheEntry Tests', () {
    test('should create CacheEntry with all properties', () {
      final now = DateTime.now();
      final entry = CacheEntry(
        url: 'https://example.com/file.pdf',
        localPath: '/path/to/cached/file.pdf',
        fileName: 'file.pdf',
        fileSize: 1024,
        cachedAt: now,
        lastAccessed: now,
        expiresAt: now.add(const Duration(days: 30)),
      );

      expect(entry.url, equals('https://example.com/file.pdf'));
      expect(entry.localPath, equals('/path/to/cached/file.pdf'));
      expect(entry.fileName, equals('file.pdf'));
      expect(entry.fileSize, equals(1024));
      expect(entry.cachedAt, equals(now));
      expect(entry.lastAccessed, equals(now));
      expect(entry.expiresAt, equals(now.add(const Duration(days: 30))));
    });

    test('should copy with updated properties', () {
      final now = DateTime.now();
      final original = CacheEntry(
        url: 'https://example.com/file.pdf',
        localPath: '/path/to/cached/file.pdf',
        fileName: 'file.pdf',
        fileSize: 1024,
        cachedAt: now,
        lastAccessed: now,
        expiresAt: now.add(const Duration(days: 30)),
      );

      final newAccessTime = now.add(const Duration(hours: 1));
      final updated = original.copyWith(lastAccessed: newAccessTime);

      expect(updated.url, equals(original.url));
      expect(updated.localPath, equals(original.localPath));
      expect(updated.fileName, equals(original.fileName));
      expect(updated.fileSize, equals(original.fileSize));
      expect(updated.cachedAt, equals(original.cachedAt));
      expect(updated.lastAccessed, equals(newAccessTime));
      expect(updated.expiresAt, equals(original.expiresAt));
    });

    test('should serialize to and from JSON', () {
      final now = DateTime.now();
      final entry = CacheEntry(
        url: 'https://example.com/file.pdf',
        localPath: '/path/to/cached/file.pdf',
        fileName: 'file.pdf',
        fileSize: 1024,
        cachedAt: now,
        lastAccessed: now,
        expiresAt: now.add(const Duration(days: 30)),
      );

      final json = entry.toJson();
      final restored = CacheEntry.fromJson(json);

      expect(restored.url, equals(entry.url));
      expect(restored.localPath, equals(entry.localPath));
      expect(restored.fileName, equals(entry.fileName));
      expect(restored.fileSize, equals(entry.fileSize));
      expect(
        restored.cachedAt.millisecondsSinceEpoch,
        equals(entry.cachedAt.millisecondsSinceEpoch),
      );
      expect(
        restored.lastAccessed.millisecondsSinceEpoch,
        equals(entry.lastAccessed.millisecondsSinceEpoch),
      );
      expect(
        restored.expiresAt.millisecondsSinceEpoch,
        equals(entry.expiresAt.millisecondsSinceEpoch),
      );
    });
  });

  group('CacheStats Tests', () {
    test('should create CacheStats with all properties', () {
      const stats = CacheStats(
        totalFiles: 5,
        totalSizeBytes: 5120,
        expiredFiles: 1,
        cacheDirectory: '/path/to/cache',
      );

      expect(stats.totalFiles, equals(5));
      expect(stats.totalSizeBytes, equals(5120));
      expect(stats.expiredFiles, equals(1));
      expect(stats.cacheDirectory, equals('/path/to/cache'));
    });

    test('should format total size correctly', () {
      const stats1 = CacheStats(
        totalFiles: 1,
        totalSizeBytes: 512,
        expiredFiles: 0,
      );

      const stats2 = CacheStats(
        totalFiles: 1,
        totalSizeBytes: 1536, // 1.5 KB
        expiredFiles: 0,
      );

      const stats3 = CacheStats(
        totalFiles: 1,
        totalSizeBytes: 1572864, // 1.5 MB
        expiredFiles: 0,
      );

      const stats4 = CacheStats(
        totalFiles: 1,
        totalSizeBytes: 1610612736, // 1.5 GB
        expiredFiles: 0,
      );

      expect(stats1.formattedSize, equals('512 B'));
      expect(stats2.formattedSize, equals('1.5 KB'));
      expect(stats3.formattedSize, equals('1.5 MB'));
      expect(stats4.formattedSize, equals('1.5 GB'));
    });

    test('should handle zero size', () {
      const stats = CacheStats(
        totalFiles: 0,
        totalSizeBytes: 0,
        expiredFiles: 0,
      );

      expect(stats.formattedSize, equals('0 B'));
    });

    test('should handle edge cases for size formatting', () {
      const stats1 = CacheStats(
        totalFiles: 1,
        totalSizeBytes: 1023, // Just under 1 KB
        expiredFiles: 0,
      );

      const stats2 = CacheStats(
        totalFiles: 1,
        totalSizeBytes: 1024, // Exactly 1 KB
        expiredFiles: 0,
      );

      const stats3 = CacheStats(
        totalFiles: 1,
        totalSizeBytes: 1048575, // Just under 1 MB
        expiredFiles: 0,
      );

      const stats4 = CacheStats(
        totalFiles: 1,
        totalSizeBytes: 1048576, // Exactly 1 MB
        expiredFiles: 0,
      );

      expect(stats1.formattedSize, equals('1023 B'));
      expect(stats2.formattedSize, equals('1.0 KB'));
      expect(stats3.formattedSize, equals('1024.0 KB'));
      expect(stats4.formattedSize, equals('1.0 MB'));
    });
  });

  group('FileCacheService Tests', () {
    late FileCacheService cacheService;

    setUp(() {
      cacheService = FileCacheService();
    });

    test('should be singleton', () {
      final instance1 = FileCacheService();
      final instance2 = FileCacheService();

      expect(identical(instance1, instance2), isTrue);
    });

    test('should generate consistent cache keys', () {
      const url1 = 'https://example.com/file1.pdf';
      const url2 = 'https://example.com/file2.pdf';
      const url3 = 'https://example.com/file1.pdf'; // Same as url1

      // Note: We can't directly test the private _generateCacheKey method,
      // but we can test that the same URL produces consistent behavior
      expect(url1.hashCode.toString(), equals(url3.hashCode.toString()));
      expect(url1.hashCode.toString(), isNot(equals(url2.hashCode.toString())));
    });

    test('should handle cache stats with no files', () {
      final stats = cacheService.getCacheStats();

      expect(stats.totalFiles, equals(0));
      expect(stats.totalSizeBytes, equals(0));
      expect(stats.expiredFiles, equals(0));
      expect(stats.formattedSize, equals('0 B'));
    });
  });

  group('Cache Key Generation Tests', () {
    test('should generate different keys for different URLs', () {
      const url1 = 'https://example.com/file1.pdf';
      const url2 = 'https://example.com/file2.pdf';
      const url3 = 'https://different.com/file1.pdf';

      final key1 = url1.hashCode.toString();
      final key2 = url2.hashCode.toString();
      final key3 = url3.hashCode.toString();

      expect(key1, isNot(equals(key2)));
      expect(key1, isNot(equals(key3)));
      expect(key2, isNot(equals(key3)));
    });

    test('should generate same key for same URL', () {
      const url = 'https://example.com/file.pdf';

      final key1 = url.hashCode.toString();
      final key2 = url.hashCode.toString();

      expect(key1, equals(key2));
    });
  });

  group('Cache File Name Generation Tests', () {
    test('should preserve file extension', () {
      const fileName1 = 'document.pdf';
      const fileName2 = 'image.jpg';
      const fileName3 = 'archive.zip';

      // Simulate the file name generation logic
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final generatedName1 = '${timestamp}_${fileName1.hashCode}.pdf';
      final generatedName2 = '${timestamp}_${fileName2.hashCode}.jpg';
      final generatedName3 = '${timestamp}_${fileName3.hashCode}.zip';

      expect(generatedName1.endsWith('.pdf'), isTrue);
      expect(generatedName2.endsWith('.jpg'), isTrue);
      expect(generatedName3.endsWith('.zip'), isTrue);
    });

    test('should handle files without extension', () {
      const fileName = 'README';

      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final generatedName = '${timestamp}_${fileName.hashCode}.README';

      expect(generatedName.endsWith('.README'), isTrue);
    });

    test('should generate unique names for same file name', () {
      const fileName = 'document.pdf';

      final timestamp1 = DateTime.now().millisecondsSinceEpoch;
      // Simulate a small delay
      final timestamp2 = timestamp1 + 1;

      final generatedName1 = '${timestamp1}_${fileName.hashCode}.pdf';
      final generatedName2 = '${timestamp2}_${fileName.hashCode}.pdf';

      expect(generatedName1, isNot(equals(generatedName2)));
    });
  });
}
