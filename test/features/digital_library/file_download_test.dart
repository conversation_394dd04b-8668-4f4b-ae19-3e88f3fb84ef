import 'package:flutter_test/flutter_test.dart';
import 'package:scholara_student/features/digital_library/models/download_progress_model.dart';
import 'package:scholara_student/features/digital_library/enums/download_state.dart';

void main() {
  group('DownloadProgressModel Tests', () {
    test('should create started download progress', () {
      const id = 'test_download';

      final progress = DownloadProgressModel.started(id);

      expect(progress.fileId, equals(id));
      expect(progress.state, equals(DownloadState.downloading));
      expect(progress.progress, equals(0.0));
      expect(progress.bytesDownloaded, equals(0));
    });

    test('should create in-progress download progress', () {
      const id = 'test_download';

      final progress = DownloadProgressModel.inProgress(
        fileId: id,
        progress: 0.5,
        bytesDownloaded: 500,
        totalBytes: 1000,
      );

      expect(progress.fileId, equals(id));
      expect(progress.state, equals(DownloadState.downloading));
      expect(progress.progress, equals(0.5));
      expect(progress.bytesDownloaded, equals(500));
      expect(progress.totalBytes, equals(1000));
    });

    test('should create completed download progress', () {
      const id = 'test_download';
      const totalBytes = 1024;

      final progress = DownloadProgressModel.completed(id, totalBytes);

      expect(progress.fileId, equals(id));
      expect(progress.state, equals(DownloadState.downloaded));
      expect(progress.progress, equals(1.0));
      expect(progress.bytesDownloaded, equals(totalBytes));
      expect(progress.totalBytes, equals(totalBytes));
      expect(progress.endTime, isNotNull);
    });

    test('should create failed download progress', () {
      const id = 'test_download';
      const errorMessage = 'Network error';

      final progress = DownloadProgressModel.failed(id, errorMessage);

      expect(progress.fileId, equals(id));
      expect(progress.state, equals(DownloadState.failed));
      expect(progress.errorMessage, equals(errorMessage));
      expect(progress.endTime, isNotNull);
    });

    test('should format file sizes correctly', () {
      final progress1 = DownloadProgressModel(
        fileId: 'test',
        state: DownloadState.downloading,
        progress: 0.5,
        bytesDownloaded: 512,
        totalBytes: 1024,
      );

      final progress2 = DownloadProgressModel(
        fileId: 'test',
        state: DownloadState.downloading,
        progress: 0.5,
        bytesDownloaded: 1536, // 1.5 KB
        totalBytes: 3072, // 3 KB
      );

      final progress3 = DownloadProgressModel(
        fileId: 'test',
        state: DownloadState.downloading,
        progress: 0.5,
        bytesDownloaded: 1572864, // 1.5 MB
        totalBytes: 3145728, // 3 MB
      );

      expect(progress1.formattedSize, equals('1.0 KB'));
      expect(progress2.formattedSize, equals('3.0 KB'));
      expect(progress3.formattedSize, equals('3.0 MB'));
    });

    test('should format progress percentage correctly', () {
      final progress1 = DownloadProgressModel(
        fileId: 'test',
        state: DownloadState.downloading,
        progress: 0.0,
        bytesDownloaded: 0,
      );

      final progress2 = DownloadProgressModel(
        fileId: 'test',
        state: DownloadState.downloading,
        progress: 0.5,
        bytesDownloaded: 512,
      );

      final progress3 = DownloadProgressModel(
        fileId: 'test',
        state: DownloadState.downloading,
        progress: 0.756,
        bytesDownloaded: 756,
      );

      expect(progress1.progress, equals(0.0));
      expect(progress2.progress, equals(0.5));
      expect(progress3.progress, equals(0.756));
    });

    test('should format estimated time remaining correctly', () {
      final progress1 = DownloadProgressModel(
        fileId: 'test',
        state: DownloadState.downloading,
        progress: 0.5,
        bytesDownloaded: 512,
        estimatedTimeRemainingSeconds: 30,
      );

      final progress2 = DownloadProgressModel(
        fileId: 'test',
        state: DownloadState.downloading,
        progress: 0.5,
        bytesDownloaded: 512,
        estimatedTimeRemainingSeconds: 120, // 2 minutes
      );

      final progress3 = DownloadProgressModel(
        fileId: 'test',
        state: DownloadState.downloading,
        progress: 0.5,
        bytesDownloaded: 512,
        estimatedTimeRemainingSeconds: 3600, // 1 hour
      );

      expect(progress1.formattedTimeRemaining, equals('30s'));
      expect(progress2.formattedTimeRemaining, equals('2m 0s'));
      expect(progress3.formattedTimeRemaining, equals('1h 0m'));
    });

    test('should correctly identify progress states', () {
      final downloading = DownloadProgressModel(
        fileId: 'test',
        state: DownloadState.downloading,
        progress: 0.5,
        bytesDownloaded: 512,
      );

      final completed = DownloadProgressModel.completed('test', 1024);
      final failed = DownloadProgressModel.failed('test', 'Error');

      expect(downloading.isInProgress, isTrue);
      expect(downloading.isComplete, isFalse);
      expect(downloading.hasFailed, isFalse);

      expect(completed.isInProgress, isFalse);
      expect(completed.isComplete, isTrue);
      expect(completed.hasFailed, isFalse);

      expect(failed.isInProgress, isFalse);
      expect(failed.isComplete, isFalse);
      expect(failed.hasFailed, isTrue);
    });

    test('should copy with updated properties', () {
      final original = DownloadProgressModel(
        fileId: 'test',
        state: DownloadState.downloading,
        progress: 0.5,
        bytesDownloaded: 512,
      );

      final updated = original.copyWith(progress: 0.75, bytesDownloaded: 768);

      expect(updated.fileId, equals(original.fileId));
      expect(updated.state, equals(original.state));
      expect(updated.progress, equals(0.75));
      expect(updated.bytesDownloaded, equals(768));
    });

    test('should handle unknown size gracefully', () {
      final progress = DownloadProgressModel(
        fileId: 'test',
        state: DownloadState.downloading,
        progress: 0.5,
        bytesDownloaded: 512,
        totalBytes: null,
      );

      expect(progress.formattedSize, equals('Unknown size'));
    });

    test('should handle unknown speed gracefully', () {
      final progress = DownloadProgressModel(
        fileId: 'test',
        state: DownloadState.downloading,
        progress: 0.5,
        bytesDownloaded: 512,
        speedBytesPerSecond: null,
      );

      expect(progress.formattedSpeed, equals('Unknown'));
    });

    test('should handle unknown time remaining gracefully', () {
      final progress = DownloadProgressModel(
        fileId: 'test',
        state: DownloadState.downloading,
        progress: 0.5,
        bytesDownloaded: 512,
        estimatedTimeRemainingSeconds: null,
      );

      expect(progress.formattedTimeRemaining, equals('Unknown'));
    });
  });

  group('Download Progress Equality Tests', () {
    test('should be equal when IDs match', () {
      final progress1 = DownloadProgressModel(
        fileId: 'test',
        state: DownloadState.downloading,
        progress: 0.5,
        bytesDownloaded: 512,
      );

      final progress2 = DownloadProgressModel(
        fileId: 'test',
        state: DownloadState.downloaded,
        progress: 1.0,
        bytesDownloaded: 1024,
      );

      expect(progress1, equals(progress2));
      expect(progress1.hashCode, equals(progress2.hashCode));
    });

    test('should not be equal when IDs differ', () {
      final progress1 = DownloadProgressModel(
        fileId: 'test1',
        state: DownloadState.downloading,
        progress: 0.5,
        bytesDownloaded: 512,
      );

      final progress2 = DownloadProgressModel(
        fileId: 'test2',
        state: DownloadState.downloading,
        progress: 0.5,
        bytesDownloaded: 512,
      );

      expect(progress1, isNot(equals(progress2)));
      expect(progress1.hashCode, isNot(equals(progress2.hashCode)));
    });
  });

  group('Download Progress String Representation Tests', () {
    test('should provide meaningful string representation', () {
      final progress = DownloadProgressModel(
        fileId: 'test_download',
        state: DownloadState.downloading,
        progress: 0.5,
        bytesDownloaded: 512,
        totalBytes: 1024,
      );

      final stringRep = progress.toString();

      expect(stringRep, contains('test_download'));
      expect(stringRep, contains('downloading'));
      expect(stringRep, contains('0.5'));
    });
  });
}
