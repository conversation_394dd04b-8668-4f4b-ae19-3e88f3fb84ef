{"indexes": [{"collectionGroup": "classroom_activities", "queryScope": "COLLECTION", "fields": [{"fieldPath": "classroomId", "order": "ASCENDING"}, {"fieldPath": "isActive", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "classroom_activities", "queryScope": "COLLECTION", "fields": [{"fieldPath": "classroomId", "order": "ASCENDING"}, {"fieldPath": "isActive", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "classroom_activities", "queryScope": "COLLECTION", "fields": [{"fieldPath": "classroomId", "order": "ASCENDING"}, {"fieldPath": "type", "order": "ASCENDING"}, {"fieldPath": "isActive", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "classrooms", "queryScope": "COLLECTION", "fields": [{"fieldPath": "isActive", "order": "ASCENDING"}, {"fieldPath": "name", "order": "ASCENDING"}]}, {"collectionGroup": "classrooms", "queryScope": "COLLECTION", "fields": [{"fieldPath": "studentIds", "arrayConfig": "CONTAINS"}, {"fieldPath": "isActive", "order": "ASCENDING"}, {"fieldPath": "name", "order": "ASCENDING"}]}, {"collectionGroup": "classrooms", "queryScope": "COLLECTION", "fields": [{"fieldPath": "studentIds", "arrayConfig": "CONTAINS"}, {"fieldPath": "isPrimaryClass", "order": "ASCENDING"}, {"fieldPath": "isActive", "order": "ASCENDING"}]}, {"collectionGroup": "classrooms", "queryScope": "COLLECTION", "fields": [{"fieldPath": "teacherId", "order": "ASCENDING"}, {"fieldPath": "isActive", "order": "ASCENDING"}, {"fieldPath": "name", "order": "ASCENDING"}]}, {"collectionGroup": "classrooms", "queryScope": "COLLECTION", "fields": [{"fieldPath": "type", "order": "ASCENDING"}, {"fieldPath": "isActive", "order": "ASCENDING"}, {"fieldPath": "name", "order": "ASCENDING"}]}, {"collectionGroup": "homeworks", "queryScope": "COLLECTION", "fields": [{"fieldPath": "assignmentType", "order": "ASCENDING"}, {"fieldPath": "assignedAt", "order": "DESCENDING"}]}, {"collectionGroup": "homeworks", "queryScope": "COLLECTION", "fields": [{"fieldPath": "classId", "order": "ASCENDING"}, {"fieldPath": "assignedDate", "order": "DESCENDING"}]}, {"collectionGroup": "homeworks", "queryScope": "COLLECTION", "fields": [{"fieldPath": "classId", "order": "ASCENDING"}, {"fieldPath": "assignmentType", "order": "ASCENDING"}, {"fieldPath": "assignedAt", "order": "DESCENDING"}]}, {"collectionGroup": "library_files", "queryScope": "COLLECTION", "fields": [{"fieldPath": "accessType", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "library_files", "queryScope": "COLLECTION", "fields": [{"fieldPath": "classId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "library_files", "queryScope": "COLLECTION", "fields": [{"fieldPath": "fileType", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "library_files", "queryScope": "COLLECTION", "fields": [{"fieldPath": "folderId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "library_files", "queryScope": "COLLECTION", "fields": [{"fieldPath": "restrictedUserIds", "arrayConfig": "CONTAINS"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "library_files", "queryScope": "COLLECTION", "fields": [{"fieldPath": "sharedUserIds", "arrayConfig": "CONTAINS"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "library_files", "queryScope": "COLLECTION", "fields": [{"fieldPath": "subject", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "library_files", "queryScope": "COLLECTION", "fields": [{"fieldPath": "uploaderId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "library_files", "queryScope": "COLLECTION", "fields": [{"fieldPath": "uploaderId", "order": "ASCENDING"}, {"fieldPath": "usageType", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "library_files", "queryScope": "COLLECTION", "fields": [{"fieldPath": "uploaderId", "order": "ASCENDING"}, {"fieldPath": "accessType", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "library_files", "queryScope": "COLLECTION", "fields": [{"fieldPath": "accessType", "order": "ASCENDING"}, {"fieldPath": "usageType", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "library_folders", "queryScope": "COLLECTION", "fields": [{"fieldPath": "accessType", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "library_folders", "queryScope": "COLLECTION", "fields": [{"fieldPath": "accessType", "order": "ASCENDING"}, {"fieldPath": "parentFolderId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "library_folders", "queryScope": "COLLECTION", "fields": [{"fieldPath": "classId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "library_folders", "queryScope": "COLLECTION", "fields": [{"fieldPath": "creatorId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "library_folders", "queryScope": "COLLECTION", "fields": [{"fieldPath": "creatorId", "order": "ASCENDING"}, {"fieldPath": "parentFolderId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "library_folders", "queryScope": "COLLECTION", "fields": [{"fieldPath": "creatorId", "order": "ASCENDING"}, {"fieldPath": "usageType", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "library_folders", "queryScope": "COLLECTION", "fields": [{"fieldPath": "creatorId", "order": "ASCENDING"}, {"fieldPath": "folderType", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "library_folders", "queryScope": "COLLECTION", "fields": [{"fieldPath": "folderType", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "library_folders", "queryScope": "COLLECTION", "fields": [{"fieldPath": "parentFolderId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "library_folders", "queryScope": "COLLECTION", "fields": [{"fieldPath": "restrictedUserIds", "arrayConfig": "CONTAINS"}, {"fieldPath": "parentFolderId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "library_folders", "queryScope": "COLLECTION", "fields": [{"fieldPath": "sharedUserIds", "arrayConfig": "CONTAINS"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "library_folders", "queryScope": "COLLECTION", "fields": [{"fieldPath": "subject", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "library_upload_sessions", "queryScope": "COLLECTION", "fields": [{"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "ASCENDING"}]}, {"collectionGroup": "library_upload_sessions", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "library_upload_sessions", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "announcements", "queryScope": "COLLECTION", "fields": [{"fieldPath": "isActive", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "announcements", "queryScope": "COLLECTION", "fields": [{"fieldPath": "classroomId", "order": "ASCENDING"}, {"fieldPath": "isActive", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "announcements", "queryScope": "COLLECTION", "fields": [{"fieldPath": "type", "order": "ASCENDING"}, {"fieldPath": "isActive", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "announcements", "queryScope": "COLLECTION", "fields": [{"fieldPath": "priority", "order": "ASCENDING"}, {"fieldPath": "isActive", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "announcements", "queryScope": "COLLECTION", "fields": [{"fieldPath": "usageType", "order": "ASCENDING"}, {"fieldPath": "isActive", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "announcements", "queryScope": "COLLECTION", "fields": [{"fieldPath": "isPinned", "order": "ASCENDING"}, {"fieldPath": "isActive", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "announcements", "queryScope": "COLLECTION", "fields": [{"fieldPath": "targetAudience", "arrayConfig": "CONTAINS"}, {"fieldPath": "isActive", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "announcements", "queryScope": "COLLECTION", "fields": [{"fieldPath": "readBy", "arrayConfig": "CONTAINS"}, {"fieldPath": "isActive", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "announcements", "queryScope": "COLLECTION", "fields": [{"fieldPath": "authorId", "order": "ASCENDING"}, {"fieldPath": "isActive", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "announcements", "queryScope": "COLLECTION", "fields": [{"fieldPath": "organizationId", "order": "ASCENDING"}, {"fieldPath": "isActive", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "announcements", "queryScope": "COLLECTION", "fields": [{"fieldPath": "tags", "arrayConfig": "CONTAINS"}, {"fieldPath": "isActive", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "announcement_reads", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "readAt", "order": "DESCENDING"}]}, {"collectionGroup": "announcement_reads", "queryScope": "COLLECTION", "fields": [{"fieldPath": "announcementId", "order": "ASCENDING"}, {"fieldPath": "readAt", "order": "DESCENDING"}]}], "fieldOverrides": []}