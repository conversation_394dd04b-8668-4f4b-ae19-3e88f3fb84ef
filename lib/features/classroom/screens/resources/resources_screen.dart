import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_symbols_icons/symbols.dart';

import '../../../../core/widgets/responsive/responsive_padding.dart';
import '../../models/class_model.dart';
import '../../../debug/mock_data/generators/classroom_mock_generator.dart';

import 'sections/filter_tabs_section.dart';
import 'sections/placeholder_content_section.dart';

/// Placeholder screen for classroom resources/digital library
/// This will be fully implemented when the Digital Library feature is developed
class ResourcesScreen extends StatefulWidget {
  /// ID of the classroom to display resources for
  final String classroomId;

  const ResourcesScreen({super.key, required this.classroomId});

  @override
  State<ResourcesScreen> createState() => _ResourcesScreenState();
}

class _ResourcesScreenState extends State<ResourcesScreen> {
  /// Current classroom data
  ClassModel? _classroom;

  @override
  void initState() {
    super.initState();
    _loadClassroomData();
  }

  /// Load classroom data
  void _loadClassroomData() {
    try {
      _classroom = mockClassesList.firstWhere(
        (classroom) => classroom.id == widget.classroomId,
      );
      setState(() {});
    } catch (e) {
      debugPrint('Classroom not found: ${widget.classroomId}');
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(
          _classroom != null ? '${_classroom!.name} Resources' : 'Resources',
          style: theme.textTheme.titleLarge,
        ),
        elevation: 0,
        backgroundColor: theme.colorScheme.surface,
        foregroundColor: theme.colorScheme.onSurface,
        actions: [
          IconButton(
            icon: const Icon(Symbols.filter_list),
            onPressed: () {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Filters coming soon')),
              );
            },
          ),
        ],
      ),
      body: ResponsivePadding(
        mobile: EdgeInsets.all(16.w),
        child: Column(
          children: [
            // Filter tabs section
            const FilterTabsSection(),

            SizedBox(height: 24.h),

            // Main content area
            const Expanded(
              child: PlaceholderContentSection(),
            ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _handleUploadResource,
        icon: const Icon(Symbols.upload),
        label: const Text('Upload'),
        backgroundColor: theme.colorScheme.primary,
        foregroundColor: theme.colorScheme.onPrimary,
      ),
    );
  }

  /// Handle upload resource action
  void _handleUploadResource() {
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('Upload feature coming soon')));
  }
}
