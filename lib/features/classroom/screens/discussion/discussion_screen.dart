import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_symbols_icons/symbols.dart';

import '../../../../core/widgets/responsive/responsive_padding.dart';
import '../../models/class_model.dart';
import '../../../debug/mock_data/generators/classroom_mock_generator.dart';

import 'sections/post_type_tabs_section.dart';
import 'sections/placeholder_content_section.dart';

/// Placeholder screen for classroom discussions
/// This will be fully implemented when the Discussion feature is developed
class DiscussionScreen extends StatefulWidget {
  /// ID of the classroom to display discussions for
  final String classroomId;

  const DiscussionScreen({super.key, required this.classroomId});

  @override
  State<DiscussionScreen> createState() => _DiscussionScreenState();
}

class _DiscussionScreenState extends State<DiscussionScreen> {
  /// Current classroom data
  ClassModel? _classroom;

  @override
  void initState() {
    super.initState();
    _loadClassroomData();
  }

  /// Load classroom data
  void _loadClassroomData() {
    try {
      _classroom = mockClassesList.firstWhere(
        (classroom) => classroom.id == widget.classroomId,
      );
      setState(() {});
    } catch (e) {
      debugPrint('Classroom not found: ${widget.classroomId}');
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(
          _classroom != null
              ? '${_classroom!.name} Discussions'
              : 'Discussions',
          style: theme.textTheme.titleLarge,
        ),
        elevation: 0,
        backgroundColor: theme.colorScheme.surface,
        foregroundColor: theme.colorScheme.onSurface,
        actions: [
          IconButton(
            icon: const Icon(Symbols.search),
            onPressed: () {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Search coming soon')),
              );
            },
          ),
        ],
      ),
      body: ResponsivePadding(
        mobile: EdgeInsets.all(16.w),
        child: Column(
          children: [
            // Post type tabs section
            const PostTypeTabsSection(),

            SizedBox(height: 24.h),

            // Main content area
            const Expanded(
              child: PlaceholderContentSection(),
            ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _handleCreatePost,
        icon: const Icon(Symbols.add),
        label: const Text('New Post'),
        backgroundColor: theme.colorScheme.primary,
        foregroundColor: theme.colorScheme.onPrimary,
      ),
    );
  }

  /// Handle create post action
  void _handleCreatePost() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Create post feature coming soon')),
    );
  }
}
