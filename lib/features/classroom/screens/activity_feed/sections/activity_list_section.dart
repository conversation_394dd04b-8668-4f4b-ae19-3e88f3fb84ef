import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../models/activity_model.dart';
import '../../../widgets/activity_tile.dart';

/// Activity list section displaying the list of activities with count
class ActivityListSection extends StatelessWidget {
  final List<ActivityModel> activities;
  final Function(ActivityModel) onActivityTap;

  const ActivityListSection({
    super.key,
    required this.activities,
    required this.onActivityTap,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Column(
      children: [
        // Results count
        Text(
          '${activities.length} activit${activities.length != 1 ? 'ies' : 'y'}',
          style: theme.textTheme.bodyMedium?.copyWith(
            color: theme.colorScheme.onSurface.withValues(
              alpha: 0.7,
            ),
          ),
        ),

        SizedBox(height: 12.h),

        // Activities list
        Expanded(
          child: ListView.separated(
            itemCount: activities.length,
            separatorBuilder: (context, index) => SizedBox(height: 12.h),
            itemBuilder: (context, index) {
              final activity = activities[index];
              return ActivityTile(
                activity: activity,
                onTap: () => onActivityTap(activity),
                isCompact: false, // Full version for activity feed
              );
            },
          ),
        ),
      ],
    );
  }
}
