import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_symbols_icons/symbols.dart';
import '../../../enums/activity_type.dart';

/// Empty state section when no activities are found
class EmptyStateSection extends StatelessWidget {
  final String searchQuery;
  final ActivityType? selectedFilter;

  const EmptyStateSection({
    super.key,
    required this.searchQuery,
    this.selectedFilter,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final hasFilters = searchQuery.isNotEmpty || selectedFilter != null;

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Symbols.timeline,
            size: 64.sp,
            color: theme.colorScheme.onSurface.withValues(alpha: 0.3),
          ),
          SizedBox(height: 16.h),
          Text(
            hasFilters ? 'No activities found' : 'No activities yet',
            style: theme.textTheme.titleMedium?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            hasFilters
                ? 'Try adjusting your search or filters'
                : 'Activities will appear here as they are created',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
