import 'package:flutter/material.dart';
import '../../../widgets/activity_filter_chips.dart';
import '../../../enums/activity_type.dart';

/// Activity filter section for activity feed screen
class ActivityFilterSection extends StatelessWidget {
  final ActivityType? selectedFilter;
  final Function(ActivityType?) onFilterChanged;

  const ActivityFilterSection({
    super.key,
    required this.selectedFilter,
    required this.onFilterChanged,
  });

  @override
  Widget build(BuildContext context) {
    return ActivityFilterChips(
      selectedFilter: selectedFilter,
      onFilterChanged: onFilterChanged,
    );
  }
}
