import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_symbols_icons/symbols.dart';

import '../../../../core/widgets/responsive/responsive_padding.dart';
import '../../models/activity_model.dart';
import '../../enums/activity_type.dart';
import '../../controllers/activity_controller.dart';
import '../../controllers/classroom_controller.dart';

import 'sections/activity_search_section.dart';
import 'sections/activity_filter_section.dart';
import 'sections/activity_list_section.dart';
import 'sections/empty_state_section.dart';
import 'sections/error_state_section.dart';

/// Screen displaying full activity feed for a classroom with filters and search
class ActivityFeedScreen extends ConsumerStatefulWidget {
  /// ID of the classroom to display activities for
  final String classroomId;

  const ActivityFeedScreen({super.key, required this.classroomId});

  @override
  ConsumerState<ActivityFeedScreen> createState() => _ActivityFeedScreenState();
}

class _ActivityFeedScreenState extends ConsumerState<ActivityFeedScreen> {
  /// Current search query
  String _searchQuery = '';

  /// Currently selected activity type filter
  ActivityType? _selectedFilter;

  /// Get filtered activities based on search and filter
  List<ActivityModel> _getFilteredActivities(List<ActivityModel> activities) {
    var filtered = activities;

    // Apply type filter
    if (_selectedFilter != null) {
      filtered = filtered
          .where((activity) => activity.type == _selectedFilter)
          .toList();
    }

    // Apply search filter
    if (_searchQuery.isNotEmpty) {
      filtered = filtered.where((activity) {
        final query = _searchQuery.toLowerCase();
        return activity.title.toLowerCase().contains(query) ||
            (activity.description?.toLowerCase().contains(query) ?? false) ||
            activity.createdByName.toLowerCase().contains(query);
      }).toList();
    }

    // Sort by date (newest first)
    filtered.sort((a, b) => b.createdAt.compareTo(a.createdAt));

    return filtered;
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    // Watch classroom detail
    final classroomAsync = ref.watch(
      classroomDetailProvider(widget.classroomId),
    );

    // Watch activities for this classroom
    final activitiesAsync = ref.watch(
      classroomActivitiesProvider(widget.classroomId),
    );

    return Scaffold(
      appBar: AppBar(
        title: classroomAsync.when(
          data: (classroom) => Text(
            '${classroom?.name ?? 'Classroom'} Activity',
            style: theme.textTheme.titleLarge,
          ),
          loading: () => const Text('Activity Feed'),
          error: (_, __) => const Text('Activity Feed'),
        ),
        elevation: 0,
        backgroundColor: theme.colorScheme.surface,
        foregroundColor: theme.colorScheme.onSurface,
      ),
      body: ResponsivePadding(
        mobile: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Search section
            ActivitySearchSection(
              onSearchChanged: (query) {
                setState(() {
                  _searchQuery = query;
                });
              },
            ),

            SizedBox(height: 16.h),

            // Filter section
            ActivityFilterSection(
              selectedFilter: _selectedFilter,
              onFilterChanged: (filter) {
                setState(() {
                  _selectedFilter = filter;
                });
              },
            ),

            SizedBox(height: 16.h),

            // Activities content
            Expanded(
              child: activitiesAsync.when(
                data: (activities) {
                  final filteredActivities = _getFilteredActivities(activities);

                  if (filteredActivities.isEmpty) {
                    return EmptyStateSection(
                      searchQuery: _searchQuery,
                      selectedFilter: _selectedFilter,
                    );
                  }

                  return ActivityListSection(
                    activities: filteredActivities,
                    onActivityTap: _handleActivityTap,
                  );
                },
                loading: () => const Center(child: CircularProgressIndicator()),
                error: (error, stack) => ErrorStateSection(
                  error: error.toString(),
                ),
              ),
            ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _handleCreateAnnouncement,
        icon: const Icon(Symbols.add),
        label: const Text('Announcement'),
        backgroundColor: theme.colorScheme.primary,
        foregroundColor: theme.colorScheme.onPrimary,
      ),
    );
  }

  /// Handle activity item tap
  void _handleActivityTap(ActivityModel activity) {
    if (!activity.type.isClickable) return;

    // TODO: Implement navigation to specific activity details
    // This will be implemented when individual activity detail screens are created
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('${activity.type.label} details coming soon')),
    );
  }

  /// Handle create announcement action
  void _handleCreateAnnouncement() {
    // TODO: Check if user has permission to create announcements
    // For now, show a dialog or navigate to create announcement screen
    _showCreateAnnouncementDialog();
  }

  /// Show create announcement dialog
  void _showCreateAnnouncementDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Create Announcement'),
        content: const Text('Announcement creation feature coming soon'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
}
