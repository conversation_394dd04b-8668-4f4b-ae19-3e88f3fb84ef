import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import '../../../../../core/routes/app_routes.dart';
import '../../../models/activity_model.dart';
import 'recent_activity_feed_widget.dart';
import '../../../enums/activity_type.dart';

/// Recent activity section for classroom detail screen
class RecentActivitySection extends StatelessWidget {
  final String classroomId;
  final AsyncValue<List<ActivityModel>> activitiesAsync;

  const RecentActivitySection({
    super.key,
    required this.classroomId,
    required this.activitiesAsync,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Section header
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Recent Activity',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            activitiesAsync.when(
              data: (activities) => activities.isNotEmpty
                  ? TextButton(
                      onPressed: () => _navigateToActivityFeed(context),
                      child: const Text('View All'),
                    )
                  : const SizedBox.shrink(),
              loading: () => const SizedBox.shrink(),
              error: (_, __) => const SizedBox.shrink(),
            ),
          ],
        ),

        SizedBox(height: 12.h),

        // Activities content
        activitiesAsync.when(
          loading: () => const Center(child: CircularProgressIndicator()),
          error: (error, stackTrace) => Text(
            'Failed to load activities: $error',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.error,
            ),
          ),
          data: (activities) {
            if (activities.isEmpty) {
              return Text(
                'No recent activity',
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                ),
              );
            }

            return RecentActivityFeedWidget(
              activities: activities,
              onActivityTap: (activity) =>
                  _handleActivityTap(context, activity),
            );
          },
        ),
      ],
    );
  }

  void _navigateToActivityFeed(BuildContext context) {
    context.pushNamed(
      RouteNames.activityFeed,
      pathParameters: {'id': classroomId},
    );
  }

  void _handleActivityTap(BuildContext context, ActivityModel activity) {
    if (!activity.type.isClickable) return;

    switch (activity.type) {
      case ActivityType.homework:
        if (activity.referenceId != null) {
          context.pushNamed(
            RouteNames.homeworkDetail,
            pathParameters: {'id': activity.referenceId!},
          );
        }
        break;
      case ActivityType.announcement:
        // Show announcement details in a dialog or bottom sheet
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: Text(activity.title),
            content: Text(activity.description ?? 'No description'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Close'),
              ),
            ],
          ),
        );
        break;
      default:
        // For other types, show coming soon message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('${activity.type.label} details coming soon')),
        );
    }
  }
}
