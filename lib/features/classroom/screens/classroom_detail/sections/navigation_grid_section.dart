import 'package:flutter/material.dart';
import '../../../models/class_model.dart';
import 'navigation_grid_widget.dart';

/// Navigation grid section for classroom detail screen
class NavigationGridSection extends StatelessWidget {
  final String classroomId;
  final ClassModel classroom;
  final Function(String) onNavigate;

  const NavigationGridSection({
    super.key,
    required this.classroomId,
    required this.classroom,
    required this.onNavigate,
  });

  @override
  Widget build(BuildContext context) {
    return NavigationGridWidget(
      classroomId: classroomId,
      onNavigate: (action) => onNavigate(action),
    );
  }
}
