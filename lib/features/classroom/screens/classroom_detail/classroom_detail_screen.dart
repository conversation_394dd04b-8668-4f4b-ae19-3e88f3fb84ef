import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:material_symbols_icons/symbols.dart';

import '../../../../core/routes/app_routes.dart';
import '../../../../core/widgets/responsive/responsive_padding.dart';
import '../../models/class_model.dart';
import '../../controllers/classroom_controller.dart';
import '../../controllers/activity_controller.dart';

import 'sections/classroom_header_section.dart';
import 'sections/navigation_grid_section.dart';
import 'sections/recent_activity_section.dart';
import 'sections/error_state_section.dart';
import 'sections/not_found_state_section.dart';

/// Screen displaying detailed information about a specific classroom
class ClassroomDetailScreen extends ConsumerWidget {
  /// ID of the classroom to display
  final String classroomId;

  const ClassroomDetailScreen({super.key, required this.classroomId});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final classroomAsync = ref.watch(classroomDetailProvider(classroomId));
    final activitiesAsync = ref.watch(
      recentClassroomActivitiesProvider(classroomId),
    );

    return Scaffold(
      appBar: AppBar(
        title: classroomAsync.when(
          data: (classroom) => Text(
            classroom?.name ?? 'Classroom',
            style: theme.textTheme.titleLarge,
          ),
          loading: () => Text('Loading...', style: theme.textTheme.titleLarge),
          error: (_, __) => Text('Error', style: theme.textTheme.titleLarge),
        ),
        elevation: 0,
        backgroundColor: theme.colorScheme.surface,
        foregroundColor: theme.colorScheme.onSurface,
        actions: [
          IconButton(
            icon: const Icon(Symbols.more_vert),
            onPressed: () {
              // TODO: Show classroom options menu
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Classroom options coming soon')),
              );
            },
          ),
        ],
      ),
      body: classroomAsync.when(
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (error, stackTrace) => ErrorStateSection(
          error: error.toString(),
          onRetry: () => ref.invalidate(classroomDetailProvider(classroomId)),
        ),
        data: (classroom) {
          if (classroom == null) {
            return const NotFoundStateSection();
          }

          return ResponsivePadding(
            mobile: EdgeInsets.all(16.w),
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Classroom header section
                  ClassroomHeaderSection(classroom: classroom),

                  SizedBox(height: 24.h),

                  // Navigation grid section
                  NavigationGridSection(
                    classroomId: classroomId,
                    classroom: classroom,
                    onNavigate: (action) =>
                        _handleNavigation(context, ref, action, classroom),
                  ),

                  SizedBox(height: 24.h),

                  // Recent activity section
                  RecentActivitySection(
                    classroomId: classroomId,
                    activitiesAsync: activitiesAsync,
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  /// Handle navigation to different sections
  void _handleNavigation(
    BuildContext context,
    WidgetRef ref,
    String section,
    ClassModel classroom,
  ) {
    switch (section) {
      case 'homework':
        // Navigate to homework list with classroom filter
        context.pushNamed(
          RouteNames.homeworkList,
          queryParameters: {'classroomId': classroomId},
        );
        break;
      case 'notes':
        // TODO: Navigate to notes screen
        _showComingSoonMessage(context, 'Notes');
        break;
      case 'quizzes':
        // TODO: Navigate to quizzes screen
        _showComingSoonMessage(context, 'Quizzes');
        break;
      case 'announcements':
        // Navigate to announcements list with classroom filter
        context.pushNamed(
          RouteNames.announcementsList,
          queryParameters: {'classroomId': classroomId},
        );
        break;
      case 'discussions':
        context.pushNamed(
          RouteNames.classroomDiscussion,
          pathParameters: {'id': classroomId},
        );
        break;
      case 'attendance':
        // TODO: Navigate to attendance screen
        _showComingSoonMessage(context, 'Attendance');
        break;
      case 'digital_library':
        context.pushNamed(
          RouteNames.classroomResources,
          pathParameters: {'id': classroomId},
        );
        break;
      case 'study_plans':
        // Navigate to study plans list with classroom filter
        context.pushNamed(
          RouteNames.studyPlansList,
          queryParameters: {'classroomId': classroomId},
        );
        break;
      case 'chat':
        // Navigate to chat list with classroom filter
        context.pushNamed(
          RouteNames.chatList,
          queryParameters: {'classroomId': classroomId},
        );
        break;
    }
  }

  /// Show coming soon message for unimplemented features
  void _showComingSoonMessage(BuildContext context, String feature) {
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(SnackBar(content: Text('$feature feature coming soon')));
  }
}
