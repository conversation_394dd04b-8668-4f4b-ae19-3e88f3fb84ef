import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../widgets/classroom_search_bar.dart';
import '../../../controllers/classroom_controller.dart';

/// Search bar section for classrooms list screen
class SearchBarSection extends ConsumerWidget {
  const SearchBarSection({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return ClassroomSearchBar(
      onSearchChanged: (query) {
        ref.read(classroomSearchProvider.notifier).state = query;
      },
    );
  }
}
