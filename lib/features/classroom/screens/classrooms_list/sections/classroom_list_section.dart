import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../models/class_model.dart';
import '../../../widgets/classroom_card.dart';

/// Classroom list section displaying the list of classrooms with count
class ClassroomListSection extends StatelessWidget {
  final List<ClassModel> classrooms;
  final Function(String) onClassroomTap;
  final Function(String, ClassModel) onMenuAction;

  const ClassroomListSection({
    super.key,
    required this.classrooms,
    required this.onClassroomTap,
    required this.onMenuAction,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Results count
        Text(
          '${classrooms.length} classroom${classrooms.length != 1 ? 's' : ''}',
          style: theme.textTheme.bodyMedium?.copyWith(
            color: theme.colorScheme.onSurface.withValues(
              alpha: 0.7,
            ),
          ),
        ),

        SizedBox(height: 12.h),

        // Classroom list
        Expanded(
          child: ListView.separated(
            itemCount: classrooms.length,
            separatorBuilder: (context, index) => SizedBox(height: 12.h),
            itemBuilder: (context, index) {
              final classroom = classrooms[index];
              return ClassroomCard(
                classroom: classroom,
                onTap: () => onClassroomTap(classroom.id),
                onMenuAction: (action) => onMenuAction(action, classroom),
              );
            },
          ),
        ),
      ],
    );
  }
}
