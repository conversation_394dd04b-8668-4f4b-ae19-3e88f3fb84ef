import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../widgets/classroom_filter_chips.dart';
import '../../../controllers/classroom_controller.dart';

/// Filter chips section for classrooms list screen
class FilterChipsSection extends ConsumerWidget {
  const FilterChipsSection({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return ClassroomFilterChips(
      selectedFilter: ref.watch(classroomFilterProvider),
      onFilterChanged: (filter) {
        ref.read(classroomFilterProvider.notifier).state = filter;
      },
    );
  }
}
