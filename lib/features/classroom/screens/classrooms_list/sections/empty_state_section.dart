import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_symbols_icons/symbols.dart';
import '../../../enums/classroom_type.dart';

/// Empty state section when no classrooms are found
class EmptyStateSection extends StatelessWidget {
  final String searchQuery;
  final ClassroomType? selectedFilter;

  const EmptyStateSection({
    super.key,
    required this.searchQuery,
    this.selectedFilter,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final hasFilters = searchQuery.isNotEmpty || selectedFilter != null;

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            hasFilters ? Symbols.search_off : Symbols.school,
            size: 64.sp,
            color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
          ),
          Sized<PERSON>ox(height: 16.h),
          Text(
            hasFilters ? 'No classrooms found' : 'No classrooms yet',
            style: theme.textTheme.titleMedium?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            hasFilters
                ? 'Try adjusting your search or filters'
                : 'Create your first classroom to get started',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
