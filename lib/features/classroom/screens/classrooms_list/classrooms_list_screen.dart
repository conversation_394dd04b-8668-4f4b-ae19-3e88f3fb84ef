import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';

import '../../../../core/routes/app_routes.dart';
import '../../../../core/widgets/responsive/responsive_padding.dart';
import '../../models/class_model.dart';
import '../../controllers/classroom_controller.dart';

import 'sections/search_bar_section.dart';
import 'sections/filter_chips_section.dart';
import 'sections/classroom_list_section.dart';
import 'sections/empty_state_section.dart';
import 'sections/error_state_section.dart';

/// Screen displaying all enrolled classrooms with filtering and search functionality
class ClassroomsListScreen extends ConsumerStatefulWidget {
  const ClassroomsListScreen({super.key});

  @override
  ConsumerState<ClassroomsListScreen> createState() =>
      _ClassroomsListScreenState();
}

class _ClassroomsListScreenState extends ConsumerState<ClassroomsListScreen> {
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final classroomsAsync = ref.watch(filteredClassroomsProvider);

    return Scaffold(
      appBar: AppBar(
        title: Text('My Classrooms', style: theme.textTheme.titleLarge),
        elevation: 0,
        backgroundColor: theme.colorScheme.surface,
        foregroundColor: theme.colorScheme.onSurface,
      ),
      body: ResponsivePadding(
        mobile: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Search bar section
            const SearchBarSection(),

            SizedBox(height: 16.h),

            // Filter chips section
            const FilterChipsSection(),

            SizedBox(height: 16.h),

            // Results and content
            Expanded(
              child: classroomsAsync.when(
                loading: () => const Center(child: CircularProgressIndicator()),
                error: (error, stackTrace) => ErrorStateSection(
                  error: error.toString(),
                  onRetry: () {
                    ref.invalidate(filteredClassroomsProvider);
                    ref.invalidate(userClassroomsProvider);
                  },
                ),
                data: (classrooms) {
                  if (classrooms.isEmpty) {
                    return EmptyStateSection(
                      searchQuery: ref.watch(classroomSearchProvider),
                      selectedFilter: ref.watch(classroomFilterProvider),
                    );
                  }

                  return ClassroomListSection(
                    classrooms: classrooms,
                    onClassroomTap: _navigateToClassroomDetail,
                    onMenuAction: _handleMenuAction,
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Navigate to classroom detail screen
  void _navigateToClassroomDetail(String classroomId) {
    context.pushNamed(
      RouteNames.classroomDetail,
      pathParameters: {'id': classroomId},
    );
  }

  /// Handle menu actions on classroom cards
  void _handleMenuAction(String action, ClassModel classroom) {
    switch (action) {
      case 'archive':
        // TODO: Implement archive functionality
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Archived ${classroom.name}')));
        break;
      case 'leave':
        // TODO: Implement leave classroom functionality
        _showLeaveClassroomDialog(classroom);
        break;
    }
  }

  /// Show leave classroom confirmation dialog
  void _showLeaveClassroomDialog(ClassModel classroom) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Leave Classroom'),
        content: Text('Are you sure you want to leave ${classroom.name}?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              // TODO: Implement actual leave logic
              ScaffoldMessenger.of(
                context,
              ).showSnackBar(SnackBar(content: Text('Left ${classroom.name}')));
            },
            child: const Text('Leave'),
          ),
        ],
      ),
    );
  }
}
