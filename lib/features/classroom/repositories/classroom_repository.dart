import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:logger/logger.dart';

import '../../../core/constants/firebase_collections.dart';
import '../models/class_model.dart';
import '../enums/classroom_type.dart';

/// Repository for managing classroom data with Firebase Firestore
/// Follows the same patterns as HomeworkRepository for consistency
class ClassroomRepository {
  static final ClassroomRepository _instance = ClassroomRepository._internal();
  factory ClassroomRepository() => _instance;
  ClassroomRepository._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final Logger _logger = Logger();

  // Collection name - using 'classrooms' to distinguish from homework's 'classes'
  static const String _classroomsCollection = FirebaseCollections.classrooms;

  /// Fetch all active classrooms
  Future<List<ClassModel>> getAllClassrooms() async {
    try {
      _logger.i('Fetching all active classrooms');

      final querySnapshot = await _firestore
          .collection(_classroomsCollection)
          .where('isActive', isEqualTo: true)
          .orderBy('name')
          .get();

      final classroomList = querySnapshot.docs
          .map((doc) {
            try {
              final data = doc.data();
              data['id'] = doc.id; // Ensure the document ID is included
              return ClassModel.fromJson(data);
            } catch (e) {
              _logger.e('Error parsing classroom document ${doc.id}: $e');
              return null;
            }
          })
          .where((classroom) => classroom != null)
          .cast<ClassModel>()
          .toList();

      _logger.i('Successfully fetched ${classroomList.length} classrooms');
      return classroomList;
    } catch (e) {
      _logger.e('Error fetching classrooms: $e');
      rethrow;
    }
  }

  /// Fetch a specific classroom by ID
  Future<ClassModel?> getClassroomById(String classroomId) async {
    try {
      _logger.i('Fetching classroom by ID: $classroomId');

      final docSnapshot = await _firestore
          .collection(_classroomsCollection)
          .doc(classroomId)
          .get();

      if (!docSnapshot.exists) {
        _logger.w('Classroom not found: $classroomId');
        return null;
      }

      final data = docSnapshot.data()!;
      data['id'] = docSnapshot.id;
      final classroom = ClassModel.fromJson(data);

      _logger.i('Successfully fetched classroom: ${classroom.name}');
      return classroom;
    } catch (e) {
      _logger.e('Error fetching classroom by ID: $e');
      rethrow;
    }
  }

  /// Fetch classrooms where a specific student is enrolled
  Future<List<ClassModel>> getClassroomsForStudent(String studentId) async {
    try {
      _logger.i('Fetching classrooms for student: $studentId');

      final querySnapshot = await _firestore
          .collection(_classroomsCollection)
          .where('studentIds', arrayContains: studentId)
          .where('isActive', isEqualTo: true)
          .orderBy('name')
          .get();

      final classroomList = querySnapshot.docs
          .map((doc) {
            try {
              final data = doc.data();
              data['id'] = doc.id;
              return ClassModel.fromJson(data);
            } catch (e) {
              _logger.e('Error parsing classroom document ${doc.id}: $e');
              return null;
            }
          })
          .where((classroom) => classroom != null)
          .cast<ClassModel>()
          .toList();

      _logger.i(
        'Successfully fetched ${classroomList.length} classrooms for student $studentId',
      );
      return classroomList;
    } catch (e) {
      _logger.e('Error fetching classrooms for student: $e');
      rethrow;
    }
  }

  /// Fetch classrooms taught by a specific teacher
  Future<List<ClassModel>> getClassroomsForTeacher(String teacherId) async {
    try {
      _logger.i('Fetching classrooms for teacher: $teacherId');

      final querySnapshot = await _firestore
          .collection(_classroomsCollection)
          .where('teacherId', isEqualTo: teacherId)
          .where('isActive', isEqualTo: true)
          .orderBy('name')
          .get();

      final classroomList = querySnapshot.docs
          .map((doc) {
            try {
              final data = doc.data();
              data['id'] = doc.id;
              return ClassModel.fromJson(data);
            } catch (e) {
              _logger.e('Error parsing classroom document ${doc.id}: $e');
              return null;
            }
          })
          .where((classroom) => classroom != null)
          .cast<ClassModel>()
          .toList();

      _logger.i(
        'Successfully fetched ${classroomList.length} classrooms for teacher $teacherId',
      );
      return classroomList;
    } catch (e) {
      _logger.e('Error fetching classrooms for teacher: $e');
      rethrow;
    }
  }

  /// Fetch classrooms by type
  Future<List<ClassModel>> getClassroomsByType(ClassroomType type) async {
    try {
      _logger.i('Fetching classrooms by type: ${type.label}');

      final querySnapshot = await _firestore
          .collection(_classroomsCollection)
          .where('type', isEqualTo: type.value)
          .where('isActive', isEqualTo: true)
          .orderBy('name')
          .get();

      final classroomList = querySnapshot.docs
          .map((doc) {
            try {
              final data = doc.data();
              data['id'] = doc.id;
              return ClassModel.fromJson(data);
            } catch (e) {
              _logger.e('Error parsing classroom document ${doc.id}: $e');
              return null;
            }
          })
          .where((classroom) => classroom != null)
          .cast<ClassModel>()
          .toList();

      _logger.i(
        'Successfully fetched ${classroomList.length} classrooms of type ${type.label}',
      );
      return classroomList;
    } catch (e) {
      _logger.e('Error fetching classrooms by type: $e');
      rethrow;
    }
  }

  /// Get primary classroom for a student
  Future<ClassModel?> getPrimaryClassroomForStudent(String studentId) async {
    try {
      _logger.i('Fetching primary classroom for student: $studentId');

      final querySnapshot = await _firestore
          .collection(_classroomsCollection)
          .where('studentIds', arrayContains: studentId)
          .where('isPrimaryClass', isEqualTo: true)
          .where('isActive', isEqualTo: true)
          .limit(1)
          .get();

      if (querySnapshot.docs.isEmpty) {
        _logger.w('No primary classroom found for student: $studentId');
        return null;
      }

      final doc = querySnapshot.docs.first;
      final data = doc.data();
      data['id'] = doc.id;
      final classroom = ClassModel.fromJson(data);

      _logger.i('Successfully fetched primary classroom: ${classroom.name}');
      return classroom;
    } catch (e) {
      _logger.e('Error fetching primary classroom for student: $e');
      rethrow;
    }
  }

  /// Create a new classroom
  Future<void> createClassroom(ClassModel classroom) async {
    try {
      _logger.i('Creating new classroom: ${classroom.name}');

      await _firestore
          .collection(_classroomsCollection)
          .doc(classroom.id)
          .set(classroom.toJson());

      _logger.i('Successfully created classroom: ${classroom.name}');
    } catch (e) {
      _logger.e('Error creating classroom: $e');
      rethrow;
    }
  }

  /// Update an existing classroom
  Future<void> updateClassroom(ClassModel classroom) async {
    try {
      _logger.i('Updating classroom: ${classroom.name}');

      await _firestore
          .collection(_classroomsCollection)
          .doc(classroom.id)
          .update(classroom.toJson());

      _logger.i('Successfully updated classroom: ${classroom.name}');
    } catch (e) {
      _logger.e('Error updating classroom: $e');
      rethrow;
    }
  }

  /// Delete a classroom (soft delete by setting isActive to false)
  Future<void> deleteClassroom(String classroomId) async {
    try {
      _logger.i('Deleting classroom: $classroomId');

      await _firestore
          .collection(_classroomsCollection)
          .doc(classroomId)
          .update({'isActive': false});

      _logger.i('Successfully deleted classroom: $classroomId');
    } catch (e) {
      _logger.e('Error deleting classroom: $e');
      rethrow;
    }
  }

  /// Add a student to a classroom
  Future<void> addStudentToClassroom(
    String classroomId,
    String studentId,
  ) async {
    try {
      _logger.i('Adding student $studentId to classroom $classroomId');

      await _firestore
          .collection(_classroomsCollection)
          .doc(classroomId)
          .update({
            'studentIds': FieldValue.arrayUnion([studentId]),
          });

      _logger.i('Successfully added student to classroom');
    } catch (e) {
      _logger.e('Error adding student to classroom: $e');
      rethrow;
    }
  }

  /// Remove a student from a classroom
  Future<void> removeStudentFromClassroom(
    String classroomId,
    String studentId,
  ) async {
    try {
      _logger.i('Removing student $studentId from classroom $classroomId');

      await _firestore
          .collection(_classroomsCollection)
          .doc(classroomId)
          .update({
            'studentIds': FieldValue.arrayRemove([studentId]),
          });

      _logger.i('Successfully removed student from classroom');
    } catch (e) {
      _logger.e('Error removing student from classroom: $e');
      rethrow;
    }
  }
}
