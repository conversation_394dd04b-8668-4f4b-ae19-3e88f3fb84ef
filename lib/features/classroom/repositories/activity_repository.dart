import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:logger/logger.dart';

import '../../../core/constants/firebase_collections.dart';
import '../models/activity_model.dart';
import '../enums/activity_type.dart';

/// Repository for managing classroom activity feed data with Firebase Firestore
/// Handles activities like homework, notices, announcements, etc.
class ActivityRepository {
  static final ActivityRepository _instance = ActivityRepository._internal();
  factory ActivityRepository() => _instance;
  ActivityRepository._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final Logger _logger = Logger();

  // Collection name
  static const String _activitiesCollection =
      FirebaseCollections.classroomActivities;

  /// Fetch all activities for a specific classroom
  Future<List<ActivityModel>> getActivitiesForClassroom(
    String classroomId,
  ) async {
    try {
      _logger.i('Fetching activities for classroom: $classroomId');

      final querySnapshot = await _firestore
          .collection(_activitiesCollection)
          .where('classroomId', isEqualTo: classroomId)
          .where('isActive', isEqualTo: true)
          .orderBy('createdAt', descending: true)
          .get();

      final activityList = querySnapshot.docs
          .map((doc) {
            try {
              final data = doc.data();
              data['id'] = doc.id; // Ensure the document ID is included
              return ActivityModel.fromJson(data);
            } catch (e) {
              _logger.e('Error parsing activity document ${doc.id}: $e');
              return null;
            }
          })
          .where((activity) => activity != null)
          .cast<ActivityModel>()
          .toList();

      _logger.i(
        'Successfully fetched ${activityList.length} activities for classroom $classroomId',
      );
      return activityList;
    } catch (e) {
      _logger.e('Error fetching activities for classroom: $e');
      rethrow;
    }
  }

  /// Fetch recent activities for a classroom (limited to last 3 days or 10 items)
  Future<List<ActivityModel>> getRecentActivitiesForClassroom(
    String classroomId, {
    int limit = 10,
    int dayLimit = 3,
  }) async {
    try {
      _logger.i('Fetching recent activities for classroom: $classroomId');

      // Calculate the date limit (3 days ago)
      final dateLimit = DateTime.now().subtract(Duration(days: dayLimit));

      final querySnapshot = await _firestore
          .collection(_activitiesCollection)
          .where('classroomId', isEqualTo: classroomId)
          .where('isActive', isEqualTo: true)
          .where(
            'createdAt',
            isGreaterThanOrEqualTo: dateLimit.toIso8601String(),
          )
          .orderBy('createdAt', descending: true)
          .limit(limit)
          .get();

      final activityList = querySnapshot.docs
          .map((doc) {
            try {
              final data = doc.data();
              data['id'] = doc.id;
              return ActivityModel.fromJson(data);
            } catch (e) {
              _logger.e('Error parsing activity document ${doc.id}: $e');
              return null;
            }
          })
          .where((activity) => activity != null)
          .cast<ActivityModel>()
          .toList();

      _logger.i(
        'Successfully fetched ${activityList.length} recent activities for classroom $classroomId',
      );
      return activityList;
    } catch (e) {
      _logger.e('Error fetching recent activities for classroom: $e');
      rethrow;
    }
  }

  /// Fetch activities by type for a classroom
  Future<List<ActivityModel>> getActivitiesByType(
    String classroomId,
    ActivityType type,
  ) async {
    try {
      _logger.i(
        'Fetching ${type.label} activities for classroom: $classroomId',
      );

      final querySnapshot = await _firestore
          .collection(_activitiesCollection)
          .where('classroomId', isEqualTo: classroomId)
          .where('type', isEqualTo: type.value)
          .where('isActive', isEqualTo: true)
          .orderBy('createdAt', descending: true)
          .get();

      final activityList = querySnapshot.docs
          .map((doc) {
            try {
              final data = doc.data();
              data['id'] = doc.id;
              return ActivityModel.fromJson(data);
            } catch (e) {
              _logger.e('Error parsing activity document ${doc.id}: $e');
              return null;
            }
          })
          .where((activity) => activity != null)
          .cast<ActivityModel>()
          .toList();

      _logger.i(
        'Successfully fetched ${activityList.length} ${type.label} activities for classroom $classroomId',
      );
      return activityList;
    } catch (e) {
      _logger.e('Error fetching activities by type: $e');
      rethrow;
    }
  }

  /// Fetch a specific activity by ID
  Future<ActivityModel?> getActivityById(String activityId) async {
    try {
      _logger.i('Fetching activity by ID: $activityId');

      final docSnapshot = await _firestore
          .collection(_activitiesCollection)
          .doc(activityId)
          .get();

      if (!docSnapshot.exists) {
        _logger.w('Activity not found: $activityId');
        return null;
      }

      final data = docSnapshot.data()!;
      data['id'] = docSnapshot.id;
      final activity = ActivityModel.fromJson(data);

      _logger.i('Successfully fetched activity: ${activity.title}');
      return activity;
    } catch (e) {
      _logger.e('Error fetching activity by ID: $e');
      rethrow;
    }
  }

  /// Create a new activity (e.g., announcement)
  Future<void> createActivity(ActivityModel activity) async {
    try {
      _logger.i('Creating new activity: ${activity.title}');

      await _firestore
          .collection(_activitiesCollection)
          .doc(activity.id)
          .set(activity.toJson());

      _logger.i('Successfully created activity: ${activity.title}');
    } catch (e) {
      _logger.e('Error creating activity: $e');
      rethrow;
    }
  }

  /// Update an existing activity
  Future<void> updateActivity(ActivityModel activity) async {
    try {
      _logger.i('Updating activity: ${activity.title}');

      await _firestore
          .collection(_activitiesCollection)
          .doc(activity.id)
          .update(activity.toJson());

      _logger.i('Successfully updated activity: ${activity.title}');
    } catch (e) {
      _logger.e('Error updating activity: $e');
      rethrow;
    }
  }

  /// Delete an activity (soft delete by setting isActive to false)
  Future<void> deleteActivity(String activityId) async {
    try {
      _logger.i('Deleting activity: $activityId');

      await _firestore.collection(_activitiesCollection).doc(activityId).update(
        {'isActive': false},
      );

      _logger.i('Successfully deleted activity: $activityId');
    } catch (e) {
      _logger.e('Error deleting activity: $e');
      rethrow;
    }
  }

  /// Pin/unpin an activity (for important announcements)
  Future<void> toggleActivityPin(String activityId, bool isPinned) async {
    try {
      _logger.i('${isPinned ? 'Pinning' : 'Unpinning'} activity: $activityId');

      await _firestore.collection(_activitiesCollection).doc(activityId).update(
        {'isPinned': isPinned, 'updatedAt': DateTime.now().toIso8601String()},
      );

      _logger.i('Successfully ${isPinned ? 'pinned' : 'unpinned'} activity');
    } catch (e) {
      _logger.e('Error toggling activity pin: $e');
      rethrow;
    }
  }

  /// Search activities by title or description
  Future<List<ActivityModel>> searchActivities(
    String classroomId,
    String searchTerm,
  ) async {
    try {
      _logger.i(
        'Searching activities in classroom $classroomId for: $searchTerm',
      );

      // Note: Firestore doesn't support full-text search natively
      // This is a basic implementation that fetches all activities and filters locally
      // For production, consider using Algolia or similar search service
      final allActivities = await getActivitiesForClassroom(classroomId);

      final searchResults = allActivities.where((activity) {
        final titleMatch = activity.title.toLowerCase().contains(
          searchTerm.toLowerCase(),
        );
        final descriptionMatch =
            activity.description?.toLowerCase().contains(
              searchTerm.toLowerCase(),
            ) ??
            false;
        return titleMatch || descriptionMatch;
      }).toList();

      _logger.i(
        'Found ${searchResults.length} activities matching search term',
      );
      return searchResults;
    } catch (e) {
      _logger.e('Error searching activities: $e');
      rethrow;
    }
  }

  /// Get pinned activities for a classroom
  Future<List<ActivityModel>> getPinnedActivities(String classroomId) async {
    try {
      _logger.i('Fetching pinned activities for classroom: $classroomId');

      final querySnapshot = await _firestore
          .collection(_activitiesCollection)
          .where('classroomId', isEqualTo: classroomId)
          .where('isPinned', isEqualTo: true)
          .where('isActive', isEqualTo: true)
          .orderBy('createdAt', descending: true)
          .get();

      final activityList = querySnapshot.docs
          .map((doc) {
            try {
              final data = doc.data();
              data['id'] = doc.id;
              return ActivityModel.fromJson(data);
            } catch (e) {
              _logger.e('Error parsing activity document ${doc.id}: $e');
              return null;
            }
          })
          .where((activity) => activity != null)
          .cast<ActivityModel>()
          .toList();

      _logger.i(
        'Successfully fetched ${activityList.length} pinned activities for classroom $classroomId',
      );
      return activityList;
    } catch (e) {
      _logger.e('Error fetching pinned activities: $e');
      rethrow;
    }
  }
}
