# Debug Feature

The debug feature provides comprehensive debugging tools, mock data management, and development utilities for the Scholara Student app.

## Structure

```
lib/features/debug/
├── README.md                    # This file
├── mock_data/                   # Mock data functionality
│   ├── mock_data.dart          # Main export file
│   ├── generators/             # Mock data generators
│   │   ├── classroom_mock_generator.dart
│   │   ├── homework_mock_generator.dart
│   │   ├── profile_mock_generator.dart
│   │   └── activity_mock_generator.dart
│   ├── services/               # Upload and management services
│   │   ├── mock_data_upload_service.dart
│   │   └── profile_upload_service.dart
│   └── validators/             # Data validation utilities
│       └── mock_data_validator.dart
├── screens/                    # Debug screens
│   ├── debug_info_screen.dart
│   ├── debug_logs_screen.dart
│   └── mock_data_screen.dart
└── widgets/                    # Debug widgets
    ├── debug_info_section.dart
    ├── debug_log_filter_chip.dart
    └── debug_log_tile.dart
```

## Mock Data Management

### Overview
All mock data functionality has been centralized in the debug feature to improve organization and maintainability. This includes:

- **Generators**: Create realistic mock data for all features
- **Services**: Upload and manage mock data in Firebase
- **Validators**: Ensure data quality and consistency

### Generators

#### Classroom Mock Generator
- Generates realistic classroom data with diverse subjects and types
- Creates core subjects, electives, clubs, and teams
- Ensures proper student enrollment and teacher assignments
- Supports primary class eligibility settings

#### Homework Mock Generator
- Creates comprehensive homework assignments with all status types
- Generates realistic submission data with file attachments
- Ensures proper homework-submission relationships
- Includes teacher remarks and review data

#### Profile Mock Generator
- Creates user profiles for students, teachers, and admins
- Generates realistic personal information and preferences
- Handles class enrollments and primary class assignments
- Includes the current user profile for testing

#### Activity Mock Generator
- Generates classroom activities (announcements, assignments, materials, events, discussions)
- Creates realistic activity content and metadata
- Ensures proper classroom-activity relationships

### Services

#### Mock Data Upload Service
- Main service for uploading all mock data to Firebase
- Handles batch uploads with error handling and logging
- Provides data validation before upload
- Supports selective uploads (classes only, homework only, etc.)
- Includes cleanup functionality for removing test data

#### Profile Upload Service
- Specialized service for profile data management
- Supports user type filtering and validation
- Provides detailed statistics and validation reports
- Handles profile-specific operations

### Validators

#### Mock Data Validator
- Comprehensive validation for all mock data types
- Tests data coverage and distribution
- Validates relationships and consistency
- Provides detailed reporting and statistics

## Usage

### Importing Mock Data
```dart
// Import all mock data functionality
import '../mock_data/mock_data.dart';

// Access mock data
final classes = mockClassesList;
final homework = mockHomeworkList;
final profiles = mockProfilesList;
```

### Uploading Mock Data
```dart
// Upload all data
final uploadService = MockDataUploadService();
await uploadService.uploadAllMockData();

// Upload specific data types
await uploadService.uploadMockClasses();
await uploadService.uploadMockHomework();

// Upload profiles
final profileService = ProfileUploadService();
await profileService.uploadAllProfiles();
```

### Validating Mock Data
```dart
// Run comprehensive validation
final isValid = MockDataValidator.validateAllMockData();

// Test specific aspects
MockDataValidator.testMockDataCoverage();
MockDataValidator.testDataConsistency();
```

## Debug Screens

### Mock Data Management Screen
- Central interface for all mock data operations
- Upload, validate, and manage mock data
- View statistics and test coverage
- Clear test data from Firebase

### Debug Info Screen
- System information and app state
- Provider states and Firebase configuration
- Device and network information
- Real-time debugging data

### Debug Logs Screen
- Centralized logging interface
- Filter logs by type and source
- Export and clear log data
- Real-time log monitoring

## Best Practices

1. **Data Consistency**: Always validate mock data before uploading
2. **Cleanup**: Use the cleanup functions to remove test data
3. **Validation**: Run validators after generating new mock data
4. **Organization**: Keep mock data generators focused and modular
5. **Documentation**: Update this README when adding new functionality

## Current User ID
The mock data uses a fixed current user ID for testing: `XRTanMcAUWSMq3mrRvve2Y9IMP12`

This ensures consistent testing and allows for predictable data relationships.

## Migration Notes

All mock data functionality has been moved from individual feature folders to this centralized debug feature:

- `lib/features/homework/mock/` → `lib/features/debug/mock_data/generators/`
- `lib/features/classroom/mock/` → `lib/features/debug/mock_data/generators/`
- `lib/features/profile/mock/` → `lib/features/debug/mock_data/generators/`
- Upload services consolidated into `lib/features/debug/mock_data/services/`
- Validation utilities moved to `lib/features/debug/mock_data/validators/`

All imports have been updated to use the new locations.
