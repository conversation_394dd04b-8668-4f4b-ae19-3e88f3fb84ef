import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import '../../../../core/constants/firebase_collections.dart';
import '../generators/classroom_mock_generator.dart';
import '../generators/homework_mock_generator.dart';
import '../generators/profile_mock_generator.dart';
import '../generators/activity_mock_generator.dart';
import 'announcements_upload_service.dart';
import 'profile_upload_service.dart';
import 'digital_library_upload_service.dart';
import 'study_plan_upload_service.dart';

/// Main service for uploading all mock data to Firestore
class MockDataUploadService {
  static final MockDataUploadService _instance =
      MockDataUploadService._internal();
  factory MockDataUploadService() => _instance;
  MockDataUploadService._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// Upload all mock data (classes, homework, submissions, activities, and profiles) to Firestore
  Future<void> uploadAllMockData() async {
    try {
      debugPrint('=== Starting comprehensive mock data upload ===');

      // Upload in order: classes first, then profiles, then homework, then submissions, then activities
      await uploadMockClasses();

      // Upload profiles
      final profileUploader = ProfileUploadService();
      await profileUploader.uploadAllProfiles();

      await uploadMockHomework();
      await uploadMockSubmissions();
      await uploadMockActivities();

      // Upload digital library data
      final digitalLibraryUploader = DigitalLibraryUploadService();
      await digitalLibraryUploader.uploadAllDigitalLibraryData();

      // Upload announcements data
      final announcementsUploader = AnnouncementsUploadService();
      await announcementsUploader.uploadAllAnnouncements();

      // Upload study plans data
      final studyPlanUploader = StudyPlanUploadService();
      await studyPlanUploader.uploadAllStudyPlans();

      debugPrint('=== All mock data upload completed successfully! ===');
    } catch (e) {
      debugPrint('Error during comprehensive mock data upload: $e');
      rethrow;
    }
  }

  /// Upload mock classroom data to Firestore
  Future<void> uploadMockClasses() async {
    try {
      debugPrint('📚 Starting classroom data upload...');
      debugPrint('Total classes to upload: ${mockClassesList.length}');

      int successCount = 0;
      int errorCount = 0;

      for (final classModel in mockClassesList) {
        try {
          final classDoc = _firestore
              .collection(FirebaseCollections.classrooms)
              .doc(classModel.id);
          await classDoc.set(classModel.toJson());
          successCount++;
          debugPrint(
            '✅ Uploaded class: ${classModel.name} (${classModel.subject})',
          );
        } catch (e) {
          errorCount++;
          debugPrint('❌ Failed to upload class ${classModel.id}: $e');
        }
      }

      debugPrint(
        '📚 Classroom upload summary: $successCount successful, $errorCount failed',
      );

      if (errorCount > 0) {
        throw Exception('Failed to upload $errorCount classroom(s)');
      }
    } catch (e) {
      debugPrint('Error uploading classroom data: $e');
      rethrow;
    }
  }

  /// Upload mock homework data to Firestore
  Future<void> uploadMockHomework() async {
    try {
      debugPrint('📝 Starting homework data upload...');
      debugPrint('Total homework items to upload: ${mockHomeworkList.length}');

      int successCount = 0;
      int errorCount = 0;
      final statusCounts = <String, int>{};

      for (final homework in mockHomeworkList) {
        try {
          final homeworkDoc = _firestore
              .collection(FirebaseCollections.homeworks)
              .doc(homework.id);
          await homeworkDoc.set(homework.toJson());
          successCount++;

          // Track status distribution
          final status = homework.status.name;
          statusCounts[status] = (statusCounts[status] ?? 0) + 1;

          debugPrint(
            '✅ Uploaded homework: ${homework.title} (${homework.status.name})',
          );
        } catch (e) {
          errorCount++;
          debugPrint('❌ Failed to upload homework ${homework.id}: $e');
        }
      }

      debugPrint(
        '📝 Homework upload summary: $successCount successful, $errorCount failed',
      );
      debugPrint('📊 Status distribution: $statusCounts');

      if (errorCount > 0) {
        throw Exception('Failed to upload $errorCount homework item(s)');
      }
    } catch (e) {
      debugPrint('Error uploading homework data: $e');
      rethrow;
    }
  }

  /// Upload mock submission data to Firestore
  Future<void> uploadMockSubmissions() async {
    try {
      debugPrint('📤 Starting submission data upload...');
      debugPrint(
        'Total submissions to upload: ${mockHomeworkSubmissions.length}',
      );

      int successCount = 0;
      int errorCount = 0;
      int reviewedCount = 0;

      for (final submission in mockHomeworkSubmissions) {
        try {
          final submissionDoc = _firestore
              .collection(FirebaseCollections.homeworkSubmissions)
              .doc(submission.id);
          await submissionDoc.set(submission.toJson());
          successCount++;

          if (submission.teacherRemark != null) {
            reviewedCount++;
          }

          debugPrint('✅ Uploaded submission: ${submission.id}');
        } catch (e) {
          errorCount++;
          debugPrint('❌ Failed to upload submission ${submission.id}: $e');
        }
      }

      debugPrint(
        '📤 Submission upload summary: $successCount successful, $errorCount failed',
      );
      debugPrint('📊 Reviewed submissions: $reviewedCount');

      if (errorCount > 0) {
        throw Exception('Failed to upload $errorCount submission(s)');
      }
    } catch (e) {
      debugPrint('Error uploading submission data: $e');
      rethrow;
    }
  }

  /// Upload mock activity data to Firestore
  Future<void> uploadMockActivities() async {
    try {
      debugPrint('🎯 Starting activity data upload...');
      debugPrint('Total activities to upload: ${mockActivitiesList.length}');

      int successCount = 0;
      int errorCount = 0;
      final typeCounts = <String, int>{};

      for (final activity in mockActivitiesList) {
        try {
          final activityDoc = _firestore
              .collection(FirebaseCollections.classroomActivities)
              .doc(activity.id);
          await activityDoc.set(activity.toJson());
          successCount++;

          // Track type distribution
          final type = activity.type.name;
          typeCounts[type] = (typeCounts[type] ?? 0) + 1;

          debugPrint(
            '✅ Uploaded activity: ${activity.title} (${activity.type.name})',
          );
        } catch (e) {
          errorCount++;
          debugPrint('❌ Failed to upload activity ${activity.id}: $e');
        }
      }

      debugPrint(
        '🎯 Activity upload summary: $successCount successful, $errorCount failed',
      );
      debugPrint('📊 Type distribution: $typeCounts');

      if (errorCount > 0) {
        throw Exception('Failed to upload $errorCount activity/activities');
      }
    } catch (e) {
      debugPrint('Error uploading activity data: $e');
      rethrow;
    }
  }

  /// Clear all mock data from Firestore (useful for testing)
  Future<void> clearAllMockData() async {
    try {
      debugPrint('🗑️ Starting mock data cleanup...');

      // Clear submissions first (to avoid foreign key issues)
      await _clearCollection(FirebaseCollections.homeworkSubmissions);
      await _clearCollection(FirebaseCollections.homeworks);
      await _clearCollection(FirebaseCollections.classroomActivities);
      await _clearCollection(FirebaseCollections.classrooms);
      await _clearCollection(FirebaseCollections.users); // Clear profiles

      debugPrint('🗑️ Mock data cleanup completed successfully!');
    } catch (e) {
      debugPrint('Error during mock data cleanup: $e');
      rethrow;
    }
  }

  /// Clear a specific collection
  Future<void> _clearCollection(String collectionName) async {
    try {
      debugPrint('🗑️ Clearing collection: $collectionName');

      final collection = _firestore.collection(collectionName);
      final snapshot = await collection.get();

      if (snapshot.docs.isEmpty) {
        debugPrint('✅ Collection $collectionName is already empty');
        return;
      }

      final batch = _firestore.batch();
      for (final doc in snapshot.docs) {
        batch.delete(doc.reference);
      }

      await batch.commit();
      debugPrint(
        '✅ Cleared ${snapshot.docs.length} documents from $collectionName',
      );
    } catch (e) {
      debugPrint('❌ Error clearing collection $collectionName: $e');
      rethrow;
    }
  }

  /// Get upload statistics
  Map<String, dynamic> getUploadStats() {
    return {
      'classrooms': mockClassesList.length,
      'homework': mockHomeworkList.length,
      'submissions': mockHomeworkSubmissions.length,
      'profiles': mockProfilesList.length,
      'total':
          mockClassesList.length +
          mockHomeworkList.length +
          mockHomeworkSubmissions.length +
          mockProfilesList.length,
    };
  }

  /// Validate mock data before upload
  bool validateMockData() {
    try {
      // Check if all required data is generated
      if (mockClassesList.isEmpty) {
        debugPrint('❌ No classroom data found');
        return false;
      }

      if (mockHomeworkList.isEmpty) {
        debugPrint('❌ No homework data found');
        return false;
      }

      if (mockProfilesList.isEmpty) {
        debugPrint('❌ No profile data found');
        return false;
      }

      // Check data consistency
      final homeworkWithSubmissions = mockHomeworkList
          .where((hw) => hw.submissionId != null)
          .length;

      if (homeworkWithSubmissions != mockHomeworkSubmissions.length) {
        debugPrint('❌ Homework-submission count mismatch');
        return false;
      }

      debugPrint('✅ Mock data validation passed');
      return true;
    } catch (e) {
      debugPrint('❌ Mock data validation failed: $e');
      return false;
    }
  }

  /// Upload mock announcements data to Firestore
  Future<void> uploadMockAnnouncements() async {
    try {
      final announcementsUploader = AnnouncementsUploadService();
      await announcementsUploader.uploadAllAnnouncements();
    } catch (e) {
      debugPrint('Error uploading announcements: $e');
      rethrow;
    }
  }
}
