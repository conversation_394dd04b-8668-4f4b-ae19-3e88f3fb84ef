import 'package:cloud_firestore/cloud_firestore.dart';

import '../../../../core/constants/firebase_collections.dart';
import '../../../chat/models/chat_model.dart';
import '../../../chat/models/message_model.dart';
import '../../../chat/models/chat_participant.dart';

/// Service for uploading chat mock data to Firebase
class ChatUploadService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// Upload chats to Firebase
  static Future<void> uploadChats(List<ChatModel> chats) async {
    final batch = _firestore.batch();

    for (final chat in chats) {
      final chatRef = _firestore
          .collection(FirebaseCollections.chats)
          .doc(chat.id);

      batch.set(chatRef, chat.toJson());
    }

    await batch.commit();
  }

  /// Upload messages to Firebase
  static Future<void> uploadMessages(List<MessageModel> messages) async {
    final batch = _firestore.batch();

    for (final message in messages) {
      final messageRef = _firestore
          .collection(FirebaseCollections.chatMessages)
          .doc(message.id);

      batch.set(messageRef, message.toJson());
    }

    await batch.commit();
  }

  /// Upload chat participants to Firebase
  /// Note: This method expects a map of chatId to participants list
  static Future<void> uploadChatParticipants(
    Map<String, List<ChatParticipant>> chatParticipants,
  ) async {
    final batch = _firestore.batch();

    for (final entry in chatParticipants.entries) {
      final chatId = entry.key;
      final participants = entry.value;

      for (final participant in participants) {
        final participantRef = _firestore
            .collection(FirebaseCollections.chatParticipants)
            .doc('${chatId}_${participant.userId}');

        // Add chatId to the participant data
        final participantData = participant.toJson();
        participantData['chatId'] = chatId;

        batch.set(participantRef, participantData);
      }
    }

    await batch.commit();
  }

  /// Clear all chat data from Firebase
  static Future<void> clearAllChatData() async {
    final batch = _firestore.batch();

    // Clear chats
    final chatsSnapshot = await _firestore
        .collection(FirebaseCollections.chats)
        .get();

    for (final doc in chatsSnapshot.docs) {
      batch.delete(doc.reference);
    }

    // Clear messages
    final messagesSnapshot = await _firestore
        .collection(FirebaseCollections.chatMessages)
        .get();

    for (final doc in messagesSnapshot.docs) {
      batch.delete(doc.reference);
    }

    // Clear chat participants
    final participantsSnapshot = await _firestore
        .collection(FirebaseCollections.chatParticipants)
        .get();

    for (final doc in participantsSnapshot.docs) {
      batch.delete(doc.reference);
    }

    await batch.commit();
  }

  /// Upload all chat mock data (chats, messages, and participants)
  static Future<void> uploadAllChatData({
    required List<ChatModel> chats,
    required List<MessageModel> messages,
    required Map<String, List<ChatParticipant>> participants,
  }) async {
    try {
      // Upload in sequence to avoid overwhelming Firebase
      await uploadChats(chats);
      await uploadMessages(messages);
      await uploadChatParticipants(participants);
    } catch (e) {
      throw Exception('Failed to upload chat data: $e');
    }
  }

  /// Get chat statistics from Firebase
  static Future<Map<String, int>> getChatStatistics() async {
    try {
      final chatsSnapshot = await _firestore
          .collection(FirebaseCollections.chats)
          .get();

      final messagesSnapshot = await _firestore
          .collection(FirebaseCollections.chatMessages)
          .get();

      final participantsSnapshot = await _firestore
          .collection(FirebaseCollections.chatParticipants)
          .get();

      return {
        'chats': chatsSnapshot.docs.length,
        'messages': messagesSnapshot.docs.length,
        'participants': participantsSnapshot.docs.length,
      };
    } catch (e) {
      throw Exception('Failed to get chat statistics: $e');
    }
  }

  /// Verify chat data integrity
  static Future<Map<String, dynamic>> verifyChatDataIntegrity() async {
    try {
      final stats = await getChatStatistics();
      final issues = <String>[];

      // Check if we have chats
      if (stats['chats'] == 0) {
        issues.add('No chats found');
      }

      // Check if we have messages for chats
      if (stats['chats']! > 0 && stats['messages'] == 0) {
        issues.add('Chats exist but no messages found');
      }

      // Check if we have participants for chats
      if (stats['chats']! > 0 && stats['participants'] == 0) {
        issues.add('Chats exist but no participants found');
      }

      return {'isValid': issues.isEmpty, 'issues': issues, 'statistics': stats};
    } catch (e) {
      return {
        'isValid': false,
        'issues': ['Failed to verify data integrity: $e'],
        'statistics': <String, int>{},
      };
    }
  }
}
