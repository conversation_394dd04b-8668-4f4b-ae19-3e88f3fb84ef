import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:logger/logger.dart';

import '../../../../core/constants/firebase_collections.dart';
import '../generators/digital_library_mock_generator.dart';

/// Service for uploading digital library mock data to Firebase
class DigitalLibraryUploadService {
  static final DigitalLibraryUploadService _instance = DigitalLibraryUploadService._internal();
  factory DigitalLibraryUploadService() => _instance;
  DigitalLibraryUploadService._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final Logger _logger = Logger();

  /// Upload all digital library mock data to Firebase
  Future<bool> uploadAllDigitalLibraryData() async {
    try {
      _logger.i('🚀 Starting digital library mock data upload...');

      // Upload folders first (since files reference folders)
      await _uploadFolders();
      
      // Upload files
      await _uploadFiles();
      
      // Upload upload sessions
      await _uploadSessions();

      _logger.i('✅ Digital library mock data upload completed successfully');
      return true;
    } catch (e, stackTrace) {
      _logger.e('❌ Failed to upload digital library mock data', error: e, stackTrace: stackTrace);
      return false;
    }
  }

  /// Upload library folders to Firebase
  Future<void> _uploadFolders() async {
    _logger.i('📁 Uploading ${mockLibraryFolders.length} library folders...');
    
    final batch = _firestore.batch();
    int batchCount = 0;
    
    for (final folder in mockLibraryFolders) {
      final docRef = _firestore
          .collection(FirebaseCollections.libraryFolders)
          .doc(folder.id);
      
      batch.set(docRef, folder.toJson());
      batchCount++;
      
      // Commit batch every 500 operations (Firestore limit)
      if (batchCount >= 500) {
        await batch.commit();
        batchCount = 0;
        _logger.i('📁 Committed batch of folders...');
      }
    }
    
    // Commit remaining operations
    if (batchCount > 0) {
      await batch.commit();
    }
    
    _logger.i('✅ Successfully uploaded ${mockLibraryFolders.length} folders');
  }

  /// Upload library files to Firebase
  Future<void> _uploadFiles() async {
    _logger.i('📄 Uploading ${mockLibraryFiles.length} library files...');
    
    final batch = _firestore.batch();
    int batchCount = 0;
    
    for (final file in mockLibraryFiles) {
      final docRef = _firestore
          .collection(FirebaseCollections.libraryFiles)
          .doc(file.id);
      
      batch.set(docRef, file.toJson());
      batchCount++;
      
      // Commit batch every 500 operations (Firestore limit)
      if (batchCount >= 500) {
        await batch.commit();
        batchCount = 0;
        _logger.i('📄 Committed batch of files...');
      }
    }
    
    // Commit remaining operations
    if (batchCount > 0) {
      await batch.commit();
    }
    
    _logger.i('✅ Successfully uploaded ${mockLibraryFiles.length} files');
  }

  /// Upload upload sessions to Firebase
  Future<void> _uploadSessions() async {
    _logger.i('⬆️ Uploading ${mockUploadSessions.length} upload sessions...');
    
    final batch = _firestore.batch();
    int batchCount = 0;
    
    for (final session in mockUploadSessions) {
      final docRef = _firestore
          .collection(FirebaseCollections.libraryUploadSessions)
          .doc(session.id);
      
      batch.set(docRef, session.toJson());
      batchCount++;
      
      // Commit batch every 500 operations (Firestore limit)
      if (batchCount >= 500) {
        await batch.commit();
        batchCount = 0;
        _logger.i('⬆️ Committed batch of upload sessions...');
      }
    }
    
    // Commit remaining operations
    if (batchCount > 0) {
      await batch.commit();
    }
    
    _logger.i('✅ Successfully uploaded ${mockUploadSessions.length} upload sessions');
  }

  /// Clear all digital library data from Firebase
  Future<bool> clearAllDigitalLibraryData() async {
    try {
      _logger.i('🗑️ Clearing all digital library data from Firebase...');

      // Clear in reverse order (files first, then folders)
      await _clearCollection(FirebaseCollections.libraryUploadSessions);
      await _clearCollection(FirebaseCollections.libraryFiles);
      await _clearCollection(FirebaseCollections.libraryFolders);

      _logger.i('✅ Successfully cleared all digital library data');
      return true;
    } catch (e, stackTrace) {
      _logger.e('❌ Failed to clear digital library data', error: e, stackTrace: stackTrace);
      return false;
    }
  }

  /// Clear a specific collection
  Future<void> _clearCollection(String collectionName) async {
    _logger.i('🗑️ Clearing collection: $collectionName');
    
    final collection = _firestore.collection(collectionName);
    final snapshot = await collection.get();
    
    if (snapshot.docs.isEmpty) {
      _logger.i('📭 Collection $collectionName is already empty');
      return;
    }
    
    final batch = _firestore.batch();
    int batchCount = 0;
    
    for (final doc in snapshot.docs) {
      batch.delete(doc.reference);
      batchCount++;
      
      // Commit batch every 500 operations (Firestore limit)
      if (batchCount >= 500) {
        await batch.commit();
        batchCount = 0;
        _logger.i('🗑️ Committed deletion batch for $collectionName...');
      }
    }
    
    // Commit remaining operations
    if (batchCount > 0) {
      await batch.commit();
    }
    
    _logger.i('✅ Successfully cleared ${snapshot.docs.length} documents from $collectionName');
  }

  /// Get statistics about uploaded digital library data
  Future<Map<String, int>> getUploadStatistics() async {
    try {
      final foldersSnapshot = await _firestore
          .collection(FirebaseCollections.libraryFolders)
          .get();
      
      final filesSnapshot = await _firestore
          .collection(FirebaseCollections.libraryFiles)
          .get();
      
      final sessionsSnapshot = await _firestore
          .collection(FirebaseCollections.libraryUploadSessions)
          .get();

      return {
        'folders': foldersSnapshot.docs.length,
        'files': filesSnapshot.docs.length,
        'uploadSessions': sessionsSnapshot.docs.length,
      };
    } catch (e) {
      _logger.e('❌ Failed to get upload statistics', error: e);
      return {
        'folders': 0,
        'files': 0,
        'uploadSessions': 0,
      };
    }
  }

  /// Validate uploaded data integrity
  Future<bool> validateUploadedData() async {
    try {
      _logger.i('🔍 Validating uploaded digital library data...');

      final stats = await getUploadStatistics();
      
      // Check if all data was uploaded
      if (stats['folders'] != mockLibraryFolders.length) {
        _logger.e('❌ Folder count mismatch: expected ${mockLibraryFolders.length}, got ${stats['folders']}');
        return false;
      }
      
      if (stats['files'] != mockLibraryFiles.length) {
        _logger.e('❌ File count mismatch: expected ${mockLibraryFiles.length}, got ${stats['files']}');
        return false;
      }
      
      if (stats['uploadSessions'] != mockUploadSessions.length) {
        _logger.e('❌ Upload session count mismatch: expected ${mockUploadSessions.length}, got ${stats['uploadSessions']}');
        return false;
      }

      _logger.i('✅ Digital library data validation passed');
      return true;
    } catch (e) {
      _logger.e('❌ Failed to validate uploaded data', error: e);
      return false;
    }
  }
}
