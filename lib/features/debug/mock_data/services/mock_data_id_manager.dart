import 'package:uuid/uuid.dart';

/// Centralized ID manager for mock data to ensure consistency across generators
/// This ensures that classroom IDs, teacher IDs, and student IDs remain consistent
/// across multiple mock data generations
class MockDataIdManager {
  static final MockDataIdManager _instance = MockDataIdManager._internal();
  factory MockDataIdManager() => _instance;
  MockDataIdManager._internal();

  static const _uuid = Uuid();

  // Cached IDs to ensure consistency
  static final Map<String, String> _classroomIds = {};
  static final Map<String, String> _teacherIds = {};
  static final Map<String, String> _studentIds = {};
  static final Map<String, String> _activityIds = {};
  static final Map<String, String> _homeworkIds = {};
  static final Map<String, String> _submissionIds = {};
  static final Map<String, String> _libraryFileIds = {};
  static final Map<String, String> _libraryFolderIds = {};
  static final Map<String, String> _uploadSessionIds = {};
  static final Map<String, String> _announcementIds = {};
  static final Map<String, String> _studyPlanIds = {};
  static final Map<String, String> _studyPlanSectionIds = {};
  static final Map<String, String> _studyPlanTaskIds = {};
  static final Map<String, String> _taskResourceIds = {};
  static final Map<String, String> _taskSubmissionIds = {};
  static final Map<String, String> _milestoneIds = {};

  /// Clear all cached IDs (useful for regenerating fresh mock data)
  static void clearAllIds() {
    _classroomIds.clear();
    _teacherIds.clear();
    _studentIds.clear();
    _activityIds.clear();
    _homeworkIds.clear();
    _submissionIds.clear();
    _libraryFileIds.clear();
    _libraryFolderIds.clear();
    _uploadSessionIds.clear();
    _announcementIds.clear();
    _studyPlanIds.clear();
    _studyPlanSectionIds.clear();
    _studyPlanTaskIds.clear();
    _taskResourceIds.clear();
    _taskSubmissionIds.clear();
    _milestoneIds.clear();
  }

  /// Get or create a classroom ID for a given key
  static String getClassroomId(String key) {
    return _classroomIds.putIfAbsent(key, () => _uuid.v4());
  }

  /// Get or create a teacher ID for a given key
  static String getTeacherId(String key) {
    return _teacherIds.putIfAbsent(key, () => _uuid.v4());
  }

  /// Get or create a student ID for a given key
  static String getStudentId(String key) {
    return _studentIds.putIfAbsent(key, () => _uuid.v4());
  }

  /// Get or create an activity ID for a given key
  static String getActivityId(String key) {
    return _activityIds.putIfAbsent(key, () => _uuid.v4());
  }

  /// Get or create a homework ID for a given key
  static String getHomeworkId(String key) {
    return _homeworkIds.putIfAbsent(key, () => _uuid.v4());
  }

  /// Get or create a submission ID for a given key
  static String getSubmissionId(String key) {
    return _submissionIds.putIfAbsent(key, () => _uuid.v4());
  }

  /// Get or create a library file ID for a given key
  static String getLibraryFileId(String key) {
    return _libraryFileIds.putIfAbsent(key, () => _uuid.v4());
  }

  /// Get or create a library folder ID for a given key
  static String getLibraryFolderId(String key) {
    return _libraryFolderIds.putIfAbsent(key, () => _uuid.v4());
  }

  /// Get or create an upload session ID for a given key
  static String getUploadSessionId(String key) {
    return _uploadSessionIds.putIfAbsent(key, () => _uuid.v4());
  }

  /// Get or create an announcement ID for a given key
  static String getAnnouncementId(String key) {
    return _announcementIds.putIfAbsent(key, () => _uuid.v4());
  }

  /// Get or create a study plan ID for a given key
  static String getStudyPlanId(String key) {
    return _studyPlanIds.putIfAbsent(key, () => _uuid.v4());
  }

  /// Get or create a study plan section ID for a given key
  static String getStudyPlanSectionId(String key) {
    return _studyPlanSectionIds.putIfAbsent(key, () => _uuid.v4());
  }

  /// Get or create a study plan task ID for a given key
  static String getStudyPlanTaskId(String key) {
    return _studyPlanTaskIds.putIfAbsent(key, () => _uuid.v4());
  }

  /// Get or create a task resource ID for a given key
  static String getTaskResourceId(String key) {
    return _taskResourceIds.putIfAbsent(key, () => _uuid.v4());
  }

  /// Get or create a task submission ID for a given key
  static String getTaskSubmissionId(String key) {
    return _taskSubmissionIds.putIfAbsent(key, () => _uuid.v4());
  }

  /// Get or create a milestone ID for a given key
  static String getMilestoneId(String key) {
    return _milestoneIds.putIfAbsent(key, () => _uuid.v4());
  }

  /// Generate a unique ID for a given type (used by mock generators)
  static String generateId(String type) {
    return _uuid.v4();
  }

  /// Get all cached classroom IDs
  static List<String> getAllClassroomIds() {
    return _classroomIds.values.toList();
  }

  /// Get all cached teacher IDs
  static List<String> getAllTeacherIds() {
    return _teacherIds.values.toList();
  }

  /// Get all cached student IDs
  static List<String> getAllStudentIds() {
    return _studentIds.values.toList();
  }

  /// Get all cached announcement IDs
  static List<String> getAllAnnouncementIds() {
    return _announcementIds.values.toList();
  }

  /// Get statistics about cached IDs
  static Map<String, int> getIdStatistics() {
    return {
      'classrooms': _classroomIds.length,
      'teachers': _teacherIds.length,
      'students': _studentIds.length,
      'activities': _activityIds.length,
      'homework': _homeworkIds.length,
      'submissions': _submissionIds.length,
      'libraryFiles': _libraryFileIds.length,
      'libraryFolders': _libraryFolderIds.length,
      'uploadSessions': _uploadSessionIds.length,
      'announcements': _announcementIds.length,
    };
  }

  /// Pre-generate a set of consistent IDs for mock data
  static void preGenerateIds() {
    clearAllIds();

    // Pre-generate teacher IDs (15 teachers)
    for (int i = 0; i < 15; i++) {
      getTeacherId('teacher_$i');
    }

    // Pre-generate student IDs (50 students + current user)
    getStudentId('current_user'); // Current user ID
    for (int i = 0; i < 50; i++) {
      getStudentId('student_$i');
    }

    // Pre-generate classroom IDs based on subjects and types
    final coreSubjects = [
      'Mathematics',
      'Physics',
      'Chemistry',
      'Biology',
      'English Literature',
      'History',
      'Geography',
    ];

    final electiveSubjects = [
      'Computer Science',
      'Art',
      'Music',
      'Psychology',
      'Economics',
    ];

    final clubs = [
      'Chess Club',
      'Debate Club',
      'Robotics Club',
      'Drama Club',
      'Photography Club',
    ];

    final teams = [
      'Basketball Team',
      'Football Team',
      'Cricket Team',
      'Volleyball Team',
    ];

    final gradeLevels = ['10', '11', '12'];
    final sections = ['A', 'B', 'C'];

    // Generate core subject classroom IDs
    for (final subject in coreSubjects) {
      for (final grade in gradeLevels) {
        for (final section in sections) {
          getClassroomId('core_${subject}_${grade}_$section');
        }
      }
    }

    // Generate elective classroom IDs
    for (final subject in electiveSubjects) {
      getClassroomId('elective_$subject');
    }

    // Generate club classroom IDs
    for (final club in clubs) {
      getClassroomId('club_$club');
    }

    // Generate team classroom IDs
    for (final team in teams) {
      getClassroomId('team_$team');
    }
  }

  /// Get the current user ID (consistent across all mock data)
  static String getCurrentUserId() {
    // Use the actual test user ID for consistency
    const testUserId = 'XRTanMcAUWSMq3mrRvve2Y9IMP12';
    _studentIds['current_user'] = testUserId;
    return testUserId;
  }

  /// Set the current user ID to a specific value
  static void setCurrentUserId(String userId) {
    _studentIds['current_user'] = userId;
  }
}
