import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';

import '../../../../core/constants/firebase_collections.dart';
import '../generators/announcement_mock_generator.dart';

/// Service for uploading mock announcement data to Firestore
class AnnouncementsUploadService {
  static final AnnouncementsUploadService _instance =
      AnnouncementsUploadService._internal();
  factory AnnouncementsUploadService() => _instance;
  AnnouncementsUploadService._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // Collection name
  static const String _announcementsCollection =
      FirebaseCollections.announcements;

  /// Upload all mock announcement data to Firestore
  Future<void> uploadAllAnnouncements() async {
    try {
      debugPrint('📢 Starting announcements data upload...');
      debugPrint(
        'Total announcements to upload: ${mockAnnouncementsList.length}',
      );

      int successCount = 0;
      int errorCount = 0;
      final Map<String, int> typeCounts = {};
      final Map<String, int> scopeCounts = {};

      for (final announcement in mockAnnouncementsList) {
        try {
          final announcementDoc = _firestore
              .collection(_announcementsCollection)
              .doc(announcement.id);
          await announcementDoc.set(announcement.toJson());
          successCount++;

          // Track type distribution
          final type = announcement.type.name;
          typeCounts[type] = (typeCounts[type] ?? 0) + 1;

          // Track scope distribution
          final scope = announcement.usageType.name;
          scopeCounts[scope] = (scopeCounts[scope] ?? 0) + 1;

          debugPrint(
            '✅ Uploaded announcement: ${announcement.title} (${announcement.type.name})',
          );
        } catch (e) {
          errorCount++;
          debugPrint('❌ Failed to upload announcement ${announcement.id}: $e');
        }
      }

      debugPrint(
        '📢 Announcements upload summary: $successCount successful, $errorCount failed',
      );
      debugPrint('📊 Type distribution: $typeCounts');
      debugPrint('📊 Scope distribution: $scopeCounts');

      if (errorCount > 0) {
        throw Exception('Failed to upload $errorCount announcement(s)');
      }
    } catch (e) {
      debugPrint('❌ Failed to upload announcements: $e');
      rethrow;
    }
  }

  /// Clear all announcement data from Firestore
  Future<void> clearAllAnnouncements() async {
    try {
      debugPrint('🗑️ Starting announcements data cleanup...');

      final batch = _firestore.batch();
      final querySnapshot = await _firestore
          .collection(_announcementsCollection)
          .get();

      for (final doc in querySnapshot.docs) {
        batch.delete(doc.reference);
      }

      await batch.commit();

      debugPrint(
        '🗑️ Cleared ${querySnapshot.docs.length} announcements from Firestore',
      );
    } catch (e) {
      debugPrint('❌ Failed to clear announcements: $e');
      rethrow;
    }
  }

  /// Get announcement statistics from Firestore
  Future<Map<String, dynamic>> getAnnouncementStats() async {
    try {
      final querySnapshot = await _firestore
          .collection(_announcementsCollection)
          .get();

      final Map<String, int> typeCounts = {};
      final Map<String, int> scopeCounts = {};
      final Map<String, int> statusCounts = {};

      for (final doc in querySnapshot.docs) {
        final data = doc.data();

        // Count by type
        final type = data['type'] as String?;
        if (type != null) {
          typeCounts[type] = (typeCounts[type] ?? 0) + 1;
        }

        // Count by usage type
        final scope = data['usageType'] as String?;
        if (scope != null) {
          scopeCounts[scope] = (scopeCounts[scope] ?? 0) + 1;
        }

        // Count by status
        final status = data['status'] as String?;
        if (status != null) {
          statusCounts[status] = (statusCounts[status] ?? 0) + 1;
        }
      }

      return {
        'total': querySnapshot.docs.length,
        'typeDistribution': typeCounts,
        'scopeDistribution': scopeCounts,
        'statusDistribution': statusCounts,
      };
    } catch (e) {
      debugPrint('❌ Failed to get announcement stats: $e');
      return {
        'total': 0,
        'typeDistribution': <String, int>{},
        'scopeDistribution': <String, int>{},
        'statusDistribution': <String, int>{},
      };
    }
  }
}
