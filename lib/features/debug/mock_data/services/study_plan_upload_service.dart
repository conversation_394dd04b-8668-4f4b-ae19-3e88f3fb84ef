import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';

import '../../../../core/constants/firebase_collections.dart';
import '../generators/study_plan_mock_generator.dart';

/// Service for uploading mock study plan data to Firestore
class StudyPlanUploadService {
  static final StudyPlanUploadService _instance =
      StudyPlanUploadService._internal();
  factory StudyPlanUploadService() => _instance;
  StudyPlanUploadService._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // Collection names
  static const String _studyPlansCollection = FirebaseCollections.studyPlans;

  /// Upload all mock study plan data to Firestore
  Future<void> uploadAllStudyPlans() async {
    try {
      debugPrint('📚 Starting study plans data upload...');
      debugPrint('Total study plans to upload: ${mockStudyPlansList.length}');

      int successCount = 0;
      int errorCount = 0;
      final Map<String, int> typeCounts = {};
      final Map<String, int> statusCounts = {};
      final Map<String, int> priorityCounts = {};

      for (final studyPlan in mockStudyPlansList) {
        try {
          final studyPlanDoc = _firestore
              .collection(_studyPlansCollection)
              .doc(studyPlan.id);
          await studyPlanDoc.set(studyPlan.toJson());
          successCount++;

          // Track type distribution
          final type = studyPlan.type.name;
          typeCounts[type] = (typeCounts[type] ?? 0) + 1;

          // Track status distribution
          final status = studyPlan.status.name;
          statusCounts[status] = (statusCounts[status] ?? 0) + 1;

          // Track priority distribution
          final priority = studyPlan.priority.name;
          priorityCounts[priority] = (priorityCounts[priority] ?? 0) + 1;

          debugPrint(
            '✅ Uploaded study plan: ${studyPlan.title} (${studyPlan.type.name})',
          );
        } catch (e) {
          errorCount++;
          debugPrint('❌ Failed to upload study plan ${studyPlan.title}: $e');
        }
      }

      // Print summary
      debugPrint('\n📊 Study Plans Upload Summary:');
      debugPrint('✅ Successfully uploaded: $successCount study plans');
      if (errorCount > 0) {
        debugPrint('❌ Failed uploads: $errorCount study plans');
      }

      debugPrint('\n📈 Type Distribution:');
      typeCounts.forEach((type, planCount) {
        debugPrint('  $type: $planCount plans');
      });

      debugPrint('\n📊 Status Distribution:');
      statusCounts.forEach((status, planCount) {
        debugPrint('  $status: $planCount plans');
      });

      debugPrint('\n🎯 Priority Distribution:');
      priorityCounts.forEach((priority, planCount) {
        debugPrint('  $priority: $planCount plans');
      });

      debugPrint('\n🎉 Study plans upload completed!');
    } catch (e) {
      debugPrint('💥 Error during study plans upload: $e');
      rethrow;
    }
  }

  /// Clear all study plan data from Firestore
  Future<void> clearAllStudyPlans() async {
    try {
      debugPrint('🗑️ Clearing all study plan data...');

      final studyPlansSnapshot = await _firestore
          .collection(_studyPlansCollection)
          .get();

      int deletedCount = 0;
      for (final doc in studyPlansSnapshot.docs) {
        await doc.reference.delete();
        deletedCount++;
      }

      debugPrint('✅ Cleared $deletedCount study plans from Firestore');
    } catch (e) {
      debugPrint('❌ Error clearing study plan data: $e');
      rethrow;
    }
  }

  /// Upload a specific study plan by ID
  Future<void> uploadStudyPlan(String studyPlanId) async {
    try {
      final studyPlan = mockStudyPlansList.firstWhere(
        (plan) => plan.id == studyPlanId,
      );

      final studyPlanDoc = _firestore
          .collection(_studyPlansCollection)
          .doc(studyPlan.id);
      await studyPlanDoc.set(studyPlan.toJson());

      debugPrint('✅ Uploaded study plan: ${studyPlan.title}');
    } catch (e) {
      debugPrint('❌ Failed to upload study plan $studyPlanId: $e');
      rethrow;
    }
  }

  /// Get study plan statistics
  Future<Map<String, dynamic>> getStudyPlanStatistics() async {
    try {
      final studyPlansSnapshot = await _firestore
          .collection(_studyPlansCollection)
          .get();

      final stats = <String, dynamic>{
        'totalPlans': studyPlansSnapshot.docs.length,
        'typeDistribution': <String, int>{},
        'statusDistribution': <String, int>{},
        'priorityDistribution': <String, int>{},
        'scopeDistribution': <String, int>{},
      };

      for (final doc in studyPlansSnapshot.docs) {
        final data = doc.data();

        // Count types
        final type = data['type'] as String?;
        if (type != null) {
          stats['typeDistribution'][type] =
              (stats['typeDistribution'][type] ?? 0) + 1;
        }

        // Count statuses
        final status = data['status'] as String?;
        if (status != null) {
          stats['statusDistribution'][status] =
              (stats['statusDistribution'][status] ?? 0) + 1;
        }

        // Count priorities
        final priority = data['priority'] as String?;
        if (priority != null) {
          stats['priorityDistribution'][priority] =
              (stats['priorityDistribution'][priority] ?? 0) + 1;
        }

        // Count scopes
        final scope = data['scope'] as String?;
        if (scope != null) {
          stats['scopeDistribution'][scope] =
              (stats['scopeDistribution'][scope] ?? 0) + 1;
        }
      }

      return stats;
    } catch (e) {
      debugPrint('❌ Error getting study plan statistics: $e');
      rethrow;
    }
  }

  /// Validate study plan data integrity
  Future<Map<String, dynamic>> validateStudyPlanData() async {
    try {
      debugPrint('🔍 Validating study plan data...');

      final validationResults = <String, dynamic>{
        'totalPlans': mockStudyPlansList.length,
        'validPlans': 0,
        'invalidPlans': 0,
        'errors': <String>[],
        'warnings': <String>[],
      };

      for (final studyPlan in mockStudyPlansList) {
        final planErrors = <String>[];
        final planWarnings = <String>[];

        // Validate required fields
        if (studyPlan.title.isEmpty) {
          planErrors.add('${studyPlan.id}: Title is empty');
        }
        if (studyPlan.creatorId.isEmpty) {
          planErrors.add('${studyPlan.id}: Creator ID is empty');
        }

        // Validate sections
        if (studyPlan.sections.isEmpty) {
          planWarnings.add('${studyPlan.id}: No sections defined');
        }

        // Validate dates
        if (studyPlan.startDate != null && studyPlan.endDate != null) {
          if (studyPlan.startDate!.isAfter(studyPlan.endDate!)) {
            planErrors.add('${studyPlan.id}: Start date is after end date');
          }
        }

        // Validate progress
        if (studyPlan.progress.overallCompletion < 0 ||
            studyPlan.progress.overallCompletion > 100) {
          planErrors.add(
            '${studyPlan.id}: Invalid progress percentage: ${studyPlan.progress.overallCompletion}',
          );
        }

        if (planErrors.isEmpty) {
          validationResults['validPlans']++;
        } else {
          validationResults['invalidPlans']++;
          validationResults['errors'].addAll(planErrors);
        }

        if (planWarnings.isNotEmpty) {
          validationResults['warnings'].addAll(planWarnings);
        }
      }

      debugPrint('✅ Validation completed');
      debugPrint('Valid plans: ${validationResults['validPlans']}');
      debugPrint('Invalid plans: ${validationResults['invalidPlans']}');
      debugPrint('Warnings: ${validationResults['warnings'].length}');

      return validationResults;
    } catch (e) {
      debugPrint('❌ Error during validation: $e');
      rethrow;
    }
  }

  /// Get study plans by type
  List<dynamic> getStudyPlansByType(String type) {
    return mockStudyPlansList
        .where((plan) => plan.type.name == type)
        .map(
          (plan) => {
            'id': plan.id,
            'title': plan.title,
            'status': plan.status.name,
            'priority': plan.priority.name,
            'progress': plan.progress.overallCompletion,
          },
        )
        .toList();
  }

  /// Get study plans by status
  List<dynamic> getStudyPlansByStatus(String status) {
    return mockStudyPlansList
        .where((plan) => plan.status.name == status)
        .map(
          (plan) => {
            'id': plan.id,
            'title': plan.title,
            'type': plan.type.name,
            'priority': plan.priority.name,
            'progress': plan.progress.overallCompletion,
          },
        )
        .toList();
  }
}
