import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:logger/logger.dart';

import '../../../../core/constants/firebase_collections.dart';
import '../../../../core/enums/auth_enums.dart';
import '../../../profile/models/profile_model.dart';
import '../generators/profile_mock_generator.dart';

/// Service for uploading mock profile data to Firestore
class ProfileUploadService {
  static final ProfileUploadService _instance =
      ProfileUploadService._internal();
  factory ProfileUploadService() => _instance;
  ProfileUploadService._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final Logger _logger = Logger();

  // Collection name
  static const String _profilesCollection = FirebaseCollections.users;

  /// Upload all mock profiles to Firestore
  Future<void> uploadAllProfiles() async {
    try {
      _logger.i('Starting upload of ${mockProfilesList.length} mock profiles');

      final batch = _firestore.batch();
      int batchCount = 0;
      const batchSize = 500; // Firestore batch limit

      for (final profile in mockProfilesList) {
        final docRef = _firestore
            .collection(_profilesCollection)
            .doc(profile.id);
        batch.set(docRef, profile.toJson());
        batchCount++;

        // Commit batch if we reach the limit
        if (batchCount >= batchSize) {
          await batch.commit();
          _logger.i('Committed batch of $batchCount profiles');
          batchCount = 0;
        }
      }

      // Commit remaining profiles
      if (batchCount > 0) {
        await batch.commit();
        _logger.i('Committed final batch of $batchCount profiles');
      }

      _logger.i(
        'Successfully uploaded ${mockProfilesList.length} mock profiles to Firestore',
      );

      // Log upload statistics
      final stats = getMockProfileStats();
      _logger.i('Upload statistics: $stats');
    } catch (e) {
      _logger.e('Error uploading mock profiles: $e');
      rethrow;
    }
  }

  /// Upload a specific profile by ID
  Future<void> uploadProfile(String profileId) async {
    try {
      final profile = mockProfilesList.firstWhere(
        (p) => p.id == profileId,
        orElse: () => throw Exception('Profile not found: $profileId'),
      );

      await _firestore
          .collection(_profilesCollection)
          .doc(profile.id)
          .set(profile.toJson());

      _logger.i('Successfully uploaded profile: ${profile.fullName}');
    } catch (e) {
      _logger.e('Error uploading profile $profileId: $e');
      rethrow;
    }
  }

  /// Upload profiles by user type
  Future<void> uploadProfilesByType(UserType userType) async {
    try {
      final profilesOfType = mockProfilesList
          .where((p) => p.userType == userType)
          .toList();

      if (profilesOfType.isEmpty) {
        _logger.w('No profiles found for user type: ${userType.name}');
        return;
      }

      _logger.i(
        'Uploading ${profilesOfType.length} profiles of type: ${userType.name}',
      );

      final batch = _firestore.batch();
      for (final profile in profilesOfType) {
        final docRef = _firestore
            .collection(_profilesCollection)
            .doc(profile.id);
        batch.set(docRef, profile.toJson());
      }

      await batch.commit();
      _logger.i(
        'Successfully uploaded ${profilesOfType.length} ${userType.name} profiles',
      );
    } catch (e) {
      _logger.e('Error uploading ${userType.name} profiles: $e');
      rethrow;
    }
  }

  /// Clear all profiles from Firestore
  Future<void> clearAllProfiles() async {
    try {
      _logger.i('Starting to clear all profiles from Firestore');

      final collection = _firestore.collection(_profilesCollection);
      final snapshot = await collection.get();

      if (snapshot.docs.isEmpty) {
        _logger.i('No profiles to clear');
        return;
      }

      final batch = _firestore.batch();
      for (final doc in snapshot.docs) {
        batch.delete(doc.reference);
      }

      await batch.commit();
      _logger.i('Successfully cleared ${snapshot.docs.length} profiles');
    } catch (e) {
      _logger.e('Error clearing profiles: $e');
      rethrow;
    }
  }

  /// Get statistics about mock profile data
  Map<String, dynamic> getMockProfileStats() {
    final stats = <String, dynamic>{
      'total': mockProfilesList.length,
      'byUserType': <String, int>{},
      'withPrimaryClass': 0,
      'withMultipleEnrollments': 0,
      'withProfileImage': 0,
      'withBio': 0,
      'byGrade': <String, int>{},
      'bySchool': <String, int>{},
    };

    for (final profile in mockProfilesList) {
      // Count by user type
      final userType = profile.userType.name;
      stats['byUserType'][userType] = (stats['byUserType'][userType] ?? 0) + 1;

      // Count profiles with primary class
      if (profile.primaryClassId != null) {
        stats['withPrimaryClass']++;
      }

      // Count profiles with profile image
      if (profile.profileImageUrl != null) {
        stats['withProfileImage']++;
      }

      // Count profiles with bio
      if (profile.bio != null && profile.bio!.isNotEmpty) {
        stats['withBio']++;
      }

      // Count by grade (students only)
      if (profile.grade != null) {
        final grade = profile.grade!;
        stats['byGrade'][grade] = (stats['byGrade'][grade] ?? 0) + 1;
      }

      // Count by school
      final school = profile.school;
      stats['bySchool'][school] = (stats['bySchool'][school] ?? 0) + 1;
    }

    return stats;
  }

  /// Validate mock profile data
  bool validateMockData() {
    try {
      if (mockProfilesList.isEmpty) {
        _logger.e('No mock profiles found');
        return false;
      }

      // Check for current user profile
      const currentUserId = 'XRTanMcAUWSMq3mrRvve2Y9IMP12';
      final currentUserProfile = mockProfilesList
          .where((p) => p.id == currentUserId)
          .toList();

      if (currentUserProfile.isEmpty) {
        _logger.e('Current user profile not found');
        return false;
      }

      if (currentUserProfile.length > 1) {
        _logger.e('Multiple profiles found for current user');
        return false;
      }

      // Check user type distribution
      final userTypeStats = <UserType, int>{};
      for (final profile in mockProfilesList) {
        userTypeStats[profile.userType] =
            (userTypeStats[profile.userType] ?? 0) + 1;
      }

      // Ensure we have students (should be majority)
      final studentCount = userTypeStats[UserType.student] ?? 0;
      if (studentCount < mockProfilesList.length * 0.7) {
        _logger.w('Low student count in mock data');
      }

      // Check for duplicate emails
      final emails = mockProfilesList.map((p) => p.email).toList();
      final uniqueEmails = emails.toSet();
      if (emails.length != uniqueEmails.length) {
        _logger.e('Duplicate emails found in mock data');
        return false;
      }

      // Check for duplicate student IDs
      final studentIds = mockProfilesList
          .where((p) => p.studentId != null)
          .map((p) => p.studentId!)
          .toList();
      final uniqueStudentIds = studentIds.toSet();
      if (studentIds.length != uniqueStudentIds.length) {
        _logger.e('Duplicate student IDs found in mock data');
        return false;
      }

      _logger.i('Mock profile data validation passed');
      _logger.i('Validation stats: $userTypeStats');
      return true;
    } catch (e) {
      _logger.e('Error validating mock profile data: $e');
      return false;
    }
  }

  /// Get profile by ID
  ProfileModel? getProfileById(String id) {
    try {
      return mockProfilesList.firstWhere((p) => p.id == id);
    } catch (e) {
      return null;
    }
  }

  /// Get profiles by user type
  List<ProfileModel> getProfilesByType(UserType userType) {
    return mockProfilesList.where((p) => p.userType == userType).toList();
  }

  /// Get current user profile
  ProfileModel? getCurrentUserProfile() {
    const currentUserId = 'XRTanMcAUWSMq3mrRvve2Y9IMP12';
    return getProfileById(currentUserId);
  }
}
