import 'package:faker/faker.dart';
import '../../../../core/enums/homework/assignment_type.dart';
import '../../../../core/enums/homework/homework_status.dart';
import '../../../../core/enums/homework/submission_type.dart';
import '../../../homework/models/homework_model.dart';
import '../../../homework/models/homework_submission_model.dart';
import '../services/mock_data_id_manager.dart';
import 'classroom_mock_generator.dart';

/// Mock data for homework assignments with comprehensive test coverage
final List<HomeworkModel> mockHomeworkList = _generateMockHomework();

/// Generate comprehensive mock homework data ensuring each status has at least 3 test cases
List<HomeworkModel> _generateMockHomework() {
  final faker = Faker();
  final homeworkList = <HomeworkModel>[];

  // Current user ID for individual/group assignments
  final currentUserId = MockDataIdManager.getCurrentUserId();

  // Get available classes for assignment
  final availableClasses = mockClassesList;

  // Define subjects with realistic homework topics
  final subjectTopics = {
    'Mathematics': [
      'Algebra Practice Problems',
      'Geometry Proofs',
      'Calculus Integration',
      'Statistics Analysis',
      'Trigonometry Applications',
      'Linear Equations',
      'Quadratic Functions',
      'Mathematical Modeling',
    ],
    'Physics': [
      'Newton\'s Laws Lab Report',
      'Electromagnetic Waves Study',
      'Thermodynamics Problems',
      'Quantum Mechanics Research',
      'Optics Experiment',
      'Mechanics Problem Set',
      'Energy Conservation Analysis',
      'Wave Motion Study',
    ],
    'Chemistry': [
      'Organic Compounds Analysis',
      'Chemical Reactions Lab',
      'Periodic Table Study',
      'Molecular Structure Research',
      'Acid-Base Titration',
      'Chemical Bonding Assignment',
      'Stoichiometry Problems',
      'Electrochemistry Lab',
    ],
    'Biology': [
      'Cell Structure Diagram',
      'Genetics Problem Set',
      'Ecosystem Analysis',
      'Human Anatomy Study',
      'Plant Biology Research',
      'Evolution Theory Essay',
      'Microbiology Lab Report',
      'DNA Replication Study',
    ],
    'English Literature': [
      'Shakespeare Analysis',
      'Poetry Interpretation',
      'Novel Character Study',
      'Creative Writing Assignment',
      'Literary Criticism Essay',
      'Grammar Exercises',
      'Vocabulary Building',
      'Reading Comprehension',
    ],
    'History': [
      'World War II Research',
      'Ancient Civilizations Study',
      'Historical Timeline Creation',
      'Primary Source Analysis',
      'Cultural Impact Essay',
      'Political Systems Comparison',
      'Historical Figure Biography',
      'Archaeological Evidence Study',
    ],
    'Geography': [
      'Climate Change Analysis',
      'Map Reading Exercise',
      'Population Demographics Study',
      'Natural Resources Research',
      'Urban Planning Project',
      'Environmental Impact Assessment',
      'Cultural Geography Essay',
      'Economic Geography Analysis',
    ],
    'Computer Science': [
      'Algorithm Implementation',
      'Database Design Project',
      'Web Development Assignment',
      'Data Structures Practice',
      'Software Testing Lab',
      'Programming Logic Problems',
      'System Architecture Design',
      'Cybersecurity Research',
    ],
  };

  // Status distribution requirements (minimum 3 of each)
  final statusRequirements = {
    HomeworkStatus.pending: 3,
    HomeworkStatus.done: 3,
    HomeworkStatus.submitted: 3,
    HomeworkStatus.accepted: 3,
    HomeworkStatus.rejected: 3,
  };

  final statusCounts = <HomeworkStatus, int>{};

  // Generate homework ensuring minimum status coverage
  for (final entry in statusRequirements.entries) {
    final status = entry.key;
    final minCount = entry.value;

    for (int i = 0; i < minCount; i++) {
      final homework = _generateHomeworkWithStatus(
        faker,
        availableClasses,
        subjectTopics,
        status,
        currentUserId,
      );
      homeworkList.add(homework);
      statusCounts[status] = (statusCounts[status] ?? 0) + 1;
    }
  }

  // Generate additional random homework (15-20 more items)
  final additionalCount = faker.randomGenerator.integer(20, min: 15);
  for (int i = 0; i < additionalCount; i++) {
    final randomStatus = HomeworkStatus
        .values[faker.randomGenerator.integer(HomeworkStatus.values.length)];

    final homework = _generateHomeworkWithStatus(
      faker,
      availableClasses,
      subjectTopics,
      randomStatus,
      currentUserId,
    );
    homeworkList.add(homework);
    statusCounts[randomStatus] = (statusCounts[randomStatus] ?? 0) + 1;
  }

  // Shuffle the list for realistic ordering
  homeworkList.shuffle();

  return homeworkList;
}

/// Generate a homework item with specific status
HomeworkModel _generateHomeworkWithStatus(
  Faker faker,
  List<dynamic> availableClasses,
  Map<String, List<String>> subjectTopics,
  HomeworkStatus status,
  String currentUserId,
) {
  // Select a random class
  final selectedClass =
      availableClasses[faker.randomGenerator.integer(availableClasses.length)];

  // Get subject and topic
  final subject = selectedClass.subject ?? 'General';
  final topics = subjectTopics[subject] ?? ['General Assignment'];
  final topic = topics[faker.randomGenerator.integer(topics.length)];

  // Determine assignment type
  final assignmentType = faker.randomGenerator.boolean()
      ? AssignmentType.classAssignment
      : AssignmentType.individual;

  // Generate realistic dates
  final daysAgo = faker.randomGenerator.integer(30, min: 1);
  final assignedAt = DateTime.now().subtract(Duration(days: daysAgo));

  // Due date logic based on status
  DateTime? dueAt;
  if (status == HomeworkStatus.pending || status == HomeworkStatus.rejected) {
    // Future due date for pending/rejected
    final daysUntilDue = faker.randomGenerator.integer(7, min: 1);
    dueAt = DateTime.now().add(Duration(days: daysUntilDue));
  } else {
    // Past due date for completed statuses
    final daysPastDue = faker.randomGenerator.integer(5, min: 0);
    dueAt = assignedAt.add(Duration(days: daysPastDue));
  }

  // Submission requirements
  final requiresSubmission = status != HomeworkStatus.done;
  final submissionType = faker.randomGenerator.boolean()
      ? SubmissionType.online
      : SubmissionType.offline;

  // Generate assigned user IDs
  final assignedUserIds = <String>[currentUserId];
  if (assignmentType == AssignmentType.classAssignment) {
    // Add some other students for class assignments
    final additionalCount = faker.randomGenerator.integer(3, min: 1);
    final otherStudents = selectedClass.studentIds
        .where((id) => id != currentUserId)
        .take(additionalCount)
        .toList();
    assignedUserIds.addAll(otherStudents);
  }

  final homeworkId = MockDataIdManager.getHomeworkId(
    '${subject}_${topic}_${assignedAt.millisecondsSinceEpoch}',
  );

  return HomeworkModel(
    id: homeworkId,
    subject: subject,
    title: topic,
    description: faker.randomGenerator.boolean()
        ? faker.lorem
              .sentences(faker.randomGenerator.integer(3, min: 1))
              .join(' ')
        : null,
    assignedAt: assignedAt,
    dueAt: dueAt,
    requiresSubmission: requiresSubmission,
    submissionType: submissionType,
    status: status,
    resourceUrls: List.generate(
      faker.randomGenerator.integer(3, min: 0),
      (i) => 'https://example.com/resources/${faker.lorem.word()}_${i + 1}.pdf',
    ),
    teacherNote: faker.randomGenerator.boolean()
        ? faker.lorem.sentence()
        : null,
    classId: assignmentType == AssignmentType.classAssignment
        ? selectedClass.id
        : null,
    teacherId: selectedClass.teacherId,
    submissionId:
        (status == HomeworkStatus.submitted ||
            status == HomeworkStatus.accepted ||
            status == HomeworkStatus.rejected)
        ? MockDataIdManager.getSubmissionId('sub_$homeworkId')
        : null,
    assignmentType: assignmentType,
    assignedUserIds: assignedUserIds,
  );
}

/// Mock data for homework submissions with comprehensive test coverage
final List<HomeworkSubmissionModel> mockHomeworkSubmissions =
    _generateMockSubmissions();

/// Generate mock submissions for homework that requires them
List<HomeworkSubmissionModel> _generateMockSubmissions() {
  final faker = Faker();
  final submissions = <HomeworkSubmissionModel>[];

  // Current user ID
  const currentUserId = 'XRTanMcAUWSMq3mrRvve2Y9IMP12';

  // Get homework that requires submissions and has submission status
  final homeworkWithSubmissions = mockHomeworkList
      .where(
        (hw) =>
            hw.requiresSubmission &&
            (hw.status == HomeworkStatus.submitted ||
                hw.status == HomeworkStatus.accepted ||
                hw.status == HomeworkStatus.rejected),
      )
      .toList();

  for (final homework in homeworkWithSubmissions) {
    final submission = _generateSubmissionForHomework(
      faker,
      homework,
      currentUserId,
    );
    submissions.add(submission);
  }

  return submissions;
}

/// Generate a submission for specific homework
HomeworkSubmissionModel _generateSubmissionForHomework(
  Faker faker,
  HomeworkModel homework,
  String currentUserId,
) {
  // Generate realistic submission date
  final submittedAt =
      homework.dueAt?.subtract(
        Duration(hours: faker.randomGenerator.integer(48, min: 1)),
      ) ??
      DateTime.now().subtract(
        Duration(hours: faker.randomGenerator.integer(24, min: 1)),
      );

  // Generate file URLs based on submission type
  final fileUrls = <String>[];
  if (homework.submissionType == SubmissionType.online) {
    final fileCount = faker.randomGenerator.integer(3, min: 1);
    final fileTypes = ['pdf', 'docx', 'jpg', 'png', 'txt'];

    for (int i = 0; i < fileCount; i++) {
      final fileType =
          fileTypes[faker.randomGenerator.integer(fileTypes.length)];
      fileUrls.add(
        'https://example.com/submissions/${homework.id}_file_${i + 1}.$fileType',
      );
    }
  }

  // Generate teacher review data for accepted/rejected submissions
  String? teacherRemark;
  DateTime? reviewedAt;

  if (homework.status == HomeworkStatus.accepted ||
      homework.status == HomeworkStatus.rejected) {
    reviewedAt = submittedAt.add(
      Duration(hours: faker.randomGenerator.integer(72, min: 2)),
    );

    if (homework.status == HomeworkStatus.accepted) {
      teacherRemark = faker.randomGenerator.element([
        'Excellent work! Well done.',
        'Good effort and clear understanding.',
        'Nice job on this assignment.',
        'Great analysis and presentation.',
        'Well researched and organized.',
      ]);
    } else {
      teacherRemark = faker.randomGenerator.element([
        'Please review the requirements and resubmit.',
        'Missing key components. See feedback.',
        'Needs more detail and analysis.',
        'Please address the formatting issues.',
        'Incomplete submission. Please complete all sections.',
      ]);
    }
  }

  return HomeworkSubmissionModel(
    id: homework.submissionId!,
    homeworkId: homework.id,
    userId: currentUserId,
    submittedAt: submittedAt,
    fileUrls: fileUrls,
    studentNote: faker.randomGenerator.boolean()
        ? faker.lorem.sentence()
        : null,
    teacherRemark: teacherRemark,
    reviewedAt: reviewedAt,
  );
}
