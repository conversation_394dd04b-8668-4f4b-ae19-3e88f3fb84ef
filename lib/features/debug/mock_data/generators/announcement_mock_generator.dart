import 'package:faker/faker.dart';
import 'package:scholara_student/core/enums/auth_enums.dart';
import '../../../announcements/enums/announcement_type.dart';
import '../../../announcements/enums/announcement_priority.dart';
import '../../../announcements/enums/announcement_status.dart';
import '../../../announcements/enums/announcement_usage_type.dart';
import '../../../announcements/models/announcement_model.dart';
import '../services/mock_data_id_manager.dart';
import 'classroom_mock_generator.dart';
import 'profile_mock_generator.dart';

/// Mock data for announcements with comprehensive test coverage
final List<AnnouncementModel> mockAnnouncementsList =
    _generateMockAnnouncements();

/// Generate realistic mock announcement data
List<AnnouncementModel> _generateMockAnnouncements() {
  final faker = Faker();
  final announcements = <AnnouncementModel>[];

  // Pre-generate consistent IDs
  MockDataIdManager.preGenerateIds();

  // Set the current user ID to the expected value
  MockDataIdManager.setCurrentUserId('XRTanMcAUWSMq3mrRvve2Y9IMP12');

  // Get existing data for relationships
  final classrooms = mockClassesList;
  final profiles = mockProfilesList;

  // Generate different types of announcements
  announcements.addAll(_generateSchoolNotices(faker, profiles));
  announcements.addAll(
    _generateClassroomAnnouncements(faker, classrooms, profiles),
  );
  announcements.addAll(_generateAcademicReminders(faker, classrooms, profiles));
  announcements.addAll(_generateNewsUpdates(faker, profiles));
  announcements.addAll(_generateEmergencyAlerts(faker, profiles));
  announcements.addAll(_generateGeneralAnnouncements(faker, profiles));

  // Sort by creation date (newest first)
  announcements.sort((a, b) => b.createdAt.compareTo(a.createdAt));

  return announcements;
}

/// Generate school/organization notices
List<AnnouncementModel> _generateSchoolNotices(Faker faker, List profiles) {
  final notices = <AnnouncementModel>[];
  final adminProfiles = profiles
      .where((p) => p.userType == UserType.admin)
      .toList();

  final noticeTopics = [
    'Holiday Schedule Update',
    'New Academic Policy',
    'Fee Payment Deadline',
    'Parent-Teacher Meeting',
    'Examination Schedule',
    'Library Hours Update',
    'Uniform Policy Changes',
    'Transportation Updates',
  ];

  for (int i = 0; i < 8; i++) {
    final author = adminProfiles.isNotEmpty
        ? adminProfiles[faker.randomGenerator.integer(adminProfiles.length)]
        : profiles[0];

    final createdAt = faker.date.dateTimeBetween(
      DateTime.now().subtract(const Duration(days: 30)),
      DateTime.now(),
    );

    notices.add(
      AnnouncementModel(
        id: MockDataIdManager.getAnnouncementId('notice_$i'),
        title: noticeTopics[i],
        content: _generateNoticeContent(faker, noticeTopics[i]),
        type: AnnouncementType.notice,
        priority: _getRandomPriority(faker, [
          AnnouncementPriority.important,
          AnnouncementPriority.normal,
        ]),
        status: AnnouncementStatus.published,
        authorId: author.id,
        authorName: author.fullName,
        authorRole: author.userType.toString(),
        createdAt: createdAt,
        usageType: AnnouncementUsageType.organization,
        targetAudience: profiles.map<String>((p) => p.id).toList(),
        organizationId: 'org_scholara_main',
        isPinned: faker.randomGenerator.boolean(), // Random pinning
        tags: _generateNoticeTags(noticeTopics[i]),
        viewCount: faker.randomGenerator.integer(200, min: 50),
        readBy: _generateReadByList(faker, profiles, 0.7), // 70% read rate
        readTimestamps: {},
      ),
    );
  }

  return notices;
}

/// Generate classroom-specific announcements
List<AnnouncementModel> _generateClassroomAnnouncements(
  Faker faker,
  List classrooms,
  List profiles,
) {
  final announcements = <AnnouncementModel>[];
  final teacherProfiles = profiles
      .where((p) => p.userType == UserType.teacher)
      .toList();

  final announcementTopics = [
    'Assignment Submission Reminder',
    'Class Schedule Change',
    'Quiz Announcement',
    'Project Guidelines',
    'Study Material Available',
    'Lab Session Update',
    'Guest Lecture Announcement',
    'Field Trip Information',
  ];

  for (int i = 0; i < 12; i++) {
    final classroom =
        classrooms[faker.randomGenerator.integer(classrooms.length)];
    final author = teacherProfiles.isNotEmpty
        ? teacherProfiles[faker.randomGenerator.integer(teacherProfiles.length)]
        : profiles[0];

    final createdAt = faker.date.dateTimeBetween(
      DateTime.now().subtract(const Duration(days: 14)),
      DateTime.now(),
    );

    announcements.add(
      AnnouncementModel(
        id: MockDataIdManager.getAnnouncementId('classroom_announcement_$i'),
        title:
            '${classroom.subject ?? classroom.name}: ${announcementTopics[i % announcementTopics.length]}',
        content: _generateClassroomAnnouncementContent(
          faker,
          announcementTopics[i % announcementTopics.length],
        ),
        type: AnnouncementType.notice,
        priority: _getRandomPriority(faker, [
          AnnouncementPriority.normal,
          AnnouncementPriority.important,
        ]),
        status: AnnouncementStatus.published,
        authorId: author.id,
        authorName: author.fullName,
        authorRole: author.userType.toString(),
        createdAt: createdAt,
        usageType: AnnouncementUsageType.classroom,
        targetAudience: classroom.studentIds,
        classroomId: classroom.id,
        organizationId: 'org_scholara_main',
        tags: _generateClassroomTags(classroom.subject ?? classroom.name),
        viewCount: faker.randomGenerator.integer(50, min: 10),
        readBy: _generateReadByList(
          faker,
          classroom.studentIds,
          0.8,
        ), // 80% read rate
        readTimestamps: {},
      ),
    );
  }

  return announcements;
}

/// Generate academic reminders
List<AnnouncementModel> _generateAcademicReminders(
  Faker faker,
  List classrooms,
  List profiles,
) {
  final reminders = <AnnouncementModel>[];
  final teacherProfiles = profiles
      .where((p) => p.userType == UserType.teacher)
      .toList();

  final reminderTypes = [
    'Assignment Due Tomorrow',
    'Exam Next Week',
    'Project Submission Deadline',
    'Library Book Return',
    'Fee Payment Reminder',
    'Registration Deadline',
  ];

  for (int i = 0; i < 8; i++) {
    final author = teacherProfiles.isNotEmpty
        ? teacherProfiles[faker.randomGenerator.integer(teacherProfiles.length)]
        : profiles[0];

    final createdAt = faker.date.dateTimeBetween(
      DateTime.now().subtract(const Duration(days: 7)),
      DateTime.now(),
    );

    final dueDate = faker.date.dateTimeBetween(
      DateTime.now().add(const Duration(hours: 12)),
      DateTime.now().add(const Duration(days: 7)),
    );

    reminders.add(
      AnnouncementModel(
        id: MockDataIdManager.getAnnouncementId('reminder_$i'),
        title: reminderTypes[i % reminderTypes.length],
        content: _generateReminderContent(
          faker,
          reminderTypes[i % reminderTypes.length],
          dueDate,
        ),
        type: AnnouncementType.reminder,
        priority: AnnouncementPriority.urgent,
        status: AnnouncementStatus.published,
        authorId: author.id,
        authorName: author.fullName,
        authorRole: author.userType.toString(),
        createdAt: createdAt,
        expiresAt: dueDate,
        usageType: faker.randomGenerator.boolean()
            ? AnnouncementUsageType.academic
            : AnnouncementUsageType.classroom,
        targetAudience: faker.randomGenerator.boolean()
            ? profiles.map((p) {
                return p.id as String;
              }).toList()
            : classrooms[faker.randomGenerator.integer(classrooms.length)]
                  .studentIds,
        classroomId: faker.randomGenerator.boolean()
            ? classrooms[faker.randomGenerator.integer(classrooms.length)].id
            : null,
        organizationId: 'org_scholara_main',
        tags: ['reminder', 'deadline', 'urgent'],
        viewCount: faker.randomGenerator.integer(150, min: 30),
        readBy: _generateReadByList(
          faker,
          profiles,
          0.9,
        ), // 90% read rate for urgent reminders
        readTimestamps: {},
        metadata: {
          'dueDate': dueDate.toIso8601String(),
          'reminderType': reminderTypes[i % reminderTypes.length],
        },
      ),
    );
  }

  return reminders;
}

/// Generate news and updates
List<AnnouncementModel> _generateNewsUpdates(Faker faker, List profiles) {
  final news = <AnnouncementModel>[];
  final adminProfiles = profiles
      .where((p) => p.userType == UserType.admin)
      .toList();

  final newsTopics = [
    'Student Achievement Recognition',
    'New Faculty Joining',
    'Infrastructure Upgrade',
    'Academic Excellence Awards',
    'School Ranking Update',
    'Community Service Initiative',
  ];

  for (int i = 0; i < 5; i++) {
    final author = adminProfiles.isNotEmpty
        ? adminProfiles[faker.randomGenerator.integer(adminProfiles.length)]
        : profiles[0];

    final createdAt = faker.date.dateTimeBetween(
      DateTime.now().subtract(const Duration(days: 15)),
      DateTime.now(),
    );

    news.add(
      AnnouncementModel(
        id: MockDataIdManager.getAnnouncementId('news_$i'),
        title: newsTopics[i],
        content: _generateNewsContent(faker, newsTopics[i]),
        type: AnnouncementType.news,
        priority: AnnouncementPriority.normal,
        status: AnnouncementStatus.published,
        authorId: author.id,
        authorName: author.fullName,
        authorRole: author.userType.toString(),
        createdAt: createdAt,
        usageType: AnnouncementUsageType.organization,
        targetAudience: profiles.map<String>((p) => p.id).toList(),
        organizationId: 'org_scholara_main',
        tags: ['news', 'update', 'achievement'],
        viewCount: faker.randomGenerator.integer(250, min: 80),
        readBy: _generateReadByList(faker, profiles, 0.5), // 50% read rate
        readTimestamps: {},
      ),
    );
  }

  return news;
}

/// Generate emergency alerts
List<AnnouncementModel> _generateEmergencyAlerts(Faker faker, List profiles) {
  final alerts = <AnnouncementModel>[];
  final adminProfiles = profiles
      .where((p) => p.userType == UserType.admin)
      .toList();

  final alertTypes = [
    'Weather Alert - Classes Suspended',
    'Emergency Drill Announcement',
    'Security Update',
  ];

  for (int i = 0; i < 2; i++) {
    final author = adminProfiles.isNotEmpty
        ? adminProfiles[faker.randomGenerator.integer(adminProfiles.length)]
        : profiles[0];

    final createdAt = faker.date.dateTimeBetween(
      DateTime.now().subtract(const Duration(days: 5)),
      DateTime.now(),
    );

    alerts.add(
      AnnouncementModel(
        id: MockDataIdManager.getAnnouncementId('alert_$i'),
        title: alertTypes[i],
        content: _generateAlertContent(faker, alertTypes[i]),
        type: AnnouncementType.alert,
        priority: AnnouncementPriority.emergency,
        status: AnnouncementStatus.published,
        authorId: author.id,
        authorName: author.fullName,
        authorRole: author.userType.toString(),
        createdAt: createdAt,
        usageType: AnnouncementUsageType.organization,
        targetAudience: profiles.map<String>((p) => p.id).toList(),
        organizationId: 'org_scholara_main',
        isPinned: true,
        tags: ['emergency', 'alert', 'urgent'],
        viewCount: faker.randomGenerator.integer(400, min: 200),
        readBy: _generateReadByList(
          faker,
          profiles,
          0.95,
        ), // 95% read rate for emergencies
        readTimestamps: {},
      ),
    );
  }

  return alerts;
}

/// Generate general announcements
List<AnnouncementModel> _generateGeneralAnnouncements(
  Faker faker,
  List profiles,
) {
  final general = <AnnouncementModel>[];
  final adminProfiles = profiles
      .where((p) => p.userType == UserType.admin)
      .toList();

  final generalTopics = [
    'Cafeteria Menu Update',
    'Lost and Found Items',
    'Parking Guidelines',
    'Health and Wellness Tips',
    'Technology Usage Policy',
  ];

  for (int i = 0; i < 4; i++) {
    final author = adminProfiles.isNotEmpty
        ? adminProfiles[faker.randomGenerator.integer(adminProfiles.length)]
        : profiles[0];

    final createdAt = faker.date.dateTimeBetween(
      DateTime.now().subtract(const Duration(days: 10)),
      DateTime.now(),
    );

    general.add(
      AnnouncementModel(
        id: MockDataIdManager.getAnnouncementId('general_$i'),
        title: generalTopics[i],
        content: _generateGeneralContent(faker, generalTopics[i]),
        type: AnnouncementType.general,
        priority: AnnouncementPriority.normal,
        status: AnnouncementStatus.published,
        authorId: author.id,
        authorName: author.fullName,
        authorRole: author.userType.toString(),
        createdAt: createdAt,
        usageType: AnnouncementUsageType.organization,
        targetAudience: profiles.map<String>((p) => p.id).toList(),
        organizationId: 'org_scholara_main',
        tags: ['general', 'information'],
        viewCount: faker.randomGenerator.integer(100, min: 20),
        readBy: _generateReadByList(faker, profiles, 0.4), // 40% read rate
        readTimestamps: {},
      ),
    );
  }

  return general;
}

// Helper functions for content generation

/// Generate random priority from a list of options
AnnouncementPriority _getRandomPriority(
  Faker faker,
  List<AnnouncementPriority> options,
) {
  return options[faker.randomGenerator.integer(options.length)];
}

/// Generate read by list based on read rate
List<String> _generateReadByList(
  Faker faker,
  dynamic profiles,
  double readRate,
) {
  final List userIds;

  if (profiles is List<String>) {
    userIds = profiles;
  } else {
    userIds = profiles.map((p) => p.id).toList();
  }

  final readCount = (userIds.length * readRate).round();
  final shuffled = List<String>.from(userIds)..shuffle();
  return shuffled.take(readCount).toList();
}

/// Generate notice content
String _generateNoticeContent(Faker faker, String topic) {
  switch (topic) {
    case 'Holiday Schedule Update':
      return 'Dear Students and Parents,\n\nWe would like to inform you about the updated holiday schedule for this academic year. Please note the following changes:\n\n• ${faker.date.dateTime().day}/${faker.date.dateTime().month} - Additional holiday\n• Regular classes will resume as per the revised schedule\n\nFor any queries, please contact the administration office.\n\nBest regards,\nSchool Administration';
    case 'Fee Payment Deadline':
      return 'This is a reminder that the fee payment deadline is approaching. Please ensure all dues are cleared by the specified date to avoid any inconvenience.\n\nPayment can be made through:\n• Online portal\n• Bank transfer\n• Cash at the accounts office\n\nThank you for your cooperation.';
    default:
      return faker.lorem
          .sentences(faker.randomGenerator.integer(5, min: 3))
          .join(' ');
  }
}

/// Generate classroom announcement content
String _generateClassroomAnnouncementContent(Faker faker, String topic) {
  switch (topic) {
    case 'Assignment Submission Reminder':
      return 'Dear Students,\n\nThis is a reminder that your assignment is due soon. Please ensure you submit your work on time through the designated platform.\n\nRequirements:\n• Proper formatting\n• Original work\n• References cited\n\nLate submissions will result in grade deduction.';
    case 'Quiz Announcement':
      return 'Students,\n\nA quiz has been scheduled for next week. Please prepare the following topics:\n\n• ${faker.lorem.word()}\n• ${faker.lorem.word()}\n• ${faker.lorem.word()}\n\nGood luck with your preparation!';
    default:
      return faker.lorem
          .sentences(faker.randomGenerator.integer(4, min: 2))
          .join(' ');
  }
}

/// Generate reminder content
String _generateReminderContent(
  Faker faker,
  String reminderType,
  DateTime dueDate,
) {
  return 'URGENT REMINDER: $reminderType\n\nDeadline: ${dueDate.day}/${dueDate.month}/${dueDate.year} at ${dueDate.hour}:${dueDate.minute.toString().padLeft(2, '0')}\n\nPlease ensure you complete the required action before the deadline. Late submissions may not be accepted.\n\nFor any assistance, please contact the relevant department immediately.';
}

/// Generate news content
String _generateNewsContent(Faker faker, String newsType) {
  switch (newsType) {
    case 'Student Achievement Recognition':
      return 'We are proud to announce that our students have achieved remarkable success in recent competitions. Congratulations to all the winners and participants for their outstanding performance.\n\nAchievements:\n• First place in ${faker.lorem.word()} competition\n• Excellence in ${faker.lorem.word()} category\n• Recognition for ${faker.lorem.word()} skills\n\nWe celebrate these achievements and encourage all students to continue striving for excellence.';
    default:
      return faker.lorem
          .sentences(faker.randomGenerator.integer(4, min: 2))
          .join(' ');
  }
}

/// Generate alert content
String _generateAlertContent(Faker faker, String alertType) {
  switch (alertType) {
    case 'Weather Alert - Classes Suspended':
      return 'EMERGENCY ALERT\n\nDue to severe weather conditions, all classes are suspended for today. The safety of our students and staff is our top priority.\n\nImportant Information:\n• All outdoor activities are cancelled\n• Campus will remain closed\n• Updates will be provided regularly\n\nPlease stay safe and follow local weather advisories.';
    default:
      return 'URGENT: $alertType\n\n${faker.lorem.sentences(2).join(' ')}\n\nPlease follow the instructions provided and stay alert for further updates.';
  }
}

/// Generate general content
String _generateGeneralContent(Faker faker, String topic) {
  return 'Information regarding $topic:\n\n${faker.lorem.sentences(faker.randomGenerator.integer(3, min: 2)).join(' ')}\n\nFor more details, please contact the administration office during working hours.';
}

/// Generate notice tags
List<String> _generateNoticeTags(String topic) {
  final baseTags = ['notice', 'official'];

  if (topic.toLowerCase().contains('holiday')) {
    baseTags.addAll(['holiday', 'schedule']);
  } else if (topic.toLowerCase().contains('fee')) {
    baseTags.addAll(['fee', 'payment', 'deadline']);
  } else if (topic.toLowerCase().contains('exam')) {
    baseTags.addAll(['exam', 'academic']);
  }

  return baseTags;
}

/// Generate classroom tags
List<String> _generateClassroomTags(String subject) {
  return ['classroom', subject.toLowerCase().replaceAll(' ', '_'), 'academic'];
}
