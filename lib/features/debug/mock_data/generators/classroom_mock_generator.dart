import 'package:faker/faker.dart';
import '../../../classroom/models/class_model.dart';
import '../../../classroom/enums/classroom_type.dart';
import '../services/mock_data_id_manager.dart';

/// Mock data for classes with realistic data generation
final List<ClassModel> mockClassesList = _generateMockClasses();

/// Generate realistic mock classroom data
List<ClassModel> _generateMockClasses() {
  final faker = Faker();
  final classes = <ClassModel>[];

  // Pre-generate consistent IDs
  MockDataIdManager.preGenerateIds();

  // Set the current user ID to the expected value
  MockDataIdManager.setCurrentUserId('XRTanMcAUWSMq3mrRvve2Y9IMP12');

  // Define realistic subjects and grade levels
  final coreSubjects = [
    'Mathematics',
    'Physics',
    'Chemistry',
    'Biology',
    'English Literature',
    'History',
    'Geography',
  ];

  final electiveSubjects = [
    'Computer Science',
    'Art',
    'Music',
    'Psychology',
    'Economics',
  ];

  final clubs = [
    'Chess Club',
    'Debate Club',
    'Robotics Club',
    'Drama Club',
    'Photography Club',
  ];

  final teams = [
    'Basketball Team',
    'Football Team',
    'Cricket Team',
    'Volleyball Team',
  ];

  final gradeLevels = ['10', '11', '12'];
  final sections = ['A', 'B', 'C'];

  // Current user ID
  final currentUserId = MockDataIdManager.getCurrentUserId();

  // Generate realistic teacher data
  final teachers = _generateTeachers(faker);

  // Generate student IDs (excluding current user)
  final allStudentIds = MockDataIdManager.getAllStudentIds()
      .where((id) => id != currentUserId)
      .toList();

  // Generate core subject classes (4-6 classes)
  final coreClassCount = faker.randomGenerator.integer(6, min: 4);
  final shuffledCoreSubjects = List.from(coreSubjects)..shuffle();

  for (int i = 0; i < coreClassCount; i++) {
    final subject = shuffledCoreSubjects[i % shuffledCoreSubjects.length];
    final gradeLevel =
        gradeLevels[faker.randomGenerator.integer(gradeLevels.length)];
    final section = sections[faker.randomGenerator.integer(sections.length)];
    final teacher = teachers[faker.randomGenerator.integer(teachers.length)];

    // Generate student list for this class (15-25 students including current user)
    final studentCount = faker.randomGenerator.integer(25, min: 15);
    final shuffledStudents = List.from(allStudentIds)..shuffle();
    final classStudents = <String>[currentUserId];

    for (int j = 0; j < studentCount - 1 && j < shuffledStudents.length; j++) {
      if (shuffledStudents[j] != currentUserId) {
        classStudents.add(shuffledStudents[j]);
      }
    }

    classes.add(
      ClassModel(
        id: MockDataIdManager.getClassroomId(
          'core_${subject}_${gradeLevel}_$section',
        ),
        name: '$subject $gradeLevel$section',
        subject: subject,
        description: _generateClassroomDescription(
          subject,
          ClassroomType.coreSubject,
        ),
        type: ClassroomType.coreSubject,
        teacherId: teacher['id']!,
        teacherName: teacher['name']!,
        studentIds: classStudents,
        gradeLevel: gradeLevel,
        section: section,
        isPrimaryClass: true, // Core subjects can be primary classes
        createdAt: DateTime.now().subtract(
          Duration(days: faker.randomGenerator.integer(200, min: 30)),
        ),
        isActive: true,
      ),
    );
  }

  // Generate elective classes (2-3 classes)
  final electiveClassCount = faker.randomGenerator.integer(3, min: 2);
  final shuffledElectives = List.from(electiveSubjects)..shuffle();

  for (int i = 0; i < electiveClassCount; i++) {
    final subject = shuffledElectives[i % shuffledElectives.length];
    final teacher = teachers[faker.randomGenerator.integer(teachers.length)];

    // Generate student list for electives (8-15 students including current user)
    final studentCount = faker.randomGenerator.integer(15, min: 8);
    final shuffledStudents = List.from(allStudentIds)..shuffle();
    final classStudents = <String>[currentUserId];

    for (int j = 0; j < studentCount - 1 && j < shuffledStudents.length; j++) {
      if (shuffledStudents[j] != currentUserId) {
        classStudents.add(shuffledStudents[j]);
      }
    }

    classes.add(
      ClassModel(
        id: MockDataIdManager.getClassroomId('elective_$subject'),
        name: subject,
        subject: subject,
        description: _generateClassroomDescription(
          subject,
          ClassroomType.elective,
        ),
        type: ClassroomType.elective,
        teacherId: teacher['id']!,
        teacherName: teacher['name']!,
        studentIds: classStudents,
        isPrimaryClass: false, // Electives cannot be primary classes
        createdAt: DateTime.now().subtract(
          Duration(days: faker.randomGenerator.integer(150, min: 20)),
        ),
        isActive: true,
      ),
    );
  }

  // Generate club classes (1-2 clubs)
  final clubCount = faker.randomGenerator.integer(2, min: 1);
  final shuffledClubs = List.from(clubs)..shuffle();

  for (int i = 0; i < clubCount; i++) {
    final clubName = shuffledClubs[i % shuffledClubs.length];
    final teacher = teachers[faker.randomGenerator.integer(teachers.length)];

    // Generate student list for clubs (5-12 students including current user)
    final studentCount = faker.randomGenerator.integer(12, min: 5);
    final shuffledStudents = List.from(allStudentIds)..shuffle();
    final classStudents = <String>[currentUserId];

    for (int j = 0; j < studentCount - 1 && j < shuffledStudents.length; j++) {
      if (shuffledStudents[j] != currentUserId) {
        classStudents.add(shuffledStudents[j]);
      }
    }

    classes.add(
      ClassModel(
        id: MockDataIdManager.getClassroomId('club_$clubName'),
        name: clubName,
        description: _generateClassroomDescription(
          clubName,
          ClassroomType.club,
        ),
        type: ClassroomType.club,
        teacherId: teacher['id']!,
        teacherName: teacher['name']!,
        studentIds: classStudents,
        isPrimaryClass: false, // Clubs cannot be primary classes
        createdAt: DateTime.now().subtract(
          Duration(days: faker.randomGenerator.integer(100, min: 10)),
        ),
        isActive: true,
      ),
    );
  }

  // Generate team classes (0-1 team)
  if (faker.randomGenerator.boolean()) {
    final teamName = teams[faker.randomGenerator.integer(teams.length)];
    final coach = teachers[faker.randomGenerator.integer(teachers.length)];

    // Generate student list for teams (8-15 students including current user)
    final studentCount = faker.randomGenerator.integer(15, min: 8);
    final shuffledStudents = List.from(allStudentIds)..shuffle();
    final classStudents = <String>[currentUserId];

    for (int j = 0; j < studentCount - 1 && j < shuffledStudents.length; j++) {
      if (shuffledStudents[j] != currentUserId) {
        classStudents.add(shuffledStudents[j]);
      }
    }

    classes.add(
      ClassModel(
        id: MockDataIdManager.getClassroomId('team_$teamName'),
        name: teamName,
        description: _generateClassroomDescription(
          teamName,
          ClassroomType.team,
        ),
        type: ClassroomType.team,
        teacherId: coach['id']!,
        teacherName: coach['name']!,
        studentIds: classStudents,
        isPrimaryClass: false, // Teams cannot be primary classes
        createdAt: DateTime.now().subtract(
          Duration(days: faker.randomGenerator.integer(80, min: 5)),
        ),
        isActive: true,
      ),
    );
  }

  return classes;
}

/// Generate realistic teacher data
List<Map<String, String>> _generateTeachers(Faker faker) {
  return List.generate(15, (index) {
    return {
      'id': MockDataIdManager.getTeacherId('teacher_$index'),
      'name': faker.person.name(),
    };
  });
}

/// Generate a realistic description for a classroom based on subject and type
String? _generateClassroomDescription(String? subject, ClassroomType type) {
  final faker = Faker();

  // Only generate descriptions for some classrooms (about 60%)
  if (faker.randomGenerator.integer(100) > 60) {
    return null;
  }

  switch (type) {
    case ClassroomType.coreSubject:
      return _getCoreSubjectDescription(subject, faker);
    case ClassroomType.elective:
      return _getElectiveDescription(subject, faker);
    case ClassroomType.club:
      return _getClubDescription(subject, faker);
    case ClassroomType.team:
      return _getTeamDescription(subject, faker);
    case ClassroomType.projectGroup:
      return _getClubDescription(subject, faker); // Reuse club description
    case ClassroomType.grade:
      return _getCoreSubjectDescription(
        subject,
        faker,
      ); // Reuse core description
    case ClassroomType.custom:
      return _getCoreSubjectDescription(
        subject,
        faker,
      ); // Reuse core description
  }
}

String _getCoreSubjectDescription(String? subject, Faker faker) {
  final descriptions = {
    'Mathematics': [
      'Advanced mathematical concepts and problem-solving techniques.',
      'Comprehensive study of algebra, geometry, and calculus.',
      'Mathematical reasoning and analytical thinking development.',
    ],
    'Physics': [
      'Exploration of fundamental physical principles and laws.',
      'Laboratory experiments and theoretical physics concepts.',
      'Understanding motion, energy, and matter interactions.',
    ],
    'Chemistry': [
      'Study of chemical reactions and molecular structures.',
      'Laboratory work with chemical compounds and elements.',
      'Understanding atomic theory and chemical bonding.',
    ],
    'Biology': [
      'Comprehensive study of living organisms and life processes.',
      'Cell biology, genetics, and ecosystem interactions.',
      'Laboratory work with biological specimens and data analysis.',
    ],
    'English Literature': [
      'Analysis of classic and contemporary literary works.',
      'Development of critical thinking and writing skills.',
      'Exploration of themes, characters, and literary techniques.',
    ],
    'History': [
      'Study of historical events and their impact on society.',
      'Analysis of primary sources and historical evidence.',
      'Understanding cultural and political developments.',
    ],
    'Geography': [
      'Study of Earth\'s physical features and human populations.',
      'Environmental geography and climate change analysis.',
      'Mapping skills and spatial analysis techniques.',
    ],
  };

  final subjectDescriptions =
      descriptions[subject] ??
      [
        'Comprehensive study of the subject matter.',
        'Development of critical thinking and analytical skills.',
        'Practical application of theoretical concepts.',
      ];

  return subjectDescriptions[faker.randomGenerator.integer(
    subjectDescriptions.length,
  )];
}

String _getElectiveDescription(String? subject, Faker faker) {
  final descriptions = {
    'Computer Science': [
      'Programming fundamentals and software development.',
      'Algorithm design and data structure implementation.',
      'Introduction to computer systems and technology.',
    ],
    'Art': [
      'Creative expression through various artistic mediums.',
      'Development of artistic techniques and visual literacy.',
      'Exploration of art history and contemporary practices.',
    ],
    'Music': [
      'Musical theory, composition, and performance skills.',
      'Exploration of different musical genres and styles.',
      'Development of listening skills and musical appreciation.',
    ],
    'Psychology': [
      'Introduction to human behavior and mental processes.',
      'Study of cognitive development and social psychology.',
      'Research methods in psychological science.',
    ],
    'Economics': [
      'Principles of microeconomics and macroeconomics.',
      'Understanding market systems and economic policies.',
      'Analysis of economic data and trends.',
    ],
  };

  final subjectDescriptions =
      descriptions[subject] ??
      [
        'Specialized study in the chosen field.',
        'Development of subject-specific skills and knowledge.',
        'Practical application and project-based learning.',
      ];

  return subjectDescriptions[faker.randomGenerator.integer(
    subjectDescriptions.length,
  )];
}

String _getClubDescription(String? clubName, Faker faker) {
  final descriptions = [
    'A community of students sharing common interests and goals.',
    'Regular meetings and activities to develop skills and friendships.',
    'Opportunities for leadership and collaborative projects.',
    'Extracurricular activities that complement academic learning.',
  ];

  return descriptions[faker.randomGenerator.integer(descriptions.length)];
}

String _getTeamDescription(String? teamName, Faker faker) {
  final descriptions = [
    'Competitive sports team focused on skill development and teamwork.',
    'Regular training sessions and inter-school competitions.',
    'Building physical fitness, discipline, and sportsmanship.',
    'Representing the school in various tournaments and events.',
  ];

  return descriptions[faker.randomGenerator.integer(descriptions.length)];
}
