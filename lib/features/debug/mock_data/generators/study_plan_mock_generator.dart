import 'package:faker/faker.dart';
import '../../../study_plan/enums/study_plan_enums.dart';
import '../../../study_plan/models/study_plan_models.dart';
import '../services/mock_data_id_manager.dart';
import 'classroom_mock_generator.dart';

/// Mock data for study plans with comprehensive test coverage
final List<StudyPlanModel> mockStudyPlansList = _generateMockStudyPlans();

/// Generate comprehensive mock study plan data ensuring each type and status has test cases
List<StudyPlanModel> _generateMockStudyPlans() {
  final faker = Faker();
  final studyPlansList = <StudyPlanModel>[];

  // Current user ID for assignments - use actual test user ID
  const currentUserId = 'XRTanMcAUWSMq3mrRvve2Y9IMP12';

  // Get available classes for assignment
  final availableClasses = mockClassesList;

  // Define study plan templates by type
  final studyPlanTemplates = {
    StudyPlanType.shortTerm: [
      'Weekly Math Review',
      'Physics Lab Preparation',
      'Chemistry Quiz Prep',
      'Literature Essay Planning',
      'History Timeline Study',
    ],
    StudyPlanType.longTerm: [
      'Semester Physics Mastery',
      'Annual Mathematics Journey',
      'Complete Chemistry Curriculum',
      'Literature Analysis Program',
      'Historical Research Project',
    ],
    StudyPlanType.examPreparation: [
      'Final Exam Preparation',
      'Midterm Review Strategy',
      'SAT Math Preparation',
      'AP Physics Exam Prep',
      'Chemistry Board Exam',
    ],
    StudyPlanType.revision: [
      'Algebra Concepts Review',
      'Physics Laws Revision',
      'Chemistry Formulas Review',
      'Literature Themes Revision',
      'History Events Review',
    ],
    StudyPlanType.classPlan: [
      'Class Mathematics Curriculum',
      'Physics Laboratory Schedule',
      'Chemistry Practical Plan',
      'Literature Reading Program',
      'History Research Timeline',
    ],
    StudyPlanType.selfStudy: [
      'Personal Math Improvement',
      'Independent Physics Study',
      'Self-Paced Chemistry',
      'Literature Exploration',
      'History Deep Dive',
    ],
    StudyPlanType.projectBased: [
      'Science Fair Project',
      'Mathematics Research',
      'Chemistry Lab Project',
      'Literature Analysis Project',
      'History Documentary',
    ],
    StudyPlanType.general: [
      'General Academic Plan',
      'Study Skills Development',
      'Academic Excellence Plan',
      'Learning Optimization',
      'Knowledge Building Plan',
    ],
  };

  // Generate plans for each type and status combination
  for (final type in StudyPlanType.values) {
    for (final status in StudyPlanStatus.values) {
      final templates = studyPlanTemplates[type] ?? ['Generic Study Plan'];

      for (int i = 0; i < 2; i++) {
        // 2 plans per type-status combination
        final template = templates[i % templates.length];
        final classData =
            availableClasses[faker.randomGenerator.integer(
              availableClasses.length,
            )];

        // Create base dates
        final createdAt = faker.date.dateTimeBetween(
          DateTime.now().subtract(const Duration(days: 90)),
          DateTime.now().subtract(const Duration(days: 1)),
        );

        final startDate = _getStartDateForStatus(status, createdAt, faker);
        final endDate = _getEndDateForType(type, startDate, faker);

        // Generate sections for the plan
        final sections = _generateSectionsForPlan(type, faker);

        // Calculate progress based on status
        final progress = _calculateProgressForStatus(status, sections);

        final studyPlan = StudyPlanModel(
          id: MockDataIdManager.generateId('study_plan'),
          title: '$template - ${type.displayName}',
          description: faker.lorem.sentences(2).join(' '),
          type: type,
          status: status,
          creatorId: _getCreatorIdForStatus(status, currentUserId, faker),
          creatorName: faker.person.name(),
          creatorRole: _getCreatorRoleForType(type, faker),
          assignedUserIds: _getAssignedUsersForScope(
            _getScopeForType(type, faker),
            currentUserId,
            classData.studentIds,
            faker,
          ),
          classId: type == StudyPlanType.classPlan ? classData.id : null,
          subjectId: _getSubjectIdForPlan(faker),
          scope: _getScopeForType(type, faker),
          isCollaborative: faker.randomGenerator.boolean(),
          isMandatory:
              type == StudyPlanType.classPlan ||
              faker.randomGenerator.boolean(),
          canBeEditedAsTemplate: faker.randomGenerator.boolean(),
          associatedExamId: type == StudyPlanType.examPreparation
              ? MockDataIdManager.generateId('exam')
              : null,
          associatedClassroomId: type == StudyPlanType.classPlan
              ? classData.id
              : null,
          associationType: _getAssociationTypeForType(type),
          createdAt: createdAt,
          startDate: startDate,
          endDate: endDate,
          estimatedDuration: _getEstimatedDurationForType(type, faker),
          hasFlexibleScheduling:
              type != StudyPlanType.classPlan &&
              faker.randomGenerator.boolean(),
          allowsScheduleEditing: faker.randomGenerator.boolean(),
          sections: sections,
          settings: _generateStudyPlanSettings(faker),
          progress: progress,
          isTemplate: faker.randomGenerator.boolean(),
          templateId: faker.randomGenerator.boolean()
              ? MockDataIdManager.generateId('template')
              : null,
          allowsCustomScheduling: faker.randomGenerator.boolean(),
          templateSettings: faker.randomGenerator.boolean()
              ? _generateTemplateSettings(faker)
              : null,
          currentCreationStep: status == StudyPlanStatus.draft
              ? _getRandomCreationStep(faker)
              : PlanCreationStep.preview,
          isCreationComplete: status != StudyPlanStatus.draft,
          creationStepData: status == StudyPlanStatus.draft
              ? _generateCreationStepData(faker)
              : null,
          tags: _generateTagsForType(type, faker),
          priority: _getPriorityForType(type, faker),
          origin: _getOriginForPlan(faker),
        );

        studyPlansList.add(studyPlan);
      }
    }
  }

  return studyPlansList;
}

/// Generate sections for a study plan based on type
List<StudyPlanSection> _generateSectionsForPlan(
  StudyPlanType type,
  Faker faker,
) {
  final sectionCount = type == StudyPlanType.longTerm
      ? faker.randomGenerator.integer(8, min: 4)
      : faker.randomGenerator.integer(5, min: 2);

  final sections = <StudyPlanSection>[];

  for (int i = 0; i < sectionCount; i++) {
    final section = StudyPlanSection(
      id: MockDataIdManager.generateId('section'),
      title: _getSectionTitleForType(type, i, faker),
      description: faker.lorem.sentence(),
      type: _getSectionTypeForIndex(i, faker),
      order: i,
      subjectId: _getSubjectIdForPlan(faker),
      parentSectionId: null, // Simplified - no nested sections for now
      subSections: [], // Simplified - no nested sections for now
      tasks: _generateTasksForSection(faker),
      milestones: _generateMilestonesForSection(faker),
      startDate: faker.date.dateTimeBetween(
        DateTime.now(),
        DateTime.now().add(Duration(days: i * 7)),
      ),
      endDate: faker.date.dateTimeBetween(
        DateTime.now().add(Duration(days: (i + 1) * 7)),
        DateTime.now().add(Duration(days: (i + 2) * 7)),
      ),
      estimatedDuration: Duration(
        hours: faker.randomGenerator.integer(20, min: 5),
      ),
      progress: SectionProgress(
        completionPercentage: faker.randomGenerator.decimal(scale: 100),
        totalTasks: faker.randomGenerator.integer(10, min: 3),
        completedTasks: faker.randomGenerator.integer(5, min: 0),
        overdueTasks: faker.randomGenerator.integer(2, min: 0),
        lastUpdated: faker.date.dateTimeBetween(
          DateTime.now().subtract(const Duration(days: 7)),
          DateTime.now(),
        ),
      ),
      isCompleted: faker.randomGenerator.boolean(),
      completedAt: faker.randomGenerator.boolean()
          ? faker.date.dateTimeBetween(
              DateTime.now().subtract(const Duration(days: 30)),
              DateTime.now(),
            )
          : null,
    );

    sections.add(section);
  }

  return sections;
}

/// Generate tasks for a section
List<StudyPlanTask> _generateTasksForSection(Faker faker) {
  final taskCount = faker.randomGenerator.integer(6, min: 2);
  final tasks = <StudyPlanTask>[];

  for (int i = 0; i < taskCount; i++) {
    final task = StudyPlanTask(
      id: MockDataIdManager.generateId('task'),
      title: _getTaskTitle(i, faker),
      description: faker.lorem.sentence(),
      type: TaskType
          .values[faker.randomGenerator.integer(TaskType.values.length)],
      priority: TaskPriority
          .values[faker.randomGenerator.integer(TaskPriority.values.length)],
      status: TaskStatus
          .values[faker.randomGenerator.integer(TaskStatus.values.length)],
      sectionId: MockDataIdManager.generateId('section'),
      order: i,
      assignedUserIds: [MockDataIdManager.getCurrentUserId()],
      dueDate: faker.date.dateTimeBetween(
        DateTime.now(),
        DateTime.now().add(const Duration(days: 30)),
      ),
      startDate: faker.date.dateTimeBetween(
        DateTime.now().subtract(const Duration(days: 7)),
        DateTime.now().add(const Duration(days: 7)),
      ),
      dateRange: faker.randomGenerator.boolean()
          ? DateRange(
              startDate: DateTime.now(),
              endDate: DateTime.now().add(const Duration(days: 7)),
              isFlexible: faker.randomGenerator.boolean(),
              description: 'Flexible deadline range',
            )
          : null,
      estimatedDuration: Duration(
        hours: faker.randomGenerator.integer(8, min: 1),
      ),
      hasFlexibleDeadline: faker.randomGenerator.boolean(),
      resources: _generateTaskResources(faker),
      attachmentIds: _generateAttachmentIds(faker),
      homeworkId: faker.randomGenerator.boolean()
          ? MockDataIdManager.generateId('homework')
          : null,
      progressPercentage: faker.randomGenerator.decimal(scale: 100),
      submissions: _generateTaskSubmissions(faker),
      feedback: faker.randomGenerator.boolean() ? faker.lorem.sentence() : null,
    );

    tasks.add(task);
  }

  return tasks;
}

/// Generate task resources
List<TaskResource> _generateTaskResources(Faker faker) {
  final resourceCount = faker.randomGenerator.integer(4, min: 0);
  final resources = <TaskResource>[];

  for (int i = 0; i < resourceCount; i++) {
    final resource = TaskResource(
      id: MockDataIdManager.generateId('resource'),
      title: _getResourceTitle(i, faker),
      description: faker.lorem.sentence(),
      type:
          TaskResourceType.values[faker.randomGenerator.integer(
            TaskResourceType.values.length,
          )],
      url: faker.internet.httpsUrl(),
      fileId: faker.randomGenerator.boolean()
          ? MockDataIdManager.generateId('file')
          : null,
      mimeType: _getMimeTypeForResourceType(faker),
      fileSizeBytes: faker.randomGenerator.integer(10000000, min: 1000),
      thumbnailUrl: faker.randomGenerator.boolean()
          ? faker.internet.httpsUrl()
          : null,
      isRequired: faker.randomGenerator.boolean(),
      order: i,
      createdAt: faker.date.dateTimeBetween(
        DateTime.now().subtract(const Duration(days: 30)),
        DateTime.now(),
      ),
      createdBy: MockDataIdManager.getCurrentUserId(),
    );

    resources.add(resource);
  }

  return resources;
}

/// Generate task submissions
List<TaskSubmission> _generateTaskSubmissions(Faker faker) {
  final submissionCount = faker.randomGenerator.integer(2, min: 0);
  final submissions = <TaskSubmission>[];

  for (int i = 0; i < submissionCount; i++) {
    final submission = TaskSubmission(
      id: MockDataIdManager.generateId('submission'),
      taskId: MockDataIdManager.generateId('task'),
      submittedBy: MockDataIdManager.getCurrentUserId(),
      submitterName: faker.person.name(),
      submittedAt: faker.date.dateTimeBetween(
        DateTime.now().subtract(const Duration(days: 14)),
        DateTime.now(),
      ),
      status:
          TaskSubmissionStatus.values[faker.randomGenerator.integer(
            TaskSubmissionStatus.values.length,
          )],
      content: faker.lorem.sentences(3).join(' '),
      attachmentIds: _generateAttachmentIds(faker),
      grade: faker.randomGenerator.boolean()
          ? faker.randomGenerator.decimal(scale: 100, min: 60)
          : null,
      maxGrade: 100.0,
      feedback: faker.randomGenerator.boolean() ? faker.lorem.sentence() : null,
      feedbackBy: faker.randomGenerator.boolean()
          ? MockDataIdManager.generateId('teacher')
          : null,
      timeSpent: Duration(
        hours: faker.randomGenerator.integer(5, min: 1),
        minutes: faker.randomGenerator.integer(60),
      ),
    );

    submissions.add(submission);
  }

  return submissions;
}

/// Generate milestones for a section
List<StudyPlanMilestone> _generateMilestonesForSection(Faker faker) {
  final milestoneCount = faker.randomGenerator.integer(3, min: 1);
  final milestones = <StudyPlanMilestone>[];

  for (int i = 0; i < milestoneCount; i++) {
    final milestone = StudyPlanMilestone(
      id: MockDataIdManager.generateId('milestone'),
      title: _getMilestoneTitle(i, faker),
      description: faker.lorem.sentence(),
      targetDate: faker.date.dateTimeBetween(
        DateTime.now(),
        DateTime.now().add(const Duration(days: 60)),
      ),
      isCompleted: faker.randomGenerator.boolean(),
      completedAt: faker.randomGenerator.boolean()
          ? faker.date.dateTimeBetween(
              DateTime.now().subtract(const Duration(days: 30)),
              DateTime.now(),
            )
          : null,
      order: i,
    );

    milestones.add(milestone);
  }

  return milestones;
}

/// Generate study plan settings
StudyPlanSettings _generateStudyPlanSettings(Faker faker) {
  return StudyPlanSettings(
    allowCollaboration: faker.randomGenerator.boolean(),
    sendNotifications: faker.randomGenerator.boolean(),
    trackProgress: faker.randomGenerator.boolean(),
    allowComments: faker.randomGenerator.boolean(),
    customSettings: {
      'autoReminders': faker.randomGenerator.boolean(),
      'progressSharing': faker.randomGenerator.boolean(),
      'deadlineFlexibility': faker.randomGenerator.boolean(),
    },
  );
}

/// Generate template settings
TemplateSettings _generateTemplateSettings(Faker faker) {
  return TemplateSettings(
    allowCustomScheduling: faker.randomGenerator.boolean(),
    allowTaskModification: faker.randomGenerator.boolean(),
    allowSectionModification: faker.randomGenerator.boolean(),
    editableFields: ['title', 'description', 'dueDate'],
    defaultValues: {
      'priority': 'normal',
      'estimatedHours': 2,
      'isRequired': true,
    },
    instructionsForUsers: faker.lorem.sentence(),
  );
}

/// Generate attachment IDs
List<String> _generateAttachmentIds(Faker faker) {
  final attachmentCount = faker.randomGenerator.integer(3, min: 0);
  return List.generate(
    attachmentCount,
    (index) => MockDataIdManager.generateId('attachment'),
  );
}

// Helper functions for generating realistic data

/// Get start date based on status
DateTime? _getStartDateForStatus(
  StudyPlanStatus status,
  DateTime createdAt,
  Faker faker,
) {
  switch (status) {
    case StudyPlanStatus.draft:
      return null; // Draft plans don't have start dates yet
    case StudyPlanStatus.active:
      return faker.date.dateTimeBetween(
        createdAt,
        DateTime.now().add(const Duration(days: 7)),
      );
    case StudyPlanStatus.paused:
    case StudyPlanStatus.completed:
    case StudyPlanStatus.cancelled:
    case StudyPlanStatus.archived:
      return faker.date.dateTimeBetween(
        createdAt,
        createdAt.add(const Duration(days: 14)),
      );
  }
}

/// Get end date based on type and start date
DateTime? _getEndDateForType(
  StudyPlanType type,
  DateTime? startDate,
  Faker faker,
) {
  if (startDate == null) return null;

  final baseDuration = switch (type) {
    StudyPlanType.shortTerm => const Duration(days: 21), // 3 weeks
    StudyPlanType.longTerm => const Duration(days: 180), // 6 months
    StudyPlanType.examPreparation => const Duration(days: 42), // 6 weeks
    StudyPlanType.revision => const Duration(days: 14), // 2 weeks
    StudyPlanType.classPlan => const Duration(days: 120), // 4 months
    StudyPlanType.selfStudy => const Duration(days: 60), // 2 months
    StudyPlanType.projectBased => const Duration(days: 90), // 3 months
    StudyPlanType.general => const Duration(days: 30), // 1 month
  };

  return startDate.add(baseDuration);
}

/// Calculate progress based on status
StudyPlanProgress _calculateProgressForStatus(
  StudyPlanStatus status,
  List<StudyPlanSection> sections,
) {
  final totalSections = sections.length;
  final totalTasks = sections.fold<int>(
    0,
    (sum, section) => sum + section.tasks.length,
  );

  final (
    completedSections,
    completedTasks,
    overdueTasks,
    overallCompletion,
  ) = switch (status) {
    StudyPlanStatus.draft => (0, 0, 0, 0.0),
    StudyPlanStatus.active => (
      (totalSections * 0.3).round(),
      (totalTasks * 0.4).round(),
      (totalTasks * 0.1).round(),
      40.0,
    ),
    StudyPlanStatus.paused => (
      (totalSections * 0.5).round(),
      (totalTasks * 0.6).round(),
      (totalTasks * 0.15).round(),
      60.0,
    ),
    StudyPlanStatus.completed => (totalSections, totalTasks, 0, 100.0),
    StudyPlanStatus.cancelled => (
      (totalSections * 0.2).round(),
      (totalTasks * 0.25).round(),
      (totalTasks * 0.05).round(),
      25.0,
    ),
    StudyPlanStatus.archived => (totalSections, totalTasks, 0, 100.0),
  };

  return StudyPlanProgress(
    overallCompletion: overallCompletion,
    totalSections: totalSections,
    completedSections: completedSections,
    totalTasks: totalTasks,
    completedTasks: completedTasks,
    overdueTasks: overdueTasks,
    lastUpdated: DateTime.now().subtract(
      Duration(days: Faker().randomGenerator.integer(7)),
    ),
    sectionProgress: Map.fromEntries(
      sections.map(
        (section) =>
            MapEntry(section.id, Faker().randomGenerator.decimal(scale: 100)),
      ),
    ),
  );
}

/// Get creator ID based on status
String _getCreatorIdForStatus(
  StudyPlanStatus status,
  String currentUserId,
  Faker faker,
) {
  // For active and completed plans, sometimes use current user, sometimes others
  if (status == StudyPlanStatus.active || status == StudyPlanStatus.completed) {
    return faker.randomGenerator.boolean()
        ? currentUserId
        : MockDataIdManager.generateId('user');
  }
  return currentUserId;
}

/// Get creator role based on type
UserRole _getCreatorRoleForType(StudyPlanType type, Faker faker) {
  return switch (type) {
    StudyPlanType.classPlan => UserRole.teacher,
    StudyPlanType.selfStudy => UserRole.student,
    StudyPlanType.examPreparation =>
      faker.randomGenerator.boolean() ? UserRole.teacher : UserRole.student,
    _ => UserRole.values[faker.randomGenerator.integer(UserRole.values.length)],
  };
}

/// Get assignment scope based on type
AssignmentScope _getScopeForType(StudyPlanType type, Faker faker) {
  return switch (type) {
    StudyPlanType.classPlan => AssignmentScope.classroom,
    StudyPlanType.selfStudy => AssignmentScope.individual,
    StudyPlanType.projectBased =>
      faker.randomGenerator.boolean()
          ? AssignmentScope.group
          : AssignmentScope.individual,
    _ =>
      AssignmentScope.values[faker.randomGenerator.integer(
        AssignmentScope.values.length,
      )],
  };
}

/// Get assigned users based on scope
List<String> _getAssignedUsersForScope(
  AssignmentScope scope,
  String currentUserId,
  List<String> availableStudentIds,
  Faker faker,
) {
  switch (scope) {
    case AssignmentScope.individual:
      return [currentUserId];
    case AssignmentScope.group:
      final groupSize = faker.randomGenerator.integer(5, min: 2);
      final selectedIds = [currentUserId];
      final shuffledIds = List<String>.from(availableStudentIds)..shuffle();
      selectedIds.addAll(shuffledIds.take(groupSize - 1));
      return selectedIds;
    case AssignmentScope.classroom:
      return [currentUserId, ...availableStudentIds];
    case AssignmentScope.organization:
      return [currentUserId, ...availableStudentIds];
  }
}

/// Get subject ID for plan
String _getSubjectIdForPlan(Faker faker) {
  final subjects = [
    'math',
    'physics',
    'chemistry',
    'literature',
    'history',
    'biology',
  ];
  return subjects[faker.randomGenerator.integer(subjects.length)];
}

/// Get association type based on plan type
AssociationType? _getAssociationTypeForType(StudyPlanType type) {
  return switch (type) {
    StudyPlanType.classPlan => AssociationType.classroom,
    StudyPlanType.examPreparation => AssociationType.exam,
    StudyPlanType.projectBased => AssociationType.project,
    _ => null,
  };
}

/// Get estimated duration based on type
Duration _getEstimatedDurationForType(StudyPlanType type, Faker faker) {
  return switch (type) {
    StudyPlanType.shortTerm => Duration(
      hours: faker.randomGenerator.integer(40, min: 10),
    ),
    StudyPlanType.longTerm => Duration(
      hours: faker.randomGenerator.integer(200, min: 80),
    ),
    StudyPlanType.examPreparation => Duration(
      hours: faker.randomGenerator.integer(60, min: 20),
    ),
    StudyPlanType.revision => Duration(
      hours: faker.randomGenerator.integer(30, min: 10),
    ),
    StudyPlanType.classPlan => Duration(
      hours: faker.randomGenerator.integer(150, min: 60),
    ),
    StudyPlanType.selfStudy => Duration(
      hours: faker.randomGenerator.integer(80, min: 20),
    ),
    StudyPlanType.projectBased => Duration(
      hours: faker.randomGenerator.integer(100, min: 40),
    ),
    StudyPlanType.general => Duration(
      hours: faker.randomGenerator.integer(50, min: 15),
    ),
  };
}

/// Get random creation step for draft plans
PlanCreationStep _getRandomCreationStep(Faker faker) {
  return PlanCreationStep.values[faker.randomGenerator.integer(
    PlanCreationStep.values.length,
  )];
}

/// Generate creation step data for draft plans
Map<String, dynamic> _generateCreationStepData(Faker faker) {
  return {
    'currentStep': faker.randomGenerator.integer(4, min: 1),
    'completedSteps': faker.randomGenerator.integer(3, min: 0),
    'stepData': {
      'basicDetails': faker.randomGenerator.boolean(),
      'tasksAndGoals': faker.randomGenerator.boolean(),
      'scheduling': faker.randomGenerator.boolean(),
    },
  };
}

/// Generate tags based on type
List<String> _generateTagsForType(StudyPlanType type, Faker faker) {
  final baseTags = switch (type) {
    StudyPlanType.shortTerm => ['short-term', 'focused', 'intensive'],
    StudyPlanType.longTerm => ['long-term', 'comprehensive', 'semester'],
    StudyPlanType.examPreparation => ['exam', 'preparation', 'review'],
    StudyPlanType.revision => ['revision', 'review', 'practice'],
    StudyPlanType.classPlan => ['class', 'curriculum', 'structured'],
    StudyPlanType.selfStudy => ['self-study', 'independent', 'personal'],
    StudyPlanType.projectBased => ['project', 'practical', 'hands-on'],
    StudyPlanType.general => ['general', 'flexible', 'adaptive'],
  };

  final additionalTags = [
    'important',
    'priority',
    'academic',
    'learning',
    'study',
  ];
  final selectedAdditional = additionalTags
      .where((_) => faker.randomGenerator.boolean())
      .toList();

  return [...baseTags, ...selectedAdditional];
}

/// Get priority based on type
StudyPlanPriority _getPriorityForType(StudyPlanType type, Faker faker) {
  return switch (type) {
    StudyPlanType.examPreparation => StudyPlanPriority.high,
    StudyPlanType.classPlan => StudyPlanPriority.high,
    StudyPlanType.shortTerm =>
      faker.randomGenerator.boolean()
          ? StudyPlanPriority.normal
          : StudyPlanPriority.high,
    _ =>
      StudyPlanPriority.values[faker.randomGenerator.integer(
        StudyPlanPriority.values.length,
      )],
  };
}

/// Get origin for plan
PlanOrigin _getOriginForPlan(Faker faker) {
  return PlanOrigin.values[faker.randomGenerator.integer(
    PlanOrigin.values.length,
  )];
}

/// Get section title based on type and index
String _getSectionTitleForType(StudyPlanType type, int index, Faker faker) {
  final templates = switch (type) {
    StudyPlanType.shortTerm => [
      'Week $index Focus',
      'Sprint $index Goals',
      'Phase $index Tasks',
    ],
    StudyPlanType.longTerm => [
      'Module $index',
      'Unit $index',
      'Chapter $index',
    ],
    StudyPlanType.examPreparation => [
      'Topic $index Review',
      'Subject $index Prep',
      'Area $index Study',
    ],
    StudyPlanType.revision => [
      'Review Session $index',
      'Practice Set $index',
      'Revision Block $index',
    ],
    StudyPlanType.classPlan => [
      'Lesson $index',
      'Unit $index',
      'Chapter $index',
    ],
    StudyPlanType.selfStudy => [
      'Learning Goal $index',
      'Study Block $index',
      'Progress Milestone $index',
    ],
    StudyPlanType.projectBased => [
      'Project Phase $index',
      'Milestone $index',
      'Deliverable $index',
    ],
    StudyPlanType.general => ['Section $index', 'Part $index', 'Stage $index'],
  };

  return templates[faker.randomGenerator.integer(templates.length)];
}

/// Get section type based on index
SectionType _getSectionTypeForIndex(int index, Faker faker) {
  if (index == 0) return SectionType.subject;
  return SectionType.values[faker.randomGenerator.integer(
    SectionType.values.length,
  )];
}

/// Get task title
String _getTaskTitle(int index, Faker faker) {
  final taskTypes = [
    'Complete Assignment $index',
    'Read Chapter $index',
    'Practice Problems $index',
    'Review Notes $index',
    'Prepare Presentation $index',
    'Research Topic $index',
    'Write Essay $index',
    'Solve Exercises $index',
  ];

  return taskTypes[faker.randomGenerator.integer(taskTypes.length)];
}

/// Get resource title
String _getResourceTitle(int index, Faker faker) {
  final resourceTypes = [
    'Study Guide $index',
    'Reference Material $index',
    'Practice Worksheet $index',
    'Video Tutorial $index',
    'Online Resource $index',
    'Textbook Chapter $index',
    'Research Paper $index',
    'Interactive Tool $index',
  ];

  return resourceTypes[faker.randomGenerator.integer(resourceTypes.length)];
}

/// Get MIME type for resource
String _getMimeTypeForResourceType(Faker faker) {
  final mimeTypes = [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'text/plain',
    'image/jpeg',
    'image/png',
    'video/mp4',
    'audio/mpeg',
  ];

  return mimeTypes[faker.randomGenerator.integer(mimeTypes.length)];
}

/// Get milestone title
String _getMilestoneTitle(int index, Faker faker) {
  final milestoneTypes = [
    'Complete Module $index',
    'Finish Chapter $index',
    'Master Concept $index',
    'Achieve Goal $index',
    'Reach Checkpoint $index',
    'Submit Assignment $index',
    'Pass Assessment $index',
    'Complete Project Phase $index',
  ];

  return milestoneTypes[faker.randomGenerator.integer(milestoneTypes.length)];
}
