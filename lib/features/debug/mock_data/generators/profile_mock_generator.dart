import 'package:faker/faker.dart';

import '../../../../core/enums/auth_enums.dart';
import '../../../profile/models/profile_model.dart';
import '../services/mock_data_id_manager.dart';
import 'classroom_mock_generator.dart';

/// Mock data for user profiles with comprehensive test coverage
final List<ProfileModel> mockProfilesList = _generateMockProfiles();

/// Generate comprehensive mock profile data ensuring proper distribution
List<ProfileModel> _generateMockProfiles() {
  final faker = Faker();
  final profiles = <ProfileModel>[];

  // Current user ID for testing
  final currentUserId = MockDataIdManager.getCurrentUserId();

  // Get available classes for enrollment
  final availableClasses = mockClassesList;
  final classIds = availableClasses.map((c) => c.id).toList();

  // Define realistic data pools
  final schools = [
    'Greenwood High School',
    'Riverside Academy',
    'Oakmont Secondary School',
    'Westfield International School',
    'Maple Valley High School',
    'Sunnydale Preparatory School',
    'Pine Ridge Academy',
    'Cedar Creek High School',
  ];

  final grades = ['9', '10', '11', '12'];

  final subjects = [
    'Mathematics',
    'Physics',
    'Chemistry',
    'Biology',
    'English Literature',
    'History',
    'Geography',
    'Computer Science',
    'Art',
    'Music',
    'Physical Education',
    'Economics',
    'Psychology',
    'Environmental Science',
  ];

  // Create current user profile first
  profiles.add(
    _createCurrentUserProfile(
      currentUserId,
      faker,
      schools,
      grades,
      subjects,
      classIds,
    ),
  );

  // Generate 29 additional profiles (total 30)
  for (int i = 0; i < 29; i++) {
    final userId = MockDataIdManager.getStudentId('profile_user_$i');
    final userType = _getRandomUserType(faker, i);

    profiles.add(
      _createProfile(
        userId: userId,
        faker: faker,
        schools: schools,
        grades: grades,
        subjects: subjects,
        classIds: classIds,
        userType: userType,
        index: i,
      ),
    );
  }

  return profiles;
}

/// Create the current user's profile with fixed data for consistency
ProfileModel _createCurrentUserProfile(
  String userId,
  Faker faker,
  List<String> schools,
  List<String> grades,
  List<String> subjects,
  List<String> classIds,
) {
  // Find a primary class (core subject that can be primary)
  final primaryEligibleClasses = mockClassesList
      .where((c) => c.isPrimaryClass == true)
      .toList();

  final primaryClassId = primaryEligibleClasses.isNotEmpty
      ? primaryEligibleClasses.first.id
      : null;

  // Select 3-5 subjects for current user
  final shuffledSubjects = List.from(subjects)..shuffle();
  final userSubjects = shuffledSubjects.take(4).toList();

  return ProfileModel(
    id: userId,
    fullName: 'Alex Johnson', // Fixed name for current user
    email: '<EMAIL>',
    userType: UserType.student,
    primaryClassId: primaryClassId,
    profileImageUrl: null, // Will use default avatar
    grade: grades[faker.randomGenerator.integer(grades.length)],
    school: schools[0], // Assign to first school
    studentId: 'STU${userId.substring(0, 8).toUpperCase()}',
    bio:
        'Passionate student interested in technology and science. Love solving complex problems and working on creative projects.',
    subjects: userSubjects.cast<String>(),
    phoneNumber: faker.phoneNumber.us(),
    dateOfBirth: _generateRealisticBirthDate(faker),
    address: faker.address.streetAddress(),
    emergencyContact: faker.person.name(),
    emergencyContactPhone: faker.phoneNumber.us(),
    createdAt: DateTime.now().subtract(
      Duration(days: faker.randomGenerator.integer(365, min: 30)),
    ),
    updatedAt: DateTime.now().subtract(
      Duration(days: faker.randomGenerator.integer(30, min: 1)),
    ),
  );
}

/// Create a profile for other users
ProfileModel _createProfile({
  required String userId,
  required Faker faker,
  required List<String> schools,
  required List<String> grades,
  required List<String> subjects,
  required List<String> classIds,
  required UserType userType,
  required int index,
}) {
  // Determine if this user should have class enrollments (90% chance)
  final hasClassEnrollments = faker.randomGenerator.integer(100) < 90;

  String? primaryClassId;
  if (hasClassEnrollments && userType == UserType.student) {
    // Find primary eligible classes
    final primaryEligibleClasses = mockClassesList
        .where((c) => c.isPrimaryClass == true)
        .toList();

    if (primaryEligibleClasses.isNotEmpty) {
      primaryClassId =
          primaryEligibleClasses[faker.randomGenerator.integer(
                primaryEligibleClasses.length,
              )]
              .id;
    }
  }

  // Generate realistic subject interests (2-6 subjects)
  final shuffledSubjects = List.from(subjects)..shuffle();
  final subjectCount = faker.randomGenerator.integer(5, min: 2);
  final userSubjects = shuffledSubjects.take(subjectCount).toList();

  // Generate profile image URL (30% chance)
  final profileImageUrl = faker.randomGenerator.integer(100) < 30
      ? 'https://example.com/avatars/user_${index + 1}.jpg'
      : null;

  // Generate bio (70% chance)
  final bio = faker.randomGenerator.integer(100) < 70
      ? _generateRealisticBio(faker, userType, userSubjects.cast<String>())
      : null;

  return ProfileModel(
    id: userId,
    fullName: faker.person.name(),
    email: _generateRealisticEmail(faker, userType),
    userType: userType,
    primaryClassId: primaryClassId,
    profileImageUrl: profileImageUrl,
    grade: userType == UserType.student
        ? grades[faker.randomGenerator.integer(grades.length)]
        : null,
    school: schools[faker.randomGenerator.integer(schools.length)],
    studentId: userType == UserType.student
        ? 'STU${userId.substring(0, 8).toUpperCase()}'
        : null,
    bio: bio,
    subjects: userSubjects.cast<String>(),
    phoneNumber: faker.phoneNumber.us(),
    dateOfBirth: _generateRealisticBirthDate(faker),
    address: faker.address.streetAddress(),
    emergencyContact: faker.person.name(),
    emergencyContactPhone: faker.phoneNumber.us(),
    createdAt: DateTime.now().subtract(
      Duration(days: faker.randomGenerator.integer(365, min: 30)),
    ),
    updatedAt: DateTime.now().subtract(
      Duration(days: faker.randomGenerator.integer(30, min: 1)),
    ),
  );
}

/// Get random user type with realistic distribution
UserType _getRandomUserType(Faker faker, int index) {
  // 85% students, 10% teachers, 5% admins
  final random = faker.randomGenerator.integer(100);

  if (random < 85) {
    return UserType.student;
  } else if (random < 95) {
    return UserType.teacher;
  } else {
    return UserType.admin;
  }
}

/// Generate realistic email based on user type
String _generateRealisticEmail(Faker faker, UserType userType) {
  final firstName = faker.person.firstName().toLowerCase();
  final lastName = faker.person.lastName().toLowerCase();

  switch (userType) {
    case UserType.student:
      return '$firstName.$<EMAIL>';
    case UserType.teacher:
      return '$firstName.$<EMAIL>';
    case UserType.admin:
      return '$firstName.$<EMAIL>';
    case UserType.parent:
      return '$firstName.$<EMAIL>';
    case UserType.other:
      return '$firstName.$<EMAIL>';
  }
}

/// Generate realistic bio based on user type and interests
String _generateRealisticBio(
  Faker faker,
  UserType userType,
  List<String> subjects,
) {
  final templates = {
    UserType.student: [
      'Passionate student with interests in {subjects}. Love learning and exploring new concepts.',
      'Dedicated learner focused on {subjects}. Enjoy collaborative projects and problem-solving.',
      'Enthusiastic about {subjects} and always eager to take on new challenges.',
      'Student with a keen interest in {subjects}. Aspiring to make a positive impact.',
    ],
    UserType.teacher: [
      'Experienced educator specializing in {subjects}. Committed to student success.',
      'Passionate teacher with expertise in {subjects}. Love inspiring young minds.',
      'Dedicated educator focused on {subjects}. Believe in hands-on learning.',
      'Teaching professional with deep knowledge in {subjects}. Student-centered approach.',
    ],
    UserType.admin: [
      'Educational administrator with background in {subjects}. Focused on institutional excellence.',
      'Administrative professional supporting academic programs in {subjects}.',
      'Experienced in educational management with interests in {subjects}.',
      'Administrative leader committed to educational quality and {subjects} programs.',
    ],
  };

  final userTemplates = templates[userType]!;
  final template =
      userTemplates[faker.randomGenerator.integer(userTemplates.length)];

  // Replace {subjects} with actual subjects
  final subjectText = subjects.take(2).join(' and ');
  return template.replaceAll('{subjects}', subjectText);
}

/// Generate realistic birth date based on user type
DateTime _generateRealisticBirthDate(Faker faker) {
  final now = DateTime.now();

  // Students: 14-18 years old
  // Teachers: 25-55 years old
  // Admins: 30-60 years old
  final minAge = 15;
  final maxAge = 18;

  final age = faker.randomGenerator.integer(maxAge - minAge + 1) + minAge;
  return DateTime(
    now.year - age,
    faker.randomGenerator.integer(12) + 1,
    faker.randomGenerator.integer(28) + 1,
  );
}
