import 'package:faker/faker.dart';
import 'package:scholara_student/core/enums/auth_enums.dart';
import '../../../../features/digital_library/enums/file_access_type.dart';
import '../../../../features/digital_library/enums/file_usage_type.dart';
import '../../../../features/digital_library/enums/folder_type.dart';
import '../../../../features/digital_library/enums/library_file_type.dart';
import '../../../../features/digital_library/enums/offline_access.dart';
import '../../../../features/digital_library/enums/upload_status.dart';
import '../../../../features/digital_library/models/library_file_model.dart';
import '../../../../features/digital_library/models/library_folder_model.dart';
import '../../../../features/digital_library/models/upload_session_model.dart';
import '../../../../features/profile/models/profile_model.dart';
import '../services/mock_data_id_manager.dart';
import 'profile_mock_generator.dart';

/// Mock data for digital library with comprehensive test coverage
final List<LibraryFolderModel> mockLibraryFolders = _generateMockFolders();
final List<LibraryFileModel> mockLibraryFiles = _generateMockFiles();
final List<UploadSessionModel> mockUploadSessions =
    _generateMockUploadSessions();

/// Generate realistic mock folder data
List<LibraryFolderModel> _generateMockFolders() {
  final faker = Faker();
  final folders = <LibraryFolderModel>[];

  // Ensure consistent IDs
  MockDataIdManager.preGenerateIds();
  MockDataIdManager.setCurrentUserId('XRTanMcAUWSMq3mrRvve2Y9IMP12');

  // Get current user for folder creation
  final currentUser = mockProfilesList.firstWhere(
    (user) => user.id == 'XRTanMcAUWSMq3mrRvve2Y9IMP12',
    orElse: () => mockProfilesList.first,
  );

  // Root level folders for current user with different usage types
  final rootFolders = [
    _createFolder(
      'folder_math_001',
      'Mathematics',
      'All math-related study materials',
      FolderType.subject,
      FileAccessType.publicDownloadable,
      usageType: FileUsageType.classResource,
      creatorUser: currentUser,
    ),
    _createFolder(
      'folder_physics_001',
      'Physics',
      'Physics notes, experiments, and resources',
      FolderType.subject,
      FileAccessType.publicDownloadable,
      usageType: FileUsageType.classResource,
      creatorUser: currentUser,
    ),
    _createFolder(
      'folder_personal_001',
      'Personal Notes',
      'My private study notes',
      FolderType.personal,
      FileAccessType.private,
      usageType: FileUsageType.personal,
      creatorUser: currentUser,
    ),
    _createFolder(
      'folder_shared_001',
      'Shared Documents',
      'Documents shared with me by others',
      FolderType.shared,
      FileAccessType.restricted,
      usageType: FileUsageType.shared,
      creatorUser: currentUser,
    ),
    _createFolder(
      'folder_projects_001',
      'Group Projects',
      'Collaborative project materials',
      FolderType.project,
      FileAccessType.restricted,
      usageType: FileUsageType.classResource,
      creatorUser: currentUser,
    ),
    _createFolder(
      'folder_resources_001',
      'Reference Materials',
      'Shared reference documents and resources',
      FolderType.resource,
      FileAccessType.publicViewOnly,
      usageType: FileUsageType.classResource,
      creatorUser: currentUser,
    ),
    _createFolder(
      'folder_media_001',
      'Media Files',
      'Images, videos, and audio files',
      FolderType.media,
      FileAccessType.publicDownloadable,
      usageType: FileUsageType.personal,
      creatorUser: currentUser,
    ),
  ];

  folders.addAll(rootFolders);

  // Subfolders for Mathematics (created by current user)
  final mathSubfolders = [
    _createSubfolder(
      'folder_math_algebra_001',
      'Algebra',
      'Algebraic equations and formulas',
      'folder_math_001',
      FolderType.subject,
      FileAccessType.publicDownloadable,
      creatorUser: currentUser,
    ),
    _createSubfolder(
      'folder_math_calculus_001',
      'Calculus',
      'Differential and integral calculus',
      'folder_math_001',
      FolderType.subject,
      FileAccessType.publicDownloadable,
      creatorUser: currentUser,
    ),
    _createSubfolder(
      'folder_math_geometry_001',
      'Geometry',
      'Geometric shapes and proofs',
      'folder_math_001',
      FolderType.subject,
      FileAccessType.publicDownloadable,
      creatorUser: currentUser,
    ),
  ];

  folders.addAll(mathSubfolders);

  // Subfolders for Physics
  final physicsSubfolders = [
    _createSubfolder(
      'folder_physics_mechanics_001',
      'Mechanics',
      'Classical mechanics and motion',
      'folder_physics_001',
      FolderType.subject,
      FileAccessType.publicDownloadable,
    ),
    _createSubfolder(
      'folder_physics_thermodynamics_001',
      'Thermodynamics',
      'Heat, temperature, and energy',
      'folder_physics_001',
      FolderType.subject,
      FileAccessType.publicDownloadable,
    ),
    _createSubfolder(
      'folder_physics_experiments_001',
      'Lab Experiments',
      'Physics laboratory experiments and reports',
      'folder_physics_001',
      FolderType.subject,
      FileAccessType.restricted,
    ),
  ];

  folders.addAll(physicsSubfolders);

  // Subfolders for Projects
  final projectSubfolders = [
    _createSubfolder(
      'folder_project_science_001',
      'Science Fair Project',
      'Materials for the annual science fair',
      'folder_projects_001',
      FolderType.project,
      FileAccessType.restricted,
    ),
    _createSubfolder(
      'folder_project_history_001',
      'History Research',
      'Collaborative history research project',
      'folder_projects_001',
      FolderType.project,
      FileAccessType.restricted,
    ),
  ];

  folders.addAll(projectSubfolders);

  // Update folder counts
  for (int i = 0; i < folders.length; i++) {
    final folder = folders[i];
    final subfolderCount = folders
        .where((f) => f.parentFolderId == folder.id)
        .length;

    folders[i] = folder.copyWith(
      subfolderCount: subfolderCount,
      fileCount: faker.randomGenerator.integer(
        15,
        min: 0,
      ), // Will be updated when files are generated
      totalSizeBytes: faker.randomGenerator.integer(
        1000000000,
        min: 1000000,
      ), // 1MB to 1GB
      isFavorite: faker.randomGenerator.integer(100) < 20, // 20% chance
      isPinned: faker.randomGenerator.integer(100) < 10, // 10% chance
    );
  }

  return folders;
}

/// Generate realistic mock file data
List<LibraryFileModel> _generateMockFiles() {
  final faker = Faker();
  final files = <LibraryFileModel>[];

  // Get available folders
  final folders = mockLibraryFolders;

  // File types with realistic extensions
  final fileTypeData = [
    {
      'type': LibraryFileType.pdf,
      'extensions': ['pdf'],
      'sizes': [500000, 5000000],
    }, // 500KB - 5MB
    {
      'type': LibraryFileType.document,
      'extensions': ['docx', 'doc', 'txt'],
      'sizes': [50000, 1000000],
    }, // 50KB - 1MB
    {
      'type': LibraryFileType.presentation,
      'extensions': ['pptx', 'ppt'],
      'sizes': [1000000, 10000000],
    }, // 1MB - 10MB
    {
      'type': LibraryFileType.spreadsheet,
      'extensions': ['xlsx', 'csv'],
      'sizes': [100000, 2000000],
    }, // 100KB - 2MB
    {
      'type': LibraryFileType.image,
      'extensions': ['jpg', 'png', 'gif'],
      'sizes': [100000, 5000000],
    }, // 100KB - 5MB
    {
      'type': LibraryFileType.video,
      'extensions': ['mp4', 'mov'],
      'sizes': [10000000, 100000000],
    }, // 10MB - 100MB
    {
      'type': LibraryFileType.audio,
      'extensions': ['mp3', 'wav'],
      'sizes': [3000000, 15000000],
    }, // 3MB - 15MB
    {
      'type': LibraryFileType.note,
      'extensions': ['md', 'txt'],
      'sizes': [5000, 100000],
    }, // 5KB - 100KB
    {
      'type': LibraryFileType.code,
      'extensions': ['dart', 'js', 'py'],
      'sizes': [1000, 50000],
    }, // 1KB - 50KB
    {
      'type': LibraryFileType.archive,
      'extensions': ['zip', 'rar'],
      'sizes': [1000000, 50000000],
    }, // 1MB - 50MB
  ];

  // Subject-specific file names
  final subjectFiles = {
    'Mathematics': [
      'Quadratic Equations Formula Sheet',
      'Calculus Integration Rules',
      'Geometry Theorem Proofs',
      'Linear Algebra Notes',
      'Statistics Cheat Sheet',
      'Trigonometry Practice Problems',
    ],
    'Physics': [
      'Newton\'s Laws Explanation',
      'Thermodynamics Lab Report',
      'Quantum Mechanics Introduction',
      'Electromagnetic Waves Theory',
      'Optics Experiment Results',
      'Mechanics Problem Solutions',
    ],
    'Personal Notes': [
      'Study Schedule',
      'Exam Preparation Checklist',
      'Important Formulas',
      'Quick Reference Guide',
      'Practice Test Answers',
    ],
    'Group Projects': [
      'Project Proposal Draft',
      'Research Findings',
      'Presentation Slides',
      'Team Meeting Notes',
      'Final Report',
    ],
    'Reference Materials': [
      'Academic Writing Guide',
      'Citation Format Examples',
      'Research Methodology',
      'Statistical Analysis Guide',
      'Laboratory Safety Manual',
    ],
    'Media Files': [
      'Experiment Video Recording',
      'Lecture Audio Notes',
      'Diagram Screenshots',
      'Presentation Images',
      'Educational Animations',
    ],
  };

  // Generate files for each folder
  for (final folder in folders) {
    final folderFileNames =
        subjectFiles[folder.name] ??
        ['Sample Document', 'Study Notes', 'Reference Material'];
    final fileCount = faker.randomGenerator.integer(8, min: 2);

    for (int i = 0; i < fileCount; i++) {
      final typeData = faker.randomGenerator.element(fileTypeData);
      final fileType = typeData['type'] as LibraryFileType;
      final extensions = typeData['extensions'] as List<String>;
      final sizeRange = typeData['sizes'] as List<int>;

      final extension = faker.randomGenerator.element(extensions);
      final fileName =
          '${faker.randomGenerator.element(folderFileNames)}_${i + 1}.$extension';
      final fileSize = faker.randomGenerator.integer(
        sizeRange[1],
        min: sizeRange[0],
      );

      // Determine access type based on folder type
      FileAccessType accessType;
      if (folder.accessType == FileAccessType.private) {
        accessType = FileAccessType.private;
      } else if (folder.folderType == FolderType.project) {
        accessType = FileAccessType.restricted;
      } else {
        accessType = faker.randomGenerator.element([
          FileAccessType.publicDownloadable,
          FileAccessType.publicViewOnly,
          FileAccessType.restricted,
        ]);
      }

      // Generate realistic upload status distribution
      final uploadStatus = faker.randomGenerator.element([
        UploadStatus.completed, // 85%
        UploadStatus.completed,
        UploadStatus.completed,
        UploadStatus.completed,
        UploadStatus.completed,
        UploadStatus.completed,
        UploadStatus.completed,
        UploadStatus.completed,
        UploadStatus.uploading, // 5%
        UploadStatus.failed, // 5%
        UploadStatus.processing, // 5%
      ]);

      // Generate offline access status
      final offlineAccess = faker.randomGenerator.element([
        OfflineAccess.none, // 60%
        OfflineAccess.none,
        OfflineAccess.none,
        OfflineAccess.none,
        OfflineAccess.none,
        OfflineAccess.none,
        OfflineAccess.available, // 25%
        OfflineAccess.available,
        OfflineAccess.available,
        OfflineAccess.pending, // 10%
        OfflineAccess.downloading, // 3%
        OfflineAccess.failed, // 2%
      ]);

      final fileId = MockDataIdManager.getLibraryFileId('file_${folder.id}_$i');
      // Use current user as uploader for their own folders, random for others
      final uploader = folder.creatorId == 'XRTanMcAUWSMq3mrRvve2Y9IMP12'
          ? mockProfilesList.firstWhere(
              (user) => user.id == 'XRTanMcAUWSMq3mrRvve2Y9IMP12',
              orElse: () => faker.randomGenerator.element(mockProfilesList),
            )
          : faker.randomGenerator.element(mockProfilesList);

      final file = LibraryFileModel(
        id: fileId,
        fileName: fileName,
        title: faker.randomGenerator.integer(100) < 70
            ? faker.lorem.sentence().replaceAll('.', '')
            : null,
        description: faker.randomGenerator.integer(100) < 40
            ? faker.lorem.sentences(2).join(' ')
            : null,
        fileType: fileType,
        fileSizeBytes: fileSize,
        fileExtension: extension,
        mimeType: _getMimeType(extension),
        accessType: accessType,
        usageType: _generateUsageType(folder.folderType, accessType),
        offlineAccess: offlineAccess,
        uploadStatus: uploadStatus,
        uploadProgress: uploadStatus == UploadStatus.completed
            ? 1.0
            : uploadStatus == UploadStatus.uploading
            ? faker.randomGenerator.decimal()
            : 0.0,
        tags: _generateTags(fileType, folder.folderType),
        folderId: folder.id,
        uploaderId: uploader.id,
        uploaderName: uploader.fullName,
        sharedUserIds: accessType == FileAccessType.restricted
            ? mockProfilesList
                  .take(faker.randomGenerator.integer(4, min: 1))
                  .map((u) => u.id)
                  .toList()
            : [],
        cloudUrl: uploadStatus == UploadStatus.completed
            ? 'https://storage.scholara.com/files/$fileId'
            : null,
        localPath: offlineAccess.canAccessOffline
            ? '/storage/emulated/0/Scholara/files/$fileName'
            : null,
        thumbnailUrl:
            fileType == LibraryFileType.image ||
                fileType == LibraryFileType.video
            ? 'https://storage.scholara.com/thumbnails/$fileId'
            : null,
        createdAt: faker.date.dateTimeBetween(
          DateTime.now().subtract(const Duration(days: 180)),
          DateTime.now().subtract(const Duration(days: 1)),
        ),
        modifiedAt: faker.randomGenerator.integer(100) < 30
            ? faker.date.dateTimeBetween(
                DateTime.now().subtract(const Duration(days: 30)),
                DateTime.now(),
              )
            : null,
        lastAccessedAt: faker.randomGenerator.integer(100) < 60
            ? faker.date.dateTimeBetween(
                DateTime.now().subtract(const Duration(days: 7)),
                DateTime.now(),
              )
            : null,
        viewCount: faker.randomGenerator.integer(50, min: 0),
        downloadCount: faker.randomGenerator.integer(20, min: 0),
        isFavorite: faker.randomGenerator.integer(100) < 15,
        errorMessage: uploadStatus == UploadStatus.failed
            ? faker.randomGenerator.element([
                'Network connection lost during upload',
                'File size exceeds maximum limit',
                'Invalid file format',
                'Server error occurred',
              ])
            : null,
        metadata: {
          'originalPath': '/Users/<USER>/Documents/$fileName',
          'checksum': faker.randomGenerator.string(32, min: 32),
          if (fileType == LibraryFileType.image) ...{
            'width': faker.randomGenerator.integer(4000, min: 800),
            'height': faker.randomGenerator.integer(3000, min: 600),
          },
          if (fileType == LibraryFileType.video) ...{
            'duration': faker.randomGenerator.integer(3600, min: 30), // seconds
            'resolution': faker.randomGenerator.element([
              '720p',
              '1080p',
              '4K',
            ]),
          },
        },
      );

      files.add(file);
    }
  }

  return files;
}

/// Generate realistic mock upload sessions
List<UploadSessionModel> _generateMockUploadSessions() {
  final faker = Faker();
  final sessions = <UploadSessionModel>[];

  // Generate 5-10 upload sessions
  final sessionCount = faker.randomGenerator.integer(10, min: 5);

  for (int i = 0; i < sessionCount; i++) {
    final sessionId = MockDataIdManager.getUploadSessionId('session_$i');
    final uploader = faker.randomGenerator.element(mockProfilesList);
    final targetFolder = faker.randomGenerator.integer(100) < 70
        ? faker.randomGenerator.element(mockLibraryFolders)
        : null;

    // Generate 1-5 files per session
    final fileCount = faker.randomGenerator.integer(5, min: 1);
    final files = <UploadFileItem>[];

    for (int j = 0; j < fileCount; j++) {
      final fileType = faker.randomGenerator.element(LibraryFileType.values);
      final extension = faker.randomGenerator.element(
        fileType.extensions.isNotEmpty ? fileType.extensions : ['txt'],
      );

      final uploadFileItem = UploadFileItem(
        id: MockDataIdManager.getLibraryFileId('upload_file_${i}_$j'),
        fileName: '${faker.lorem.word()}_$j.$extension',
        title: faker.randomGenerator.integer(100) < 50
            ? faker.lorem.sentence().replaceAll('.', '')
            : null,
        description: faker.randomGenerator.integer(100) < 30
            ? faker.lorem.sentence()
            : null,
        fileType: fileType,
        fileSizeBytes: faker.randomGenerator.integer(10000000, min: 1000),
        fileExtension: extension,
        accessType: faker.randomGenerator.element(FileAccessType.values),
        tags: _generateTags(
          fileType,
          targetFolder?.folderType ?? FolderType.general,
        ),
        localPath: '/storage/emulated/0/temp/upload_${i}_$j.$extension',
        status: faker.randomGenerator.element([
          UploadStatus.completed,
          UploadStatus.uploading,
          UploadStatus.failed,
          UploadStatus.pending,
        ]),
        progress: faker.randomGenerator.decimal(),
        errorMessage: faker.randomGenerator.integer(100) < 20
            ? 'Upload failed: Network error'
            : null,
        cloudUrl: faker.randomGenerator.integer(100) < 60
            ? 'https://storage.scholara.com/files/upload_${i}_$j'
            : null,
      );

      files.add(uploadFileItem);
    }

    // Determine session status based on file statuses
    final completedFiles = files
        .where((f) => f.status == UploadStatus.completed)
        .length;
    final failedFiles = files
        .where((f) => f.status == UploadStatus.failed)
        .length;
    final uploadingFiles = files
        .where((f) => f.status == UploadStatus.uploading)
        .length;

    UploadStatus sessionStatus;
    if (completedFiles == files.length) {
      sessionStatus = UploadStatus.completed;
    } else if (failedFiles > 0 && uploadingFiles == 0) {
      sessionStatus = UploadStatus.failed;
    } else if (uploadingFiles > 0) {
      sessionStatus = UploadStatus.uploading;
    } else {
      sessionStatus = UploadStatus.pending;
    }

    final session = UploadSessionModel(
      id: sessionId,
      userId: uploader.id,
      userName: uploader.fullName,
      folderId: targetFolder?.id,
      files: files,
      status: sessionStatus,
      progress: completedFiles / files.length,
      createdAt: faker.date.dateTimeBetween(
        DateTime.now().subtract(const Duration(days: 7)),
        DateTime.now(),
      ),
      startedAt: sessionStatus != UploadStatus.pending
          ? faker.date.dateTimeBetween(
              DateTime.now().subtract(const Duration(hours: 24)),
              DateTime.now(),
            )
          : null,
      completedAt: sessionStatus == UploadStatus.completed
          ? faker.date.dateTimeBetween(
              DateTime.now().subtract(const Duration(hours: 12)),
              DateTime.now(),
            )
          : null,
      errorMessage: sessionStatus == UploadStatus.failed
          ? 'Multiple files failed to upload'
          : null,
      retryCount: faker.randomGenerator.integer(2, min: 0),
    );

    sessions.add(session);
  }

  return sessions;
}

/// Helper function to create a folder
LibraryFolderModel _createFolder(
  String id,
  String name,
  String description,
  FolderType folderType,
  FileAccessType accessType, {
  FileUsageType? usageType,
  ProfileModel? creatorUser,
}) {
  final faker = Faker();
  late final ProfileModel creator;

  if (creatorUser != null) {
    creator = creatorUser;
  } else if (mockProfilesList.isNotEmpty) {
    creator = faker.randomGenerator.element(mockProfilesList);
  } else {
    creator = ProfileModel(
      id: 'default_user',
      fullName: 'Default User',
      email: '<EMAIL>',
      userType: UserType.student,
      createdAt: DateTime.now(),
    );
  }

  return LibraryFolderModel(
    id: MockDataIdManager.getLibraryFolderId(id),
    name: name,
    description: description,
    folderType: folderType,
    accessType: accessType,
    usageType: usageType ?? _generateUsageType(folderType, accessType),
    creatorId: creator.id,
    creatorName: creator.fullName,
    sharedUserIds: accessType == FileAccessType.restricted
        ? mockProfilesList
              .take(faker.randomGenerator.integer(3, min: 1))
              .map((u) => u.id)
              .toList()
        : [],
    tags: _generateFolderTags(folderType),
    createdAt: faker.date.dateTimeBetween(
      DateTime.now().subtract(const Duration(days: 365)),
      DateTime.now().subtract(const Duration(days: 30)),
    ),
    colorHex: folderType.color.toString().substring(
      10,
      16,
    ), // Extract hex from Color(0xff123456) format
  );
}

/// Helper function to create a subfolder
LibraryFolderModel _createSubfolder(
  String id,
  String name,
  String description,
  String parentId,
  FolderType folderType,
  FileAccessType accessType, {
  ProfileModel? creatorUser,
}) {
  final folder = _createFolder(
    id,
    name,
    description,
    folderType,
    accessType,
    creatorUser: creatorUser,
  );
  return folder.copyWith(
    parentFolderId: MockDataIdManager.getLibraryFolderId(parentId),
  );
}

/// Generate realistic tags based on file type and folder type
List<String> _generateTags(LibraryFileType fileType, FolderType folderType) {
  final faker = Faker();
  final tags = <String>[];

  // Add file type specific tags
  switch (fileType) {
    case LibraryFileType.pdf:
      tags.addAll(['document', 'reference', 'study-material']);
      break;
    case LibraryFileType.image:
      tags.addAll(['visual', 'diagram', 'illustration']);
      break;
    case LibraryFileType.video:
      tags.addAll(['lecture', 'tutorial', 'demonstration']);
      break;
    case LibraryFileType.audio:
      tags.addAll(['recording', 'lecture', 'notes']);
      break;
    case LibraryFileType.presentation:
      tags.addAll(['slides', 'presentation', 'lecture']);
      break;
    case LibraryFileType.note:
      tags.addAll(['notes', 'summary', 'quick-reference']);
      break;
    default:
      tags.add('document');
  }

  // Add folder type specific tags
  switch (folderType) {
    case FolderType.subject:
      tags.addAll(['academic', 'curriculum']);
      break;
    case FolderType.project:
      tags.addAll(['collaborative', 'group-work']);
      break;
    case FolderType.personal:
      tags.addAll(['private', 'personal']);
      break;
    case FolderType.resource:
      tags.addAll(['reference', 'resource']);
      break;
    default:
      break;
  }

  // Add some random academic tags
  final academicTags = [
    'important',
    'exam-prep',
    'homework',
    'research',
    'theory',
    'practice',
  ];
  tags.addAll(
    faker.randomGenerator.amount(
      (_) => faker.randomGenerator.element(academicTags),
      2,
    ),
  );

  return tags.toSet().toList(); // Remove duplicates
}

/// Generate folder-specific tags
List<String> _generateFolderTags(FolderType folderType) {
  switch (folderType) {
    case FolderType.subject:
      return ['academic', 'curriculum', 'study'];
    case FolderType.project:
      return ['collaborative', 'group-work', 'project'];
    case FolderType.personal:
      return ['private', 'personal', 'notes'];
    case FolderType.resource:
      return ['reference', 'resource', 'shared'];
    case FolderType.media:
      return ['media', 'visual', 'multimedia'];
    case FolderType.archive:
      return ['archive', 'old', 'completed'];
    default:
      return ['general'];
  }
}

/// Get MIME type for file extension
String? _getMimeType(String extension) {
  switch (extension.toLowerCase()) {
    case 'pdf':
      return 'application/pdf';
    case 'doc':
    case 'docx':
      return 'application/msword';
    case 'txt':
      return 'text/plain';
    case 'jpg':
    case 'jpeg':
      return 'image/jpeg';
    case 'png':
      return 'image/png';
    case 'gif':
      return 'image/gif';
    case 'mp4':
      return 'video/mp4';
    case 'mp3':
      return 'audio/mpeg';
    case 'zip':
      return 'application/zip';
    default:
      return null;
  }
}

/// Generate realistic usage type based on folder type and access type
FileUsageType _generateUsageType(
  FolderType folderType,
  FileAccessType accessType,
) {
  final faker = Faker();

  // Base usage type on folder type with some randomization
  switch (folderType) {
    case FolderType.personal:
      return FileUsageType.personal;

    case FolderType.subject:
    case FolderType.resource:
      // Class resources are more likely for subject/resource folders
      return faker.randomGenerator.integer(100) < 70
          ? FileUsageType.classResource
          : FileUsageType.library;

    case FolderType.project:
      // Project folders are more likely to be shared
      return faker.randomGenerator.integer(100) < 60
          ? FileUsageType.shared
          : FileUsageType.library;

    case FolderType.smart:
    case FolderType.general:
    default:
      // For general folders, distribute across different usage types
      final random = faker.randomGenerator.integer(100);
      if (random < 30) return FileUsageType.personal;
      if (random < 50) return FileUsageType.shared;
      if (random < 70) return FileUsageType.classResource;
      if (random < 80) return FileUsageType.homeworkSubmission;
      if (random < 85) return FileUsageType.noticeAttachment;
      if (random < 90) return FileUsageType.classroomActivity;
      if (random < 95) return FileUsageType.profile;
      return FileUsageType.library;
  }
}
