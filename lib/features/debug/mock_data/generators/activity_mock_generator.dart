import 'package:faker/faker.dart';
import '../../../classroom/models/activity_model.dart';
import '../../../classroom/enums/activity_type.dart';
import '../services/mock_data_id_manager.dart';
import 'classroom_mock_generator.dart';

/// Mock data for classroom activities with realistic data generation
final List<ActivityModel> mockActivitiesList = _generateMockActivities();

/// Generate realistic mock activity data for all classrooms
List<ActivityModel> _generateMockActivities() {
  final faker = Faker();
  final activities = <ActivityModel>[];

  // Get all mock classrooms
  final classrooms = mockClassesList;

  // Generate activities for each classroom
  for (final classroom in classrooms) {
    // Generate 3-8 activities per classroom
    final activityCount = faker.randomGenerator.integer(8, min: 3);

    for (int i = 0; i < activityCount; i++) {
      // Select activity type with weighted distribution for more realistic variety
      final activityType = _selectWeightedActivityType(faker);

      // Generate activity based on type
      final activity = _generateActivityByType(
        faker,
        classroom.id,
        activityType,
        classroom,
      );

      if (activity != null) {
        activities.add(activity);
      }
    }
  }

  // Sort activities by creation date (newest first)
  activities.sort((a, b) => b.createdAt.compareTo(a.createdAt));

  return activities;
}

/// Select activity type with weighted distribution for more realistic variety
ActivityType _selectWeightedActivityType(Faker faker) {
  // Define weights for different activity types (higher = more common)
  final weightedTypes = [
    // Most common (30% total)
    ActivityType.announcement,
    ActivityType.announcement,
    ActivityType.announcement,

    // Very common (25% total)
    ActivityType.homework, ActivityType.homework, ActivityType.homework,

    // Common (20% total)
    ActivityType.notice, ActivityType.notice,

    // Moderately common (15% total)
    ActivityType.reminder, ActivityType.quiz,

    // Less common (10% total)
    ActivityType.discussion, ActivityType.digitalLibrary,

    // Rare but present
    ActivityType.attendance, ActivityType.notification,
  ];

  return weightedTypes[faker.randomGenerator.integer(weightedTypes.length)];
}

/// Generate activity based on type
ActivityModel? _generateActivityByType(
  Faker faker,
  String classroomId,
  ActivityType type,
  dynamic classroom,
) {
  final createdAt = DateTime.now().subtract(
    Duration(
      days: faker.randomGenerator.integer(30, min: 0),
      hours: faker.randomGenerator.integer(24, min: 0),
      minutes: faker.randomGenerator.integer(60, min: 0),
    ),
  );

  switch (type) {
    case ActivityType.announcement:
      return _generateAnnouncement(faker, classroomId, createdAt, classroom);
    case ActivityType.homework:
      return _generateHomeworkActivity(
        faker,
        classroomId,
        createdAt,
        classroom,
      );
    case ActivityType.digitalLibrary:
      return _generateDigitalLibraryActivity(
        faker,
        classroomId,
        createdAt,
        classroom,
      );
    case ActivityType.notice:
      return _generateNoticeActivity(faker, classroomId, createdAt, classroom);
    case ActivityType.discussion:
      return _generateDiscussion(faker, classroomId, createdAt, classroom);
    case ActivityType.quiz:
      return _generateQuizActivity(faker, classroomId, createdAt, classroom);
    case ActivityType.reminder:
      return _generateReminderActivity(
        faker,
        classroomId,
        createdAt,
        classroom,
      );
    case ActivityType.attendance:
      return _generateAttendanceActivity(
        faker,
        classroomId,
        createdAt,
        classroom,
      );
    case ActivityType.notification:
      return _generateNotificationActivity(
        faker,
        classroomId,
        createdAt,
        classroom,
      );
  }
}

/// Generate announcement activity
ActivityModel _generateAnnouncement(
  Faker faker,
  String classroomId,
  DateTime createdAt,
  dynamic classroom,
) {
  final announcements = [
    'Welcome to the new semester! Looking forward to a great year ahead.',
    'Reminder: Mid-term exams start next week. Please review the schedule.',
    'Class will be held in the computer lab tomorrow.',
    'Project presentations will begin on Monday. Please be prepared.',
    'Office hours have been extended this week for extra help.',
    'Field trip permission slips are due by Friday.',
    'Guest speaker will be joining us next Thursday.',
    'Lab safety training is mandatory for all students.',
    'Study group sessions available in the library.',
    'Important: Class timing has been changed for next week.',
  ];

  final title =
      announcements[faker.randomGenerator.integer(announcements.length)];

  return ActivityModel(
    id: MockDataIdManager.getActivityId(
      'announcement_${classroomId}_${createdAt.millisecondsSinceEpoch}',
    ),
    classroomId: classroomId,
    type: ActivityType.announcement,
    title: title,
    description: faker.randomGenerator.boolean()
        ? faker.lorem
              .sentences(faker.randomGenerator.integer(3, min: 1))
              .join(' ')
        : null,
    createdAt: createdAt,
    createdById: classroom.teacherId,
    createdByName: classroom.teacherName,
  );
}

/// Generate discussion activity
ActivityModel _generateDiscussion(
  Faker faker,
  String classroomId,
  DateTime createdAt,
  dynamic classroom,
) {
  final discussions = [
    'What are your thoughts on the latest chapter?',
    'How can we apply this concept in real life?',
    'Share your project ideas for feedback',
    'Discuss the implications of this theory',
    'What challenges did you face in the assignment?',
    'Let\'s brainstorm solutions to this problem',
    'Share interesting articles related to our topic',
    'What questions do you have about the exam?',
    'Discuss your research findings',
    'How has this course changed your perspective?',
  ];

  final title = discussions[faker.randomGenerator.integer(discussions.length)];

  return ActivityModel(
    id: MockDataIdManager.getActivityId(
      'discussion_${classroomId}_${createdAt.millisecondsSinceEpoch}',
    ),
    classroomId: classroomId,
    type: ActivityType.discussion,
    title: title,
    description: faker.randomGenerator.boolean()
        ? faker.lorem
              .sentences(faker.randomGenerator.integer(2, min: 1))
              .join(' ')
        : null,
    createdAt: createdAt,
    createdById: classroom.teacherId,
    createdByName: classroom.teacherName,
  );
}

/// Generate homework activity
ActivityModel _generateHomeworkActivity(
  Faker faker,
  String classroomId,
  DateTime createdAt,
  dynamic classroom,
) {
  final homeworkTitles = [
    'New Assignment Posted',
    'Homework Due Tomorrow',
    'Assignment Reminder',
    'Practice Problems Available',
    'Project Guidelines Updated',
    'Reading Assignment Posted',
    'Lab Report Due',
    'Essay Assignment Available',
  ];

  final title =
      homeworkTitles[faker.randomGenerator.integer(homeworkTitles.length)];

  return ActivityModel(
    id: MockDataIdManager.getActivityId(
      'homework_${classroomId}_${createdAt.millisecondsSinceEpoch}',
    ),
    classroomId: classroomId,
    type: ActivityType.homework,
    title: title,
    description: faker.randomGenerator.boolean()
        ? faker.lorem
              .sentences(faker.randomGenerator.integer(2, min: 1))
              .join(' ')
        : null,
    createdAt: createdAt,
    createdById: classroom.teacherId,
    createdByName: classroom.teacherName,
  );
}

/// Generate digital library activity
ActivityModel _generateDigitalLibraryActivity(
  Faker faker,
  String classroomId,
  DateTime createdAt,
  dynamic classroom,
) {
  final libraryTitles = [
    'New Resources Added',
    'E-book Collection Updated',
    'Research Materials Available',
    'Digital Textbook Access',
    'Online References Posted',
    'Study Materials Uploaded',
    'Video Lectures Available',
    'Interactive Content Added',
  ];

  final title =
      libraryTitles[faker.randomGenerator.integer(libraryTitles.length)];

  return ActivityModel(
    id: MockDataIdManager.getActivityId(
      'library_${classroomId}_${createdAt.millisecondsSinceEpoch}',
    ),
    classroomId: classroomId,
    type: ActivityType.digitalLibrary,
    title: title,
    description: faker.randomGenerator.boolean()
        ? faker.lorem
              .sentences(faker.randomGenerator.integer(2, min: 1))
              .join(' ')
        : null,
    createdAt: createdAt,
    createdById: classroom.teacherId,
    createdByName: classroom.teacherName,
  );
}

/// Generate notice activity
ActivityModel _generateNoticeActivity(
  Faker faker,
  String classroomId,
  DateTime createdAt,
  dynamic classroom,
) {
  final noticeTitles = [
    'Important Notice',
    'Class Schedule Change',
    'Exam Date Announcement',
    'Holiday Notice',
    'School Event Update',
    'Policy Update',
    'Safety Guidelines',
    'Parent-Teacher Meeting',
  ];

  final title =
      noticeTitles[faker.randomGenerator.integer(noticeTitles.length)];

  return ActivityModel(
    id: MockDataIdManager.getActivityId(
      'notice_${classroomId}_${createdAt.millisecondsSinceEpoch}',
    ),
    classroomId: classroomId,
    type: ActivityType.notice,
    title: title,
    description: faker.randomGenerator.boolean()
        ? faker.lorem
              .sentences(faker.randomGenerator.integer(3, min: 1))
              .join(' ')
        : null,
    createdAt: createdAt,
    createdById: classroom.teacherId,
    createdByName: classroom.teacherName,
  );
}

/// Generate quiz activity
ActivityModel _generateQuizActivity(
  Faker faker,
  String classroomId,
  DateTime createdAt,
  dynamic classroom,
) {
  final quizTitles = [
    'Pop Quiz Tomorrow',
    'Weekly Assessment',
    'Chapter Review Quiz',
    'Practice Test Available',
    'Online Quiz Posted',
    'Unit Test Announcement',
    'Quick Assessment',
    'Knowledge Check',
  ];

  final title = quizTitles[faker.randomGenerator.integer(quizTitles.length)];

  return ActivityModel(
    id: MockDataIdManager.getActivityId(
      'quiz_${classroomId}_${createdAt.millisecondsSinceEpoch}',
    ),
    classroomId: classroomId,
    type: ActivityType.quiz,
    title: title,
    description: faker.randomGenerator.boolean()
        ? faker.lorem
              .sentences(faker.randomGenerator.integer(2, min: 1))
              .join(' ')
        : null,
    createdAt: createdAt,
    createdById: classroom.teacherId,
    createdByName: classroom.teacherName,
  );
}

/// Generate reminder activity
ActivityModel _generateReminderActivity(
  Faker faker,
  String classroomId,
  DateTime createdAt,
  dynamic classroom,
) {
  final reminderTitles = [
    'Assignment Due Reminder',
    'Test Tomorrow',
    'Bring Required Materials',
    'Submit Project Today',
    'Class Timing Change',
    'Don\'t Forget Homework',
    'Exam Preparation Reminder',
    'Field Trip Permission Slip',
  ];

  final title =
      reminderTitles[faker.randomGenerator.integer(reminderTitles.length)];

  return ActivityModel(
    id: MockDataIdManager.getActivityId(
      'reminder_${classroomId}_${createdAt.millisecondsSinceEpoch}',
    ),
    classroomId: classroomId,
    type: ActivityType.reminder,
    title: title,
    description: faker.randomGenerator.boolean()
        ? faker.lorem
              .sentences(faker.randomGenerator.integer(2, min: 1))
              .join(' ')
        : null,
    createdAt: createdAt,
    createdById: classroom.teacherId,
    createdByName: classroom.teacherName,
  );
}

/// Generate attendance activity
ActivityModel _generateAttendanceActivity(
  Faker faker,
  String classroomId,
  DateTime createdAt,
  dynamic classroom,
) {
  final attendanceTitles = [
    'Attendance Marked',
    'Roll Call Complete',
    'Class Attendance Update',
    'Present Students Recorded',
    'Attendance Summary',
    'Daily Attendance',
    'Class Participation Noted',
    'Attendance Report',
  ];

  final title =
      attendanceTitles[faker.randomGenerator.integer(attendanceTitles.length)];

  return ActivityModel(
    id: MockDataIdManager.getActivityId(
      'attendance_${classroomId}_${createdAt.millisecondsSinceEpoch}',
    ),
    classroomId: classroomId,
    type: ActivityType.attendance,
    title: title,
    description: faker.randomGenerator.boolean()
        ? faker.lorem
              .sentences(faker.randomGenerator.integer(1, min: 1))
              .join(' ')
        : null,
    createdAt: createdAt,
    createdById: classroom.teacherId,
    createdByName: classroom.teacherName,
  );
}

/// Generate notification activity
ActivityModel _generateNotificationActivity(
  Faker faker,
  String classroomId,
  DateTime createdAt,
  dynamic classroom,
) {
  final notificationTitles = [
    'System Notification',
    'Grade Posted',
    'Message from Teacher',
    'Class Update',
    'New Material Available',
    'Feedback Received',
    'Progress Update',
    'Achievement Unlocked',
  ];

  final title =
      notificationTitles[faker.randomGenerator.integer(
        notificationTitles.length,
      )];

  return ActivityModel(
    id: MockDataIdManager.getActivityId(
      'notification_${classroomId}_${createdAt.millisecondsSinceEpoch}',
    ),
    classroomId: classroomId,
    type: ActivityType.notification,
    title: title,
    description: faker.randomGenerator.boolean()
        ? faker.lorem
              .sentences(faker.randomGenerator.integer(2, min: 1))
              .join(' ')
        : null,
    createdAt: createdAt,
    createdById: classroom.teacherId,
    createdByName: classroom.teacherName,
  );
}
