import 'package:flutter/foundation.dart';
import '../../../../core/enums/homework/assignment_type.dart';
import '../../../../core/enums/homework/homework_status.dart';
import '../../../../core/enums/homework/submission_type.dart';
import '../../../classroom/enums/classroom_type.dart';
import '../../../digital_library/enums/file_access_type.dart';
import '../../../digital_library/enums/folder_type.dart';
import '../../../digital_library/enums/library_file_type.dart';
import '../../../digital_library/enums/upload_status.dart';
import '../generators/classroom_mock_generator.dart';
import '../generators/homework_mock_generator.dart';
import '../generators/profile_mock_generator.dart';
import '../generators/activity_mock_generator.dart';
import '../generators/digital_library_mock_generator.dart';

/// Comprehensive validator for all mock data
class MockDataValidator {
  /// Test function to verify mock data generation coverage
  static void testMockDataCoverage() {
    debugPrint('=== Testing Mock Data Coverage ===');

    _testClassroomData();
    _testHomeworkData();
    _testSubmissionData();
    _testActivityData();
    _testProfileData();
    _testDigitalLibraryData();

    debugPrint('=== Mock Data Coverage Test Completed ===');
  }

  /// Test classroom data coverage
  static void _testClassroomData() {
    debugPrint('\n📚 Testing Classroom Data:');
    debugPrint('Total classes: ${mockClassesList.length}');

    final subjectCounts = <String, int>{};
    final typeCounts = <ClassroomType, int>{};
    int totalStudents = 0;
    int primaryEligibleCount = 0;

    for (final classModel in mockClassesList) {
      final subject = classModel.subject ?? 'General';
      subjectCounts[subject] = (subjectCounts[subject] ?? 0) + 1;
      typeCounts[classModel.type] = (typeCounts[classModel.type] ?? 0) + 1;
      totalStudents += classModel.studentIds.length;

      if (classModel.isPrimaryClass == true) {
        primaryEligibleCount++;
      }
    }

    debugPrint('Subject distribution: $subjectCounts');
    debugPrint(
      'Type distribution: ${typeCounts.map((k, v) => MapEntry(k.name, v))}',
    );
    debugPrint('Primary eligible classes: $primaryEligibleCount');
    debugPrint(
      'Average students per class: ${(totalStudents / mockClassesList.length).toStringAsFixed(1)}',
    );

    // Verify we have diverse subjects
    if (subjectCounts.length >= 3) {
      debugPrint('✅ Good subject diversity');
    } else {
      debugPrint('⚠️ Limited subject diversity');
    }

    // Verify we have different classroom types
    if (typeCounts.length >= 2) {
      debugPrint('✅ Good classroom type diversity');
    } else {
      debugPrint('⚠️ Limited classroom type diversity');
    }

    // Verify we have primary eligible classes
    if (primaryEligibleCount > 0) {
      debugPrint('✅ Primary eligible classes available');
    } else {
      debugPrint('⚠️ No primary eligible classes found');
    }
  }

  /// Test homework data coverage
  static void _testHomeworkData() {
    debugPrint('\n📝 Testing Homework Data:');
    debugPrint('Total homework items: ${mockHomeworkList.length}');

    final statusCounts = <HomeworkStatus, int>{};
    final typeCounts = <AssignmentType, int>{};
    final submissionTypeCounts = <SubmissionType, int>{};
    int withDescriptionCount = 0;
    int withResourcesCount = 0;
    int withTeacherNoteCount = 0;

    for (final homework in mockHomeworkList) {
      statusCounts[homework.status] = (statusCounts[homework.status] ?? 0) + 1;
      typeCounts[homework.assignmentType] =
          (typeCounts[homework.assignmentType] ?? 0) + 1;

      submissionTypeCounts[homework.submissionType] =
          (submissionTypeCounts[homework.submissionType] ?? 0) + 1;

      if (homework.description?.isNotEmpty == true) {
        withDescriptionCount++;
      }

      if (homework.resourceUrls.isNotEmpty) {
        withResourcesCount++;
      }

      if (homework.teacherNote != null && homework.teacherNote!.isNotEmpty) {
        withTeacherNoteCount++;
      }
    }

    debugPrint(
      'Status distribution: ${statusCounts.map((k, v) => MapEntry(k.name, v))}',
    );
    debugPrint(
      'Assignment type distribution: ${typeCounts.map((k, v) => MapEntry(k.name, v))}',
    );
    debugPrint(
      'Submission type distribution: ${submissionTypeCounts.map((k, v) => MapEntry(k.name, v))}',
    );
    debugPrint('Homework attributes:');
    debugPrint('- With descriptions: $withDescriptionCount');
    debugPrint('- With resources: $withResourcesCount');
    debugPrint('- With teacher notes: $withTeacherNoteCount');

    // Verify status coverage (minimum 3 of each)
    bool hasGoodStatusCoverage = true;
    for (final status in HomeworkStatus.values) {
      final count = statusCounts[status] ?? 0;
      if (count < 3) {
        debugPrint(
          '⚠️ Insufficient coverage for status: ${status.name} ($count)',
        );
        hasGoodStatusCoverage = false;
      }
    }

    if (hasGoodStatusCoverage) {
      debugPrint('✅ Good status coverage (minimum 3 of each)');
    }

    // Verify assignment type coverage
    if (typeCounts.length >= 2) {
      debugPrint('✅ Good assignment type diversity');
    } else {
      debugPrint('⚠️ Limited assignment type diversity');
    }
  }

  /// Test submission data coverage
  static void _testSubmissionData() {
    debugPrint('\n📤 Testing Submission Data:');
    debugPrint('Total submissions: ${mockHomeworkSubmissions.length}');

    int withTeacherRemarkCount = 0;
    int reviewedCount = 0;
    int withStudentNoteCount = 0;
    final fileTypeCounts = <String, int>{};

    for (final submission in mockHomeworkSubmissions) {
      if (submission.teacherRemark != null) {
        withTeacherRemarkCount++;
      }
      if (submission.reviewedAt != null) {
        reviewedCount++;
      }
      if (submission.studentNote != null) {
        withStudentNoteCount++;
      }

      // Count file types
      for (final fileUrl in submission.fileUrls) {
        final extension = fileUrl.split('.').last.toLowerCase();
        fileTypeCounts[extension] = (fileTypeCounts[extension] ?? 0) + 1;
      }
    }

    debugPrint('Submission attributes:');
    debugPrint('- With teacher remarks: $withTeacherRemarkCount');
    debugPrint('- Reviewed: $reviewedCount');
    debugPrint('- With student notes: $withStudentNoteCount');
    debugPrint('- File type distribution: $fileTypeCounts');

    // Verify submission coverage
    if (mockHomeworkSubmissions.isNotEmpty) {
      debugPrint('✅ Submission data generated');
    } else {
      debugPrint('⚠️ No submission data found');
    }

    if (withTeacherRemarkCount > 0) {
      debugPrint('✅ Teacher remarks included');
    } else {
      debugPrint('⚠️ No teacher remarks found');
    }

    if (fileTypeCounts.length >= 3) {
      debugPrint('✅ Good file type diversity');
    } else {
      debugPrint('⚠️ Limited file type diversity');
    }
  }

  /// Test profile data coverage
  static void _testProfileData() {
    debugPrint('\n👤 Testing Profile Data:');
    debugPrint('Total profiles: ${mockProfilesList.length}');

    final userTypeCounts = <String, int>{};
    final gradeCounts = <String, int>{};
    final schoolCounts = <String, int>{};
    int withPrimaryClassCount = 0;
    int withProfileImageCount = 0;
    int withBioCount = 0;

    for (final profile in mockProfilesList) {
      final userType = profile.userType.name;
      userTypeCounts[userType] = (userTypeCounts[userType] ?? 0) + 1;

      if (profile.grade != null) {
        gradeCounts[profile.grade!] = (gradeCounts[profile.grade!] ?? 0) + 1;
      }

      if (profile.school != null) {
        schoolCounts[profile.school!] =
            (schoolCounts[profile.school!] ?? 0) + 1;
      }

      if (profile.primaryClassId != null) {
        withPrimaryClassCount++;
      }

      if (profile.profileImageUrl != null) {
        withProfileImageCount++;
      }

      if (profile.bio != null && profile.bio!.isNotEmpty) {
        withBioCount++;
      }
    }

    debugPrint('User type distribution: $userTypeCounts');
    debugPrint('Grade distribution: $gradeCounts');
    debugPrint('School distribution: $schoolCounts');
    debugPrint('Profile attributes:');
    debugPrint('- With primary class: $withPrimaryClassCount');
    debugPrint('- With profile image: $withProfileImageCount');
    debugPrint('- With bio: $withBioCount');

    // Verify current user profile exists
    const currentUserId = 'XRTanMcAUWSMq3mrRvve2Y9IMP12';
    final currentUserProfile = mockProfilesList
        .where((p) => p.id == currentUserId)
        .toList();

    if (currentUserProfile.length == 1) {
      debugPrint('✅ Current user profile found');
    } else if (currentUserProfile.isEmpty) {
      debugPrint('❌ Current user profile not found');
    } else {
      debugPrint('❌ Multiple current user profiles found');
    }

    // Verify user type distribution
    final studentCount = userTypeCounts['student'] ?? 0;
    final studentPercentage = (studentCount / mockProfilesList.length * 100);
    if (studentPercentage >= 70) {
      debugPrint(
        '✅ Good student distribution (${studentPercentage.toStringAsFixed(1)}%)',
      );
    } else {
      debugPrint(
        '⚠️ Low student percentage (${studentPercentage.toStringAsFixed(1)}%)',
      );
    }
  }

  /// Test activity data coverage
  static void _testActivityData() {
    debugPrint('\n🎯 Testing Activity Data:');
    debugPrint('Total activities: ${mockActivitiesList.length}');

    final typeCounts = <String, int>{};
    final classroomCounts = <String, int>{};
    int withDescriptionCount = 0;

    for (final activity in mockActivitiesList) {
      final type = activity.type.name;
      typeCounts[type] = (typeCounts[type] ?? 0) + 1;

      classroomCounts[activity.classroomId] =
          (classroomCounts[activity.classroomId] ?? 0) + 1;

      if (activity.description != null && activity.description!.isNotEmpty) {
        withDescriptionCount++;
      }
    }

    debugPrint('Activity type distribution: $typeCounts');
    debugPrint(
      'Activities per classroom: ${classroomCounts.length} classrooms have activities',
    );
    debugPrint('Activities with descriptions: $withDescriptionCount');

    // Verify activity-classroom relationships
    final classIds = mockClassesList.map((c) => c.id).toSet();
    final activityClassIds = mockActivitiesList
        .map((a) => a.classroomId)
        .toSet();

    final orphanedActivities = activityClassIds.difference(classIds);
    if (orphanedActivities.isEmpty) {
      debugPrint('✅ All activities linked to valid classrooms');
    } else {
      debugPrint(
        '❌ Found activities with invalid classroom IDs: $orphanedActivities',
      );
    }

    // Verify each classroom has activities
    final classroomsWithActivities = activityClassIds.length;
    final totalClassrooms = mockClassesList.length;
    final coveragePercentage =
        (classroomsWithActivities / totalClassrooms * 100);

    if (coveragePercentage >= 80) {
      debugPrint(
        '✅ Good classroom coverage (${coveragePercentage.toStringAsFixed(1)}%)',
      );
    } else {
      debugPrint(
        '⚠️ Low classroom coverage (${coveragePercentage.toStringAsFixed(1)}%)',
      );
    }
  }

  /// Test activity-classroom ID consistency
  static void _testActivityClassroomConsistency() {
    debugPrint('\n🔗 Testing Activity-Classroom ID Consistency:');

    final classroomIds = mockClassesList.map((c) => c.id).toSet();
    final activityClassroomIds = mockActivitiesList
        .map((a) => a.classroomId)
        .toSet();

    debugPrint('Total classrooms: ${classroomIds.length}');
    debugPrint(
      'Unique classroom IDs in activities: ${activityClassroomIds.length}',
    );

    // Check for orphaned activities (activities with invalid classroom IDs)
    final orphanedActivities = activityClassroomIds.difference(classroomIds);
    if (orphanedActivities.isEmpty) {
      debugPrint('✅ All activities have valid classroom IDs');
    } else {
      debugPrint(
        '❌ Found ${orphanedActivities.length} activities with invalid classroom IDs',
      );
      debugPrint('Invalid classroom IDs: $orphanedActivities');
    }

    // Check classroom coverage (how many classrooms have activities)
    final classroomsWithActivities = classroomIds.intersection(
      activityClassroomIds,
    );
    final coveragePercentage =
        (classroomsWithActivities.length / classroomIds.length * 100);

    debugPrint(
      'Classrooms with activities: ${classroomsWithActivities.length}/${classroomIds.length}',
    );
    debugPrint('Coverage: ${coveragePercentage.toStringAsFixed(1)}%');

    if (coveragePercentage >= 80) {
      debugPrint('✅ Good classroom coverage for activities');
    } else {
      debugPrint('⚠️ Low classroom coverage for activities');
    }

    // Show sample activity-classroom mappings
    debugPrint('\nSample Activity-Classroom Mappings:');
    for (int i = 0; i < 3 && i < mockActivitiesList.length; i++) {
      final activity = mockActivitiesList[i];
      final classroom = mockClassesList.firstWhere(
        (c) => c.id == activity.classroomId,
        orElse: () =>
            throw Exception('Classroom not found for activity ${activity.id}'),
      );
      debugPrint(
        'Activity "${activity.title}" → Classroom "${classroom.name}"',
      );
    }
  }

  /// Test data relationships and consistency
  static void testDataConsistency() {
    debugPrint('\n🔗 Testing Data Consistency:');

    // Test activity-classroom consistency first
    _testActivityClassroomConsistency();

    // Test homework-submission relationships
    final homeworkWithSubmissions = mockHomeworkList
        .where(
          (hw) =>
              hw.status == HomeworkStatus.submitted ||
              hw.status == HomeworkStatus.accepted ||
              hw.status == HomeworkStatus.rejected,
        )
        .toList();

    final submissionHomeworkIds = mockHomeworkSubmissions
        .map((sub) => sub.homeworkId)
        .toSet();

    debugPrint(
      'Homework requiring submissions: ${homeworkWithSubmissions.length}',
    );
    debugPrint('Actual submissions: ${mockHomeworkSubmissions.length}');
    debugPrint(
      'Unique homework IDs in submissions: ${submissionHomeworkIds.length}',
    );

    // Verify homework-submission consistency
    bool consistencyPassed = true;
    for (final homework in homeworkWithSubmissions) {
      if (homework.submissionId == null) {
        debugPrint(
          '❌ Homework ${homework.id} has submission status but no submission ID',
        );
        consistencyPassed = false;
        continue;
      }

      final submission = mockHomeworkSubmissions
          .where((sub) => sub.id == homework.submissionId)
          .toList();

      if (submission.isEmpty) {
        debugPrint('❌ Missing submission for homework ${homework.id}');
        consistencyPassed = false;
      } else if (submission.length > 1) {
        debugPrint('❌ Multiple submissions found for homework ${homework.id}');
        consistencyPassed = false;
      }
    }

    if (consistencyPassed) {
      debugPrint('✅ Homework-submission consistency verified');
    }

    // Test class-homework relationships
    final classIds = mockClassesList.map((c) => c.id).toSet();
    final homeworkClassIds = mockHomeworkList
        .where((hw) => hw.classId != null)
        .map((hw) => hw.classId!)
        .toSet();

    final orphanedHomework = homeworkClassIds.difference(classIds);
    if (orphanedHomework.isEmpty) {
      debugPrint('✅ All homework linked to valid classes');
    } else {
      debugPrint('❌ Found homework with invalid class IDs: $orphanedHomework');
    }

    debugPrint('🔗 Data consistency test completed');
  }

  /// Validate all mock data
  static bool validateAllMockData() {
    try {
      debugPrint('🔍 Starting comprehensive mock data validation...');

      testMockDataCoverage();
      testDataConsistency();

      debugPrint('✅ Mock data validation completed successfully');
      return true;
    } catch (e) {
      debugPrint('❌ Mock data validation failed: $e');
      return false;
    }
  }

  /// Test digital library data coverage
  static void _testDigitalLibraryData() {
    debugPrint('\n📚 Testing Digital Library Data:');
    debugPrint('Total folders: ${mockLibraryFolders.length}');
    debugPrint('Total files: ${mockLibraryFiles.length}');
    debugPrint('Total upload sessions: ${mockUploadSessions.length}');

    // Test folder distribution
    final folderTypeCounts = <FolderType, int>{};
    final accessTypeCounts = <FileAccessType, int>{};
    int rootFolders = 0;
    int subfolders = 0;

    for (final folder in mockLibraryFolders) {
      folderTypeCounts[folder.folderType] =
          (folderTypeCounts[folder.folderType] ?? 0) + 1;
      accessTypeCounts[folder.accessType] =
          (accessTypeCounts[folder.accessType] ?? 0) + 1;

      if (folder.parentFolderId == null) {
        rootFolders++;
      } else {
        subfolders++;
      }
    }

    debugPrint('Root folders: $rootFolders, Subfolders: $subfolders');
    debugPrint('Folder types: $folderTypeCounts');
    debugPrint('Folder access types: $accessTypeCounts');

    // Test file distribution
    final fileTypeCounts = <LibraryFileType, int>{};
    final fileAccessCounts = <FileAccessType, int>{};
    final uploadStatusCounts = <UploadStatus, int>{};
    int filesWithThumbnails = 0;
    int favoriteFiles = 0;

    for (final file in mockLibraryFiles) {
      fileTypeCounts[file.fileType] = (fileTypeCounts[file.fileType] ?? 0) + 1;
      fileAccessCounts[file.accessType] =
          (fileAccessCounts[file.accessType] ?? 0) + 1;
      uploadStatusCounts[file.uploadStatus] =
          (uploadStatusCounts[file.uploadStatus] ?? 0) + 1;

      if (file.thumbnailUrl != null) filesWithThumbnails++;
      if (file.isFavorite) favoriteFiles++;
    }

    debugPrint('File types: $fileTypeCounts');
    debugPrint('File access types: $fileAccessCounts');
    debugPrint('Upload statuses: $uploadStatusCounts');
    debugPrint('Files with thumbnails: $filesWithThumbnails');
    debugPrint('Favorite files: $favoriteFiles');

    // Test upload sessions
    final sessionStatusCounts = <UploadStatus, int>{};
    int totalUploadFiles = 0;

    for (final session in mockUploadSessions) {
      sessionStatusCounts[session.status] =
          (sessionStatusCounts[session.status] ?? 0) + 1;
      totalUploadFiles += session.files.length;
    }

    debugPrint('Session statuses: $sessionStatusCounts');
    debugPrint('Total files in upload sessions: $totalUploadFiles');
  }

  /// Validate digital library data integrity
  static bool validateDigitalLibrary() {
    debugPrint('\n🔍 Validating Digital Library Data...');

    try {
      // Validate folders
      for (final folder in mockLibraryFolders) {
        if (folder.id.isEmpty || folder.name.isEmpty) {
          debugPrint('❌ Invalid folder: ${folder.id}');
          return false;
        }

        // Check parent-child relationships
        if (folder.parentFolderId != null) {
          final parentExists = mockLibraryFolders.any(
            (f) => f.id == folder.parentFolderId,
          );
          if (!parentExists) {
            debugPrint(
              '❌ Folder ${folder.id} has invalid parent: ${folder.parentFolderId}',
            );
            return false;
          }
        }
      }

      // Validate files
      for (final file in mockLibraryFiles) {
        if (file.id.isEmpty || file.fileName.isEmpty) {
          debugPrint('❌ Invalid file: ${file.id}');
          return false;
        }

        // Check folder relationships
        if (file.folderId != null) {
          final folderExists = mockLibraryFolders.any(
            (f) => f.id == file.folderId,
          );
          if (!folderExists) {
            debugPrint(
              '❌ File ${file.id} has invalid folder: ${file.folderId}',
            );
            return false;
          }
        }

        // Check uploader exists
        final uploaderExists = mockProfilesList.any(
          (u) => u.id == file.uploaderId,
        );
        if (!uploaderExists) {
          debugPrint(
            '❌ File ${file.id} has invalid uploader: ${file.uploaderId}',
          );
          return false;
        }
      }

      // Validate upload sessions
      for (final session in mockUploadSessions) {
        if (session.id.isEmpty || session.files.isEmpty) {
          debugPrint('❌ Invalid upload session: ${session.id}');
          return false;
        }

        // Check user exists
        final userExists = mockProfilesList.any((u) => u.id == session.userId);
        if (!userExists) {
          debugPrint(
            '❌ Upload session ${session.id} has invalid user: ${session.userId}',
          );
          return false;
        }

        // Check folder exists if specified
        if (session.folderId != null) {
          final folderExists = mockLibraryFolders.any(
            (f) => f.id == session.folderId,
          );
          if (!folderExists) {
            debugPrint(
              '❌ Upload session ${session.id} has invalid folder: ${session.folderId}',
            );
            return false;
          }
        }
      }

      debugPrint('✅ Digital library data validation passed');
      return true;
    } catch (e) {
      debugPrint('❌ Digital library validation failed: $e');
      return false;
    }
  }
}
