import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logger/logger.dart';

import '../models/announcement_model.dart';
import '../enums/announcement_type.dart';
import '../enums/announcement_priority.dart';
import '../enums/announcement_usage_type.dart';
import '../services/announcement_preferences_service.dart';
import 'announcements_controller.dart';

/// Logger for announcement filter controller
final _logger = Logger();

/// Enum for announcement filter types
enum AnnouncementFilterType { all, unread, important, pinned }

/// Advanced filter configuration model
class AnnouncementAdvancedFilter {
  final Set<AnnouncementType> types;
  final Set<AnnouncementPriority> priorities;
  final Set<AnnouncementUsageType> usageTypes;
  final DateTime? startDate;
  final DateTime? endDate;
  final bool? isRead;
  final bool? isPinned;
  final bool? isActive;
  final String? classroomId;
  final String? authorId;
  final List<String> tags;

  const AnnouncementAdvancedFilter({
    this.types = const {},
    this.priorities = const {},
    this.usageTypes = const {},
    this.startDate,
    this.endDate,
    this.isRead,
    this.isPinned,
    this.isActive,
    this.classroomId,
    this.authorId,
    this.tags = const [],
  });

  AnnouncementAdvancedFilter copyWith({
    Set<AnnouncementType>? types,
    Set<AnnouncementPriority>? priorities,
    Set<AnnouncementUsageType>? usageTypes,
    DateTime? startDate,
    DateTime? endDate,
    bool? isRead,
    bool? isPinned,
    bool? isActive,
    String? classroomId,
    String? authorId,
    List<String>? tags,
  }) {
    return AnnouncementAdvancedFilter(
      types: types ?? this.types,
      priorities: priorities ?? this.priorities,
      usageTypes: usageTypes ?? this.usageTypes,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      isRead: isRead ?? this.isRead,
      isPinned: isPinned ?? this.isPinned,
      isActive: isActive ?? this.isActive,
      classroomId: classroomId ?? this.classroomId,
      authorId: authorId ?? this.authorId,
      tags: tags ?? this.tags,
    );
  }

  bool get isEmpty {
    return types.isEmpty &&
        priorities.isEmpty &&
        usageTypes.isEmpty &&
        startDate == null &&
        endDate == null &&
        isRead == null &&
        isPinned == null &&
        isActive == null &&
        classroomId == null &&
        authorId == null &&
        tags.isEmpty;
  }

  bool get hasActiveFilters {
    return !isEmpty;
  }

  int get activeFilterCount {
    int count = 0;
    if (types.isNotEmpty) count++;
    if (priorities.isNotEmpty) count++;
    if (usageTypes.isNotEmpty) count++;
    if (startDate != null || endDate != null) count++;
    if (isRead != null) count++;
    if (isPinned != null) count++;
    if (isActive != null) count++;
    if (classroomId != null) count++;
    if (authorId != null) count++;
    if (tags.isNotEmpty) count++;
    return count;
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is AnnouncementAdvancedFilter &&
          runtimeType == other.runtimeType &&
          types == other.types &&
          priorities == other.priorities &&
          usageTypes == other.usageTypes &&
          startDate == other.startDate &&
          endDate == other.endDate &&
          isRead == other.isRead &&
          isPinned == other.isPinned &&
          isActive == other.isActive &&
          classroomId == other.classroomId &&
          authorId == other.authorId &&
          tags == other.tags;

  @override
  int get hashCode =>
      types.hashCode ^
      priorities.hashCode ^
      usageTypes.hashCode ^
      startDate.hashCode ^
      endDate.hashCode ^
      isRead.hashCode ^
      isPinned.hashCode ^
      isActive.hashCode ^
      classroomId.hashCode ^
      authorId.hashCode ^
      tags.hashCode;

  @override
  String toString() {
    return 'AnnouncementAdvancedFilter(types: $types, priorities: $priorities, usageTypes: $usageTypes, startDate: $startDate, endDate: $endDate, isRead: $isRead, isPinned: $isPinned, isActive: $isActive, classroomId: $classroomId, authorId: $authorId, tags: $tags)';
  }
}

extension AnnouncementFilterTypeExtension on AnnouncementFilterType {
  String get displayName {
    switch (this) {
      case AnnouncementFilterType.all:
        return 'All';
      case AnnouncementFilterType.unread:
        return 'Unread';
      case AnnouncementFilterType.important:
        return 'Important';
      case AnnouncementFilterType.pinned:
        return 'Pinned';
    }
  }

  String get value {
    switch (this) {
      case AnnouncementFilterType.all:
        return 'all';
      case AnnouncementFilterType.unread:
        return 'unread';
      case AnnouncementFilterType.important:
        return 'important';
      case AnnouncementFilterType.pinned:
        return 'pinned';
    }
  }
}

// ========== FILTER STATE MANAGEMENT ==========

/// State notifier for announcement filter type
class AnnouncementFilterNotifier extends StateNotifier<AnnouncementFilterType> {
  AnnouncementFilterNotifier() : super(AnnouncementFilterType.all);

  void setFilter(AnnouncementFilterType filter) {
    _logger.i('Setting announcement filter to: ${filter.displayName}');
    state = filter;
  }

  void clearFilter() {
    _logger.i('Clearing announcement filter');
    state = AnnouncementFilterType.all;
  }
}

/// Provider for announcement filter state
final announcementFilterProvider =
    StateNotifierProvider<AnnouncementFilterNotifier, AnnouncementFilterType>(
      (ref) => AnnouncementFilterNotifier(),
    );

/// State notifier for search query
class AnnouncementSearchNotifier extends StateNotifier<String> {
  AnnouncementSearchNotifier() : super('');

  void setQuery(String query) {
    _logger.i('Setting announcement search query to: $query');
    state = query.trim();
  }

  void clearQuery() {
    _logger.i('Clearing announcement search query');
    state = '';
  }
}

/// Provider for announcement search query state
final announcementSearchQueryProvider =
    StateNotifierProvider<AnnouncementSearchNotifier, String>(
      (ref) => AnnouncementSearchNotifier(),
    );

/// State notifier for selected classroom filter
class AnnouncementClassroomFilterNotifier extends StateNotifier<String?> {
  AnnouncementClassroomFilterNotifier() : super(null);

  void setClassroom(String? classroomId) {
    _logger.i('Setting announcement classroom filter to: $classroomId');
    state = classroomId;
  }

  void clearClassroom() {
    _logger.i('Clearing announcement classroom filter');
    state = null;
  }
}

/// Provider for classroom filter state
final announcementClassroomFilterProvider =
    StateNotifierProvider<AnnouncementClassroomFilterNotifier, String?>(
      (ref) => AnnouncementClassroomFilterNotifier(),
    );

// ========== ADVANCED FILTER STATE MANAGEMENT ==========

/// State notifier for advanced announcement filters with persistence
class AnnouncementAdvancedFilterNotifier
    extends StateNotifier<AnnouncementAdvancedFilter> {
  final AnnouncementPreferencesService _preferencesService;

  AnnouncementAdvancedFilterNotifier(this._preferencesService)
    : super(const AnnouncementAdvancedFilter()) {
    _loadSavedFilter();
  }

  /// Load saved filter from local storage
  Future<void> _loadSavedFilter() async {
    final savedFilter = await _preferencesService.loadAdvancedFilter();
    if (savedFilter != null) {
      state = savedFilter;
      _logger.i(
        'Loaded saved advanced filter with ${savedFilter.activeFilterCount} active filters',
      );
    }
  }

  void _saveCurrentFilter() {
    _preferencesService.saveAdvancedFilter(state);
  }

  void setTypes(Set<AnnouncementType> types) {
    _logger.i(
      'Setting announcement type filters: ${types.map((t) => t.displayName).join(', ')}',
    );
    state = state.copyWith(types: types);
    _saveCurrentFilter();
  }

  void addType(AnnouncementType type) {
    final newTypes = Set<AnnouncementType>.from(state.types)..add(type);
    setTypes(newTypes);
  }

  void removeType(AnnouncementType type) {
    final newTypes = Set<AnnouncementType>.from(state.types)..remove(type);
    setTypes(newTypes);
  }

  void setPriorities(Set<AnnouncementPriority> priorities) {
    _logger.i(
      'Setting announcement priority filters: ${priorities.map((p) => p.displayName).join(', ')}',
    );
    state = state.copyWith(priorities: priorities);
  }

  void addPriority(AnnouncementPriority priority) {
    final newPriorities = Set<AnnouncementPriority>.from(state.priorities)
      ..add(priority);
    setPriorities(newPriorities);
  }

  void removePriority(AnnouncementPriority priority) {
    final newPriorities = Set<AnnouncementPriority>.from(state.priorities)
      ..remove(priority);
    setPriorities(newPriorities);
  }

  void setUsageTypes(Set<AnnouncementUsageType> usageTypes) {
    _logger.i(
      'Setting announcement usage type filters: ${usageTypes.map((u) => u.displayName).join(', ')}',
    );
    state = state.copyWith(usageTypes: usageTypes);
  }

  void addUsageType(AnnouncementUsageType usageType) {
    final newUsageTypes = Set<AnnouncementUsageType>.from(state.usageTypes)
      ..add(usageType);
    setUsageTypes(newUsageTypes);
  }

  void removeUsageType(AnnouncementUsageType usageType) {
    final newUsageTypes = Set<AnnouncementUsageType>.from(state.usageTypes)
      ..remove(usageType);
    setUsageTypes(newUsageTypes);
  }

  void setDateRange(DateTime? startDate, DateTime? endDate) {
    _logger.i(
      'Setting date range filter: ${startDate?.toIso8601String()} to ${endDate?.toIso8601String()}',
    );
    state = state.copyWith(startDate: startDate, endDate: endDate);
  }

  void setReadStatus(bool? isRead) {
    _logger.i('Setting read status filter: $isRead');
    state = state.copyWith(isRead: isRead);
  }

  void setPinnedStatus(bool? isPinned) {
    _logger.i('Setting pinned status filter: $isPinned');
    state = state.copyWith(isPinned: isPinned);
  }

  void setActiveStatus(bool? isActive) {
    _logger.i('Setting active status filter: $isActive');
    state = state.copyWith(isActive: isActive);
  }

  void setClassroomId(String? classroomId) {
    _logger.i('Setting classroom filter: $classroomId');
    state = state.copyWith(classroomId: classroomId);
  }

  void setAuthorId(String? authorId) {
    _logger.i('Setting author filter: $authorId');
    state = state.copyWith(authorId: authorId);
  }

  void setTags(List<String> tags) {
    _logger.i('Setting tag filters: ${tags.join(', ')}');
    state = state.copyWith(tags: tags);
  }

  void addTag(String tag) {
    final newTags = List<String>.from(state.tags)..add(tag);
    setTags(newTags);
  }

  void removeTag(String tag) {
    final newTags = List<String>.from(state.tags)..remove(tag);
    setTags(newTags);
  }

  void setFilter(AnnouncementAdvancedFilter filter) {
    _logger.i('Setting complete advanced filter');
    state = filter;
    _saveCurrentFilter();
  }

  void clearAllFilters() {
    _logger.i('Clearing all advanced filters');
    state = const AnnouncementAdvancedFilter();
    _saveCurrentFilter();
  }

  void clearTypeFilters() {
    state = state.copyWith(types: <AnnouncementType>{});
  }

  void clearPriorityFilters() {
    state = state.copyWith(priorities: <AnnouncementPriority>{});
  }

  void clearUsageTypeFilters() {
    state = state.copyWith(usageTypes: <AnnouncementUsageType>{});
  }

  void clearDateRange() {
    state = state.copyWith(startDate: null, endDate: null);
  }

  void clearStatusFilters() {
    state = state.copyWith(isRead: null, isPinned: null, isActive: null);
  }

  void clearLocationFilters() {
    state = state.copyWith(classroomId: null, authorId: null);
  }

  void clearTagFilters() {
    state = state.copyWith(tags: <String>[]);
  }
}

/// Provider for announcement preferences service
final announcementPreferencesServiceProvider =
    Provider<AnnouncementPreferencesService>(
      (ref) => AnnouncementPreferencesService(),
    );

/// Provider for advanced announcement filters
final announcementAdvancedFilterProvider =
    StateNotifierProvider<
      AnnouncementAdvancedFilterNotifier,
      AnnouncementAdvancedFilter
    >(
      (ref) => AnnouncementAdvancedFilterNotifier(
        ref.read(announcementPreferencesServiceProvider),
      ),
    );

// ========== FILTERED ANNOUNCEMENTS PROVIDER ==========

/// Provider that combines all filters and advanced filters
final filteredAndSortedAnnouncementsProvider =
    FutureProvider<List<AnnouncementModel>>((ref) async {
      final filterType = ref.watch(announcementFilterProvider);
      final searchQuery = ref.watch(announcementSearchQueryProvider);
      final selectedClassroomId = ref.watch(
        announcementClassroomFilterProvider,
      );
      final advancedFilter = ref.watch(announcementAdvancedFilterProvider);
      final currentUserId = ref.read(currentUserIdProvider);

      try {
        _logger.i(
          'Applying announcement filters - Type: ${filterType.displayName}, Query: "$searchQuery", Advanced filters: ${advancedFilter.activeFilterCount}',
        );

        List<AnnouncementModel> announcements;

        // First, get announcements based on filter type
        switch (filterType) {
          case AnnouncementFilterType.all:
            announcements = await ref.read(userAnnouncementsProvider.future);
            break;
          case AnnouncementFilterType.unread:
            announcements = await ref.read(unreadAnnouncementsProvider.future);
            break;
          case AnnouncementFilterType.important:
            announcements = await ref.read(
              importantAnnouncementsProvider.future,
            );
            break;
          case AnnouncementFilterType.pinned:
            announcements = await ref.read(pinnedAnnouncementsProvider.future);
            break;
        }

        // Apply classroom filter if selected
        if (selectedClassroomId != null) {
          announcements = announcements
              .where(
                (announcement) =>
                    announcement.classroomId == selectedClassroomId,
              )
              .toList();
        }

        // Apply search filter if query is not empty
        if (searchQuery.isNotEmpty) {
          final query = searchQuery.toLowerCase();
          announcements = announcements.where((announcement) {
            return announcement.title.toLowerCase().contains(query) ||
                announcement.content.toLowerCase().contains(query) ||
                announcement.tags.any(
                  (tag) => tag.toLowerCase().contains(query),
                );
          }).toList();
        }

        // Apply advanced filters
        announcements = _applyAdvancedFilters(
          announcements,
          advancedFilter,
          currentUserId,
        );

        _logger.i(
          'Filtered announcements result: ${announcements.length} announcements',
        );
        return announcements;
      } catch (e) {
        _logger.e('Error applying announcement filters: $e');
        rethrow;
      }
    });

/// Legacy provider for backward compatibility
final filteredAnnouncementsProvider = filteredAndSortedAnnouncementsProvider;

// ========== FILTER OPTIONS PROVIDER ==========

/// Provider to get available filter options with counts
final announcementFilterOptionsProvider =
    FutureProvider<Map<AnnouncementFilterType, int>>((ref) async {
      try {
        _logger.i('Calculating announcement filter options with counts');

        final allAnnouncements = await ref.read(
          userAnnouncementsProvider.future,
        );
        final unreadAnnouncements = await ref.read(
          unreadAnnouncementsProvider.future,
        );
        final importantAnnouncements = await ref.read(
          importantAnnouncementsProvider.future,
        );
        final pinnedAnnouncements = await ref.read(
          pinnedAnnouncementsProvider.future,
        );

        final counts = <AnnouncementFilterType, int>{
          AnnouncementFilterType.all: allAnnouncements.length,
          AnnouncementFilterType.unread: unreadAnnouncements.length,
          AnnouncementFilterType.important: importantAnnouncements.length,
          AnnouncementFilterType.pinned: pinnedAnnouncements.length,
        };

        _logger.i('Filter options calculated: $counts');
        return counts;
      } catch (e) {
        _logger.e('Error calculating filter options: $e');
        rethrow;
      }
    });

// ========== ADVANCED FILTERING PROVIDERS ==========

/// Provider for announcements filtered by type and priority
final announcementsByTypeAndPriorityProvider =
    FutureProvider.family<
      List<AnnouncementModel>,
      ({AnnouncementType type, AnnouncementPriority priority})
    >((ref, params) async {
      try {
        _logger.i(
          'Fetching announcements by type: ${params.type.displayName} and priority: ${params.priority.displayName}',
        );

        final allAnnouncements = await ref.read(
          userAnnouncementsProvider.future,
        );
        final filteredAnnouncements = allAnnouncements.where((announcement) {
          return announcement.type == params.type &&
              announcement.priority == params.priority;
        }).toList();

        _logger.i(
          'Found ${filteredAnnouncements.length} announcements matching type and priority filters',
        );
        return filteredAnnouncements;
      } catch (e) {
        _logger.e('Error fetching announcements by type and priority: $e');
        rethrow;
      }
    });

/// Provider for recent announcements (last 7 days)
final recentAnnouncementsProvider = FutureProvider<List<AnnouncementModel>>((
  ref,
) async {
  try {
    _logger.i('Fetching recent announcements (last 7 days)');

    final allAnnouncements = await ref.read(userAnnouncementsProvider.future);
    final sevenDaysAgo = DateTime.now().subtract(const Duration(days: 7));

    final recentAnnouncements = allAnnouncements.where((announcement) {
      return announcement.createdAt.isAfter(sevenDaysAgo);
    }).toList();

    _logger.i('Found ${recentAnnouncements.length} recent announcements');
    return recentAnnouncements;
  } catch (e) {
    _logger.e('Error fetching recent announcements: $e');
    rethrow;
  }
});

/// Provider for today's announcements
final todayAnnouncementsProvider = FutureProvider<List<AnnouncementModel>>((
  ref,
) async {
  try {
    _logger.i('Fetching today\'s announcements');

    final allAnnouncements = await ref.read(userAnnouncementsProvider.future);
    final today = DateTime.now();

    final todayAnnouncements = allAnnouncements.where((announcement) {
      final announcementDate = announcement.createdAt;
      return announcementDate.year == today.year &&
          announcementDate.month == today.month &&
          announcementDate.day == today.day;
    }).toList();

    _logger.i('Found ${todayAnnouncements.length} announcements for today');
    return todayAnnouncements;
  } catch (e) {
    _logger.e('Error fetching today\'s announcements: $e');
    rethrow;
  }
});

// ========== HELPER FUNCTIONS ==========

/// Apply advanced filters to a list of announcements
List<AnnouncementModel> _applyAdvancedFilters(
  List<AnnouncementModel> announcements,
  AnnouncementAdvancedFilter filter,
  String? currentUserId,
) {
  if (filter.isEmpty) return announcements;

  return announcements.where((announcement) {
    // Type filter
    if (filter.types.isNotEmpty && !filter.types.contains(announcement.type)) {
      return false;
    }

    // Priority filter
    if (filter.priorities.isNotEmpty &&
        !filter.priorities.contains(announcement.priority)) {
      return false;
    }

    // Usage type filter
    if (filter.usageTypes.isNotEmpty &&
        !filter.usageTypes.contains(announcement.usageType)) {
      return false;
    }

    // Date range filter
    if (filter.startDate != null &&
        announcement.createdAt.isBefore(filter.startDate!)) {
      return false;
    }
    if (filter.endDate != null &&
        announcement.createdAt.isAfter(filter.endDate!)) {
      return false;
    }

    // Read status filter
    if (filter.isRead != null && currentUserId != null) {
      final isRead = announcement.isReadBy(currentUserId);
      if (filter.isRead != isRead) {
        return false;
      }
    }

    // Pinned status filter
    if (filter.isPinned != null && announcement.isPinned != filter.isPinned) {
      return false;
    }

    // Active status filter
    if (filter.isActive != null && announcement.isActive != filter.isActive) {
      return false;
    }

    // Classroom filter
    if (filter.classroomId != null &&
        announcement.classroomId != filter.classroomId) {
      return false;
    }

    // Author filter
    if (filter.authorId != null && announcement.authorId != filter.authorId) {
      return false;
    }

    // Tags filter
    if (filter.tags.isNotEmpty) {
      final hasMatchingTag = filter.tags.any(
        (tag) => announcement.tags.any(
          (announcementTag) =>
              announcementTag.toLowerCase().contains(tag.toLowerCase()),
        ),
      );
      if (!hasMatchingTag) {
        return false;
      }
    }

    return true;
  }).toList();
}
