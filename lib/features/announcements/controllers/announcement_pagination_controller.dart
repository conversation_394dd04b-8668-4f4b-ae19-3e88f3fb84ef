import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logger/logger.dart';

import '../models/announcement_model.dart';
import 'announcements_controller.dart';
import 'announcement_filter_controller.dart';

/// Logger for announcement pagination controller
final _logger = Logger();

/// Pagination configuration
class PaginationConfig {
  static const int defaultPageSize = 20;
  static const int maxPageSize = 50;
  static const int preloadThreshold = 5; // Load next page when 5 items from end
}

/// Pagination state model
class PaginationState<T> {
  final List<T> items;
  final bool isLoading;
  final bool hasMore;
  final int currentPage;
  final String? error;

  const PaginationState({
    this.items = const [],
    this.isLoading = false,
    this.hasMore = true,
    this.currentPage = 0,
    this.error,
  });

  PaginationState<T> copyWith({
    List<T>? items,
    bool? isLoading,
    bool? hasMore,
    int? currentPage,
    String? error,
  }) {
    return PaginationState<T>(
      items: items ?? this.items,
      isLoading: isLoading ?? this.isLoading,
      hasMore: hasMore ?? this.hasMore,
      currentPage: currentPage ?? this.currentPage,
      error: error ?? this.error,
    );
  }

  @override
  String toString() {
    return 'PaginationState(items: ${items.length}, isLoading: $isLoading, hasMore: $hasMore, currentPage: $currentPage, error: $error)';
  }
}

/// Announcement pagination notifier
class AnnouncementPaginationNotifier
    extends StateNotifier<PaginationState<AnnouncementModel>> {
  AnnouncementPaginationNotifier(this._ref) : super(const PaginationState());

  final Ref _ref;
  final int _pageSize = PaginationConfig.defaultPageSize;

  /// Load the first page of announcements
  Future<void> loadFirstPage() async {
    if (state.isLoading) return;

    try {
      _logger.i('Loading first page of announcements');
      state = state.copyWith(isLoading: true, error: null);

      final repository = _ref.read(announcementsRepositoryProvider);
      final userId = _ref.read(currentUserIdProvider);

      // Get all announcements and take first page
      final allAnnouncements = await repository.getAnnouncementsForUser(userId);
      final firstPageItems = allAnnouncements.take(_pageSize).toList();

      state = state.copyWith(
        items: firstPageItems,
        isLoading: false,
        hasMore: allAnnouncements.length > _pageSize,
        currentPage: 1,
      );

      _logger.i(
        'Loaded first page: ${firstPageItems.length} items, hasMore: ${state.hasMore}',
      );
    } catch (e) {
      _logger.e('Error loading first page: $e');
      state = state.copyWith(isLoading: false, error: e.toString());
    }
  }

  /// Load the next page of announcements
  Future<void> loadNextPage() async {
    if (state.isLoading || !state.hasMore) return;

    try {
      _logger.i('Loading next page (${state.currentPage + 1})');
      state = state.copyWith(isLoading: true, error: null);

      final repository = _ref.read(announcementsRepositoryProvider);
      final userId = _ref.read(currentUserIdProvider);

      // Get all announcements and calculate next page
      final allAnnouncements = await repository.getAnnouncementsForUser(userId);
      final startIndex = state.currentPage * _pageSize;
      final endIndex = startIndex + _pageSize;

      if (startIndex >= allAnnouncements.length) {
        // No more items
        state = state.copyWith(isLoading: false, hasMore: false);
        return;
      }

      final nextPageItems = allAnnouncements
          .skip(startIndex)
          .take(_pageSize)
          .toList();
      final updatedItems = [...state.items, ...nextPageItems];

      state = state.copyWith(
        items: updatedItems,
        isLoading: false,
        hasMore: endIndex < allAnnouncements.length,
        currentPage: state.currentPage + 1,
      );

      _logger.i(
        'Loaded page ${state.currentPage}: ${nextPageItems.length} new items, total: ${updatedItems.length}',
      );
    } catch (e) {
      _logger.e('Error loading next page: $e');
      state = state.copyWith(isLoading: false, error: e.toString());
    }
  }

  /// Refresh all data (reset pagination)
  Future<void> refresh() async {
    _logger.i('Refreshing paginated announcements');
    state = const PaginationState();
    await loadFirstPage();
  }

  /// Check if should preload next page based on current scroll position
  void checkPreload(int currentIndex) {
    if (!state.hasMore || state.isLoading) return;

    final remainingItems = state.items.length - currentIndex;
    if (remainingItems <= PaginationConfig.preloadThreshold) {
      _logger.i('Preloading next page (remaining items: $remainingItems)');
      loadNextPage();
    }
  }

  /// Reset pagination state
  void reset() {
    _logger.i('Resetting pagination state');
    state = const PaginationState();
  }
}

/// Provider for announcement pagination
final announcementPaginationProvider =
    StateNotifierProvider<
      AnnouncementPaginationNotifier,
      PaginationState<AnnouncementModel>
    >((ref) => AnnouncementPaginationNotifier(ref));

/// Filtered pagination notifier for search and filters
class FilteredAnnouncementPaginationNotifier
    extends StateNotifier<PaginationState<AnnouncementModel>> {
  FilteredAnnouncementPaginationNotifier(this._ref)
    : super(const PaginationState());

  final Ref _ref;
  final int _pageSize = PaginationConfig.defaultPageSize;
  List<AnnouncementModel> _allFilteredItems = [];

  /// Load first page with current filters
  Future<void> loadFirstPageWithFilters() async {
    if (state.isLoading) return;

    try {
      _logger.i('Loading first page with filters');
      state = state.copyWith(isLoading: true, error: null);

      // Get filtered announcements from the filter provider
      _allFilteredItems = await _ref.read(filteredAnnouncementsProvider.future);

      final firstPageItems = _allFilteredItems.take(_pageSize).toList();

      state = state.copyWith(
        items: firstPageItems,
        isLoading: false,
        hasMore: _allFilteredItems.length > _pageSize,
        currentPage: 1,
      );

      _logger.i(
        'Loaded filtered first page: ${firstPageItems.length} items, hasMore: ${state.hasMore}',
      );
    } catch (e) {
      _logger.e('Error loading filtered first page: $e');
      state = state.copyWith(isLoading: false, error: e.toString());
    }
  }

  /// Load next page from filtered results
  Future<void> loadNextPageFiltered() async {
    if (state.isLoading || !state.hasMore) return;

    try {
      _logger.i('Loading next filtered page (${state.currentPage + 1})');
      state = state.copyWith(isLoading: true, error: null);

      final startIndex = state.currentPage * _pageSize;
      final endIndex = startIndex + _pageSize;

      if (startIndex >= _allFilteredItems.length) {
        state = state.copyWith(isLoading: false, hasMore: false);
        return;
      }

      final nextPageItems = _allFilteredItems
          .skip(startIndex)
          .take(_pageSize)
          .toList();
      final updatedItems = [...state.items, ...nextPageItems];

      state = state.copyWith(
        items: updatedItems,
        isLoading: false,
        hasMore: endIndex < _allFilteredItems.length,
        currentPage: state.currentPage + 1,
      );

      _logger.i(
        'Loaded filtered page ${state.currentPage}: ${nextPageItems.length} new items',
      );
    } catch (e) {
      _logger.e('Error loading next filtered page: $e');
      state = state.copyWith(isLoading: false, error: e.toString());
    }
  }

  /// Refresh with current filters
  Future<void> refreshWithFilters() async {
    _logger.i('Refreshing with filters');
    state = const PaginationState();
    _allFilteredItems = [];
    await loadFirstPageWithFilters();
  }

  /// Check preload for filtered results
  void checkPreloadFiltered(int currentIndex) {
    if (!state.hasMore || state.isLoading) return;

    final remainingItems = state.items.length - currentIndex;
    if (remainingItems <= PaginationConfig.preloadThreshold) {
      loadNextPageFiltered();
    }
  }
}

/// Provider for filtered announcement pagination
final filteredAnnouncementPaginationProvider =
    StateNotifierProvider<
      FilteredAnnouncementPaginationNotifier,
      PaginationState<AnnouncementModel>
    >((ref) => FilteredAnnouncementPaginationNotifier(ref));

/// Helper provider to get pagination statistics
final paginationStatsProvider = Provider<Map<String, dynamic>>((ref) {
  final paginationState = ref.watch(announcementPaginationProvider);

  return {
    'totalItems': paginationState.items.length,
    'currentPage': paginationState.currentPage,
    'isLoading': paginationState.isLoading,
    'hasMore': paginationState.hasMore,
    'hasError': paginationState.error != null,
    'error': paginationState.error,
    'pageSize': PaginationConfig.defaultPageSize,
  };
});

/// Helper provider for filtered pagination statistics
final filteredPaginationStatsProvider = Provider<Map<String, dynamic>>((ref) {
  final paginationState = ref.watch(filteredAnnouncementPaginationProvider);

  return {
    'totalItems': paginationState.items.length,
    'currentPage': paginationState.currentPage,
    'isLoading': paginationState.isLoading,
    'hasMore': paginationState.hasMore,
    'hasError': paginationState.error != null,
    'error': paginationState.error,
    'pageSize': PaginationConfig.defaultPageSize,
  };
});
