import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logger/logger.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../models/announcement_model.dart';
import '../enums/announcement_type.dart';
import '../enums/announcement_priority.dart';
import '../enums/announcement_usage_type.dart';
import 'announcements_controller.dart';

/// Logger for announcement search controller
final _logger = Logger();

/// Model for search filters
class AnnouncementSearchFilters {
  final List<AnnouncementType> types;
  final List<AnnouncementPriority> priorities;
  final List<AnnouncementUsageType> usageTypes;
  final DateTime? startDate;
  final DateTime? endDate;
  final bool? isRead;
  final bool? isPinned;

  const AnnouncementSearchFilters({
    this.types = const [],
    this.priorities = const [],
    this.usageTypes = const [],
    this.startDate,
    this.endDate,
    this.isRead,
    this.isPinned,
  });

  AnnouncementSearchFilters copyWith({
    List<AnnouncementType>? types,
    List<AnnouncementPriority>? priorities,
    List<AnnouncementUsageType>? usageTypes,
    DateTime? startDate,
    DateTime? endDate,
    bool? isRead,
    bool? isPinned,
  }) {
    return AnnouncementSearchFilters(
      types: types ?? this.types,
      priorities: priorities ?? this.priorities,
      usageTypes: usageTypes ?? this.usageTypes,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      isRead: isRead ?? this.isRead,
      isPinned: isPinned ?? this.isPinned,
    );
  }

  bool get hasActiveFilters {
    return types.isNotEmpty ||
           priorities.isNotEmpty ||
           usageTypes.isNotEmpty ||
           startDate != null ||
           endDate != null ||
           isRead != null ||
           isPinned != null;
  }

  @override
  String toString() {
    return 'AnnouncementSearchFilters(types: $types, priorities: $priorities, usageTypes: $usageTypes, startDate: $startDate, endDate: $endDate, isRead: $isRead, isPinned: $isPinned)';
  }
}

/// Model for search suggestions
class SearchSuggestion {
  final String text;
  final String type; // 'recent', 'popular', 'tag'
  final int? count;

  const SearchSuggestion({
    required this.text,
    required this.type,
    this.count,
  });

  @override
  String toString() {
    return 'SearchSuggestion(text: $text, type: $type, count: $count)';
  }
}

// ========== SEARCH STATE MANAGEMENT ==========

/// State notifier for advanced search filters
class AnnouncementSearchFiltersNotifier extends StateNotifier<AnnouncementSearchFilters> {
  AnnouncementSearchFiltersNotifier() : super(const AnnouncementSearchFilters());

  void updateTypes(List<AnnouncementType> types) {
    _logger.i('Updating search types filter: $types');
    state = state.copyWith(types: types);
  }

  void updatePriorities(List<AnnouncementPriority> priorities) {
    _logger.i('Updating search priorities filter: $priorities');
    state = state.copyWith(priorities: priorities);
  }

  void updateUsageTypes(List<AnnouncementUsageType> usageTypes) {
    _logger.i('Updating search usage types filter: $usageTypes');
    state = state.copyWith(usageTypes: usageTypes);
  }

  void updateDateRange(DateTime? startDate, DateTime? endDate) {
    _logger.i('Updating search date range: $startDate to $endDate');
    state = state.copyWith(startDate: startDate, endDate: endDate);
  }

  void updateReadStatus(bool? isRead) {
    _logger.i('Updating search read status filter: $isRead');
    state = state.copyWith(isRead: isRead);
  }

  void updatePinnedStatus(bool? isPinned) {
    _logger.i('Updating search pinned status filter: $isPinned');
    state = state.copyWith(isPinned: isPinned);
  }

  void clearAllFilters() {
    _logger.i('Clearing all search filters');
    state = const AnnouncementSearchFilters();
  }
}

/// Provider for advanced search filters
final announcementSearchFiltersProvider = StateNotifierProvider<AnnouncementSearchFiltersNotifier, AnnouncementSearchFilters>(
  (ref) => AnnouncementSearchFiltersNotifier(),
);

/// State notifier for recent searches
class RecentSearchesNotifier extends StateNotifier<List<String>> {
  RecentSearchesNotifier() : super([]) {
    _loadRecentSearches();
  }

  static const String _recentSearchesKey = 'announcement_recent_searches';
  static const int _maxRecentSearches = 10;

  Future<void> _loadRecentSearches() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final recentSearches = prefs.getStringList(_recentSearchesKey) ?? [];
      state = recentSearches;
      _logger.i('Loaded ${recentSearches.length} recent searches');
    } catch (e) {
      _logger.e('Error loading recent searches: $e');
    }
  }

  Future<void> addSearch(String query) async {
    if (query.trim().isEmpty) return;

    try {
      final trimmedQuery = query.trim();
      final updatedSearches = [trimmedQuery];
      
      // Add existing searches that don't match the new one
      for (final search in state) {
        if (search != trimmedQuery && updatedSearches.length < _maxRecentSearches) {
          updatedSearches.add(search);
        }
      }

      state = updatedSearches;

      // Save to SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      await prefs.setStringList(_recentSearchesKey, updatedSearches);
      
      _logger.i('Added search query: $trimmedQuery');
    } catch (e) {
      _logger.e('Error adding search query: $e');
    }
  }

  Future<void> removeSearch(String query) async {
    try {
      final updatedSearches = state.where((search) => search != query).toList();
      state = updatedSearches;

      // Save to SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      await prefs.setStringList(_recentSearchesKey, updatedSearches);
      
      _logger.i('Removed search query: $query');
    } catch (e) {
      _logger.e('Error removing search query: $e');
    }
  }

  Future<void> clearAll() async {
    try {
      state = [];

      // Clear from SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_recentSearchesKey);
      
      _logger.i('Cleared all recent searches');
    } catch (e) {
      _logger.e('Error clearing recent searches: $e');
    }
  }
}

/// Provider for recent searches
final recentSearchesProvider = StateNotifierProvider<RecentSearchesNotifier, List<String>>(
  (ref) => RecentSearchesNotifier(),
);

// ========== ADVANCED SEARCH PROVIDERS ==========

/// Provider for advanced search results with filters
final advancedSearchProvider = FutureProvider.family<List<AnnouncementModel>, String>((
  ref,
  query,
) async {
  final filters = ref.watch(announcementSearchFiltersProvider);
  final userId = ref.read(currentUserIdProvider);

  try {
    _logger.i('Performing advanced search with query: "$query" and filters: $filters');

    // Start with basic search results
    List<AnnouncementModel> announcements;
    if (query.trim().isEmpty) {
      announcements = await ref.read(userAnnouncementsProvider.future);
    } else {
      announcements = await ref.read(searchAnnouncementsProvider(query).future);
    }

    // Apply type filters
    if (filters.types.isNotEmpty) {
      announcements = announcements.where((announcement) => 
        filters.types.contains(announcement.type)
      ).toList();
    }

    // Apply priority filters
    if (filters.priorities.isNotEmpty) {
      announcements = announcements.where((announcement) => 
        filters.priorities.contains(announcement.priority)
      ).toList();
    }

    // Apply usage type filters
    if (filters.usageTypes.isNotEmpty) {
      announcements = announcements.where((announcement) => 
        filters.usageTypes.contains(announcement.usageType)
      ).toList();
    }

    // Apply date range filters
    if (filters.startDate != null) {
      announcements = announcements.where((announcement) => 
        announcement.createdAt.isAfter(filters.startDate!) ||
        announcement.createdAt.isAtSameMomentAs(filters.startDate!)
      ).toList();
    }

    if (filters.endDate != null) {
      final endOfDay = DateTime(filters.endDate!.year, filters.endDate!.month, filters.endDate!.day, 23, 59, 59);
      announcements = announcements.where((announcement) => 
        announcement.createdAt.isBefore(endOfDay) ||
        announcement.createdAt.isAtSameMomentAs(endOfDay)
      ).toList();
    }

    // Apply read status filter
    if (filters.isRead != null) {
      announcements = announcements.where((announcement) => 
        announcement.isReadBy(userId) == filters.isRead!
      ).toList();
    }

    // Apply pinned status filter
    if (filters.isPinned != null) {
      announcements = announcements.where((announcement) => 
        announcement.isPinned == filters.isPinned!
      ).toList();
    }

    _logger.i('Advanced search returned ${announcements.length} results');
    return announcements;
  } catch (e) {
    _logger.e('Error performing advanced search: $e');
    rethrow;
  }
});

/// Provider for search suggestions
final searchSuggestionsProvider = FutureProvider.family<List<SearchSuggestion>, String>((
  ref,
  query,
) async {
  try {
    _logger.i('Generating search suggestions for query: "$query"');

    final suggestions = <SearchSuggestion>[];
    final recentSearches = ref.read(recentSearchesProvider);

    // Add recent searches that match the query
    if (query.trim().isNotEmpty) {
      final matchingRecent = recentSearches.where((search) => 
        search.toLowerCase().contains(query.toLowerCase())
      ).take(3);

      for (final search in matchingRecent) {
        suggestions.add(SearchSuggestion(text: search, type: 'recent'));
      }
    } else {
      // If no query, show recent searches
      for (final search in recentSearches.take(5)) {
        suggestions.add(SearchSuggestion(text: search, type: 'recent'));
      }
    }

    // Add popular tags from announcements
    final allAnnouncements = await ref.read(userAnnouncementsProvider.future);
    final tagCounts = <String, int>{};

    for (final announcement in allAnnouncements) {
      for (final tag in announcement.tags) {
        if (query.trim().isEmpty || tag.toLowerCase().contains(query.toLowerCase())) {
          tagCounts[tag] = (tagCounts[tag] ?? 0) + 1;
        }
      }
    }

    // Sort tags by count and add top ones
    final sortedTags = tagCounts.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));

    for (final entry in sortedTags.take(3)) {
      suggestions.add(SearchSuggestion(
        text: entry.key,
        type: 'tag',
        count: entry.value,
      ));
    }

    _logger.i('Generated ${suggestions.length} search suggestions');
    return suggestions;
  } catch (e) {
    _logger.e('Error generating search suggestions: $e');
    rethrow;
  }
});
