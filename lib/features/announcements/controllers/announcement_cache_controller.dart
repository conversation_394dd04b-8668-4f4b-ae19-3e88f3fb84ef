import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logger/logger.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';

import '../models/announcement_model.dart';
import '../repositories/announcements_repository.dart';
import 'announcements_controller.dart';

/// Logger for announcement cache controller
final _logger = Logger();

/// Cache configuration
class AnnouncementCacheConfig {
  static const String cacheKeyPrefix = 'announcements_cache_';
  static const String lastUpdateKey = 'announcements_last_update';
  static const Duration cacheExpiration = Duration(minutes: 30);
  static const int maxCachedAnnouncements = 100;
}

/// Cache service for announcements
class AnnouncementCacheService {
  static final AnnouncementCacheService _instance = AnnouncementCacheService._internal();
  factory AnnouncementCacheService() => _instance;
  AnnouncementCacheService._internal();

  /// Cache announcements to local storage
  Future<void> cacheAnnouncements(List<AnnouncementModel> announcements) async {
    try {
      _logger.i('Caching ${announcements.length} announcements');

      final prefs = await SharedPreferences.getInstance();
      
      // Limit cache size to prevent storage issues
      final announcementsToCache = announcements.take(AnnouncementCacheConfig.maxCachedAnnouncements).toList();
      
      // Convert announcements to JSON
      final announcementsJson = announcementsToCache.map((a) => a.toJson()).toList();
      final cacheData = {
        'announcements': announcementsJson,
        'cachedAt': DateTime.now().toIso8601String(),
      };

      await prefs.setString(
        '${AnnouncementCacheConfig.cacheKeyPrefix}user_announcements',
        json.encode(cacheData),
      );

      // Update last cache time
      await prefs.setString(
        AnnouncementCacheConfig.lastUpdateKey,
        DateTime.now().toIso8601String(),
      );

      _logger.i('Successfully cached ${announcementsToCache.length} announcements');
    } catch (e) {
      _logger.e('Error caching announcements: $e');
    }
  }

  /// Get cached announcements from local storage
  Future<List<AnnouncementModel>?> getCachedAnnouncements() async {
    try {
      _logger.i('Retrieving cached announcements');

      final prefs = await SharedPreferences.getInstance();
      final cacheDataString = prefs.getString('${AnnouncementCacheConfig.cacheKeyPrefix}user_announcements');

      if (cacheDataString == null) {
        _logger.i('No cached announcements found');
        return null;
      }

      final cacheData = json.decode(cacheDataString) as Map<String, dynamic>;
      final cachedAtString = cacheData['cachedAt'] as String;
      final cachedAt = DateTime.parse(cachedAtString);

      // Check if cache is expired
      if (DateTime.now().difference(cachedAt) > AnnouncementCacheConfig.cacheExpiration) {
        _logger.i('Cache expired, removing cached data');
        await clearCache();
        return null;
      }

      final announcementsJson = cacheData['announcements'] as List<dynamic>;
      final announcements = announcementsJson
          .map((json) => AnnouncementModel.fromJson(json as Map<String, dynamic>))
          .toList();

      _logger.i('Retrieved ${announcements.length} cached announcements');
      return announcements;
    } catch (e) {
      _logger.e('Error retrieving cached announcements: $e');
      await clearCache(); // Clear corrupted cache
      return null;
    }
  }

  /// Check if cache is valid (not expired)
  Future<bool> isCacheValid() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final lastUpdateString = prefs.getString(AnnouncementCacheConfig.lastUpdateKey);

      if (lastUpdateString == null) return false;

      final lastUpdate = DateTime.parse(lastUpdateString);
      final isValid = DateTime.now().difference(lastUpdate) < AnnouncementCacheConfig.cacheExpiration;

      _logger.i('Cache validity check: $isValid');
      return isValid;
    } catch (e) {
      _logger.e('Error checking cache validity: $e');
      return false;
    }
  }

  /// Clear all cached announcements
  Future<void> clearCache() async {
    try {
      _logger.i('Clearing announcement cache');

      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('${AnnouncementCacheConfig.cacheKeyPrefix}user_announcements');
      await prefs.remove(AnnouncementCacheConfig.lastUpdateKey);

      _logger.i('Cache cleared successfully');
    } catch (e) {
      _logger.e('Error clearing cache: $e');
    }
  }

  /// Get cache statistics
  Future<Map<String, dynamic>> getCacheStats() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cacheDataString = prefs.getString('${AnnouncementCacheConfig.cacheKeyPrefix}user_announcements');
      final lastUpdateString = prefs.getString(AnnouncementCacheConfig.lastUpdateKey);

      if (cacheDataString == null) {
        return {
          'hasCachedData': false,
          'cachedCount': 0,
          'lastUpdate': null,
          'isValid': false,
        };
      }

      final cacheData = json.decode(cacheDataString) as Map<String, dynamic>;
      final announcementsJson = cacheData['announcements'] as List<dynamic>;
      final cachedAt = DateTime.parse(cacheData['cachedAt'] as String);
      final isValid = DateTime.now().difference(cachedAt) < AnnouncementCacheConfig.cacheExpiration;

      return {
        'hasCachedData': true,
        'cachedCount': announcementsJson.length,
        'lastUpdate': lastUpdateString,
        'cachedAt': cacheData['cachedAt'],
        'isValid': isValid,
        'expiresAt': cachedAt.add(AnnouncementCacheConfig.cacheExpiration).toIso8601String(),
      };
    } catch (e) {
      _logger.e('Error getting cache stats: $e');
      return {
        'hasCachedData': false,
        'cachedCount': 0,
        'lastUpdate': null,
        'isValid': false,
        'error': e.toString(),
      };
    }
  }
}

/// Provider for cache service
final announcementCacheServiceProvider = Provider<AnnouncementCacheService>((ref) {
  return AnnouncementCacheService();
});

/// Cached announcements provider with automatic cache management
final cachedUserAnnouncementsProvider = FutureProvider<List<AnnouncementModel>>((ref) async {
  final cacheService = ref.read(announcementCacheServiceProvider);
  final repository = ref.read(announcementsRepositoryProvider);
  final userId = ref.read(currentUserIdProvider);

  try {
    _logger.i('Loading announcements with cache support');

    // First, try to get cached data
    final cachedAnnouncements = await cacheService.getCachedAnnouncements();
    
    if (cachedAnnouncements != null) {
      _logger.i('Using cached announcements (${cachedAnnouncements.length} items)');
      
      // Return cached data immediately, but also refresh in background
      _refreshInBackground(ref, repository, userId, cacheService);
      return cachedAnnouncements;
    }

    // No valid cache, fetch from network
    _logger.i('No valid cache, fetching from network');
    final announcements = await repository.getAnnouncementsForUser(userId);
    
    // Cache the fresh data
    await cacheService.cacheAnnouncements(announcements);
    
    return announcements;
  } catch (e) {
    _logger.e('Error in cached announcements provider: $e');
    
    // Try to return cached data even if expired as fallback
    final cachedAnnouncements = await cacheService.getCachedAnnouncements();
    if (cachedAnnouncements != null) {
      _logger.i('Returning expired cache as fallback');
      return cachedAnnouncements;
    }
    
    rethrow;
  }
});

/// Background refresh function
void _refreshInBackground(
  Ref ref,
  AnnouncementsRepository repository,
  String userId,
  AnnouncementCacheService cacheService,
) {
  Future.microtask(() async {
    try {
      _logger.i('Refreshing announcements in background');
      final freshAnnouncements = await repository.getAnnouncementsForUser(userId);
      await cacheService.cacheAnnouncements(freshAnnouncements);
      
      // Invalidate the provider to trigger a rebuild with fresh data
      ref.invalidateSelf();
    } catch (e) {
      _logger.e('Background refresh failed: $e');
    }
  });
}

/// Provider for cache statistics
final announcementCacheStatsProvider = FutureProvider<Map<String, dynamic>>((ref) async {
  final cacheService = ref.read(announcementCacheServiceProvider);
  return await cacheService.getCacheStats();
});

/// Provider to clear cache
final clearAnnouncementCacheProvider = FutureProvider<void>((ref) async {
  final cacheService = ref.read(announcementCacheServiceProvider);
  await cacheService.clearCache();
  
  // Invalidate cached providers
  ref.invalidate(cachedUserAnnouncementsProvider);
  ref.invalidate(announcementCacheStatsProvider);
});

/// Provider to force refresh (bypass cache)
final forceRefreshAnnouncementsProvider = FutureProvider<List<AnnouncementModel>>((ref) async {
  final cacheService = ref.read(announcementCacheServiceProvider);
  final repository = ref.read(announcementsRepositoryProvider);
  final userId = ref.read(currentUserIdProvider);

  try {
    _logger.i('Force refreshing announcements (bypassing cache)');
    
    // Clear existing cache
    await cacheService.clearCache();
    
    // Fetch fresh data
    final announcements = await repository.getAnnouncementsForUser(userId);
    
    // Cache the fresh data
    await cacheService.cacheAnnouncements(announcements);
    
    // Invalidate cached provider
    ref.invalidate(cachedUserAnnouncementsProvider);
    
    return announcements;
  } catch (e) {
    _logger.e('Error in force refresh: $e');
    rethrow;
  }
});
