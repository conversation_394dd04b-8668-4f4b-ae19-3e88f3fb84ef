import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logger/logger.dart';
import 'package:share_plus/share_plus.dart';

import '../models/announcement_model.dart';
import '../controllers/announcements_controller.dart';
import '../controllers/announcement_read_status_controller.dart';
import '../controllers/announcement_cache_controller.dart';

/// Logger for announcement actions controller
final _logger = Logger();

/// Action result model
class ActionResult {
  final bool success;
  final String? message;
  final String? error;

  const ActionResult({required this.success, this.message, this.error});

  ActionResult.success([this.message]) : success = true, error = null;
  ActionResult.error(this.error) : success = false, message = null;

  @override
  String toString() {
    return 'ActionResult(success: $success, message: $message, error: $error)';
  }
}

/// Announcement actions service
class AnnouncementActionsService {
  static final AnnouncementActionsService _instance =
      AnnouncementActionsService._internal();
  factory AnnouncementActionsService() => _instance;
  AnnouncementActionsService._internal();

  /// Mark announcement as read
  Future<ActionResult> markAsRead(String announcementId, Ref ref) async {
    try {
      _logger.i('Marking announcement as read: $announcementId');

      final readStatusNotifier = ref.read(
        announcementReadStatusNotifierProvider.notifier,
      );
      await readStatusNotifier.markAsRead(announcementId);

      _logger.i('Successfully marked announcement as read');
      return ActionResult.success('Marked as read');
    } catch (e) {
      _logger.e('Error marking announcement as read: $e');
      return ActionResult.error('Failed to mark as read: $e');
    }
  }

  /// Share announcement
  Future<ActionResult> shareAnnouncement(
    AnnouncementModel announcement,
    BuildContext context,
  ) async {
    try {
      _logger.i('Sharing announcement: ${announcement.id}');

      final shareText =
          '''
${announcement.title}

${announcement.content}

From: ${announcement.authorName}
Date: ${_formatDate(announcement.createdAt)}

Shared via Scholara Student App
''';

      // ignore: deprecated_member_use
      await Share.share(shareText);

      _logger.i('Announcement shared successfully');
      return ActionResult.success('Shared successfully');
    } catch (e) {
      _logger.e('Error sharing announcement: $e');
      return ActionResult.error('Failed to share: $e');
    }
  }

  /// Copy announcement link to clipboard
  Future<ActionResult> copyAnnouncementLink(String announcementId) async {
    try {
      _logger.i('Copying announcement link: $announcementId');

      // Generate deep link (this would be actual deep link in production)
      final link = 'scholara://announcements/$announcementId';

      await Clipboard.setData(ClipboardData(text: link));

      _logger.i('Announcement link copied to clipboard');
      return ActionResult.success('Link copied to clipboard');
    } catch (e) {
      _logger.e('Error copying announcement link: $e');
      return ActionResult.error('Failed to copy link: $e');
    }
  }

  /// Add event to calendar
  Future<ActionResult> addToCalendar(AnnouncementModel announcement) async {
    try {
      _logger.i('Adding event to calendar: ${announcement.id}');

      // TODO: Implement calendar integration
      // This would use device calendar APIs to add the event

      _logger.i('Event added to calendar (placeholder implementation)');
      return ActionResult.success('Added to calendar');
    } catch (e) {
      _logger.e('Error adding to calendar: $e');
      return ActionResult.error('Failed to add to calendar: $e');
    }
  }

  /// Report announcement
  Future<ActionResult> reportAnnouncement(
    String announcementId,
    String reason,
    Ref ref,
  ) async {
    try {
      _logger.i('Reporting announcement: $announcementId, reason: $reason');

      // TODO: Implement actual reporting functionality
      // This would typically send a report to the server

      _logger.i('Announcement reported');
      return ActionResult.success('Report submitted');
    } catch (e) {
      _logger.e('Error reporting announcement: $e');
      return ActionResult.error('Failed to submit report: $e');
    }
  }

  /// Refresh announcements data
  Future<ActionResult> refreshAnnouncements(Ref ref) async {
    try {
      _logger.i('Refreshing announcements data');

      // Invalidate cache and providers
      ref.invalidate(cachedUserAnnouncementsProvider);
      ref.invalidate(userAnnouncementsProvider);

      // Wait for fresh data
      await ref.read(userAnnouncementsProvider.future);

      _logger.i('Announcements refreshed successfully');
      return ActionResult.success('Refreshed');
    } catch (e) {
      _logger.e('Error refreshing announcements: $e');
      return ActionResult.error('Failed to refresh: $e');
    }
  }

  /// Helper method to format date
  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year} ${date.hour}:${date.minute.toString().padLeft(2, '0')}';
  }
}

/// Provider for actions service
final announcementActionsServiceProvider = Provider<AnnouncementActionsService>(
  (ref) {
    return AnnouncementActionsService();
  },
);

// ========== ACTION PROVIDERS ==========

/// Provider to mark announcement as read
final markAnnouncementAsReadActionProvider =
    FutureProvider.family<ActionResult, String>((ref, announcementId) async {
      final actionsService = ref.read(announcementActionsServiceProvider);
      return await actionsService.markAsRead(announcementId, ref);
    });

/// Provider to share announcement
final shareAnnouncementActionProvider =
    FutureProvider.family<
      ActionResult,
      ({AnnouncementModel announcement, BuildContext context})
    >((ref, params) async {
      final actionsService = ref.read(announcementActionsServiceProvider);
      return await actionsService.shareAnnouncement(
        params.announcement,
        params.context,
      );
    });

/// Provider to copy announcement link
final copyAnnouncementLinkActionProvider =
    FutureProvider.family<ActionResult, String>((ref, announcementId) async {
      final actionsService = ref.read(announcementActionsServiceProvider);
      return await actionsService.copyAnnouncementLink(announcementId);
    });

/// Provider to add event to calendar
final addToCalendarActionProvider =
    FutureProvider.family<ActionResult, AnnouncementModel>((
      ref,
      announcement,
    ) async {
      final actionsService = ref.read(announcementActionsServiceProvider);
      return await actionsService.addToCalendar(announcement);
    });

/// Provider to report announcement
final reportAnnouncementActionProvider =
    FutureProvider.family<
      ActionResult,
      ({String announcementId, String reason})
    >((ref, params) async {
      final actionsService = ref.read(announcementActionsServiceProvider);
      return await actionsService.reportAnnouncement(
        params.announcementId,
        params.reason,
        ref,
      );
    });

/// Provider to refresh announcements
final refreshAnnouncementsActionProvider = FutureProvider<ActionResult>((
  ref,
) async {
  final actionsService = ref.read(announcementActionsServiceProvider);
  return await actionsService.refreshAnnouncements(ref);
});

// ========== BULK ACTION PROVIDERS ==========

/// Provider to mark multiple announcements as read
final markMultipleAsReadActionProvider =
    FutureProvider.family<ActionResult, List<String>>((
      ref,
      announcementIds,
    ) async {
      try {
        _logger.i('Marking ${announcementIds.length} announcements as read');

        final readStatusNotifier = ref.read(
          announcementReadStatusNotifierProvider.notifier,
        );
        await readStatusNotifier.markMultipleAsRead(announcementIds);

        _logger.i(
          'Successfully marked ${announcementIds.length} announcements as read',
        );
        return ActionResult.success(
          'Marked ${announcementIds.length} announcements as read',
        );
      } catch (e) {
        _logger.e('Error marking multiple announcements as read: $e');
        return ActionResult.error('Failed to mark announcements as read: $e');
      }
    });

/// Provider to mark all announcements as read
final markAllAsReadActionProvider = FutureProvider<ActionResult>((ref) async {
  try {
    _logger.i('Marking all announcements as read');

    await ref.read(markAllAnnouncementsAsReadProvider.future);

    _logger.i('Successfully marked all announcements as read');
    return ActionResult.success('All announcements marked as read');
  } catch (e) {
    _logger.e('Error marking all announcements as read: $e');
    return ActionResult.error('Failed to mark all as read: $e');
  }
});
