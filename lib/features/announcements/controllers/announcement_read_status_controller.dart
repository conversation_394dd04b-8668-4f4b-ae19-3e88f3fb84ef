import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logger/logger.dart';

import 'announcements_controller.dart';

/// Logger for announcement read status controller
final _logger = Logger();

/// Model for read status with additional metadata
class AnnouncementReadStatus {
  final String announcementId;
  final String userId;
  final bool isRead;
  final DateTime? readAt;

  const AnnouncementReadStatus({
    required this.announcementId,
    required this.userId,
    required this.isRead,
    this.readAt,
  });

  @override
  String toString() {
    return 'AnnouncementReadStatus(announcementId: $announcementId, userId: $userId, isRead: $isRead, readAt: $readAt)';
  }
}

// ========== READ STATUS MANAGEMENT ==========

/// State notifier for managing read status of announcements
class AnnouncementReadStatusNotifier extends StateNotifier<Map<String, bool>> {
  AnnouncementReadStatusNotifier(this._ref) : super({});

  final Ref _ref;

  /// Mark an announcement as read and update local state
  Future<void> markAsRead(String announcementId) async {
    try {
      _logger.i('Marking announcement as read: $announcementId');

      // Update local state immediately for better UX
      state = {...state, announcementId: true};

      // Call the repository to persist the change
      await _ref.read(markAnnouncementAsReadProvider(announcementId).future);

      _logger.i('Successfully marked announcement as read: $announcementId');
    } catch (e) {
      _logger.e('Error marking announcement as read: $e');

      // Revert local state on error
      state = {...state, announcementId: false};
      rethrow;
    }
  }

  /// Mark multiple announcements as read
  Future<void> markMultipleAsRead(List<String> announcementIds) async {
    try {
      _logger.i('Marking ${announcementIds.length} announcements as read');

      // Update local state immediately
      final newState = {...state};
      for (final id in announcementIds) {
        newState[id] = true;
      }
      state = newState;

      // Call repository for each announcement
      final futures = announcementIds.map(
        (id) => _ref.read(markAnnouncementAsReadProvider(id).future),
      );
      await Future.wait(futures);

      _logger.i(
        'Successfully marked ${announcementIds.length} announcements as read',
      );
    } catch (e) {
      _logger.e('Error marking multiple announcements as read: $e');

      // Revert local state on error
      final revertedState = {...state};
      for (final id in announcementIds) {
        revertedState[id] = false;
      }
      state = revertedState;
      rethrow;
    }
  }

  /// Load read status for multiple announcements
  Future<void> loadReadStatus(List<String> announcementIds) async {
    try {
      _logger.i(
        'Loading read status for ${announcementIds.length} announcements',
      );

      final readStatus = await _ref.read(
        multipleAnnouncementReadStatusProvider(announcementIds).future,
      );

      state = {...state, ...readStatus};
      _logger.i(
        'Successfully loaded read status for ${readStatus.length} announcements',
      );
    } catch (e) {
      _logger.e('Error loading read status: $e');
      rethrow;
    }
  }

  /// Check if an announcement is read (from local state first, then remote)
  bool isRead(String announcementId) {
    return state[announcementId] ?? false;
  }

  /// Clear all read status (useful for logout)
  void clearAll() {
    _logger.i('Clearing all read status');
    state = {};
  }
}

/// Provider for announcement read status state management
final announcementReadStatusNotifierProvider =
    StateNotifierProvider<AnnouncementReadStatusNotifier, Map<String, bool>>(
      (ref) => AnnouncementReadStatusNotifier(ref),
    );

// ========== ENHANCED READ STATUS PROVIDERS ==========

/// Provider to get read status with metadata for an announcement
final announcementReadStatusWithMetadataProvider =
    FutureProvider.family<AnnouncementReadStatus, String>((
      ref,
      announcementId,
    ) async {
      final repository = ref.read(announcementsRepositoryProvider);
      final userId = ref.read(currentUserIdProvider);

      try {
        _logger.i(
          'Getting read status with metadata for announcement: $announcementId',
        );

        final isRead = await repository.isAnnouncementReadByUser(
          announcementId,
          userId,
        );

        // Get the announcement to check read timestamp
        final announcement = await repository.getAnnouncementById(
          announcementId,
        );
        final readAt = announcement?.readTimestamps[userId];

        final readStatus = AnnouncementReadStatus(
          announcementId: announcementId,
          userId: userId,
          isRead: isRead,
          readAt: readAt,
        );

        _logger.i('Read status with metadata: $readStatus');
        return readStatus;
      } catch (e) {
        _logger.e('Error getting read status with metadata: $e');
        rethrow;
      }
    });

/// Provider to get unread count for the current user
final unreadAnnouncementCountProvider = FutureProvider<int>((ref) async {
  try {
    _logger.i('Calculating unread announcement count');

    final unreadAnnouncements = await ref.read(
      unreadAnnouncementsProvider.future,
    );
    final count = unreadAnnouncements.length;

    _logger.i('Unread announcement count: $count');
    return count;
  } catch (e) {
    _logger.e('Error calculating unread announcement count: $e');
    rethrow;
  }
});

/// Provider to get read percentage for the current user
final announcementReadPercentageProvider = FutureProvider<double>((ref) async {
  try {
    _logger.i('Calculating announcement read percentage');

    final allAnnouncements = await ref.read(userAnnouncementsProvider.future);
    final unreadAnnouncements = await ref.read(
      unreadAnnouncementsProvider.future,
    );

    if (allAnnouncements.isEmpty) {
      return 100.0;
    }

    final readCount = allAnnouncements.length - unreadAnnouncements.length;
    final percentage = (readCount / allAnnouncements.length) * 100;

    _logger.i(
      'Announcement read percentage: ${percentage.toStringAsFixed(1)}%',
    );
    return percentage;
  } catch (e) {
    _logger.e('Error calculating announcement read percentage: $e');
    rethrow;
  }
});

// ========== BULK OPERATIONS ==========

/// Provider to mark all announcements as read
final markAllAnnouncementsAsReadProvider = FutureProvider<void>((ref) async {
  try {
    _logger.i('Marking all announcements as read');

    final unreadAnnouncements = await ref.read(
      unreadAnnouncementsProvider.future,
    );
    final announcementIds = unreadAnnouncements.map((a) => a.id).toList();

    if (announcementIds.isEmpty) {
      _logger.i('No unread announcements to mark as read');
      return;
    }

    final readStatusNotifier = ref.read(
      announcementReadStatusNotifierProvider.notifier,
    );
    await readStatusNotifier.markMultipleAsRead(announcementIds);

    _logger.i(
      'Successfully marked all ${announcementIds.length} announcements as read',
    );
  } catch (e) {
    _logger.e('Error marking all announcements as read: $e');
    rethrow;
  }
});

/// Provider to mark all important announcements as read
final markAllImportantAnnouncementsAsReadProvider = FutureProvider<void>((
  ref,
) async {
  try {
    _logger.i('Marking all important announcements as read');

    final importantAnnouncements = await ref.read(
      importantAnnouncementsProvider.future,
    );
    final userId = ref.read(currentUserIdProvider);

    final unreadImportantIds = importantAnnouncements
        .where((announcement) => !announcement.isReadBy(userId))
        .map((a) => a.id)
        .toList();

    if (unreadImportantIds.isEmpty) {
      _logger.i('No unread important announcements to mark as read');
      return;
    }

    final readStatusNotifier = ref.read(
      announcementReadStatusNotifierProvider.notifier,
    );
    await readStatusNotifier.markMultipleAsRead(unreadImportantIds);

    _logger.i(
      'Successfully marked all ${unreadImportantIds.length} important announcements as read',
    );
  } catch (e) {
    _logger.e('Error marking all important announcements as read: $e');
    rethrow;
  }
});

// ========== READ STATUS ANALYTICS ==========

/// Provider to get read status analytics
final announcementReadAnalyticsProvider = FutureProvider<Map<String, dynamic>>((
  ref,
) async {
  try {
    _logger.i('Calculating announcement read analytics');

    final allAnnouncements = await ref.read(userAnnouncementsProvider.future);
    final unreadAnnouncements = await ref.read(
      unreadAnnouncementsProvider.future,
    );
    final importantAnnouncements = await ref.read(
      importantAnnouncementsProvider.future,
    );
    final userId = ref.read(currentUserIdProvider);

    final readCount = allAnnouncements.length - unreadAnnouncements.length;
    final unreadImportantCount = importantAnnouncements
        .where((announcement) => !announcement.isReadBy(userId))
        .length;

    final analytics = {
      'totalAnnouncements': allAnnouncements.length,
      'readAnnouncements': readCount,
      'unreadAnnouncements': unreadAnnouncements.length,
      'readPercentage': allAnnouncements.isEmpty
          ? 100.0
          : (readCount / allAnnouncements.length) * 100,
      'unreadImportantCount': unreadImportantCount,
      'hasUnreadImportant': unreadImportantCount > 0,
    };

    _logger.i('Read analytics calculated: $analytics');
    return analytics;
  } catch (e) {
    _logger.e('Error calculating read analytics: $e');
    rethrow;
  }
});
