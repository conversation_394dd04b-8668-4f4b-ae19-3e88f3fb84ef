import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logger/logger.dart';

import '../../../core/providers/auth_providers.dart';
import '../models/announcement_model.dart';
import '../repositories/announcements_repository.dart';
import '../enums/announcement_type.dart';
import '../enums/announcement_priority.dart';
import '../enums/announcement_usage_type.dart';

/// Logger for announcements controller
final _logger = Logger();

/// Provider for the announcements repository instance
final announcementsRepositoryProvider = Provider<AnnouncementsRepository>((
  ref,
) {
  return AnnouncementsRepository();
});

/// Provider for current user ID from authentication
final currentUserIdProvider = Provider<String>((ref) {
  // For testing purposes, use the test user ID
  // TODO: Remove this when authentication is fully implemented
  const testUserId = 'XRTanMcAUWSMq3mrRvve2Y9IMP12';

  final authState = ref.watch(authStateProvider);
  return authState.when(
    data: (state) => state.user?.id ?? testUserId,
    loading: () => testUserId,
    error: (_, __) => testUserId,
  );
});

// ========== CORE ANNOUNCEMENT PROVIDERS ==========

/// Provider to fetch all announcements for the current user
final userAnnouncementsProvider = FutureProvider<List<AnnouncementModel>>((
  ref,
) async {
  final repository = ref.read(announcementsRepositoryProvider);
  final userId = ref.read(currentUserIdProvider);

  try {
    _logger.i('Fetching announcements for current user: $userId');
    final announcements = await repository.getAnnouncementsForUser(userId);
    _logger.i(
      'Successfully fetched ${announcements.length} announcements for user $userId',
    );
    return announcements;
  } catch (e) {
    _logger.e('Error fetching announcements for user: $e');
    rethrow;
  }
});

/// Provider to fetch a specific announcement by ID
final announcementDetailProvider =
    FutureProvider.family<AnnouncementModel?, String>((
      ref,
      announcementId,
    ) async {
      final repository = ref.read(announcementsRepositoryProvider);

      try {
        _logger.i('Fetching announcement detail: $announcementId');
        final announcement = await repository.getAnnouncementById(
          announcementId,
        );
        if (announcement != null) {
          _logger.i('Successfully fetched announcement: ${announcement.title}');
        } else {
          _logger.w('Announcement not found: $announcementId');
        }
        return announcement;
      } catch (e) {
        _logger.e('Error fetching announcement detail: $e');
        rethrow;
      }
    });

/// Provider to fetch announcements for a specific classroom
final classroomAnnouncementsProvider =
    FutureProvider.family<List<AnnouncementModel>, String>((
      ref,
      classroomId,
    ) async {
      final repository = ref.read(announcementsRepositoryProvider);

      try {
        _logger.i('Fetching announcements for classroom: $classroomId');
        final announcements = await repository.getAnnouncementsForClassroom(
          classroomId,
        );
        _logger.i(
          'Successfully fetched ${announcements.length} announcements for classroom $classroomId',
        );
        return announcements;
      } catch (e) {
        _logger.e('Error fetching announcements for classroom: $e');
        rethrow;
      }
    });

// ========== FILTERED ANNOUNCEMENT PROVIDERS ==========

/// Provider to fetch unread announcements for the current user
final unreadAnnouncementsProvider = FutureProvider<List<AnnouncementModel>>((
  ref,
) async {
  final repository = ref.read(announcementsRepositoryProvider);
  final userId = ref.read(currentUserIdProvider);

  try {
    _logger.i('Fetching unread announcements for user: $userId');
    final announcements = await repository.getUnreadAnnouncements(userId);
    _logger.i(
      'Successfully fetched ${announcements.length} unread announcements for user $userId',
    );
    return announcements;
  } catch (e) {
    _logger.e('Error fetching unread announcements: $e');
    rethrow;
  }
});

/// Provider to fetch important announcements for the current user
final importantAnnouncementsProvider = FutureProvider<List<AnnouncementModel>>((
  ref,
) async {
  final repository = ref.read(announcementsRepositoryProvider);
  final userId = ref.read(currentUserIdProvider);

  try {
    _logger.i('Fetching important announcements for user: $userId');
    final announcements = await repository.getImportantAnnouncements(userId);
    _logger.i(
      'Successfully fetched ${announcements.length} important announcements for user $userId',
    );
    return announcements;
  } catch (e) {
    _logger.e('Error fetching important announcements: $e');
    rethrow;
  }
});

/// Provider to fetch pinned announcements for the current user
final pinnedAnnouncementsProvider = FutureProvider<List<AnnouncementModel>>((
  ref,
) async {
  final repository = ref.read(announcementsRepositoryProvider);
  final userId = ref.read(currentUserIdProvider);

  try {
    _logger.i('Fetching pinned announcements for user: $userId');
    final announcements = await repository.getPinnedAnnouncements(userId);
    _logger.i(
      'Successfully fetched ${announcements.length} pinned announcements for user $userId',
    );
    return announcements;
  } catch (e) {
    _logger.e('Error fetching pinned announcements: $e');
    rethrow;
  }
});

// ========== TYPE-BASED FILTERING PROVIDERS ==========

/// Provider to fetch announcements by type for the current user
final announcementsByTypeProvider =
    FutureProvider.family<List<AnnouncementModel>, AnnouncementType>((
      ref,
      type,
    ) async {
      final repository = ref.read(announcementsRepositoryProvider);
      final userId = ref.read(currentUserIdProvider);

      try {
        _logger.i(
          'Fetching announcements by type: ${type.displayName} for user: $userId',
        );
        final announcements = await repository.getAnnouncementsByType(
          userId,
          type,
        );
        _logger.i(
          'Successfully fetched ${announcements.length} announcements of type ${type.displayName}',
        );
        return announcements;
      } catch (e) {
        _logger.e('Error fetching announcements by type: $e');
        rethrow;
      }
    });

/// Provider to fetch announcements by usage type for the current user
final announcementsByUsageTypeProvider =
    FutureProvider.family<List<AnnouncementModel>, AnnouncementUsageType>((
      ref,
      usageType,
    ) async {
      final repository = ref.read(announcementsRepositoryProvider);
      final userId = ref.read(currentUserIdProvider);

      try {
        _logger.i(
          'Fetching announcements by usage type: ${usageType.displayName} for user: $userId',
        );
        final announcements = await repository.getAnnouncementsByUsageType(
          userId,
          usageType,
        );
        _logger.i(
          'Successfully fetched ${announcements.length} announcements of usage type ${usageType.displayName}',
        );
        return announcements;
      } catch (e) {
        _logger.e('Error fetching announcements by usage type: $e');
        rethrow;
      }
    });

/// Provider to fetch announcements by priority for the current user
final announcementsByPriorityProvider =
    FutureProvider.family<List<AnnouncementModel>, AnnouncementPriority>((
      ref,
      priority,
    ) async {
      final repository = ref.read(announcementsRepositoryProvider);
      final userId = ref.read(currentUserIdProvider);

      try {
        _logger.i(
          'Fetching announcements by priority: ${priority.displayName} for user: $userId',
        );
        final announcements = await repository.getAnnouncementsByPriority(
          userId,
          priority,
        );
        _logger.i(
          'Successfully fetched ${announcements.length} announcements of priority ${priority.displayName}',
        );
        return announcements;
      } catch (e) {
        _logger.e('Error fetching announcements by priority: $e');
        rethrow;
      }
    });

// ========== SEARCH PROVIDER ==========

/// Provider to search announcements for the current user
final searchAnnouncementsProvider =
    FutureProvider.family<List<AnnouncementModel>, String>((ref, query) async {
      final repository = ref.read(announcementsRepositoryProvider);
      final userId = ref.read(currentUserIdProvider);

      try {
        _logger.i(
          'Searching announcements for user: $userId with query: $query',
        );
        final announcements = await repository.searchAnnouncements(
          userId,
          query,
        );
        _logger.i(
          'Found ${announcements.length} announcements matching query: $query',
        );
        return announcements;
      } catch (e) {
        _logger.e('Error searching announcements: $e');
        rethrow;
      }
    });

// ========== READ STATUS PROVIDERS ==========

/// Provider to check if an announcement is read by the current user
final announcementReadStatusProvider = FutureProvider.family<bool, String>((
  ref,
  announcementId,
) async {
  final repository = ref.read(announcementsRepositoryProvider);
  final userId = ref.read(currentUserIdProvider);

  try {
    _logger.i(
      'Checking read status for announcement: $announcementId, user: $userId',
    );
    final isRead = await repository.isAnnouncementReadByUser(
      announcementId,
      userId,
    );
    _logger.i('Announcement read status: $isRead');
    return isRead;
  } catch (e) {
    _logger.e('Error checking announcement read status: $e');
    rethrow;
  }
});

/// Provider to get read status for multiple announcements
final multipleAnnouncementReadStatusProvider =
    FutureProvider.family<Map<String, bool>, List<String>>((
      ref,
      announcementIds,
    ) async {
      final repository = ref.read(announcementsRepositoryProvider);
      final userId = ref.read(currentUserIdProvider);

      try {
        _logger.i(
          'Getting read status for ${announcementIds.length} announcements for user: $userId',
        );
        final readStatus = await repository.getReadStatusForAnnouncements(
          announcementIds,
          userId,
        );
        _logger.i(
          'Successfully retrieved read status for ${readStatus.length} announcements',
        );
        return readStatus;
      } catch (e) {
        _logger.e('Error getting read status for announcements: $e');
        rethrow;
      }
    });

// ========== ACTION PROVIDERS ==========

/// Provider to mark an announcement as read
final markAnnouncementAsReadProvider = FutureProvider.family<void, String>((
  ref,
  announcementId,
) async {
  final repository = ref.read(announcementsRepositoryProvider);
  final userId = ref.read(currentUserIdProvider);

  try {
    _logger.i(
      'Marking announcement as read: $announcementId for user: $userId',
    );
    await repository.markAnnouncementAsRead(announcementId, userId);
    _logger.i('Successfully marked announcement as read');

    // Invalidate relevant providers to refresh data
    ref.invalidate(userAnnouncementsProvider);
    ref.invalidate(unreadAnnouncementsProvider);
    ref.invalidate(announcementReadStatusProvider(announcementId));
    ref.invalidate(announcementDetailProvider(announcementId));
  } catch (e) {
    _logger.e('Error marking announcement as read: $e');
    rethrow;
  }
});

// ========== STATISTICS PROVIDERS ==========

/// Provider to get announcement statistics for the current user
final announcementStatsProvider = FutureProvider<Map<String, int>>((ref) async {
  try {
    _logger.i('Calculating announcement statistics');

    final allAnnouncements = await ref.read(userAnnouncementsProvider.future);
    final unreadAnnouncements = await ref.read(
      unreadAnnouncementsProvider.future,
    );
    final importantAnnouncements = await ref.read(
      importantAnnouncementsProvider.future,
    );
    final pinnedAnnouncements = await ref.read(
      pinnedAnnouncementsProvider.future,
    );

    final stats = {
      'total': allAnnouncements.length,
      'unread': unreadAnnouncements.length,
      'important': importantAnnouncements.length,
      'pinned': pinnedAnnouncements.length,
      'today': allAnnouncements.where((announcement) {
        final today = DateTime.now();
        final announcementDate = announcement.createdAt;
        return announcementDate.year == today.year &&
            announcementDate.month == today.month &&
            announcementDate.day == today.day;
      }).length,
    };

    _logger.i('Announcement statistics calculated: $stats');
    return stats;
  } catch (e) {
    _logger.e('Error calculating announcement statistics: $e');
    rethrow;
  }
});

/// Provider to get announcement counts by type
final announcementCountsByTypeProvider =
    FutureProvider<Map<AnnouncementType, int>>((ref) async {
      try {
        _logger.i('Calculating announcement counts by type');

        final allAnnouncements = await ref.read(
          userAnnouncementsProvider.future,
        );
        final counts = <AnnouncementType, int>{};

        for (final type in AnnouncementType.values) {
          counts[type] = allAnnouncements
              .where((announcement) => announcement.type == type)
              .length;
        }

        _logger.i(
          'Announcement counts by type calculated: ${counts.length} types',
        );
        return counts;
      } catch (e) {
        _logger.e('Error calculating announcement counts by type: $e');
        rethrow;
      }
    });

/// Provider to get announcement counts by usage type
final announcementCountsByUsageTypeProvider =
    FutureProvider<Map<AnnouncementUsageType, int>>((ref) async {
      try {
        _logger.i('Calculating announcement counts by usage type');

        final allAnnouncements = await ref.read(
          userAnnouncementsProvider.future,
        );
        final counts = <AnnouncementUsageType, int>{};

        for (final usageType in AnnouncementUsageType.values) {
          counts[usageType] = allAnnouncements
              .where((announcement) => announcement.usageType == usageType)
              .length;
        }

        _logger.i(
          'Announcement counts by usage type calculated: ${counts.length} usage types',
        );
        return counts;
      } catch (e) {
        _logger.e('Error calculating announcement counts by usage type: $e');
        rethrow;
      }
    });
