import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_symbols_icons/symbols.dart';

/// Widget for displaying read/unread status of announcements
class ReadStatusIndicator extends StatelessWidget {
  final bool isRead;
  final bool showLabel;
  final double? iconSize;

  const ReadStatusIndicator({
    super.key,
    required this.isRead,
    this.showLabel = false,
    this.iconSize,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    if (isRead && !showLabel) {
      // For read announcements, don't show anything unless label is requested
      return const SizedBox.shrink();
    }

    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: showLabel ? 8.w : 4.w,
        vertical: 4.h,
      ),
      decoration: BoxDecoration(
        color: isRead 
            ? theme.colorScheme.surfaceContainerHighest.withValues(alpha: 0.5)
            : theme.colorScheme.primary.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(6.r),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            isRead ? Symbols.mark_email_read : Symbols.mark_email_unread,
            size: iconSize ?? 12.sp,
            color: isRead 
                ? theme.colorScheme.onSurface.withValues(alpha: 0.6)
                : theme.colorScheme.primary,
          ),
          if (showLabel) ...[
            SizedBox(width: 4.w),
            Text(
              isRead ? 'Read' : 'Unread',
              style: theme.textTheme.labelSmall?.copyWith(
                color: isRead 
                    ? theme.colorScheme.onSurface.withValues(alpha: 0.6)
                    : theme.colorScheme.primary,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ],
      ),
    );
  }
}

/// Simple unread dot indicator
class UnreadDotIndicator extends StatelessWidget {
  final bool isUnread;
  final double? size;

  const UnreadDotIndicator({
    super.key,
    required this.isUnread,
    this.size,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    if (!isUnread) {
      return const SizedBox.shrink();
    }

    return Container(
      width: size ?? 8.w,
      height: size ?? 8.w,
      decoration: BoxDecoration(
        color: theme.colorScheme.primary,
        shape: BoxShape.circle,
      ),
    );
  }
}

/// Read status with count for multiple items
class ReadStatusWithCount extends StatelessWidget {
  final int totalCount;
  final int readCount;
  final bool showPercentage;

  const ReadStatusWithCount({
    super.key,
    required this.totalCount,
    required this.readCount,
    this.showPercentage = false,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final unreadCount = totalCount - readCount;
    final readPercentage = totalCount > 0 ? (readCount / totalCount * 100).round() : 0;

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
      decoration: BoxDecoration(
        color: unreadCount > 0 
            ? theme.colorScheme.primary.withValues(alpha: 0.1)
            : theme.colorScheme.surfaceContainerHighest.withValues(alpha: 0.5),
        borderRadius: BorderRadius.circular(6.r),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            unreadCount > 0 ? Symbols.mark_email_unread : Symbols.mark_email_read,
            size: 12.sp,
            color: unreadCount > 0 
                ? theme.colorScheme.primary
                : theme.colorScheme.onSurface.withValues(alpha: 0.6),
          ),
          SizedBox(width: 4.w),
          Text(
            showPercentage 
                ? '$readPercentage% read'
                : '$readCount/$totalCount read',
            style: theme.textTheme.labelSmall?.copyWith(
              color: unreadCount > 0 
                  ? theme.colorScheme.primary
                  : theme.colorScheme.onSurface.withValues(alpha: 0.6),
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }
}
