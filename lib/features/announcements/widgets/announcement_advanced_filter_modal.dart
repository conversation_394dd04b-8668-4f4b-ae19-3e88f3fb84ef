import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_symbols_icons/symbols.dart';

import '../controllers/announcement_filter_controller.dart';
import '../enums/announcement_type.dart';
import '../enums/announcement_priority.dart';
import '../enums/announcement_usage_type.dart';

/// Advanced filter modal for announcements
class AnnouncementAdvancedFilterModal extends ConsumerStatefulWidget {
  const AnnouncementAdvancedFilterModal({super.key});

  @override
  ConsumerState<AnnouncementAdvancedFilterModal> createState() =>
      _AnnouncementAdvancedFilterModalState();
}

class _AnnouncementAdvancedFilterModalState
    extends ConsumerState<AnnouncementAdvancedFilterModal> {
  late AnnouncementAdvancedFilter _tempFilter;

  @override
  void initState() {
    super.initState();
    _tempFilter = ref.read(announcementAdvancedFilterProvider);
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      height: MediaQuery.of(context).size.height * 0.8,
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20.r)),
      ),
      child: Column(
        children: [
          _buildHeader(context, theme),
          Expanded(
            child: SingleChildScrollView(
              padding: EdgeInsets.all(16.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildTypeSection(theme),
                  SizedBox(height: 24.h),
                  _buildPrioritySection(theme),
                  SizedBox(height: 24.h),
                  _buildUsageTypeSection(theme),
                  SizedBox(height: 24.h),
                  _buildDateRangeSection(theme),
                  SizedBox(height: 24.h),
                  _buildStatusSection(theme),
                  SizedBox(height: 24.h),
                  _buildTagsSection(theme),
                ],
              ),
            ),
          ),
          _buildFooter(context, theme),
        ],
      ),
    );
  }

  Widget _buildHeader(BuildContext context, ThemeData theme) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        border: Border(bottom: BorderSide(color: theme.dividerColor, width: 1)),
      ),
      child: Row(
        children: [
          Text(
            'Advanced Filters',
            style: theme.textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const Spacer(),
          TextButton(
            onPressed: () {
              setState(() {
                _tempFilter = const AnnouncementAdvancedFilter();
              });
            },
            child: const Text('Clear All'),
          ),
          IconButton(
            onPressed: () => Navigator.of(context).pop(),
            icon: const Icon(Symbols.close),
          ),
        ],
      ),
    );
  }

  Widget _buildTypeSection(ThemeData theme) {
    return _buildFilterSection(
      theme,
      title: 'Announcement Types',
      icon: Symbols.category,
      child: Wrap(
        spacing: 8.w,
        runSpacing: 8.h,
        children: AnnouncementType.values.map((type) {
          final isSelected = _tempFilter.types.contains(type);
          return _buildFilterChip(
            theme,
            label: type.displayName,
            icon: type.icon,
            isSelected: isSelected,
            onTap: () {
              setState(() {
                final newTypes = Set<AnnouncementType>.from(_tempFilter.types);
                if (isSelected) {
                  newTypes.remove(type);
                } else {
                  newTypes.add(type);
                }
                _tempFilter = _tempFilter.copyWith(types: newTypes);
              });
            },
          );
        }).toList(),
      ),
    );
  }

  Widget _buildPrioritySection(ThemeData theme) {
    return _buildFilterSection(
      theme,
      title: 'Priority Levels',
      icon: Symbols.priority_high,
      child: Wrap(
        spacing: 8.w,
        runSpacing: 8.h,
        children: AnnouncementPriority.values.map((priority) {
          final isSelected = _tempFilter.priorities.contains(priority);
          return _buildFilterChip(
            theme,
            label: priority.displayName,
            icon: priority.icon,
            color: priority.color,
            isSelected: isSelected,
            onTap: () {
              setState(() {
                final newPriorities = Set<AnnouncementPriority>.from(
                  _tempFilter.priorities,
                );
                if (isSelected) {
                  newPriorities.remove(priority);
                } else {
                  newPriorities.add(priority);
                }
                _tempFilter = _tempFilter.copyWith(priorities: newPriorities);
              });
            },
          );
        }).toList(),
      ),
    );
  }

  Widget _buildUsageTypeSection(ThemeData theme) {
    return _buildFilterSection(
      theme,
      title: 'Usage Types',
      icon: Symbols.group,
      child: Wrap(
        spacing: 8.w,
        runSpacing: 8.h,
        children: AnnouncementUsageType.values.map((usageType) {
          final isSelected = _tempFilter.usageTypes.contains(usageType);
          return _buildFilterChip(
            theme,
            label: usageType.displayName,
            icon: usageType.icon,
            isSelected: isSelected,
            onTap: () {
              setState(() {
                final newUsageTypes = Set<AnnouncementUsageType>.from(
                  _tempFilter.usageTypes,
                );
                if (isSelected) {
                  newUsageTypes.remove(usageType);
                } else {
                  newUsageTypes.add(usageType);
                }
                _tempFilter = _tempFilter.copyWith(usageTypes: newUsageTypes);
              });
            },
          );
        }).toList(),
      ),
    );
  }

  Widget _buildDateRangeSection(ThemeData theme) {
    return _buildFilterSection(
      theme,
      title: 'Date Range',
      icon: Symbols.date_range,
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: _buildDateField(
                  theme,
                  label: 'Start Date',
                  date: _tempFilter.startDate,
                  onDateSelected: (date) {
                    setState(() {
                      _tempFilter = _tempFilter.copyWith(startDate: date);
                    });
                  },
                ),
              ),
              SizedBox(width: 16.w),
              Expanded(
                child: _buildDateField(
                  theme,
                  label: 'End Date',
                  date: _tempFilter.endDate,
                  onDateSelected: (date) {
                    setState(() {
                      _tempFilter = _tempFilter.copyWith(endDate: date);
                    });
                  },
                ),
              ),
            ],
          ),
          if (_tempFilter.startDate != null || _tempFilter.endDate != null) ...[
            SizedBox(height: 8.h),
            TextButton(
              onPressed: () {
                setState(() {
                  _tempFilter = _tempFilter.copyWith(
                    startDate: null,
                    endDate: null,
                  );
                });
              },
              child: const Text('Clear Date Range'),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildStatusSection(ThemeData theme) {
    return _buildFilterSection(
      theme,
      title: 'Status Filters',
      icon: Symbols.check_circle,
      child: Column(
        children: [
          _buildStatusToggle(
            theme,
            label: 'Read Status',
            value: _tempFilter.isRead,
            trueLabel: 'Read',
            falseLabel: 'Unread',
            onChanged: (value) {
              setState(() {
                _tempFilter = _tempFilter.copyWith(isRead: value);
              });
            },
          ),
          SizedBox(height: 12.h),
          _buildStatusToggle(
            theme,
            label: 'Pinned Status',
            value: _tempFilter.isPinned,
            trueLabel: 'Pinned',
            falseLabel: 'Not Pinned',
            onChanged: (value) {
              setState(() {
                _tempFilter = _tempFilter.copyWith(isPinned: value);
              });
            },
          ),
        ],
      ),
    );
  }

  Widget _buildTagsSection(ThemeData theme) {
    return _buildFilterSection(
      theme,
      title: 'Tags',
      icon: Symbols.tag,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Filter by tags (coming soon)',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
              fontStyle: FontStyle.italic,
            ),
          ),
          // TODO: Implement tag selection UI
        ],
      ),
    );
  }

  Widget _buildFilterSection(
    ThemeData theme, {
    required String title,
    required IconData icon,
    required Widget child,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(icon, size: 20.sp, color: theme.colorScheme.primary),
            SizedBox(width: 8.w),
            Text(
              title,
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        SizedBox(height: 12.h),
        child,
      ],
    );
  }

  Widget _buildFilterChip(
    ThemeData theme, {
    required String label,
    required IconData icon,
    Color? color,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
        decoration: BoxDecoration(
          color: isSelected
              ? (color ?? theme.colorScheme.primary)
              : theme.colorScheme.surface,
          borderRadius: BorderRadius.circular(20.r),
          border: Border.all(
            color: isSelected
                ? (color ?? theme.colorScheme.primary)
                : theme.dividerColor,
            width: 1,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              size: 16.sp,
              color: isSelected
                  ? Colors.white
                  : theme.colorScheme.onSurface.withValues(alpha: 0.7),
            ),
            SizedBox(width: 6.w),
            Text(
              label,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: isSelected ? Colors.white : theme.colorScheme.onSurface,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDateField(
    ThemeData theme, {
    required String label,
    required DateTime? date,
    required ValueChanged<DateTime?> onDateSelected,
  }) {
    return GestureDetector(
      onTap: () async {
        final selectedDate = await showDatePicker(
          context: context,
          initialDate: date ?? DateTime.now(),
          firstDate: DateTime.now().subtract(const Duration(days: 365)),
          lastDate: DateTime.now().add(const Duration(days: 365)),
        );
        if (selectedDate != null) {
          onDateSelected(selectedDate);
        }
      },
      child: Container(
        padding: EdgeInsets.all(12.w),
        decoration: BoxDecoration(
          color: theme.colorScheme.surface,
          borderRadius: BorderRadius.circular(8.r),
          border: Border.all(color: theme.dividerColor, width: 1),
        ),
        child: Row(
          children: [
            Icon(
              Symbols.calendar_today,
              size: 16.sp,
              color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
            ),
            SizedBox(width: 8.w),
            Expanded(
              child: Text(
                date != null ? '${date.day}/${date.month}/${date.year}' : label,
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: date != null
                      ? theme.colorScheme.onSurface
                      : theme.colorScheme.onSurface.withValues(alpha: 0.6),
                ),
              ),
            ),
            if (date != null)
              GestureDetector(
                onTap: () => onDateSelected(null),
                child: Icon(
                  Symbols.close,
                  size: 16.sp,
                  color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusToggle(
    ThemeData theme, {
    required String label,
    required bool? value,
    required String trueLabel,
    required String falseLabel,
    required ValueChanged<bool?> onChanged,
  }) {
    return Row(
      children: [
        Expanded(
          child: Text(
            label,
            style: theme.textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        SegmentedButton<bool?>(
          segments: [
            ButtonSegment<bool?>(
              value: null,
              label: Text('All', style: theme.textTheme.bodySmall),
            ),
            ButtonSegment<bool?>(
              value: true,
              label: Text(trueLabel, style: theme.textTheme.bodySmall),
            ),
            ButtonSegment<bool?>(
              value: false,
              label: Text(falseLabel, style: theme.textTheme.bodySmall),
            ),
          ],
          selected: {value},
          onSelectionChanged: (Set<bool?> selection) {
            onChanged(selection.first);
          },
          style: SegmentedButton.styleFrom(
            visualDensity: VisualDensity.compact,
          ),
        ),
      ],
    );
  }

  Widget _buildFooter(BuildContext context, ThemeData theme) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        border: Border(top: BorderSide(color: theme.dividerColor, width: 1)),
      ),
      child: Row(
        children: [
          Expanded(
            child: OutlinedButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
          ),
          SizedBox(width: 16.w),
          Expanded(
            child: ElevatedButton(
              onPressed: () {
                ref
                    .read(announcementAdvancedFilterProvider.notifier)
                    .setFilter(_tempFilter);
                Navigator.of(context).pop();
              },
              child: const Text('Apply Filters'),
            ),
          ),
        ],
      ),
    );
  }
}
