import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_symbols_icons/symbols.dart';

import '../controllers/announcement_filter_controller.dart';
import '../enums/announcement_type.dart';
import '../enums/announcement_priority.dart';
import '../enums/announcement_usage_type.dart';

/// Widget displaying active filter chips with ability to remove them
class AnnouncementActiveFilterChips extends ConsumerWidget {
  final VoidCallback? onAdvancedFilterTap;

  const AnnouncementActiveFilterChips({
    super.key,
    this.onAdvancedFilterTap,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final advancedFilter = ref.watch(announcementAdvancedFilterProvider);
    final advancedFilterNotifier = ref.read(announcementAdvancedFilterProvider.notifier);

    if (!advancedFilter.hasActiveFilters) {
      return const SizedBox.shrink();
    }

    final chips = <Widget>[];

    // Type filters
    for (final type in advancedFilter.types) {
      chips.add(_buildFilterChip(
        context,
        theme,
        label: type.displayName,
        icon: type.icon,
        onRemove: () => advancedFilterNotifier.removeType(type),
      ));
    }

    // Priority filters
    for (final priority in advancedFilter.priorities) {
      chips.add(_buildFilterChip(
        context,
        theme,
        label: priority.displayName,
        icon: priority.icon,
        color: priority.color,
        onRemove: () => advancedFilterNotifier.removePriority(priority),
      ));
    }

    // Usage type filters
    for (final usageType in advancedFilter.usageTypes) {
      chips.add(_buildFilterChip(
        context,
        theme,
        label: usageType.displayName,
        icon: usageType.icon,
        onRemove: () => advancedFilterNotifier.removeUsageType(usageType),
      ));
    }

    // Date range filter
    if (advancedFilter.startDate != null || advancedFilter.endDate != null) {
      final dateLabel = _formatDateRange(advancedFilter.startDate, advancedFilter.endDate);
      chips.add(_buildFilterChip(
        context,
        theme,
        label: dateLabel,
        icon: Symbols.date_range,
        onRemove: () => advancedFilterNotifier.clearDateRange(),
      ));
    }

    // Read status filter
    if (advancedFilter.isRead != null) {
      chips.add(_buildFilterChip(
        context,
        theme,
        label: advancedFilter.isRead! ? 'Read' : 'Unread',
        icon: advancedFilter.isRead! ? Symbols.mark_email_read : Symbols.mark_email_unread,
        onRemove: () => advancedFilterNotifier.setReadStatus(null),
      ));
    }

    // Pinned status filter
    if (advancedFilter.isPinned != null) {
      chips.add(_buildFilterChip(
        context,
        theme,
        label: advancedFilter.isPinned! ? 'Pinned' : 'Not Pinned',
        icon: advancedFilter.isPinned! ? Symbols.push_pin : Symbols.push_pin,
        onRemove: () => advancedFilterNotifier.setPinnedStatus(null),
      ));
    }

    // Tags filters
    for (final tag in advancedFilter.tags) {
      chips.add(_buildFilterChip(
        context,
        theme,
        label: '#$tag',
        icon: Symbols.tag,
        onRemove: () => advancedFilterNotifier.removeTag(tag),
      ));
    }

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                'Active Filters',
                style: theme.textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: theme.colorScheme.onSurface.withValues(alpha: 0.8),
                ),
              ),
              const Spacer(),
              TextButton.icon(
                onPressed: () => advancedFilterNotifier.clearAllFilters(),
                icon: Icon(Symbols.clear_all, size: 16.sp),
                label: const Text('Clear All'),
                style: TextButton.styleFrom(
                  foregroundColor: theme.colorScheme.error,
                  padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
                ),
              ),
            ],
          ),
          SizedBox(height: 8.h),
          Wrap(
            spacing: 8.w,
            runSpacing: 8.h,
            children: [
              ...chips,
              // Advanced filter button
              if (onAdvancedFilterTap != null)
                _buildAdvancedFilterButton(context, theme, advancedFilter),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildFilterChip(
    BuildContext context,
    ThemeData theme, {
    required String label,
    required IconData icon,
    Color? color,
    required VoidCallback onRemove,
  }) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
      decoration: BoxDecoration(
        color: color?.withValues(alpha: 0.1) ?? theme.colorScheme.primaryContainer,
        borderRadius: BorderRadius.circular(16.r),
        border: Border.all(
          color: color?.withValues(alpha: 0.3) ?? theme.colorScheme.primary.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 14.sp,
            color: color ?? theme.colorScheme.onPrimaryContainer,
          ),
          SizedBox(width: 4.w),
          Text(
            label,
            style: theme.textTheme.bodySmall?.copyWith(
              color: color ?? theme.colorScheme.onPrimaryContainer,
              fontWeight: FontWeight.w500,
            ),
          ),
          SizedBox(width: 4.w),
          GestureDetector(
            onTap: onRemove,
            child: Icon(
              Symbols.close,
              size: 14.sp,
              color: color ?? theme.colorScheme.onPrimaryContainer,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAdvancedFilterButton(
    BuildContext context,
    ThemeData theme,
    AnnouncementAdvancedFilter filter,
  ) {
    return GestureDetector(
      onTap: onAdvancedFilterTap,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
        decoration: BoxDecoration(
          color: theme.colorScheme.secondaryContainer,
          borderRadius: BorderRadius.circular(16.r),
          border: Border.all(
            color: theme.colorScheme.secondary.withValues(alpha: 0.3),
            width: 1,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Symbols.tune,
              size: 14.sp,
              color: theme.colorScheme.onSecondaryContainer,
            ),
            SizedBox(width: 4.w),
            Text(
              'More Filters',
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onSecondaryContainer,
                fontWeight: FontWeight.w500,
              ),
            ),
            if (filter.activeFilterCount > 0) ...[
              SizedBox(width: 4.w),
              Container(
                padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 2.h),
                decoration: BoxDecoration(
                  color: theme.colorScheme.secondary,
                  borderRadius: BorderRadius.circular(8.r),
                ),
                child: Text(
                  '${filter.activeFilterCount}',
                  style: theme.textTheme.labelSmall?.copyWith(
                    color: theme.colorScheme.onSecondary,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  String _formatDateRange(DateTime? startDate, DateTime? endDate) {
    if (startDate != null && endDate != null) {
      return '${_formatDate(startDate)} - ${_formatDate(endDate)}';
    } else if (startDate != null) {
      return 'From ${_formatDate(startDate)}';
    } else if (endDate != null) {
      return 'Until ${_formatDate(endDate)}';
    }
    return 'Date Range';
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}

/// Quick filter chips for common filter options
class AnnouncementQuickFilterChips extends ConsumerWidget {
  const AnnouncementQuickFilterChips({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final advancedFilter = ref.watch(announcementAdvancedFilterProvider);
    final advancedFilterNotifier = ref.read(announcementAdvancedFilterProvider.notifier);

    final quickFilters = [
      _QuickFilter(
        label: 'Urgent',
        icon: Symbols.priority_high,
        color: Colors.red,
        isActive: advancedFilter.priorities.contains(AnnouncementPriority.urgent),
        onTap: () {
          if (advancedFilter.priorities.contains(AnnouncementPriority.urgent)) {
            advancedFilterNotifier.removePriority(AnnouncementPriority.urgent);
          } else {
            advancedFilterNotifier.addPriority(AnnouncementPriority.urgent);
          }
        },
      ),
      _QuickFilter(
        label: 'Unread',
        icon: Symbols.mark_email_unread,
        isActive: advancedFilter.isRead == false,
        onTap: () {
          advancedFilterNotifier.setReadStatus(
            advancedFilter.isRead == false ? null : false,
          );
        },
      ),
      _QuickFilter(
        label: 'Pinned',
        icon: Symbols.push_pin,
        isActive: advancedFilter.isPinned == true,
        onTap: () {
          advancedFilterNotifier.setPinnedStatus(
            advancedFilter.isPinned == true ? null : true,
          );
        },
      ),
      _QuickFilter(
        label: 'Notice',
        icon: Symbols.info,
        isActive: advancedFilter.types.contains(AnnouncementType.notice),
        onTap: () {
          if (advancedFilter.types.contains(AnnouncementType.notice)) {
            advancedFilterNotifier.removeType(AnnouncementType.notice);
          } else {
            advancedFilterNotifier.addType(AnnouncementType.notice);
          }
        },
      ),
    ];

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
      child: Wrap(
        spacing: 8.w,
        runSpacing: 8.h,
        children: quickFilters.map((filter) {
          return GestureDetector(
            onTap: filter.onTap,
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
              decoration: BoxDecoration(
                color: filter.isActive
                    ? (filter.color ?? theme.colorScheme.primary)
                    : theme.colorScheme.surface,
                borderRadius: BorderRadius.circular(16.r),
                border: Border.all(
                  color: filter.isActive
                      ? (filter.color ?? theme.colorScheme.primary)
                      : theme.dividerColor,
                  width: 1,
                ),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    filter.icon,
                    size: 14.sp,
                    color: filter.isActive
                        ? Colors.white
                        : theme.colorScheme.onSurface.withValues(alpha: 0.7),
                  ),
                  SizedBox(width: 4.w),
                  Text(
                    filter.label,
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: filter.isActive
                          ? Colors.white
                          : theme.colorScheme.onSurface,
                      fontWeight: filter.isActive ? FontWeight.w600 : FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          );
        }).toList(),
      ),
    );
  }
}

class _QuickFilter {
  final String label;
  final IconData icon;
  final Color? color;
  final bool isActive;
  final VoidCallback onTap;

  const _QuickFilter({
    required this.label,
    required this.icon,
    this.color,
    required this.isActive,
    required this.onTap,
  });
}
