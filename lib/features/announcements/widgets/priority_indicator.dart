import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../enums/announcement_priority.dart';

/// Widget for displaying announcement priority with appropriate visual indicators
class PriorityIndicator extends StatelessWidget {
  final AnnouncementPriority priority;
  final bool showLabel;
  final double? iconSize;

  const PriorityIndicator({
    super.key,
    required this.priority,
    this.showLabel = false,
    this.iconSize,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    // Don't show indicator for normal priority
    if (priority == AnnouncementPriority.normal) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: showLabel ? 8.w : 6.w,
        vertical: 4.h,
      ),
      decoration: BoxDecoration(
        color: priority.color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(6.r),
        border: Border.all(
          color: priority.color.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            priority.icon,
            size: iconSize ?? 14.sp,
            color: priority.color,
          ),
          if (showLabel) ...[
            SizedBox(width: 4.w),
            Text(
              priority.displayName,
              style: theme.textTheme.labelSmall?.copyWith(
                color: priority.color,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ],
      ),
    );
  }
}

/// Large priority indicator for detail views
class LargePriorityIndicator extends StatelessWidget {
  final AnnouncementPriority priority;

  const LargePriorityIndicator({
    super.key,
    required this.priority,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    // Don't show indicator for normal priority
    if (priority == AnnouncementPriority.normal) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
      decoration: BoxDecoration(
        color: priority.color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(
          color: priority.color.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            priority.icon,
            size: 18.sp,
            color: priority.color,
          ),
          SizedBox(width: 8.w),
          Text(
            priority.displayName.toUpperCase(),
            style: theme.textTheme.labelMedium?.copyWith(
              color: priority.color,
              fontWeight: FontWeight.w700,
              letterSpacing: 0.5,
            ),
          ),
        ],
      ),
    );
  }
}

/// Priority badge for compact displays
class PriorityBadge extends StatelessWidget {
  final AnnouncementPriority priority;

  const PriorityBadge({
    super.key,
    required this.priority,
  });

  @override
  Widget build(BuildContext context) {
    // Don't show badge for normal priority
    if (priority == AnnouncementPriority.normal) {
      return const SizedBox.shrink();
    }

    return Container(
      width: 8.w,
      height: 8.w,
      decoration: BoxDecoration(
        color: priority.color,
        shape: BoxShape.circle,
      ),
    );
  }
}
