import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_symbols_icons/symbols.dart';

import '../models/announcement_model.dart';
import '../enums/announcement_priority.dart';
import '../enums/announcement_type.dart';
import 'priority_indicator.dart';
import 'read_status_indicator.dart';

/// List tile widget for displaying announcement information in lists
class AnnouncementListTile extends StatelessWidget {
  final AnnouncementModel announcement;
  final VoidCallback? onTap;
  final VoidCallback? onMarkAsRead;
  final VoidCallback? onShare;
  final bool showActions;

  const AnnouncementListTile({
    super.key,
    required this.announcement,
    this.onTap,
    this.onMarkAsRead,
    this.onShare,
    this.showActions = true,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isRead = _isAnnouncementRead();

    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w, vertical: 4.h),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: theme.dividerColor, width: 1),
        boxShadow: [
          BoxShadow(
            color: theme.shadowColor.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(12.r),
          child: Padding(
            padding: EdgeInsets.all(16.w),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header row with type, priority, and read status
                Row(
                  children: [
                    // Announcement type chip
                    _buildTypeChip(theme),

                    SizedBox(width: 8.w),

                    // Priority indicator
                    if (announcement.priority != AnnouncementPriority.normal)
                      PriorityIndicator(priority: announcement.priority),

                    const Spacer(),

                    // Read status indicator
                    ReadStatusIndicator(isRead: isRead),

                    // Pinned indicator
                    if (announcement.isPinned) ...[
                      SizedBox(width: 8.w),
                      Icon(
                        Symbols.push_pin,
                        size: 16.sp,
                        color: theme.colorScheme.primary,
                      ),
                    ],
                  ],
                ),

                SizedBox(height: 12.h),

                // Title
                Text(
                  announcement.title,
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: isRead ? FontWeight.w500 : FontWeight.w600,
                    color: isRead
                        ? theme.colorScheme.onSurface.withValues(alpha: 0.8)
                        : theme.colorScheme.onSurface,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),

                SizedBox(height: 8.h),

                // Content preview
                Text(
                  announcement.content,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),

                SizedBox(height: 12.h),

                // Footer row with author, time, and actions
                Row(
                  children: [
                    // Author info
                    Expanded(child: _buildAuthorInfo(theme)),

                    // Time
                    Text(
                      _formatTime(announcement.createdAt),
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.onSurface.withValues(
                          alpha: 0.5,
                        ),
                      ),
                    ),

                    // Quick actions
                    if (showActions) ...[
                      SizedBox(width: 8.w),
                      _buildQuickActions(theme),
                    ],
                  ],
                ),

                // Attachments indicator
                if (announcement.attachmentIds.isNotEmpty) ...[
                  SizedBox(height: 8.h),
                  _buildAttachmentsIndicator(theme),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTypeChip(ThemeData theme) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
      decoration: BoxDecoration(
        color: announcement.type.color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(6.r),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            announcement.type.icon,
            size: 12.sp,
            color: announcement.type.color,
          ),
          SizedBox(width: 4.w),
          Text(
            announcement.type.displayName,
            style: theme.textTheme.labelSmall?.copyWith(
              color: announcement.type.color,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAuthorInfo(ThemeData theme) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(
          Symbols.person,
          size: 14.sp,
          color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
        ),
        SizedBox(width: 4.w),
        Flexible(
          child: Text(
            announcement.authorName,
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
              fontWeight: FontWeight.w500,
            ),
            overflow: TextOverflow.ellipsis,
          ),
        ),
        if (announcement.authorRole != null) ...[
          Text(
            ' • ${announcement.authorRole}',
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildQuickActions(ThemeData theme) {
    return PopupMenuButton<String>(
      onSelected: _handleQuickAction,
      icon: Icon(
        Symbols.more_vert,
        size: 18.sp,
        color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
      ),
      itemBuilder: (context) => [
        if (!_isAnnouncementRead())
          PopupMenuItem(
            value: 'mark_read',
            child: Row(
              children: [
                Icon(Symbols.mark_email_read, size: 16.sp),
                SizedBox(width: 8.w),
                const Text('Mark as Read'),
              ],
            ),
          ),

        PopupMenuItem(
          value: 'share',
          child: Row(
            children: [
              Icon(Symbols.share, size: 16.sp),
              SizedBox(width: 8.w),
              const Text('Share'),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildAttachmentsIndicator(ThemeData theme) {
    return Row(
      children: [
        Icon(
          Symbols.attach_file,
          size: 14.sp,
          color: theme.colorScheme.primary,
        ),
        SizedBox(width: 4.w),
        Text(
          '${announcement.attachmentIds.length} attachment${announcement.attachmentIds.length == 1 ? '' : 's'}',
          style: theme.textTheme.bodySmall?.copyWith(
            color: theme.colorScheme.primary,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  bool _isAnnouncementRead() {
    // TODO: Replace with actual user ID when authentication is implemented
    const currentUserId = 'XRTanMcAUWSMq3mrRvve2Y9IMP12';
    return announcement.readBy.contains(currentUserId);
  }

  String _formatTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inDays < 1) {
      return '${difference.inHours}h ago';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}d ago';
    } else {
      return '${dateTime.day}/${dateTime.month}/${dateTime.year}';
    }
  }

  void _handleQuickAction(String action) {
    switch (action) {
      case 'mark_read':
        onMarkAsRead?.call();
        break;
      case 'share':
        onShare?.call();
        break;
    }
  }
}
