import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_symbols_icons/symbols.dart';
import 'package:go_router/go_router.dart';
import '../../../../core/routes/app_routes.dart';

import '../../controllers/announcements_controller.dart';
import '../../controllers/announcement_read_status_controller.dart';
import '../../models/announcement_model.dart';
import '../../enums/announcement_type.dart';
import '../../enums/announcement_priority.dart';

/// Detailed view of individual announcements with full content and actions
class AnnouncementDetailScreen extends ConsumerStatefulWidget {
  final String announcementId;

  const AnnouncementDetailScreen({super.key, required this.announcementId});

  @override
  ConsumerState<AnnouncementDetailScreen> createState() =>
      _AnnouncementDetailScreenState();
}

class _AnnouncementDetailScreenState
    extends ConsumerState<AnnouncementDetailScreen> {
  @override
  void initState() {
    super.initState();
    _markAsReadOnView();
  }

  void _markAsReadOnView() {
    // Mark announcement as read when viewed
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final readStatusNotifier = ref.read(
        announcementReadStatusNotifierProvider.notifier,
      );
      readStatusNotifier.markAsRead(widget.announcementId);
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final announcementAsync = ref.watch(
      announcementDetailProvider(widget.announcementId),
    );

    return Scaffold(
      backgroundColor: theme.colorScheme.surface,
      appBar: _buildAppBar(theme),
      body: SafeArea(
        child: RefreshIndicator(
          onRefresh: _onRefresh,
          child: announcementAsync.when(
            data: (announcement) => announcement != null
                ? _buildAnnouncementDetail(announcement)
                : _buildNotFoundState(),
            loading: () => _buildLoadingState(),
            error: (error, stackTrace) => _buildErrorState(),
          ),
        ),
      ),
      floatingActionButton: announcementAsync.maybeWhen(
        data: (announcement) => announcement != null
            ? _buildFloatingActionButton(announcement)
            : null,
        orElse: () => null,
      ),
    );
  }

  Future<void> _onRefresh() async {
    // Invalidate the announcement detail provider to refresh data
    ref.invalidate(announcementDetailProvider(widget.announcementId));

    // Wait for the new data to load
    await ref.read(announcementDetailProvider(widget.announcementId).future);
  }

  Widget _buildLoadingState() {
    return const Center(child: CircularProgressIndicator());
  }

  Widget _buildNotFoundState() {
    final theme = Theme.of(context);

    return Center(
      child: Padding(
        padding: EdgeInsets.all(24.w),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Symbols.announcement,
              size: 64.sp,
              color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
            ),
            SizedBox(height: 16.h),
            Text(
              'Announcement not found',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: 8.h),
            Text(
              'This announcement may have been removed or you may not have access to it.',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 24.h),
            ElevatedButton.icon(
              onPressed: () => Navigator.of(context).pop(),
              icon: Icon(Symbols.arrow_back, size: 20.sp),
              label: const Text('Go Back'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorState() {
    final theme = Theme.of(context);

    return Center(
      child: Padding(
        padding: EdgeInsets.all(24.w),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Symbols.error_outline,
              size: 64.sp,
              color: theme.colorScheme.error,
            ),
            SizedBox(height: 16.h),
            Text(
              'Failed to load announcement',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: 8.h),
            Text(
              'Please check your connection and try again',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 24.h),
            ElevatedButton.icon(
              onPressed: () {
                ref.invalidate(
                  announcementDetailProvider(widget.announcementId),
                );
              },
              icon: Icon(Symbols.refresh, size: 20.sp),
              label: const Text('Retry'),
            ),
          ],
        ),
      ),
    );
  }

  AppBar _buildAppBar(ThemeData theme) {
    return AppBar(
      backgroundColor: theme.colorScheme.surface,
      elevation: 0,
      leading: IconButton(
        onPressed: () => Navigator.of(context).pop(),
        icon: Icon(Symbols.arrow_back, size: 24.sp),
      ),
      title: Text(
        'Announcement',
        style: theme.textTheme.titleLarge?.copyWith(
          fontWeight: FontWeight.w600,
        ),
      ),
      actions: [
        // Share button
        IconButton(
          onPressed: _onSharePressed,
          icon: Icon(Symbols.share, size: 24.sp),
          tooltip: 'Share announcement',
        ),

        // More options
        PopupMenuButton<String>(
          onSelected: _handleMenuAction,
          icon: Icon(Symbols.more_vert, size: 24.sp),
          itemBuilder: (context) => [
            PopupMenuItem(
              value: 'discuss',
              child: Row(
                children: [
                  Icon(Symbols.chat, size: 18.sp),
                  SizedBox(width: 8.w),
                  const Text('Discuss'),
                ],
              ),
            ),
            PopupMenuItem(
              value: 'report',
              child: Row(
                children: [
                  Icon(Symbols.flag, size: 18.sp),
                  SizedBox(width: 8.w),
                  const Text('Report'),
                ],
              ),
            ),
          ],
        ),

        SizedBox(width: 8.w),
      ],
    );
  }

  Widget _buildAnnouncementDetail(AnnouncementModel announcement) {
    return SingleChildScrollView(
      padding: EdgeInsets.all(16.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildAnnouncementHeader(announcement),
          SizedBox(height: 24.h),
          _buildAnnouncementContent(announcement),
          SizedBox(height: 24.h),
          _buildAnnouncementMetadata(announcement),
          if (announcement.attachmentIds.isNotEmpty) ...[
            SizedBox(height: 24.h),
            _buildAttachments(announcement),
          ],
          if (announcement.tags.isNotEmpty) ...[
            SizedBox(height: 24.h),
            _buildTags(announcement),
          ],
          SizedBox(height: 100.h), // Space for FAB
        ],
      ),
    );
  }

  Widget _buildAnnouncementHeader(AnnouncementModel announcement) {
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Type and priority badges
        Row(
          children: [
            _buildTypeBadge(announcement.type),
            SizedBox(width: 8.w),
            _buildPriorityBadge(announcement.priority),
            const Spacer(),
            if (announcement.isPinned)
              Icon(
                Symbols.push_pin,
                size: 20.sp,
                color: theme.colorScheme.primary,
              ),
          ],
        ),
        SizedBox(height: 16.h),

        // Title
        Text(
          announcement.title,
          style: theme.textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.w700,
          ),
        ),
        SizedBox(height: 12.h),

        // Author and date info
        Row(
          children: [
            CircleAvatar(
              radius: 16.r,
              backgroundColor: theme.colorScheme.primaryContainer,
              child: Text(
                announcement.authorName.isNotEmpty
                    ? announcement.authorName[0].toUpperCase()
                    : 'A',
                style: theme.textTheme.labelMedium?.copyWith(
                  color: theme.colorScheme.onPrimaryContainer,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            SizedBox(width: 12.w),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    announcement.authorName,
                    style: theme.textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  if (announcement.authorRole != null)
                    Text(
                      announcement.authorRole!,
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.onSurfaceVariant,
                      ),
                    ),
                ],
              ),
            ),
            Text(
              _formatDate(announcement.createdAt),
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget? _buildFloatingActionButton(AnnouncementModel announcement) {
    // Show different FABs based on announcement type
    switch (announcement.type) {
      case AnnouncementType.reminder:
        return FloatingActionButton(
          onPressed: _onAddToCalendar,
          tooltip: 'Add to Calendar',
          child: Icon(Symbols.calendar_add_on, size: 24.sp),
        );
      default:
        return null;
    }
  }

  Widget _buildAnnouncementContent(AnnouncementModel announcement) {
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Content',
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: 12.h),
        Container(
          width: double.infinity,
          padding: EdgeInsets.all(16.w),
          decoration: BoxDecoration(
            color: theme.colorScheme.surfaceContainerLowest,
            borderRadius: BorderRadius.circular(12.r),
            border: Border.all(
              color: theme.colorScheme.outline.withValues(alpha: 0.2),
            ),
          ),
          child: Text(
            announcement.content,
            style: theme.textTheme.bodyLarge?.copyWith(height: 1.6),
          ),
        ),
      ],
    );
  }

  Widget _buildAnnouncementMetadata(AnnouncementModel announcement) {
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Details',
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: 12.h),
        Container(
          width: double.infinity,
          padding: EdgeInsets.all(16.w),
          decoration: BoxDecoration(
            color: theme.colorScheme.surfaceContainerLowest,
            borderRadius: BorderRadius.circular(12.r),
            border: Border.all(
              color: theme.colorScheme.outline.withValues(alpha: 0.2),
            ),
          ),
          child: Column(
            children: [
              _buildMetadataRow('Status', announcement.status.toString()),
              if (announcement.expiresAt != null) ...[
                SizedBox(height: 12.h),
                _buildMetadataRow(
                  'Expires',
                  _formatDate(announcement.expiresAt!),
                ),
              ],
              if (announcement.viewCount > 0) ...[
                SizedBox(height: 12.h),
                _buildMetadataRow('Views', '${announcement.viewCount}'),
              ],
              SizedBox(height: 12.h),
              _buildMetadataRow(
                'Read by',
                '${announcement.readBy.length} people',
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildMetadataRow(String label, String value) {
    final theme = Theme.of(context);
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: theme.textTheme.bodyMedium?.copyWith(
            color: theme.colorScheme.onSurfaceVariant,
          ),
        ),
        Text(
          value,
          style: theme.textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  void _onSharePressed() {
    final announcementAsync = ref.read(
      announcementDetailProvider(widget.announcementId),
    );
    announcementAsync.whenData((announcement) {
      if (announcement != null) {
        // TODO: Implement actual share functionality
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Sharing "${announcement.title}"'),
            action: SnackBarAction(label: 'Copy Link', onPressed: () {}),
          ),
        );
      }
    });
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'discuss':
        _onDiscuss();
        break;
      case 'report':
        _onReport();
        break;
    }
  }

  void _onDiscuss() {
    // Navigate to chat list with announcement context
    context.pushNamed(
      RouteNames.chatList,
      queryParameters: {
        'announcementId': widget.announcementId,
        'context': 'announcement',
      },
    );
  }

  void _onReport() {
    // TODO: Implement report functionality
    _showReportDialog();
  }

  void _showReportDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Report Announcement'),
        content: const Text(
          'Are you sure you want to report this announcement? '
          'This action will notify the administrators.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // TODO: Implement actual report functionality
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Announcement reported successfully'),
                ),
              );
            },
            child: const Text('Report'),
          ),
        ],
      ),
    );
  }

  Widget _buildTypeBadge(AnnouncementType type) {
    Color backgroundColor;
    Color textColor;
    IconData icon;

    switch (type) {
      case AnnouncementType.notice:
        backgroundColor = Colors.blue.shade100;
        textColor = Colors.blue.shade800;
        icon = Symbols.campaign;
        break;

      case AnnouncementType.reminder:
        backgroundColor = Colors.orange.shade100;
        textColor = Colors.orange.shade800;
        icon = Symbols.alarm;
        break;
      case AnnouncementType.news:
        backgroundColor = Colors.purple.shade100;
        textColor = Colors.purple.shade800;
        icon = Symbols.newspaper;
        break;
      case AnnouncementType.alert:
        backgroundColor = Colors.red.shade100;
        textColor = Colors.red.shade800;
        icon = Symbols.warning;
        break;
      case AnnouncementType.general:
        backgroundColor = Colors.grey.shade100;
        textColor = Colors.grey.shade800;
        icon = Symbols.info;
        break;
    }

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(6.r),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 14.sp, color: textColor),
          SizedBox(width: 4.w),
          Text(
            type.displayName,
            style: TextStyle(
              fontSize: 12.sp,
              fontWeight: FontWeight.w600,
              color: textColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPriorityBadge(AnnouncementPriority priority) {
    Color backgroundColor;
    Color textColor;

    switch (priority) {
      case AnnouncementPriority.normal:
        backgroundColor = Colors.grey.shade100;
        textColor = Colors.grey.shade700;
        break;
      case AnnouncementPriority.important:
        backgroundColor = Colors.blue.shade100;
        textColor = Colors.blue.shade700;
        break;
      case AnnouncementPriority.urgent:
        backgroundColor = Colors.orange.shade100;
        textColor = Colors.orange.shade700;
        break;
      case AnnouncementPriority.emergency:
        backgroundColor = Colors.red.shade100;
        textColor = Colors.red.shade700;
        break;
    }

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(6.r),
      ),
      child: Text(
        priority.displayName,
        style: TextStyle(
          fontSize: 12.sp,
          fontWeight: FontWeight.w600,
          color: textColor,
        ),
      ),
    );
  }

  Widget _buildAttachments(AnnouncementModel announcement) {
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Attachments',
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: 12.h),
        ...announcement.attachmentIds.map((attachmentId) {
          return Container(
            margin: EdgeInsets.only(bottom: 8.h),
            padding: EdgeInsets.all(12.w),
            decoration: BoxDecoration(
              color: theme.colorScheme.surfaceContainerLowest,
              borderRadius: BorderRadius.circular(8.r),
              border: Border.all(
                color: theme.colorScheme.outline.withValues(alpha: 0.2),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Symbols.attach_file,
                  size: 20.sp,
                  color: theme.colorScheme.primary,
                ),
                SizedBox(width: 12.w),
                Expanded(
                  child: Text(
                    'Attachment $attachmentId',
                    style: theme.textTheme.bodyMedium,
                  ),
                ),
                IconButton(
                  onPressed: () => _onDownloadAttachment(attachmentId),
                  icon: Icon(Symbols.download, size: 20.sp),
                  tooltip: 'Download',
                ),
              ],
            ),
          );
        }),
      ],
    );
  }

  Widget _buildTags(AnnouncementModel announcement) {
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Tags',
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: 12.h),
        Wrap(
          spacing: 8.w,
          runSpacing: 8.h,
          children: announcement.tags.map((tag) {
            return Container(
              padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
              decoration: BoxDecoration(
                color: theme.colorScheme.primaryContainer,
                borderRadius: BorderRadius.circular(16.r),
              ),
              child: Text(
                '#$tag',
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.onPrimaryContainer,
                  fontWeight: FontWeight.w500,
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays == 0) {
      if (difference.inHours == 0) {
        return '${difference.inMinutes}m ago';
      }
      return '${difference.inHours}h ago';
    } else if (difference.inDays == 1) {
      return 'Yesterday';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}d ago';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }

  void _onDownloadAttachment(String attachmentId) {
    // TODO: Implement attachment download
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Downloading attachment $attachmentId')),
    );
  }

  void _onAddToCalendar() {
    // TODO: Implement add to calendar functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Add to calendar functionality coming soon'),
      ),
    );
  }
}
