import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_symbols_icons/symbols.dart';

/// Not found state section for announcement detail screen when announcement doesn't exist
class NotFoundStateSection extends StatelessWidget {
  final String announcementId;

  const NotFoundStateSection({
    super.key,
    required this.announcementId,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Center(
      child: Padding(
        padding: EdgeInsets.all(32.w),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Not found icon
            Container(
              width: 80.w,
              height: 80.w,
              decoration: BoxDecoration(
                color: theme.colorScheme.outline.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(40.r),
              ),
              child: Icon(
                Symbols.search_off,
                size: 40.sp,
                color: theme.colorScheme.outline.withValues(alpha: 0.7),
              ),
            ),

            SizedBox(height: 24.h),

            // Not found title
            Text(
              'Announcement Not Found',
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.w600,
                color: theme.colorScheme.onSurface,
              ),
              textAlign: TextAlign.center,
            ),

            SizedBox(height: 12.h),

            // Not found description
            Text(
              'This announcement may have been removed, archived, or you may not have permission to view it.',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
              ),
              textAlign: TextAlign.center,
            ),

            SizedBox(height: 8.h),

            // Announcement ID (for debugging)
            Container(
              margin: EdgeInsets.symmetric(vertical: 8.h),
              padding: EdgeInsets.all(12.w),
              decoration: BoxDecoration(
                color: theme.colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(8.r),
                border: Border.all(
                  color: theme.colorScheme.outline.withValues(alpha: 0.2),
                  width: 1,
                ),
              ),
              child: Text(
                'ID: $announcementId',
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
                  fontFamily: 'monospace',
                ),
                textAlign: TextAlign.center,
              ),
            ),

            SizedBox(height: 32.h),

            // Action buttons
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Go back button
                OutlinedButton.icon(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: Icon(Symbols.arrow_back, size: 18.sp),
                  label: const Text('Go Back'),
                  style: OutlinedButton.styleFrom(
                    padding: EdgeInsets.symmetric(
                      horizontal: 24.w,
                      vertical: 12.h,
                    ),
                  ),
                ),
                
                SizedBox(width: 16.w),
                
                // View all announcements button
                ElevatedButton.icon(
                  onPressed: () => _onViewAllPressed(context),
                  icon: Icon(Symbols.list, size: 18.sp),
                  label: const Text('View All'),
                  style: ElevatedButton.styleFrom(
                    padding: EdgeInsets.symmetric(
                      horizontal: 24.w,
                      vertical: 12.h,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _onViewAllPressed(BuildContext context) {
    // TODO: Navigate to announcements list
    // This should pop to the announcements list screen
    Navigator.of(context).popUntil((route) => route.isFirst);
  }
}
