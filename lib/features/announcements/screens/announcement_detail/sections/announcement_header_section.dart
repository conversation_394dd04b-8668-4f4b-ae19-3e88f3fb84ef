import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_symbols_icons/symbols.dart';

import '../../../models/announcement_model.dart';
import '../../../enums/announcement_priority.dart';
import '../../../enums/announcement_type.dart';
import '../../../widgets/priority_indicator.dart';

/// Header section for announcement detail screen showing title, author, date, and priority
class AnnouncementHeaderSection extends StatelessWidget {
  final AnnouncementModel announcement;

  const AnnouncementHeaderSection({super.key, required this.announcement});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Type and priority indicators
        Row(
          children: [
            // Announcement type chip
            _buildTypeChip(theme),

            SizedBox(width: 12.w),

            // Priority indicator
            if (announcement.priority != AnnouncementPriority.normal)
              LargePriorityIndicator(priority: announcement.priority),

            const Spacer(),

            // Pinned indicator
            if (announcement.isPinned)
              Container(
                padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
                decoration: BoxDecoration(
                  color: theme.colorScheme.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(6.r),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Symbols.push_pin,
                      size: 14.sp,
                      color: theme.colorScheme.primary,
                    ),
                    SizedBox(width: 4.w),
                    Text(
                      'Pinned',
                      style: theme.textTheme.labelSmall?.copyWith(
                        color: theme.colorScheme.primary,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),
          ],
        ),

        SizedBox(height: 16.h),

        // Title
        Text(
          announcement.title,
          style: theme.textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.w700,
            color: theme.colorScheme.onSurface,
          ),
        ),

        SizedBox(height: 16.h),

        // Author and date information
        _buildAuthorDateInfo(theme),

        // Classroom/Organization context (if applicable)
        if (announcement.classroomId != null ||
            announcement.organizationId != null) ...[
          SizedBox(height: 12.h),
          _buildContextInfo(theme),
        ],

        // Scheduled/Expiry information (if applicable)
        if (announcement.scheduledAt != null ||
            announcement.expiresAt != null) ...[
          SizedBox(height: 12.h),
          _buildScheduleInfo(theme),
        ],
      ],
    );
  }

  Widget _buildTypeChip(ThemeData theme) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
      decoration: BoxDecoration(
        color: announcement.type.color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(
          color: announcement.type.color.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            announcement.type.icon,
            size: 16.sp,
            color: announcement.type.color,
          ),
          SizedBox(width: 6.w),
          Text(
            announcement.type.displayName,
            style: theme.textTheme.labelMedium?.copyWith(
              color: announcement.type.color,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAuthorDateInfo(ThemeData theme) {
    return Row(
      children: [
        // Author avatar/icon
        CircleAvatar(
          radius: 20.r,
          backgroundColor: theme.colorScheme.primary.withValues(alpha: 0.1),
          child: Icon(
            Symbols.person,
            size: 20.sp,
            color: theme.colorScheme.primary,
          ),
        ),

        SizedBox(width: 12.w),

        // Author and date details
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Author name and role
              Row(
                children: [
                  Text(
                    announcement.authorName,
                    style: theme.textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: theme.colorScheme.onSurface,
                    ),
                  ),
                  if (announcement.authorRole != null) ...[
                    SizedBox(width: 4.w),
                    Text(
                      '• ${announcement.authorRole}',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.onSurface.withValues(
                          alpha: 0.7,
                        ),
                      ),
                    ),
                  ],
                ],
              ),

              SizedBox(height: 2.h),

              // Date and time
              Row(
                children: [
                  Icon(
                    Symbols.schedule,
                    size: 14.sp,
                    color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
                  ),
                  SizedBox(width: 4.w),
                  Text(
                    _formatDateTime(announcement.createdAt),
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                    ),
                  ),
                  if (announcement.updatedAt != null &&
                      announcement.updatedAt != announcement.createdAt) ...[
                    SizedBox(width: 8.w),
                    Text(
                      '• Updated ${_formatDateTime(announcement.updatedAt!)}',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.onSurface.withValues(
                          alpha: 0.5,
                        ),
                      ),
                    ),
                  ],
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildContextInfo(ThemeData theme) {
    return Container(
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Row(
        children: [
          Icon(
            announcement.classroomId != null
                ? Symbols.school
                : Symbols.business,
            size: 16.sp,
            color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
          ),
          SizedBox(width: 8.w),
          Expanded(
            child: Text(
              announcement.classroomId != null
                  ? 'Classroom Announcement'
                  : 'Organization Announcement',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurface.withValues(alpha: 0.8),
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildScheduleInfo(ThemeData theme) {
    return Container(
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: theme.colorScheme.primary.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(
          color: theme.colorScheme.primary.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          if (announcement.scheduledAt != null)
            Row(
              children: [
                Icon(
                  Symbols.schedule_send,
                  size: 16.sp,
                  color: theme.colorScheme.primary,
                ),
                SizedBox(width: 8.w),
                Text(
                  'Scheduled for ${_formatDateTime(announcement.scheduledAt!)}',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.primary,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          if (announcement.expiresAt != null) ...[
            if (announcement.scheduledAt != null) SizedBox(height: 4.h),
            Row(
              children: [
                Icon(
                  Symbols.event_busy,
                  size: 16.sp,
                  color: theme.colorScheme.error,
                ),
                SizedBox(width: 8.w),
                Text(
                  'Expires on ${_formatDateTime(announcement.expiresAt!)}',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.error,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  String _formatDateTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays == 0) {
      // Today - show time
      final hour = dateTime.hour;
      final minute = dateTime.minute.toString().padLeft(2, '0');
      final period = hour >= 12 ? 'PM' : 'AM';
      final displayHour = hour > 12 ? hour - 12 : (hour == 0 ? 12 : hour);
      return '$displayHour:$minute $period';
    } else if (difference.inDays == 1) {
      // Yesterday
      return 'Yesterday';
    } else if (difference.inDays < 7) {
      // This week - show day
      const weekdays = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
      return weekdays[dateTime.weekday - 1];
    } else {
      // Older - show date
      const months = [
        'Jan',
        'Feb',
        'Mar',
        'Apr',
        'May',
        'Jun',
        'Jul',
        'Aug',
        'Sep',
        'Oct',
        'Nov',
        'Dec',
      ];
      return '${months[dateTime.month - 1]} ${dateTime.day}, ${dateTime.year}';
    }
  }
}
