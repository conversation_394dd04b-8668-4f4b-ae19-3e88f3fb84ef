import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_symbols_icons/symbols.dart';

import '../../../models/announcement_model.dart';
import '../../../enums/announcement_type.dart';

/// Actions section for announcement detail screen with quick action buttons
class AnnouncementActionsSection extends StatelessWidget {
  final AnnouncementModel announcement;
  final VoidCallback? onMarkAsRead;
  final VoidCallback? onShare;

  const AnnouncementActionsSection({
    super.key,
    required this.announcement,
    this.onMarkAsRead,

    this.onShare,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isRead = _isAnnouncementRead();

    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Actions',
            style: theme.textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.w600,
              color: theme.colorScheme.onSurface,
            ),
          ),

          SizedBox(height: 12.h),

          // Action buttons row
          Row(
            children: [
              // Mark as read/unread button
              if (!isRead)
                Expanded(
                  child: _buildActionButton(
                    theme,
                    icon: Symbols.mark_email_read,
                    label: 'Mark as Read',
                    onPressed: onMarkAsRead,
                    isPrimary: true,
                  ),
                ),

              if (!isRead) SizedBox(width: 12.w),

              // Share button
              Expanded(
                child: _buildActionButton(
                  theme,
                  icon: Symbols.share,
                  label: 'Share',
                  onPressed: onShare,
                ),
              ),
            ],
          ),

          // Additional context-specific actions
          if (_hasContextualActions()) ...[
            SizedBox(height: 12.h),
            _buildContextualActions(theme),
          ],
        ],
      ),
    );
  }

  Widget _buildActionButton(
    ThemeData theme, {
    required IconData icon,
    required String label,
    required VoidCallback? onPressed,
    bool isPrimary = false,
  }) {
    return SizedBox(
      height: 48.h,
      child: isPrimary
          ? ElevatedButton.icon(
              onPressed: onPressed,
              icon: Icon(icon, size: 18.sp),
              label: Text(label, style: TextStyle(fontSize: 12.sp)),
              style: ElevatedButton.styleFrom(
                backgroundColor: theme.colorScheme.primary,
                foregroundColor: theme.colorScheme.onPrimary,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8.r),
                ),
              ),
            )
          : OutlinedButton.icon(
              onPressed: onPressed,
              icon: Icon(icon, size: 18.sp),
              label: Text(label, style: TextStyle(fontSize: 12.sp)),
              style: OutlinedButton.styleFrom(
                foregroundColor: theme.colorScheme.onSurface,
                side: BorderSide(color: theme.dividerColor),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8.r),
                ),
              ),
            ),
    );
  }

  Widget _buildContextualActions(ThemeData theme) {
    return Column(
      children: [
        // Reminder-specific actions
        if (announcement.type.value == 'reminder') ...[
          Row(
            children: [
              Expanded(
                child: _buildActionButton(
                  theme,
                  icon: Symbols.alarm_add,
                  label: 'Set Reminder',
                  onPressed: _onSetReminderPressed,
                ),
              ),
            ],
          ),
        ],

      ],
    );
  }

  bool _hasContextualActions() {
    return announcement.type.value == 'reminder' ||
        announcement.type.value == 'news';
  }

  bool _isAnnouncementRead() {
    // TODO: Replace with actual user ID when authentication is implemented
    const currentUserId = 'XRTanMcAUWSMq3mrRvve2Y9IMP12';
    return announcement.readBy.contains(currentUserId);
  }

  void _onSetReminderPressed() {
    // TODO: Implement set reminder functionality
    debugPrint('Set reminder pressed for announcement: ${announcement.id}');
  }

}
