import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_symbols_icons/symbols.dart';

import '../../../../digital_library/controllers/digital_library_controller.dart';
import '../../../../digital_library/models/library_file_model.dart';
import '../../../../digital_library/enums/library_file_type.dart';

/// Attachments list section for announcement detail screen
class AttachmentsListSection extends ConsumerWidget {
  final List<String> attachmentIds;

  const AttachmentsListSection({super.key, required this.attachmentIds});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);

    if (attachmentIds.isEmpty) {
      return const SizedBox.shrink();
    }

    // Get files from Digital Library for these attachment IDs
    return FutureBuilder<List<LibraryFileModel>>(
      future: _getAttachmentFiles(ref, attachmentIds),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return _buildLoadingState(theme);
        }

        if (snapshot.hasError) {
          return _buildErrorState(theme);
        }

        final attachmentFiles = snapshot.data ?? [];
        if (attachmentFiles.isEmpty) {
          return _buildEmptyState(theme);
        }

        return _buildAttachmentsList(theme, attachmentFiles);
      },
    );
  }

  Future<List<LibraryFileModel>> _getAttachmentFiles(
    WidgetRef ref,
    List<String> fileIds,
  ) async {
    final files = <LibraryFileModel>[];

    for (final fileId in fileIds) {
      try {
        final fileAsync = await ref.read(fileByIdProvider(fileId).future);
        if (fileAsync != null) {
          files.add(fileAsync);
        }
      } catch (e) {
        // Skip files that can't be loaded
        continue;
      }
    }

    return files;
  }

  Widget _buildLoadingState(ThemeData theme) {
    return Container(
      padding: EdgeInsets.all(16.w),
      child: const Center(child: CircularProgressIndicator()),
    );
  }

  Widget _buildErrorState(ThemeData theme) {
    return Container(
      padding: EdgeInsets.all(16.w),
      child: Row(
        children: [
          Icon(
            Symbols.error_outline,
            size: 20.sp,
            color: theme.colorScheme.error,
          ),
          SizedBox(width: 8.w),
          Text(
            'Unable to load attachments',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.error,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState(ThemeData theme) {
    return Container(
      padding: EdgeInsets.all(16.w),
      child: Row(
        children: [
          Icon(
            Symbols.attach_file,
            size: 20.sp,
            color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
          ),
          SizedBox(width: 8.w),
          Text(
            'No attachments available',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAttachmentsList(
    ThemeData theme,
    List<LibraryFileModel> attachmentFiles,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Section header
        Row(
          children: [
            Icon(
              Symbols.attach_file,
              size: 20.sp,
              color: theme.colorScheme.primary,
            ),
            SizedBox(width: 8.w),
            Text(
              'Attachments (${attachmentIds.length})',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
                color: theme.colorScheme.onSurface,
              ),
            ),
          ],
        ),

        SizedBox(height: 12.h),

        // Attachments list
        Container(
          decoration: BoxDecoration(
            color: theme.colorScheme.surface,
            borderRadius: BorderRadius.circular(12.r),
            border: Border.all(color: theme.dividerColor, width: 1),
          ),
          child: Column(
            children: attachmentFiles.asMap().entries.map((entry) {
              final index = entry.key;
              final file = entry.value;
              final isLast = index == attachmentFiles.length - 1;

              return _buildAttachmentTile(theme, file, isLast);
            }).toList(),
          ),
        ),
      ],
    );
  }

  Widget _buildAttachmentTile(
    ThemeData theme,
    LibraryFileModel file,
    bool isLast,
  ) {
    return Container(
      decoration: BoxDecoration(
        border: isLast
            ? null
            : Border(bottom: BorderSide(color: theme.dividerColor, width: 1)),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => _onAttachmentTap(file),
          borderRadius: BorderRadius.vertical(
            top: isLast ? Radius.zero : Radius.circular(12.r),
            bottom: isLast ? Radius.circular(12.r) : Radius.zero,
          ),
          child: Padding(
            padding: EdgeInsets.all(16.w),
            child: Row(
              children: [
                // File icon
                Container(
                  width: 48.w,
                  height: 48.w,
                  decoration: BoxDecoration(
                    color: file.fileType.color.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8.r),
                  ),
                  child: Icon(
                    file.fileType.icon,
                    size: 24.sp,
                    color: file.fileType.color,
                  ),
                ),

                SizedBox(width: 12.w),

                // File details
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        file.title ?? file.fileName,
                        style: theme.textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                          color: theme.colorScheme.onSurface,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      SizedBox(height: 4.h),
                      Row(
                        children: [
                          Text(
                            _formatFileSize(file.fileSizeBytes ?? 0),
                            style: theme.textTheme.bodySmall?.copyWith(
                              color: theme.colorScheme.onSurface.withValues(
                                alpha: 0.6,
                              ),
                            ),
                          ),
                          SizedBox(width: 8.w),
                          Text(
                            '• ${file.fileType.label}',
                            style: theme.textTheme.bodySmall?.copyWith(
                              color: theme.colorScheme.onSurface.withValues(
                                alpha: 0.6,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),

                // Action buttons
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Download button
                    IconButton(
                      onPressed: () => _onDownloadPressed(file),
                      icon: Icon(
                        Symbols.download,
                        size: 20.sp,
                        color: theme.colorScheme.primary,
                      ),
                      tooltip: 'Download',
                    ),

                    // More options
                    PopupMenuButton<String>(
                      onSelected: (action) =>
                          _handleAttachmentAction(action, file),
                      icon: Icon(
                        Symbols.more_vert,
                        size: 20.sp,
                        color: theme.colorScheme.onSurface.withValues(
                          alpha: 0.6,
                        ),
                      ),
                      itemBuilder: (context) => [
                        PopupMenuItem(
                          value: 'view',
                          child: Row(
                            children: [
                              Icon(Symbols.visibility, size: 16.sp),
                              SizedBox(width: 8.w),
                              const Text('View'),
                            ],
                          ),
                        ),
                        PopupMenuItem(
                          value: 'share',
                          child: Row(
                            children: [
                              Icon(Symbols.share, size: 16.sp),
                              SizedBox(width: 8.w),
                              const Text('Share'),
                            ],
                          ),
                        ),
                        PopupMenuItem(
                          value: 'save_to_library',
                          child: Row(
                            children: [
                              Icon(Symbols.library_add, size: 16.sp),
                              SizedBox(width: 8.w),
                              const Text('Save to Library'),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _onAttachmentTap(LibraryFileModel file) {
    // TODO: Open attachment with Digital Library viewer
    debugPrint('Open attachment: ${file.id}');
  }

  void _onDownloadPressed(LibraryFileModel file) {
    // TODO: Download attachment using Digital Library
    debugPrint('Download attachment: ${file.id}');
  }

  void _handleAttachmentAction(String action, LibraryFileModel file) {
    switch (action) {
      case 'view':
        _onAttachmentTap(file);
        break;
      case 'download':
        _onDownloadPressed(file);
        break;
      case 'share':
        // TODO: Share attachment
        debugPrint('Share attachment: ${file.id}');
        break;
      case 'save':
        // TODO: Save to device
        debugPrint('Save attachment: ${file.id}');
        break;
    }
  }

  String _formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    }
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }
}
