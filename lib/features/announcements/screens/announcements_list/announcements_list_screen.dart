import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_symbols_icons/symbols.dart';

import '../../controllers/announcement_filter_controller.dart';

import '../../widgets/announcement_filter_chips.dart';
import '../../widgets/announcement_advanced_filter_modal.dart';
import 'sections/announcement_search_bar_section.dart';
import 'sections/announcement_filter_tabs_section.dart';
import 'sections/announcement_list_section.dart';

/// Main screen displaying the list of announcements with filtering and search capabilities
class AnnouncementsListScreen extends ConsumerStatefulWidget {
  /// Optional filter to show only specific announcement types
  final String? initialFilter;

  /// Optional classroom ID to filter announcements by classroom
  final String? classroomId;

  const AnnouncementsListScreen({
    super.key,
    this.initialFilter,
    this.classroomId,
  });

  @override
  ConsumerState<AnnouncementsListScreen> createState() =>
      _AnnouncementsListScreenState();
}

class _AnnouncementsListScreenState
    extends ConsumerState<AnnouncementsListScreen> {
  bool _isSearchActive = false;

  @override
  void initState() {
    super.initState();
    _initializeFilter();
  }

  void _initializeFilter() {
    if (widget.initialFilter != null) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        final filterNotifier = ref.read(announcementFilterProvider.notifier);
        switch (widget.initialFilter) {
          case 'Unread':
            filterNotifier.setFilter(AnnouncementFilterType.unread);
            break;
          case 'Important':
            filterNotifier.setFilter(AnnouncementFilterType.important);
            break;

          default:
            filterNotifier.setFilter(AnnouncementFilterType.all);
        }
      });
    }

    // Set classroom filter if provided
    if (widget.classroomId != null) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        final classroomFilterNotifier = ref.read(
          announcementClassroomFilterProvider.notifier,
        );
        classroomFilterNotifier.setClassroom(widget.classroomId);
      });
    }
  }

  void _onTabChanged(int index) {
    final filterNotifier = ref.read(announcementFilterProvider.notifier);
    switch (index) {
      case 0:
        filterNotifier.setFilter(AnnouncementFilterType.all);
        break;
      case 1:
        filterNotifier.setFilter(AnnouncementFilterType.unread);
        break;
      case 2:
        filterNotifier.setFilter(AnnouncementFilterType.important);
        break;
    }
  }

  void _onSearchChanged(String query) {
    final searchNotifier = ref.read(announcementSearchQueryProvider.notifier);
    searchNotifier.setQuery(query);

    setState(() {
      _isSearchActive = query.isNotEmpty;
    });
  }

  void _onSearchToggle() {
    setState(() {
      _isSearchActive = !_isSearchActive;
      if (!_isSearchActive) {
        final searchNotifier = ref.read(
          announcementSearchQueryProvider.notifier,
        );
        searchNotifier.clearQuery();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final currentFilter = ref.watch(announcementFilterProvider);
    final searchQuery = ref.watch(announcementSearchQueryProvider);

    // Define filter tabs
    final filterTabs = ['All', 'Unread', 'Important'];
    final selectedTabIndex = switch (currentFilter) {
      AnnouncementFilterType.all => 0,
      AnnouncementFilterType.unread => 1,
      AnnouncementFilterType.important => 2,
      AnnouncementFilterType.pinned => 0, // Default to All for pinned
    };

    return Scaffold(
      backgroundColor: theme.colorScheme.surface,
      appBar: _buildAppBar(theme),
      body: SafeArea(
        child: Column(
          children: [
            // Search bar section (when active)
            if (_isSearchActive)
              AnnouncementSearchBarSection(
                searchQuery: searchQuery,
                onSearchChanged: _onSearchChanged,
                onSearchClear: () => _onSearchChanged(''),
              ),

            // Filter tabs section
            AnnouncementFilterTabsSection(
              tabs: filterTabs,
              selectedIndex: selectedTabIndex,
              onTabChanged: _onTabChanged,
            ),

            // Active filter chips (only show when filters are active)
            AnnouncementActiveFilterChips(
              onAdvancedFilterTap: _onAdvancedFilterPressed,
            ),

            // Announcements list
            Expanded(
              child: RefreshIndicator(
                onRefresh: _onRefresh,
                child: _buildAnnouncementsList(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  AppBar _buildAppBar(ThemeData theme) {
    return AppBar(
      backgroundColor: theme.colorScheme.surface,
      elevation: 0,
      title: Text(
        widget.classroomId != null
            ? 'Classroom Announcements'
            : 'Announcements',
        style: theme.textTheme.titleLarge?.copyWith(
          fontWeight: FontWeight.w600,
        ),
      ),
      actions: [
        // Search toggle button
        IconButton(
          onPressed: _onSearchToggle,
          icon: Icon(
            _isSearchActive ? Symbols.search_off : Symbols.search,
            size: 24.sp,
          ),
          tooltip: _isSearchActive ? 'Close search' : 'Search announcements',
        ),

        // Filter/Sort button
        IconButton(
          onPressed: _onFilterPressed,
          icon: Icon(Symbols.tune, size: 24.sp),
          tooltip: 'Filter and sort',
        ),

        SizedBox(width: 8.w),
      ],
    );
  }

  Widget _buildAnnouncementsList() {
    final currentFilter = ref.watch(announcementFilterProvider);
    final searchQuery = ref.watch(announcementSearchQueryProvider);

    // Use filtered and sorted announcements provider for real data
    final announcementsAsync = ref.watch(
      filteredAndSortedAnnouncementsProvider,
    );

    return announcementsAsync.when(
      data: (announcements) => AnnouncementListSection(
        announcements: announcements,
        isLoading: false,
        selectedFilter: currentFilter.displayName,
        searchQuery: searchQuery,
      ),
      loading: () => AnnouncementListSection(
        announcements: const [],
        isLoading: true,
        selectedFilter: currentFilter.displayName,
        searchQuery: searchQuery,
      ),
      error: (error, stackTrace) => _buildErrorState(error),
    );
  }

  Widget _buildErrorState(Object error) {
    final theme = Theme.of(context);

    return Center(
      child: Padding(
        padding: EdgeInsets.all(24.w),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Symbols.error_outline,
              size: 64.sp,
              color: theme.colorScheme.error,
            ),
            SizedBox(height: 16.h),
            Text(
              'Failed to load announcements',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: 8.h),
            Text(
              'Please check your connection and try again',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 24.h),
            ElevatedButton.icon(
              onPressed: () {
                ref.invalidate(filteredAnnouncementsProvider);
              },
              icon: Icon(Symbols.refresh, size: 20.sp),
              label: const Text('Retry'),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _onRefresh() async {
    // Invalidate providers to refresh data from Firebase
    ref.invalidate(filteredAndSortedAnnouncementsProvider);

    // Wait for the new data to load
    await ref.read(filteredAndSortedAnnouncementsProvider.future);
  }

  void _onFilterPressed() {
    _onAdvancedFilterPressed();
  }

  void _onAdvancedFilterPressed() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => const AnnouncementAdvancedFilterModal(),
    );
  }
}
