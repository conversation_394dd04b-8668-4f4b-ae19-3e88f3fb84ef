import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';

import '../../../../../core/routes/app_routes.dart';
import '../../../models/announcement_model.dart';
import '../../../widgets/announcement_list_tile.dart';
import 'empty_state_section.dart';

/// Main announcement list section with grouping and filtering
class AnnouncementListSection extends StatelessWidget {
  final List<AnnouncementModel> announcements;
  final bool isLoading;
  final String selectedFilter;
  final String searchQuery;

  const AnnouncementListSection({
    super.key,
    required this.announcements,
    required this.isLoading,
    required this.selectedFilter,
    required this.searchQuery,
  });

  @override
  Widget build(BuildContext context) {
    if (isLoading) {
      return _buildLoadingState();
    }

    if (announcements.isEmpty) {
      return EmptyStateSection(
        filter: selectedFilter,
        searchQuery: searchQuery,
      );
    }

    return _buildAnnouncementsList(context);
  }

  Widget _buildLoadingState() {
    return const Center(child: CircularProgressIndicator());
  }

  Widget _buildAnnouncementsList(BuildContext context) {
    // Group announcements by date for better organization
    final groupedAnnouncements = _groupAnnouncementsByDate(announcements);
    final sortedKeys = groupedAnnouncements.keys.toList()
      ..sort((a, b) => b.compareTo(a)); // Most recent first

    return ListView.builder(
      padding: EdgeInsets.symmetric(vertical: 8.h),
      itemCount: sortedKeys.length,
      itemBuilder: (context, index) {
        final dateKey = sortedKeys[index];
        final dayAnnouncements = groupedAnnouncements[dateKey]!;

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Date header
            _buildDateHeader(context, dateKey),

            // Announcements for this date
            ...dayAnnouncements.map(
              (announcement) => AnnouncementListTile(
                announcement: announcement,
                onTap: () => _onAnnouncementTap(context, announcement),
                onMarkAsRead: () => _onMarkAsRead(announcement),
                onShare: () => _onShare(context, announcement),
              ),
            ),

            SizedBox(height: 8.h),
          ],
        );
      },
    );
  }

  Widget _buildDateHeader(BuildContext context, String dateKey) {
    final theme = Theme.of(context);

    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
      child: Text(
        dateKey,
        style: theme.textTheme.titleMedium?.copyWith(
          fontWeight: FontWeight.w600,
          color: theme.colorScheme.primary,
        ),
      ),
    );
  }

  Map<String, List<AnnouncementModel>> _groupAnnouncementsByDate(
    List<AnnouncementModel> announcements,
  ) {
    final Map<String, List<AnnouncementModel>> grouped = {};

    for (final announcement in announcements) {
      final dateKey = _formatDateKey(announcement.createdAt);
      grouped.putIfAbsent(dateKey, () => []).add(announcement);
    }

    // Sort announcements within each date group by creation time (newest first)
    for (final dateGroup in grouped.values) {
      dateGroup.sort((a, b) => b.createdAt.compareTo(a.createdAt));
    }

    return grouped;
  }

  String _formatDateKey(DateTime date) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(const Duration(days: 1));
    final announcementDate = DateTime(date.year, date.month, date.day);

    if (announcementDate == today) {
      return 'Today';
    } else if (announcementDate == yesterday) {
      return 'Yesterday';
    } else if (now.difference(announcementDate).inDays < 7) {
      // Show day of week for this week
      const weekdays = [
        'Monday',
        'Tuesday',
        'Wednesday',
        'Thursday',
        'Friday',
        'Saturday',
        'Sunday',
      ];
      return weekdays[date.weekday - 1];
    } else {
      // Show formatted date for older announcements
      const months = [
        'Jan',
        'Feb',
        'Mar',
        'Apr',
        'May',
        'Jun',
        'Jul',
        'Aug',
        'Sep',
        'Oct',
        'Nov',
        'Dec',
      ];
      return '${months[date.month - 1]} ${date.day}, ${date.year}';
    }
  }

  void _onAnnouncementTap(
    BuildContext context,
    AnnouncementModel announcement,
  ) {
    // Navigate to announcement detail screen
    context.pushNamed(
      RouteNames.announcementDetail,
      pathParameters: {'id': announcement.id},
    );
  }

  void _onMarkAsRead(AnnouncementModel announcement) {
    // TODO: Implement mark as read functionality
    // This will be handled by the state management in Phase 3
    debugPrint('Mark as read: ${announcement.title}');
  }

  void _onShare(BuildContext context, AnnouncementModel announcement) {
    // TODO: Implement share functionality
    // This will show a share dialog or use platform sharing
    debugPrint('Share: ${announcement.title}');
  }
}
