import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_symbols_icons/symbols.dart';

/// Empty state section for announcements list when no announcements are found
class EmptyStateSection extends StatelessWidget {
  final String filter;
  final String searchQuery;

  const EmptyStateSection({
    super.key,
    required this.filter,
    required this.searchQuery,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isSearching = searchQuery.isNotEmpty;

    return Center(
      child: Padding(
        padding: EdgeInsets.all(32.w),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Icon
            Container(
              width: 80.w,
              height: 80.w,
              decoration: BoxDecoration(
                color: theme.colorScheme.primary.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(40.r),
              ),
              child: Icon(
                _getEmptyStateIcon(isSearching, filter),
                size: 40.sp,
                color: theme.colorScheme.primary.withValues(alpha: 0.7),
              ),
            ),

            SizedBox(height: 24.h),

            // Title
            Text(
              _getEmptyStateTitle(isSearching, filter),
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.w600,
                color: theme.colorScheme.onSurface,
              ),
              textAlign: TextAlign.center,
            ),

            SizedBox(height: 12.h),

            // Description
            Text(
              _getEmptyStateDescription(isSearching, filter, searchQuery),
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
              ),
              textAlign: TextAlign.center,
            ),

            SizedBox(height: 32.h),

            // Action button (if applicable)
            if (isSearching)
              ElevatedButton.icon(
                onPressed: () {
                  // TODO: Clear search - this will be handled by parent widget
                },
                icon: Icon(Symbols.clear, size: 18.sp),
                label: const Text('Clear Search'),
                style: ElevatedButton.styleFrom(
                  padding: EdgeInsets.symmetric(
                    horizontal: 24.w,
                    vertical: 12.h,
                  ),
                ),
              )
            else if (filter == 'All')
              OutlinedButton.icon(
                onPressed: () {
                  // TODO: Refresh announcements
                },
                icon: Icon(Symbols.refresh, size: 18.sp),
                label: const Text('Refresh'),
                style: OutlinedButton.styleFrom(
                  padding: EdgeInsets.symmetric(
                    horizontal: 24.w,
                    vertical: 12.h,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  IconData _getEmptyStateIcon(bool isSearching, String filter) {
    if (isSearching) {
      return Symbols.search_off;
    }

    switch (filter.toLowerCase()) {
      case 'unread':
        return Symbols.mark_email_read;
      case 'important':
        return Symbols.priority_high;
      case 'archived':
        return Symbols.archive;
      default:
        return Symbols.notifications_none;
    }
  }

  String _getEmptyStateTitle(bool isSearching, String filter) {
    if (isSearching) {
      return 'No Results Found';
    }

    switch (filter.toLowerCase()) {
      case 'unread':
        return 'All Caught Up!';
      case 'important':
        return 'No Important Announcements';
      case 'archived':
        return 'No Archived Announcements';
      default:
        return 'No Announcements Yet';
    }
  }

  String _getEmptyStateDescription(bool isSearching, String filter, String searchQuery) {
    if (isSearching) {
      return 'No announcements found for "$searchQuery". Try adjusting your search terms or check your spelling.';
    }

    switch (filter.toLowerCase()) {
      case 'unread':
        return 'You\'ve read all your announcements. Great job staying up to date!';
      case 'important':
        return 'There are no important announcements at the moment. Check back later for updates.';
      case 'archived':
        return 'You haven\'t archived any announcements yet. Archived announcements will appear here.';
      default:
        return 'No announcements have been posted yet. Check back later for updates from your school and teachers.';
    }
  }
}
