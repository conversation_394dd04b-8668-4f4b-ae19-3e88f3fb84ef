import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_symbols_icons/symbols.dart';

import '../../../controllers/announcement_search_controller.dart';

/// Search suggestions section showing suggested search terms
class SearchSuggestionsSection extends ConsumerWidget {
  final String query;
  final ValueChanged<String> onSuggestionTap;

  const SearchSuggestionsSection({
    super.key,
    required this.query,
    required this.onSuggestionTap,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);

    // Use real search suggestions from provider
    final suggestionsAsync = ref.watch(searchSuggestionsProvider(query));

    return suggestionsAsync.when(
      data: (suggestions) {
        if (suggestions.isEmpty) {
          return const SizedBox.shrink();
        }

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Section header
            Text(
              'Suggestions',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
                color: theme.colorScheme.onSurface,
              ),
            ),

            SizedBox(height: 12.h),

            // Suggestions list
            ...suggestions.map(
              (suggestion) => _buildSuggestionTile(theme, suggestion.text),
            ),
          ],
        );
      },
      loading: () => const SizedBox.shrink(),
      error: (_, __) => const SizedBox.shrink(),
    );
  }

  Widget _buildSuggestionTile(ThemeData theme, String suggestion) {
    return Container(
      margin: EdgeInsets.only(bottom: 4.h),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => onSuggestionTap(suggestion),
          borderRadius: BorderRadius.circular(8.r),
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 12.h),
            child: Row(
              children: [
                Icon(
                  Symbols.search,
                  size: 18.sp,
                  color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
                ),
                SizedBox(width: 12.w),
                Expanded(
                  child: Text(
                    suggestion,
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: theme.colorScheme.onSurface,
                    ),
                  ),
                ),
                Icon(
                  Symbols.north_west,
                  size: 16.sp,
                  color: theme.colorScheme.onSurface.withValues(alpha: 0.3),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
