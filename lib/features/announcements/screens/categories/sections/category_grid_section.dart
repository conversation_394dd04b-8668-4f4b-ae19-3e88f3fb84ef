import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../controllers/announcements_controller.dart';
import '../../../enums/announcement_type.dart';
import '../../../enums/announcement_usage_type.dart';
import '../../../widgets/category_card.dart';

/// Grid section displaying announcement categories
class CategoryGridSection extends ConsumerWidget {
  final bool showByType;
  final Function(String categoryId, String categoryName) onCategoryTap;

  const CategoryGridSection({
    super.key,
    required this.showByType,
    required this.onCategoryTap,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);

    // Get category counts from announcements
    final announcementsAsync = ref.watch(userAnnouncementsProvider);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Section header
        Text(
          showByType ? 'Announcement Types' : 'Announcement Scopes',
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
            color: theme.colorScheme.onSurface,
          ),
        ),

        SizedBox(height: 16.h),

        // Category grid
        GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 2,
            crossAxisSpacing: 12.w,
            mainAxisSpacing: 12.h,
            childAspectRatio: 1.2,
          ),
          itemCount: showByType
              ? AnnouncementType.values.length
              : AnnouncementUsageType.values.length,
          itemBuilder: (context, index) {
            if (showByType) {
              final type = AnnouncementType.values[index];
              return CategoryCard(
                title: type.displayName,
                icon: type.icon,
                color: type.color,
                count: _getCategoryCount(announcementsAsync, type.value),
                onTap: () => onCategoryTap(type.value, type.displayName),
              );
            } else {
              final usageType = AnnouncementUsageType.values[index];
              return CategoryCard(
                title: usageType.displayName,
                icon: usageType.icon,
                color: usageType.color,
                count: _getCategoryCount(announcementsAsync, usageType.value),
                onTap: () =>
                    onCategoryTap(usageType.value, usageType.displayName),
              );
            }
          },
        ),
      ],
    );
  }

  int _getCategoryCount(
    AsyncValue<List<dynamic>> announcementsAsync,
    String categoryValue,
  ) {
    return announcementsAsync.when(
      data: (announcements) {
        return announcements.where((announcement) {
          if (showByType) {
            return announcement.type.value == categoryValue;
          } else {
            return announcement.usageType.value == categoryValue;
          }
        }).length;
      },
      loading: () => 0,
      error: (_, __) => 0,
    );
  }
}
