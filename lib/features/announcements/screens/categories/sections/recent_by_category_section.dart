import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_symbols_icons/symbols.dart';

import '../../../controllers/announcements_controller.dart';
import '../../../widgets/compact_category_card.dart';

/// Section showing recent announcements grouped by category
class RecentByCategorySection extends ConsumerWidget {
  final bool showByType;

  const RecentByCategorySection({super.key, required this.showByType});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);

    // Use real recent announcements data
    final announcementsAsync = ref.watch(userAnnouncementsProvider);

    return announcementsAsync.when(
      data: (announcements) {
        final recentCategories = _getRecentCategories(announcements);

        if (recentCategories.isEmpty) {
          return const SizedBox.shrink();
        }

        return _buildRecentCategoriesContent(theme, recentCategories);
      },
      loading: () => const SizedBox.shrink(),
      error: (_, __) => const SizedBox.shrink(),
    );
  }

  Widget _buildRecentCategoriesContent(
    ThemeData theme,
    List<Map<String, dynamic>> recentCategories,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Section header
        Row(
          children: [
            Icon(
              Symbols.history,
              size: 20.sp,
              color: theme.colorScheme.primary,
            ),
            SizedBox(width: 8.w),
            Text(
              'Recent Activity',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
                color: theme.colorScheme.onSurface,
              ),
            ),
            const Spacer(),
            TextButton(
              onPressed: _onViewAllPressed,
              child: Text(
                'View All',
                style: TextStyle(
                  color: theme.colorScheme.primary,
                  fontSize: 14.sp,
                ),
              ),
            ),
          ],
        ),

        SizedBox(height: 12.h),

        // Recent categories list
        ...recentCategories.map(
          (categoryData) => Padding(
            padding: EdgeInsets.only(bottom: 8.h),
            child: CompactCategoryCard(
              title: categoryData['name'],
              icon: categoryData['icon'],
              color: categoryData['color'],
              count: categoryData['recentCount'],
              onTap: () =>
                  _onCategoryTap(categoryData['id'], categoryData['name']),
            ),
          ),
        ),
      ],
    );
  }

  List<Map<String, dynamic>> _getRecentCategories(List<dynamic> announcements) {
    // Get recent announcements (last 7 days) and group by category
    final now = DateTime.now();
    final weekAgo = now.subtract(const Duration(days: 7));

    final recentAnnouncements = announcements.where((announcement) {
      return announcement.createdAt.isAfter(weekAgo);
    }).toList();

    // Group by category and count
    final categoryMap = <String, Map<String, dynamic>>{};

    for (final announcement in recentAnnouncements) {
      final categoryId = showByType
          ? announcement.type.value
          : announcement.usageType.value;
      final categoryName = showByType
          ? announcement.type.displayName
          : announcement.usageType.displayName;
      final icon = showByType
          ? announcement.type.icon
          : announcement.usageType.icon;
      final color = showByType
          ? announcement.type.color
          : announcement.usageType.color;

      if (categoryMap.containsKey(categoryId)) {
        categoryMap[categoryId]!['recentCount'] =
            categoryMap[categoryId]!['recentCount'] + 1;
      } else {
        categoryMap[categoryId] = {
          'id': categoryId,
          'name': categoryName,
          'icon': icon,
          'color': color,
          'recentCount': 1,
        };
      }
    }

    // Convert to list and sort by count
    final categories = categoryMap.values.toList();
    categories.sort((a, b) => b['recentCount'].compareTo(a['recentCount']));

    // Return top 3 most active categories
    return categories.take(3).toList();
  }

  void _onCategoryTap(String categoryId, String categoryName) {
    // TODO: Navigate to filtered announcements list
    debugPrint('Navigate to recent in category: $categoryId ($categoryName)');
  }

  void _onViewAllPressed() {
    // TODO: Navigate to all announcements
    debugPrint('Navigate to all announcements');
  }
}
