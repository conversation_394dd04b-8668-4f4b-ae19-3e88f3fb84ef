import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:material_symbols_icons/symbols.dart';

import '../../../../core/routes/app_routes.dart';
import '../../controllers/announcements_controller.dart';

import 'sections/category_grid_section.dart';
import 'sections/category_stats_section.dart';
import 'sections/recent_by_category_section.dart';

/// Organized view of announcements by categories and types
class CategoriesScreen extends ConsumerStatefulWidget {
  const CategoriesScreen({super.key});

  @override
  ConsumerState<CategoriesScreen> createState() => _CategoriesScreenState();
}

class _CategoriesScreenState extends ConsumerState<CategoriesScreen> {
  int _selectedTabIndex = 0;
  final List<String> _tabs = ['By Type', 'By Scope'];

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: theme.colorScheme.surface,
      appBar: _buildAppBar(theme),
      body: Safe<PERSON>rea(
        child: Column(
          children: [
            // Tab selector
            _buildTabSelector(theme),

            // Statistics section
            const CategoryStatsSection(),

            // Main content
            Expanded(
              child: RefreshIndicator(
                onRefresh: _onRefresh,
                child: SingleChildScrollView(
                  physics: const AlwaysScrollableScrollPhysics(),
                  padding: EdgeInsets.all(16.w),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Category grid
                      CategoryGridSection(
                        showByType: _selectedTabIndex == 0,
                        onCategoryTap: _onCategoryTap,
                      ),

                      SizedBox(height: 24.h),

                      // Recent announcements by category
                      RecentByCategorySection(
                        showByType: _selectedTabIndex == 0,
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  AppBar _buildAppBar(ThemeData theme) {
    return AppBar(
      backgroundColor: theme.colorScheme.surface,
      elevation: 0,
      title: Text(
        'Categories',
        style: theme.textTheme.titleLarge?.copyWith(
          fontWeight: FontWeight.w600,
        ),
      ),
      actions: [
        // Search button
        IconButton(
          onPressed: _onSearchPressed,
          icon: Icon(Symbols.search, size: 24.sp),
          tooltip: 'Search announcements',
        ),

        // Settings button
        IconButton(
          onPressed: _onSettingsPressed,
          icon: Icon(Symbols.tune, size: 24.sp),
          tooltip: 'Category preferences',
        ),

        SizedBox(width: 8.w),
      ],
    );
  }

  Widget _buildTabSelector(ThemeData theme) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: theme.dividerColor, width: 1),
      ),
      child: Row(
        children: _tabs.asMap().entries.map((entry) {
          final index = entry.key;
          final tab = entry.value;
          final isSelected = index == _selectedTabIndex;
          final isFirst = index == 0;
          final isLast = index == _tabs.length - 1;

          return Expanded(
            child: GestureDetector(
              onTap: () => _onTabChanged(index),
              child: Container(
                height: 48.h,
                decoration: BoxDecoration(
                  color: isSelected
                      ? theme.colorScheme.primary
                      : Colors.transparent,
                  borderRadius: BorderRadius.horizontal(
                    left: isFirst ? Radius.circular(11.r) : Radius.zero,
                    right: isLast ? Radius.circular(11.r) : Radius.zero,
                  ),
                ),
                child: Center(
                  child: Text(
                    tab,
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: isSelected
                          ? theme.colorScheme.onPrimary
                          : theme.colorScheme.onSurface.withValues(alpha: 0.7),
                      fontWeight: isSelected
                          ? FontWeight.w600
                          : FontWeight.w500,
                    ),
                  ),
                ),
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  void _onTabChanged(int index) {
    setState(() {
      _selectedTabIndex = index;
    });
  }

  void _onCategoryTap(String categoryId, String categoryName) {
    // Navigate to filtered announcements list
    context.pushNamed(
      RouteNames.announcementsList,
      queryParameters: {'filter': categoryId},
    );
  }

  void _onSearchPressed() {
    // Navigate to search screen
    context.pushNamed(RouteNames.announcementsSearch);
  }

  void _onSettingsPressed() {
    // TODO: Show category preferences dialog
    _showCategoryPreferences();
  }

  Future<void> _onRefresh() async {
    // Refresh announcements data which will update category statistics
    ref.invalidate(userAnnouncementsProvider);
    ref.invalidate(announcementStatsProvider);
  }

  void _showCategoryPreferences() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => _buildPreferencesBottomSheet(),
    );
  }

  Widget _buildPreferencesBottomSheet() {
    final theme = Theme.of(context);

    return Container(
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20.r)),
      ),
      padding: EdgeInsets.all(24.w),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Handle bar
          Center(
            child: Container(
              width: 40.w,
              height: 4.h,
              decoration: BoxDecoration(
                color: theme.colorScheme.onSurface.withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(2.r),
              ),
            ),
          ),

          SizedBox(height: 20.h),

          Text(
            'Category Preferences',
            style: theme.textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),

          SizedBox(height: 16.h),

          // Placeholder for preferences
          Text(
            'Category customization options will be implemented in Phase 3',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
            ),
          ),

          SizedBox(height: 24.h),

          // Close button
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Close'),
            ),
          ),

          // Add bottom padding for safe area
          SizedBox(height: MediaQuery.of(context).padding.bottom),
        ],
      ),
    );
  }
}
