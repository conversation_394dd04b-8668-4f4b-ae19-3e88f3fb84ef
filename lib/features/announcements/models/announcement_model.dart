import '../enums/announcement_type.dart';
import '../enums/announcement_priority.dart';
import '../enums/announcement_status.dart';
import '../enums/announcement_usage_type.dart';

/// Model representing an announcement in the system
class AnnouncementModel {
  /// Unique identifier for the announcement
  final String id;

  /// Title of the announcement
  final String title;

  /// Content/body of the announcement
  final String content;

  /// Type of announcement (notice, event, reminder, etc.)
  final AnnouncementType type;

  /// Priority level of the announcement
  final AnnouncementPriority priority;

  /// Current status of the announcement
  final AnnouncementStatus status;

  /// ID of the user who created this announcement
  final String authorId;

  /// Name of the user who created this announcement
  final String authorName;

  /// Role of the author (teacher, admin, etc.)
  final String? authorRole;

  /// When the announcement was created
  final DateTime createdAt;

  /// When the announcement was last updated
  final DateTime? updatedAt;

  /// When the announcement should be published (for scheduled announcements)
  final DateTime? scheduledAt;

  /// When the announcement expires and becomes inactive
  final DateTime? expiresAt;

  /// Usage type for this announcement (determines feature context)
  final AnnouncementUsageType usageType;

  /// List of target audience identifiers (user IDs, class IDs, etc.)
  final List<String> targetAudience;

  /// ID of the classroom this announcement belongs to (if classroom-specific)
  final String? classroomId;

  /// ID of the organization this announcement belongs to
  final String? organizationId;

  /// List of Digital Library file IDs attached to this announcement
  final List<String> attachmentIds;

  /// Additional metadata for the announcement
  final Map<String, dynamic>? metadata;

  /// Whether the announcement is pinned (appears at top)
  final bool isPinned;

  /// Whether the announcement is currently active
  final bool isActive;

  /// List of user IDs who have read this announcement
  final List<String> readBy;

  /// Map of user IDs to read timestamps
  final Map<String, DateTime> readTimestamps;

  /// Tags associated with the announcement for categorization
  final List<String> tags;

  /// Number of views/reads for analytics
  final int viewCount;


  const AnnouncementModel({
    required this.id,
    required this.title,
    required this.content,
    required this.type,
    required this.priority,
    required this.status,
    required this.authorId,
    required this.authorName,
    this.authorRole,
    required this.createdAt,
    this.updatedAt,
    this.scheduledAt,
    this.expiresAt,
    required this.usageType,
    required this.targetAudience,
    this.classroomId,
    this.organizationId,
    this.attachmentIds = const [],
    this.metadata,
    this.isPinned = false,
    this.isActive = true,
    this.readBy = const [],
    this.readTimestamps = const {},
    this.tags = const [],
    this.viewCount = 0,

  });

  /// Create an AnnouncementModel from JSON
  factory AnnouncementModel.fromJson(Map<String, dynamic> json) {
    // Parse read timestamps
    final readTimestampsMap = <String, DateTime>{};
    if (json['readTimestamps'] != null) {
      final timestamps = json['readTimestamps'] as Map<String, dynamic>;
      for (final entry in timestamps.entries) {
        readTimestampsMap[entry.key] = DateTime.parse(entry.value as String);
      }
    }

    return AnnouncementModel(
      id: json['id'] as String,
      title: json['title'] as String,
      content: json['content'] as String,
      type: AnnouncementTypeExtension.fromString(json['type'] as String),
      priority: AnnouncementPriorityExtension.fromString(
        json['priority'] as String,
      ),
      status: AnnouncementStatusExtension.fromString(json['status'] as String),
      authorId: json['authorId'] as String,
      authorName: json['authorName'] as String,
      authorRole: json['authorRole'] as String?,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] != null
          ? DateTime.parse(json['updatedAt'] as String)
          : null,
      scheduledAt: json['scheduledAt'] != null
          ? DateTime.parse(json['scheduledAt'] as String)
          : null,
      expiresAt: json['expiresAt'] != null
          ? DateTime.parse(json['expiresAt'] as String)
          : null,
      usageType: AnnouncementUsageTypeExtension.fromString(
        json['usageType'] as String? ?? 'organization',
      ),
      targetAudience: List<String>.from(json['targetAudience'] as List? ?? []),
      classroomId: json['classroomId'] as String?,
      organizationId: json['organizationId'] as String?,
      attachmentIds: List<String>.from(json['attachmentIds'] as List? ?? []),
      metadata: json['metadata'] as Map<String, dynamic>?,
      isPinned: json['isPinned'] as bool? ?? false,
      isActive: json['isActive'] as bool? ?? true,
      readBy: List<String>.from(json['readBy'] as List? ?? []),
      readTimestamps: readTimestampsMap,
      tags: List<String>.from(json['tags'] as List? ?? []),
      viewCount: json['viewCount'] as int? ?? 0,
    );
  }

  /// Convert AnnouncementModel to JSON
  Map<String, dynamic> toJson() {
    // Convert read timestamps to strings
    final readTimestampsJson = <String, String>{};
    for (final entry in readTimestamps.entries) {
      readTimestampsJson[entry.key] = entry.value.toIso8601String();
    }

    return {
      'id': id,
      'title': title,
      'content': content,
      'type': type.value,
      'priority': priority.value,
      'status': status.value,
      'authorId': authorId,
      'authorName': authorName,
      'authorRole': authorRole,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
      'scheduledAt': scheduledAt?.toIso8601String(),
      'expiresAt': expiresAt?.toIso8601String(),
      'usageType': usageType.value,
      'targetAudience': targetAudience,
      'classroomId': classroomId,
      'organizationId': organizationId,
      'attachmentIds': attachmentIds,
      'metadata': metadata,
      'isPinned': isPinned,
      'isActive': isActive,
      'readBy': readBy,
      'readTimestamps': readTimestampsJson,
      'tags': tags,
      'viewCount': viewCount,

    };
  }

  /// Create a copy of this announcement with updated fields
  AnnouncementModel copyWith({
    String? id,
    String? title,
    String? content,
    AnnouncementType? type,
    AnnouncementPriority? priority,
    AnnouncementStatus? status,
    String? authorId,
    String? authorName,
    String? authorRole,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? scheduledAt,
    DateTime? expiresAt,
    AnnouncementUsageType? usageType,
    List<String>? targetAudience,
    String? classroomId,
    String? organizationId,
    List<String>? attachmentIds,
    Map<String, dynamic>? metadata,
    bool? isPinned,
    bool? isActive,
    List<String>? readBy,
    Map<String, DateTime>? readTimestamps,
    List<String>? tags,
    int? viewCount,
    bool? allowsComments,
  }) {
    return AnnouncementModel(
      id: id ?? this.id,
      title: title ?? this.title,
      content: content ?? this.content,
      type: type ?? this.type,
      priority: priority ?? this.priority,
      status: status ?? this.status,
      authorId: authorId ?? this.authorId,
      authorName: authorName ?? this.authorName,
      authorRole: authorRole ?? this.authorRole,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      scheduledAt: scheduledAt ?? this.scheduledAt,
      expiresAt: expiresAt ?? this.expiresAt,
      usageType: usageType ?? this.usageType,
      targetAudience: targetAudience ?? this.targetAudience,
      classroomId: classroomId ?? this.classroomId,
      organizationId: organizationId ?? this.organizationId,
      attachmentIds: attachmentIds ?? this.attachmentIds,
      metadata: metadata ?? this.metadata,
      isPinned: isPinned ?? this.isPinned,
      isActive: isActive ?? this.isActive,
      readBy: readBy ?? this.readBy,
      readTimestamps: readTimestamps ?? this.readTimestamps,
      tags: tags ?? this.tags,
      viewCount: viewCount ?? this.viewCount,

    );
  }

  /// Check if this announcement has been read by a specific user
  bool isReadBy(String userId) {
    return readBy.contains(userId);
  }

  /// Check if this announcement is currently visible (published and not expired)
  bool get isVisible {
    if (!isActive || status != AnnouncementStatus.published) {
      return false;
    }

    if (expiresAt != null && DateTime.now().isAfter(expiresAt!)) {
      return false;
    }

    return true;
  }

  /// Check if this announcement is overdue for expiration
  bool get isExpired {
    return expiresAt != null && DateTime.now().isAfter(expiresAt!);
  }

  /// Get the read percentage for analytics
  double get readPercentage {
    if (targetAudience.isEmpty) return 0.0;
    return (readBy.length / targetAudience.length) * 100;
  }

  @override
  String toString() {
    return 'AnnouncementModel(id: $id, title: $title, type: ${type.displayName}, priority: ${priority.displayName})';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AnnouncementModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
