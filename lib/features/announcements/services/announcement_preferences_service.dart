import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:logger/logger.dart';

import '../controllers/announcement_filter_controller.dart';
import '../enums/announcement_type.dart';
import '../enums/announcement_priority.dart';
import '../enums/announcement_usage_type.dart';

/// Service for persisting announcement filter preferences
class AnnouncementPreferencesService {
  static const String _advancedFilterKey = 'announcement_advanced_filter';
  static const String _basicFilterKey = 'announcement_basic_filter';

  final Logger _logger = Logger();

  /// Save advanced filter configuration to local storage
  Future<void> saveAdvancedFilter(AnnouncementAdvancedFilter filter) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final filterJson = {
        'types': filter.types.map((type) => type.value).toList(),
        'priorities': filter.priorities
            .map((priority) => priority.value)
            .toList(),
        'usageTypes': filter.usageTypes
            .map((usageType) => usageType.value)
            .toList(),
        'startDate': filter.startDate?.toIso8601String(),
        'endDate': filter.endDate?.toIso8601String(),
        'isRead': filter.isRead,
        'isPinned': filter.isPinned,
        'isActive': filter.isActive,
        'classroomId': filter.classroomId,
        'authorId': filter.authorId,
        'tags': filter.tags,
      };

      await prefs.setString(_advancedFilterKey, jsonEncode(filterJson));
      _logger.i(
        'Saved advanced filter configuration with ${filter.activeFilterCount} active filters',
      );
    } catch (e) {
      _logger.e('Failed to save advanced filter configuration: $e');
    }
  }

  /// Load advanced filter configuration from local storage
  Future<AnnouncementAdvancedFilter?> loadAdvancedFilter() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final filterString = prefs.getString(_advancedFilterKey);

      if (filterString == null) return null;

      final filterJson = jsonDecode(filterString) as Map<String, dynamic>;

      // Parse types
      final typeValues =
          (filterJson['types'] as List<dynamic>?)?.cast<String>() ?? [];
      final types = typeValues
          .map(
            (value) => AnnouncementType.values.firstWhere(
              (type) => type.value == value,
              orElse: () => AnnouncementType.notice,
            ),
          )
          .toSet();

      // Parse priorities
      final priorityValues =
          (filterJson['priorities'] as List<dynamic>?)?.cast<String>() ?? [];
      final priorities = priorityValues
          .map(
            (value) => AnnouncementPriority.values.firstWhere(
              (priority) => priority.value == value,
              orElse: () => AnnouncementPriority.normal,
            ),
          )
          .toSet();

      // Parse usage types
      final usageTypeValues =
          (filterJson['usageTypes'] as List<dynamic>?)?.cast<String>() ?? [];
      final usageTypes = usageTypeValues
          .map(
            (value) => AnnouncementUsageType.values.firstWhere(
              (usageType) => usageType.value == value,
              orElse: () => AnnouncementUsageType.organization,
            ),
          )
          .toSet();

      // Parse dates
      DateTime? startDate;
      DateTime? endDate;

      if (filterJson['startDate'] != null) {
        startDate = DateTime.tryParse(filterJson['startDate'] as String);
      }

      if (filterJson['endDate'] != null) {
        endDate = DateTime.tryParse(filterJson['endDate'] as String);
      }

      final filter = AnnouncementAdvancedFilter(
        types: types,
        priorities: priorities,
        usageTypes: usageTypes,
        startDate: startDate,
        endDate: endDate,
        isRead: filterJson['isRead'] as bool?,
        isPinned: filterJson['isPinned'] as bool?,
        isActive: filterJson['isActive'] as bool?,
        classroomId: filterJson['classroomId'] as String?,
        authorId: filterJson['authorId'] as String?,
        tags: (filterJson['tags'] as List<dynamic>?)?.cast<String>() ?? [],
      );

      _logger.i(
        'Loaded advanced filter configuration with ${filter.activeFilterCount} active filters',
      );
      return filter;
    } catch (e) {
      _logger.e('Failed to load advanced filter configuration: $e');
      return null;
    }
  }

  /// Save basic filter type to local storage
  Future<void> saveBasicFilter(AnnouncementFilterType filterType) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_basicFilterKey, filterType.name);
      _logger.i('Saved basic filter: ${filterType.displayName}');
    } catch (e) {
      _logger.e('Failed to save basic filter: $e');
    }
  }

  /// Load basic filter type from local storage
  Future<AnnouncementFilterType?> loadBasicFilter() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final filterString = prefs.getString(_basicFilterKey);

      if (filterString == null) return null;

      final filterType = AnnouncementFilterType.values.firstWhere(
        (type) => type.name == filterString,
        orElse: () => AnnouncementFilterType.all,
      );

      _logger.i('Loaded basic filter: ${filterType.displayName}');
      return filterType;
    } catch (e) {
      _logger.e('Failed to load basic filter: $e');
      return null;
    }
  }

  /// Clear all saved preferences
  Future<void> clearAllPreferences() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await Future.wait([
        prefs.remove(_advancedFilterKey),
        prefs.remove(_basicFilterKey),
      ]);
      _logger.i('Cleared all announcement preferences');
    } catch (e) {
      _logger.e('Failed to clear announcement preferences: $e');
    }
  }

  /// Check if any preferences are saved
  Future<bool> hasAnyPreferences() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.containsKey(_advancedFilterKey) ||
          prefs.containsKey(_basicFilterKey);
    } catch (e) {
      _logger.e('Failed to check for saved preferences: $e');
      return false;
    }
  }

  /// Get a summary of saved preferences
  Future<Map<String, dynamic>> getPreferencesSummary() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return {
        'hasAdvancedFilter': prefs.containsKey(_advancedFilterKey),
        'hasBasicFilter': prefs.containsKey(_basicFilterKey),
        'advancedFilter': await loadAdvancedFilter(),
        'basicFilter': await loadBasicFilter(),
      };
    } catch (e) {
      _logger.e('Failed to get preferences summary: $e');
      return {};
    }
  }
}
