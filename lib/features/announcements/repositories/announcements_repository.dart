import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:logger/logger.dart';

import '../../../core/constants/firebase_collections.dart';
import '../models/announcement_model.dart';
import '../enums/announcement_type.dart';
import '../enums/announcement_priority.dart';
import '../enums/announcement_usage_type.dart';
import '../enums/announcement_status.dart';

/// Repository for managing announcements data with Firebase Firestore
/// Handles CRUD operations, filtering, and read tracking for announcements
class AnnouncementsRepository {
  static final AnnouncementsRepository _instance =
      AnnouncementsRepository._internal();
  factory AnnouncementsRepository() => _instance;
  AnnouncementsRepository._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final Logger _logger = Logger();

  // Collection names
  static const String _announcementsCollection =
      FirebaseCollections.announcements;
  static const String _announcementReadsCollection =
      FirebaseCollections.announcementReads;

  /// Get all announcements for a specific user
  /// Filters based on target audience and visibility rules
  Future<List<AnnouncementModel>> getAnnouncementsForUser(String userId) async {
    try {
      _logger.i('Fetching announcements for user: $userId');

      // Get announcements where user is in target audience or it's organization-wide
      final querySnapshot = await _firestore
          .collection(_announcementsCollection)
          .where('isActive', isEqualTo: true)
          .where('status', isEqualTo: AnnouncementStatus.published.value)
          .orderBy('createdAt', descending: true)
          .get();

      final announcements = <AnnouncementModel>[];

      for (final doc in querySnapshot.docs) {
        try {
          final data = doc.data();
          data['id'] = doc.id;
          final announcement = AnnouncementModel.fromJson(data);

          // Check if user is in target audience or if it's a general announcement
          if (_isAnnouncementVisibleToUser(announcement, userId)) {
            announcements.add(announcement);
          }
        } catch (e) {
          _logger.e('Error parsing announcement document ${doc.id}: $e');
        }
      }

      _logger.i(
        'Successfully fetched ${announcements.length} announcements for user $userId',
      );
      return announcements;
    } catch (e, stackTrace) {
      _logger.e(
        'Error fetching announcements for user: $e',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// Get announcements for a specific classroom
  Future<List<AnnouncementModel>> getAnnouncementsForClassroom(
    String classroomId,
  ) async {
    try {
      _logger.i('Fetching announcements for classroom: $classroomId');

      final querySnapshot = await _firestore
          .collection(_announcementsCollection)
          .where('classroomId', isEqualTo: classroomId)
          .where('isActive', isEqualTo: true)
          .where('status', isEqualTo: AnnouncementStatus.published.value)
          .orderBy('createdAt', descending: true)
          .get();

      final announcements = querySnapshot.docs
          .map((doc) {
            try {
              final data = doc.data();
              data['id'] = doc.id;
              return AnnouncementModel.fromJson(data);
            } catch (e) {
              _logger.e('Error parsing announcement document ${doc.id}: $e');
              return null;
            }
          })
          .where((announcement) => announcement != null)
          .cast<AnnouncementModel>()
          .toList();

      _logger.i(
        'Successfully fetched ${announcements.length} announcements for classroom $classroomId',
      );
      return announcements;
    } catch (e, stackTrace) {
      _logger.e(
        'Error fetching announcements for classroom: $e',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// Get a specific announcement by ID
  Future<AnnouncementModel?> getAnnouncementById(String announcementId) async {
    try {
      _logger.i('Fetching announcement by ID: $announcementId');

      final doc = await _firestore
          .collection(_announcementsCollection)
          .doc(announcementId)
          .get();

      if (!doc.exists) {
        _logger.w('Announcement not found: $announcementId');
        return null;
      }

      final data = doc.data()!;
      data['id'] = doc.id;
      final announcement = AnnouncementModel.fromJson(data);

      _logger.i('Successfully fetched announcement: ${announcement.title}');
      return announcement;
    } catch (e, stackTrace) {
      _logger.e(
        'Error fetching announcement by ID: $e',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// Get announcements filtered by type
  Future<List<AnnouncementModel>> getAnnouncementsByType(
    String userId,
    AnnouncementType type,
  ) async {
    try {
      _logger.i(
        'Fetching announcements by type: ${type.displayName} for user: $userId',
      );

      final allAnnouncements = await getAnnouncementsForUser(userId);
      final filteredAnnouncements = allAnnouncements
          .where((announcement) => announcement.type == type)
          .toList();

      _logger.i(
        'Successfully fetched ${filteredAnnouncements.length} announcements of type ${type.displayName}',
      );
      return filteredAnnouncements;
    } catch (e) {
      _logger.e('Error fetching announcements by type: $e');
      rethrow;
    }
  }

  /// Get announcements filtered by usage type
  Future<List<AnnouncementModel>> getAnnouncementsByUsageType(
    String userId,
    AnnouncementUsageType usageType,
  ) async {
    try {
      _logger.i(
        'Fetching announcements by usage type: ${usageType.displayName} for user: $userId',
      );

      final allAnnouncements = await getAnnouncementsForUser(userId);
      final filteredAnnouncements = allAnnouncements
          .where((announcement) => announcement.usageType == usageType)
          .toList();

      _logger.i(
        'Successfully fetched ${filteredAnnouncements.length} announcements of usage type ${usageType.displayName}',
      );
      return filteredAnnouncements;
    } catch (e) {
      _logger.e('Error fetching announcements by usage type: $e');
      rethrow;
    }
  }

  /// Get announcements filtered by priority
  Future<List<AnnouncementModel>> getAnnouncementsByPriority(
    String userId,
    AnnouncementPriority priority,
  ) async {
    try {
      _logger.i(
        'Fetching announcements by priority: ${priority.displayName} for user: $userId',
      );

      final allAnnouncements = await getAnnouncementsForUser(userId);
      final filteredAnnouncements = allAnnouncements
          .where((announcement) => announcement.priority == priority)
          .toList();

      _logger.i(
        'Successfully fetched ${filteredAnnouncements.length} announcements of priority ${priority.displayName}',
      );
      return filteredAnnouncements;
    } catch (e) {
      _logger.e('Error fetching announcements by priority: $e');
      rethrow;
    }
  }

  /// Search announcements by title and content
  Future<List<AnnouncementModel>> searchAnnouncements(
    String userId,
    String query,
  ) async {
    try {
      _logger.i('Searching announcements for user: $userId with query: $query');

      final allAnnouncements = await getAnnouncementsForUser(userId);
      final searchQuery = query.toLowerCase();

      final filteredAnnouncements = allAnnouncements.where((announcement) {
        return announcement.title.toLowerCase().contains(searchQuery) ||
            announcement.content.toLowerCase().contains(searchQuery) ||
            announcement.tags.any(
              (tag) => tag.toLowerCase().contains(searchQuery),
            );
      }).toList();

      _logger.i(
        'Found ${filteredAnnouncements.length} announcements matching query: $query',
      );
      return filteredAnnouncements;
    } catch (e) {
      _logger.e('Error searching announcements: $e');
      rethrow;
    }
  }

  /// Get unread announcements for a user
  Future<List<AnnouncementModel>> getUnreadAnnouncements(String userId) async {
    try {
      _logger.i('Fetching unread announcements for user: $userId');

      final allAnnouncements = await getAnnouncementsForUser(userId);
      final unreadAnnouncements = allAnnouncements
          .where((announcement) => !announcement.isReadBy(userId))
          .toList();

      _logger.i(
        'Found ${unreadAnnouncements.length} unread announcements for user $userId',
      );
      return unreadAnnouncements;
    } catch (e) {
      _logger.e('Error fetching unread announcements: $e');
      rethrow;
    }
  }

  /// Get important announcements (urgent and emergency priority)
  Future<List<AnnouncementModel>> getImportantAnnouncements(
    String userId,
  ) async {
    try {
      _logger.i('Fetching important announcements for user: $userId');

      final allAnnouncements = await getAnnouncementsForUser(userId);
      final importantAnnouncements = allAnnouncements.where((announcement) {
        return announcement.priority == AnnouncementPriority.urgent ||
            announcement.priority == AnnouncementPriority.emergency;
      }).toList();

      _logger.i(
        'Found ${importantAnnouncements.length} important announcements for user $userId',
      );
      return importantAnnouncements;
    } catch (e) {
      _logger.e('Error fetching important announcements: $e');
      rethrow;
    }
  }

  /// Get pinned announcements for a user
  Future<List<AnnouncementModel>> getPinnedAnnouncements(String userId) async {
    try {
      _logger.i('Fetching pinned announcements for user: $userId');

      final allAnnouncements = await getAnnouncementsForUser(userId);
      final pinnedAnnouncements = allAnnouncements
          .where((announcement) => announcement.isPinned)
          .toList();

      _logger.i(
        'Found ${pinnedAnnouncements.length} pinned announcements for user $userId',
      );
      return pinnedAnnouncements;
    } catch (e) {
      _logger.e('Error fetching pinned announcements: $e');
      rethrow;
    }
  }

  /// Mark announcement as read for a user
  Future<void> markAnnouncementAsRead(
    String announcementId,
    String userId,
  ) async {
    try {
      _logger.i(
        'Marking announcement $announcementId as read for user: $userId',
      );

      // Create read record in separate collection for better performance
      final readRecord = {
        'announcementId': announcementId,
        'userId': userId,
        'readAt': DateTime.now().toIso8601String(),
      };

      await _firestore
          .collection(_announcementReadsCollection)
          .doc('${announcementId}_$userId')
          .set(readRecord);

      // Also update the announcement document's readBy array
      await _firestore
          .collection(_announcementsCollection)
          .doc(announcementId)
          .update({
            'readBy': FieldValue.arrayUnion([userId]),
            'readTimestamps.$userId': DateTime.now().toIso8601String(),
            'viewCount': FieldValue.increment(1),
          });

      _logger.i('Successfully marked announcement as read');
    } catch (e, stackTrace) {
      _logger.e(
        'Error marking announcement as read: $e',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// Check if announcement is read by user
  Future<bool> isAnnouncementReadByUser(
    String announcementId,
    String userId,
  ) async {
    try {
      _logger.i(
        'Checking if announcement $announcementId is read by user: $userId',
      );

      final readDoc = await _firestore
          .collection(_announcementReadsCollection)
          .doc('${announcementId}_$userId')
          .get();

      final isRead = readDoc.exists;
      _logger.i('Announcement read status: $isRead');
      return isRead;
    } catch (e, stackTrace) {
      _logger.e(
        'Error checking announcement read status: $e',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// Get read status for multiple announcements
  Future<Map<String, bool>> getReadStatusForAnnouncements(
    List<String> announcementIds,
    String userId,
  ) async {
    try {
      _logger.i(
        'Getting read status for ${announcementIds.length} announcements for user: $userId',
      );

      final readStatus = <String, bool>{};

      // Batch read all read records
      final futures = announcementIds.map((announcementId) async {
        final readDoc = await _firestore
            .collection(_announcementReadsCollection)
            .doc('${announcementId}_$userId')
            .get();
        return MapEntry(announcementId, readDoc.exists);
      });

      final results = await Future.wait(futures);
      for (final result in results) {
        readStatus[result.key] = result.value;
      }

      _logger.i(
        'Successfully retrieved read status for ${readStatus.length} announcements',
      );
      return readStatus;
    } catch (e, stackTrace) {
      _logger.e(
        'Error getting read status for announcements: $e',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// Helper method to check if an announcement is visible to a user
  bool _isAnnouncementVisibleToUser(
    AnnouncementModel announcement,
    String userId,
  ) {
    // Check if user is explicitly in target audience
    if (announcement.targetAudience.contains(userId)) {
      return true;
    }

    // Check if it's an organization-wide announcement (empty target audience means everyone)
    if (announcement.targetAudience.isEmpty &&
        announcement.usageType == AnnouncementUsageType.organization) {
      return true;
    }

    // Check if it's a general announcement with no specific targeting
    if (announcement.targetAudience.isEmpty &&
        announcement.usageType == AnnouncementUsageType.community) {
      return true;
    }

    return false;
  }
}
