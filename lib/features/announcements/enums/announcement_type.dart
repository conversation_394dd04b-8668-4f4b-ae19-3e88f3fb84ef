import 'package:flutter/material.dart';
import 'package:material_symbols_icons/symbols.dart';

/// Enum representing different types of announcements
enum AnnouncementType { notice, reminder, news, alert, general }

/// Extension to provide additional functionality for AnnouncementType
extension AnnouncementTypeExtension on AnnouncementType {
  /// Get a user-friendly display name for the announcement type
  String get displayName {
    switch (this) {
      case AnnouncementType.notice:
        return 'Notice';
      case AnnouncementType.reminder:
        return 'Reminder';
      case AnnouncementType.news:
        return 'News';
      case AnnouncementType.alert:
        return 'Alert';
      case AnnouncementType.general:
        return 'General';
    }
  }

  /// Get the string value for the announcement type (for JSON serialization)
  String get value {
    switch (this) {
      case AnnouncementType.notice:
        return 'notice';
      case AnnouncementType.reminder:
        return 'reminder';
      case AnnouncementType.news:
        return 'news';
      case AnnouncementType.alert:
        return 'alert';
      case AnnouncementType.general:
        return 'general';
    }
  }

  /// Get a description for the announcement type
  String get description {
    switch (this) {
      case AnnouncementType.notice:
        return 'Official announcements and important notices';
      case AnnouncementType.reminder:
        return 'Reminders for deadlines and tasks';
      case AnnouncementType.news:
        return 'News, achievements, and updates';
      case AnnouncementType.alert:
        return 'Urgent alerts and emergency notifications';
      case AnnouncementType.general:
        return 'General announcements and information';
    }
  }

  /// Get the icon for the announcement type
  IconData get icon {
    switch (this) {
      case AnnouncementType.notice:
        return Symbols.campaign;
      case AnnouncementType.reminder:
        return Symbols.alarm;
      case AnnouncementType.news:
        return Symbols.newspaper;
      case AnnouncementType.alert:
        return Symbols.warning;
      case AnnouncementType.general:
        return Symbols.info;
    }
  }

  /// Get the color for the announcement type
  Color get color {
    switch (this) {
      case AnnouncementType.notice:
        return Colors.blue;
      case AnnouncementType.reminder:
        return Colors.orange;
      case AnnouncementType.news:
        return Colors.purple;
      case AnnouncementType.alert:
        return Colors.red;
      case AnnouncementType.general:
        return Colors.grey;
    }
  }

  /// Create AnnouncementType from string value
  static AnnouncementType fromString(String value) {
    switch (value.toLowerCase()) {
      case 'notice':
        return AnnouncementType.notice;
      case 'reminder':
        return AnnouncementType.reminder;
      case 'news':
        return AnnouncementType.news;
      case 'alert':
        return AnnouncementType.alert;
      case 'general':
        return AnnouncementType.general;
      default:
        return AnnouncementType.general; // Default fallback
    }
  }

  /// Get all announcement types as a list
  static List<AnnouncementType> get allTypes => AnnouncementType.values;

  /// Check if this announcement type is high priority
  bool get isHighPriority {
    switch (this) {
      case AnnouncementType.alert:
      case AnnouncementType.reminder:
        return true;
      case AnnouncementType.notice:
      case AnnouncementType.news:
      case AnnouncementType.general:
        return false;
    }
  }
}
