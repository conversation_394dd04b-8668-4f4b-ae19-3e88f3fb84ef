/// Enum representing different status states for announcements
enum AnnouncementStatus {
  /// Draft announcement (not yet published)
  draft,

  /// Scheduled announcement (will be published later)
  scheduled,

  /// Published announcement (currently active)
  published,

  /// Expired announcement (past expiration date)
  expired,
}

/// Extension to provide additional functionality for AnnouncementStatus
extension AnnouncementStatusExtension on AnnouncementStatus {
  /// Get a user-friendly display name for the announcement status
  String get displayName {
    switch (this) {
      case AnnouncementStatus.draft:
        return 'Draft';
      case AnnouncementStatus.scheduled:
        return 'Scheduled';
      case AnnouncementStatus.published:
        return 'Published';
      case AnnouncementStatus.expired:
        return 'Expired';
    }
  }

  /// Get the string value for the announcement status (for JSON serialization)
  String get value {
    switch (this) {
      case AnnouncementStatus.draft:
        return 'draft';
      case AnnouncementStatus.scheduled:
        return 'scheduled';
      case AnnouncementStatus.published:
        return 'published';
      case AnnouncementStatus.expired:
        return 'expired';
    }
  }

  /// Get a description for the announcement status
  String get description {
    switch (this) {
      case AnnouncementStatus.draft:
        return 'Draft announcement not yet published';
      case AnnouncementStatus.scheduled:
        return 'Scheduled for future publication';
      case AnnouncementStatus.published:
        return 'Currently active and visible';
      case AnnouncementStatus.expired:
        return 'Past expiration date';
    }
  }

  /// Create AnnouncementStatus from string value
  static AnnouncementStatus fromString(String value) {
    switch (value.toLowerCase()) {
      case 'draft':
        return AnnouncementStatus.draft;
      case 'scheduled':
        return AnnouncementStatus.scheduled;
      case 'published':
        return AnnouncementStatus.published;
      case 'expired':
        return AnnouncementStatus.expired;

      default:
        return AnnouncementStatus.published; // Default fallback
    }
  }

  /// Get all announcement statuses as a list
  static List<AnnouncementStatus> get allStatuses => AnnouncementStatus.values;

  /// Check if this status is visible to users
  bool get isVisible {
    switch (this) {
      case AnnouncementStatus.published:
        return true;
      case AnnouncementStatus.draft:
      case AnnouncementStatus.scheduled:
      case AnnouncementStatus.expired:
        return false;
    }
  }

  /// Check if this status is editable
  bool get isEditable {
    switch (this) {
      case AnnouncementStatus.draft:
      case AnnouncementStatus.scheduled:
        return true;
      case AnnouncementStatus.published:
      case AnnouncementStatus.expired:
        return false;
    }
  }

  /// Check if this status can be published
  bool get canBePublished {
    switch (this) {
      case AnnouncementStatus.draft:
      case AnnouncementStatus.scheduled:
        return true;
      case AnnouncementStatus.published:
      case AnnouncementStatus.expired:
        return false;
    }
  }
}
