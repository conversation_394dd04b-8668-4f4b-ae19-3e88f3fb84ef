import 'package:flutter/material.dart';

/// Enum representing different priority levels for announcements
enum AnnouncementPriority {
  /// Normal priority announcements
  normal,

  /// Important announcements that need attention
  important,

  /// Urgent announcements requiring immediate attention
  urgent,

  /// Emergency announcements with highest priority
  emergency,
}

/// Extension to provide additional functionality for AnnouncementPriority
extension AnnouncementPriorityExtension on AnnouncementPriority {
  /// Get a user-friendly display name for the announcement priority
  String get displayName {
    switch (this) {
      case AnnouncementPriority.normal:
        return 'Normal';
      case AnnouncementPriority.important:
        return 'Important';
      case AnnouncementPriority.urgent:
        return 'Urgent';
      case AnnouncementPriority.emergency:
        return 'Emergency';
    }
  }

  /// Get the string value for the announcement priority (for JSON serialization)
  String get value {
    switch (this) {
      case AnnouncementPriority.normal:
        return 'normal';
      case AnnouncementPriority.important:
        return 'important';
      case AnnouncementPriority.urgent:
        return 'urgent';
      case AnnouncementPriority.emergency:
        return 'emergency';
    }
  }

  /// Get a description for the announcement priority
  String get description {
    switch (this) {
      case AnnouncementPriority.normal:
        return 'Standard priority announcement';
      case AnnouncementPriority.important:
        return 'Important announcement requiring attention';
      case AnnouncementPriority.urgent:
        return 'Urgent announcement needing immediate attention';
      case AnnouncementPriority.emergency:
        return 'Emergency announcement with highest priority';
    }
  }

  /// Get the color associated with this priority level
  Color get color {
    switch (this) {
      case AnnouncementPriority.normal:
        return Colors.blue;
      case AnnouncementPriority.important:
        return Colors.orange;
      case AnnouncementPriority.urgent:
        return Colors.red;
      case AnnouncementPriority.emergency:
        return Colors.red.shade900;
    }
  }

  /// Get the icon associated with this priority level
  IconData get icon {
    switch (this) {
      case AnnouncementPriority.normal:
        return Icons.info_outline;
      case AnnouncementPriority.important:
        return Icons.priority_high;
      case AnnouncementPriority.urgent:
        return Icons.warning;
      case AnnouncementPriority.emergency:
        return Icons.emergency;
    }
  }

  /// Create AnnouncementPriority from string value
  static AnnouncementPriority fromString(String value) {
    switch (value.toLowerCase()) {
      case 'normal':
        return AnnouncementPriority.normal;
      case 'important':
        return AnnouncementPriority.important;
      case 'urgent':
        return AnnouncementPriority.urgent;
      case 'emergency':
        return AnnouncementPriority.emergency;
      default:
        return AnnouncementPriority.normal; // Default fallback
    }
  }

  /// Get all announcement priorities as a list
  static List<AnnouncementPriority> get allPriorities => AnnouncementPriority.values;

  /// Get the numeric value for sorting (higher number = higher priority)
  int get sortValue {
    switch (this) {
      case AnnouncementPriority.normal:
        return 1;
      case AnnouncementPriority.important:
        return 2;
      case AnnouncementPriority.urgent:
        return 3;
      case AnnouncementPriority.emergency:
        return 4;
    }
  }

  /// Check if this priority level requires immediate attention
  bool get requiresImmediateAttention {
    switch (this) {
      case AnnouncementPriority.urgent:
      case AnnouncementPriority.emergency:
        return true;
      case AnnouncementPriority.normal:
      case AnnouncementPriority.important:
        return false;
    }
  }

  /// Check if this priority level should show notification badge
  bool get showsBadge {
    switch (this) {
      case AnnouncementPriority.important:
      case AnnouncementPriority.urgent:
      case AnnouncementPriority.emergency:
        return true;
      case AnnouncementPriority.normal:
        return false;
    }
  }
}
