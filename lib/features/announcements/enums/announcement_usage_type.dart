import 'package:flutter/material.dart';
import 'package:material_symbols_icons/symbols.dart';

/// Enum representing different usage types for announcements
/// This determines how announcements are categorized and used within different features
enum AnnouncementUsageType {
  classroom,

  /// Event-related announcements (school events, activities, programs)
  events,

  /// Individual/personal announcements (direct messages, personal reminders)
  individual,

  /// Organization-wide announcements (school-wide notices, policies)
  organization,

  /// Academic announcements (curriculum, exams, academic calendar)
  academic,

  /// Community announcements (clubs, societies, extracurricular activities)
  community,
}

/// Extension to provide additional functionality for AnnouncementUsageType
extension AnnouncementUsageTypeExtension on AnnouncementUsageType {
  /// Get a user-friendly display name for the announcement usage type
  String get displayName {
    switch (this) {
      case AnnouncementUsageType.classroom:
        return 'Classroom';
      case AnnouncementUsageType.events:
        return 'Events';
      case AnnouncementUsageType.individual:
        return 'Individual';
      case AnnouncementUsageType.organization:
        return 'Organization';
      case AnnouncementUsageType.academic:
        return 'Academic';
      case AnnouncementUsageType.community:
        return 'Community';
    }
  }

  /// Get the string value for the announcement usage type (for JSON serialization)
  String get value {
    switch (this) {
      case AnnouncementUsageType.classroom:
        return 'classroom';
      case AnnouncementUsageType.events:
        return 'events';
      case AnnouncementUsageType.individual:
        return 'individual';
      case AnnouncementUsageType.organization:
        return 'organization';
      case AnnouncementUsageType.academic:
        return 'academic';
      case AnnouncementUsageType.community:
        return 'community';
    }
  }

  /// Get a description for the announcement usage type
  String get description {
    switch (this) {
      case AnnouncementUsageType.classroom:
        return 'Class-specific announcements for students and teachers';
      case AnnouncementUsageType.events:
        return 'Event-related announcements and notifications';
      case AnnouncementUsageType.individual:
        return 'Personal announcements for individual users';
      case AnnouncementUsageType.organization:
        return 'School-wide announcements for all users';
      case AnnouncementUsageType.academic:
        return 'Academic announcements (curriculum, exams, courses)';
      case AnnouncementUsageType.community:
        return 'Community announcements (clubs, societies, activities)';
    }
  }

  /// Get the icon for the announcement usage type
  IconData get icon {
    switch (this) {
      case AnnouncementUsageType.classroom:
        return Symbols.school;
      case AnnouncementUsageType.events:
        return Symbols.event;
      case AnnouncementUsageType.individual:
        return Symbols.person;
      case AnnouncementUsageType.organization:
        return Symbols.business;
      case AnnouncementUsageType.academic:
        return Symbols.menu_book;
      case AnnouncementUsageType.community:
        return Symbols.groups;
    }
  }

  /// Get the color for the announcement usage type
  Color get color {
    switch (this) {
      case AnnouncementUsageType.classroom:
        return Colors.purple;
      case AnnouncementUsageType.events:
        return Colors.green;
      case AnnouncementUsageType.individual:
        return Colors.blue;
      case AnnouncementUsageType.organization:
        return Colors.indigo;
      case AnnouncementUsageType.academic:
        return Colors.orange;
      case AnnouncementUsageType.community:
        return Colors.pink;
    }
  }

  /// Create AnnouncementUsageType from string value
  static AnnouncementUsageType fromString(String value) {
    switch (value.toLowerCase()) {
      case 'classroom':
        return AnnouncementUsageType.classroom;
      case 'events':
        return AnnouncementUsageType.events;
      case 'individual':
        return AnnouncementUsageType.individual;
      case 'organization':
        return AnnouncementUsageType.organization;
      case 'academic':
        return AnnouncementUsageType.academic;
      case 'community':
        return AnnouncementUsageType.community;
      default:
        return AnnouncementUsageType.organization; // Default fallback
    }
  }

  /// Get all announcement usage types as a list
  static List<AnnouncementUsageType> get allUsageTypes => AnnouncementUsageType.values;

  /// Check if this usage type requires specific targeting
  bool get requiresSpecificTargeting {
    switch (this) {
      case AnnouncementUsageType.classroom:
      case AnnouncementUsageType.individual:
      case AnnouncementUsageType.academic:
      case AnnouncementUsageType.community:
        return true;
      case AnnouncementUsageType.events:
      case AnnouncementUsageType.organization:
        return false;
    }
  }

  /// Check if this usage type supports multiple audience selections
  bool get supportsMultipleSelections {
    switch (this) {
      case AnnouncementUsageType.individual:
      case AnnouncementUsageType.academic:
      case AnnouncementUsageType.community:
        return true;
      case AnnouncementUsageType.classroom:
      case AnnouncementUsageType.events:
      case AnnouncementUsageType.organization:
        return false;
    }
  }

  /// Check if this usage type is typically used for urgent announcements
  bool get isTypicallyUrgent {
    switch (this) {
      case AnnouncementUsageType.academic:
      case AnnouncementUsageType.individual:
        return true;
      case AnnouncementUsageType.classroom:
      case AnnouncementUsageType.events:
      case AnnouncementUsageType.organization:
      case AnnouncementUsageType.community:
        return false;
    }
  }
}
