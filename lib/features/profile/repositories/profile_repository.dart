import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:logger/logger.dart';

import '../../../core/constants/firebase_collections.dart';
import '../models/profile_model.dart';
import '../models/profile_update_model.dart';

/// Repository for managing profile data with Firebase Firestore
class ProfileRepository {
  static final ProfileRepository _instance = ProfileRepository._internal();
  factory ProfileRepository() => _instance;
  ProfileRepository._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final Logger _logger = Logger();

  // Collection names
  static const String _profilesCollection = FirebaseCollections.users;

  /// Fetch a user's profile by user ID
  Future<ProfileModel?> getProfileById(String userId) async {
    try {
      _logger.i('Fetching profile for user ID: $userId');

      final docSnapshot = await _firestore
          .collection(_profilesCollection)
          .doc(userId)
          .get();

      if (!docSnapshot.exists) {
        _logger.w('Profile not found for user ID: $userId');
        return null;
      }

      final data = docSnapshot.data()!;
      data['id'] = docSnapshot.id; // Ensure the document ID is included

      final profile = ProfileModel.fromJson(data);
      _logger.i('Successfully fetched profile for user: ${profile.fullName}');
      return profile;
    } on FirebaseException catch (e) {
      _logger.e('Firebase error fetching profile: ${e.message}');
      throw Exception('Failed to fetch profile: ${e.message}');
    } catch (e) {
      _logger.e('Unexpected error fetching profile: $e');
      throw Exception('Failed to fetch profile: $e');
    }
  }

  /// Create a new user profile
  Future<ProfileModel> createProfile(ProfileModel profile) async {
    try {
      _logger.i('Creating profile for user: ${profile.fullName}');

      final profileData = profile.toJson();
      profileData['createdAt'] = DateTime.now().toIso8601String();
      profileData['updatedAt'] = DateTime.now().toIso8601String();

      await _firestore
          .collection(_profilesCollection)
          .doc(profile.id)
          .set(profileData);

      final createdProfile = profile.copyWith(
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      _logger.i('Successfully created profile for user: ${profile.fullName}');
      return createdProfile;
    } on FirebaseException catch (e) {
      _logger.e('Firebase error creating profile: ${e.message}');
      throw Exception('Failed to create profile: ${e.message}');
    } catch (e) {
      _logger.e('Unexpected error creating profile: $e');
      throw Exception('Failed to create profile: $e');
    }
  }

  /// Update an existing user profile
  Future<ProfileModel> updateProfile(
    String userId,
    ProfileUpdateModel updateModel,
  ) async {
    try {
      _logger.i('Updating profile for user ID: $userId');

      if (!updateModel.hasUpdates) {
        _logger.w('No updates provided for profile update');
        throw Exception('No updates provided');
      }

      final updateData = updateModel.toJson();

      await _firestore
          .collection(_profilesCollection)
          .doc(userId)
          .update(updateData);

      // Fetch the updated profile
      final updatedProfile = await getProfileById(userId);
      if (updatedProfile == null) {
        throw Exception('Failed to fetch updated profile');
      }

      _logger.i(
        'Successfully updated profile for user: ${updatedProfile.fullName}',
      );
      return updatedProfile;
    } on FirebaseException catch (e) {
      _logger.e('Firebase error updating profile: ${e.message}');
      throw Exception('Failed to update profile: ${e.message}');
    } catch (e) {
      _logger.e('Unexpected error updating profile: $e');
      throw Exception('Failed to update profile: $e');
    }
  }

  /// Delete a user profile
  Future<void> deleteProfile(String userId) async {
    try {
      _logger.i('Deleting profile for user ID: $userId');

      await _firestore.collection(_profilesCollection).doc(userId).delete();

      _logger.i('Successfully deleted profile for user ID: $userId');
    } on FirebaseException catch (e) {
      _logger.e('Firebase error deleting profile: ${e.message}');
      throw Exception('Failed to delete profile: ${e.message}');
    } catch (e) {
      _logger.e('Unexpected error deleting profile: $e');
      throw Exception('Failed to delete profile: $e');
    }
  }

  /// Fetch profiles by class ID
  Future<List<ProfileModel>> getProfilesByClassId(String classId) async {
    try {
      _logger.i('Fetching profiles for class ID: $classId');

      final querySnapshot = await _firestore
          .collection(_profilesCollection)
          .where('classIds', arrayContains: classId)
          .where('isActive', isEqualTo: true)
          .orderBy('fullName')
          .get();

      final profiles = querySnapshot.docs
          .map((doc) {
            try {
              final data = doc.data();
              data['id'] = doc.id; // Ensure the document ID is included
              return ProfileModel.fromJson(data);
            } catch (e) {
              _logger.e('Error parsing profile document ${doc.id}: $e');
              return null;
            }
          })
          .where((profile) => profile != null)
          .cast<ProfileModel>()
          .toList();

      _logger.i('Successfully fetched ${profiles.length} profiles for class');
      return profiles;
    } on FirebaseException catch (e) {
      _logger.e('Firebase error fetching profiles by class: ${e.message}');
      throw Exception('Failed to fetch profiles: ${e.message}');
    } catch (e) {
      _logger.e('Unexpected error fetching profiles by class: $e');
      throw Exception('Failed to fetch profiles: $e');
    }
  }

  /// Fetch profiles by user type
  Future<List<ProfileModel>> getProfilesByUserType(String userType) async {
    try {
      _logger.i('Fetching profiles for user type: $userType');

      final querySnapshot = await _firestore
          .collection(_profilesCollection)
          .where('userType', isEqualTo: userType)
          .where('isActive', isEqualTo: true)
          .orderBy('fullName')
          .get();

      final profiles = querySnapshot.docs
          .map((doc) {
            try {
              final data = doc.data();
              data['id'] = doc.id; // Ensure the document ID is included
              return ProfileModel.fromJson(data);
            } catch (e) {
              _logger.e('Error parsing profile document ${doc.id}: $e');
              return null;
            }
          })
          .where((profile) => profile != null)
          .cast<ProfileModel>()
          .toList();

      _logger.i(
        'Successfully fetched ${profiles.length} profiles for user type',
      );
      return profiles;
    } on FirebaseException catch (e) {
      _logger.e('Firebase error fetching profiles by user type: ${e.message}');
      throw Exception('Failed to fetch profiles: ${e.message}');
    } catch (e) {
      _logger.e('Unexpected error fetching profiles by user type: $e');
      throw Exception('Failed to fetch profiles: $e');
    }
  }

  /// Search profiles by name
  Future<List<ProfileModel>> searchProfilesByName(String searchTerm) async {
    try {
      _logger.i('Searching profiles by name: $searchTerm');

      // Note: Firestore doesn't support case-insensitive search natively
      // This is a basic implementation that can be enhanced with Algolia or similar
      final querySnapshot = await _firestore
          .collection(_profilesCollection)
          .where('isActive', isEqualTo: true)
          .orderBy('fullName')
          .get();

      final profiles = querySnapshot.docs
          .map((doc) {
            try {
              final data = doc.data();
              data['id'] = doc.id; // Ensure the document ID is included
              return ProfileModel.fromJson(data);
            } catch (e) {
              _logger.e('Error parsing profile document ${doc.id}: $e');
              return null;
            }
          })
          .where((profile) => profile != null)
          .cast<ProfileModel>()
          .where(
            (profile) => profile.fullName.toLowerCase().contains(
              searchTerm.toLowerCase(),
            ),
          )
          .toList();

      _logger.i(
        'Successfully found ${profiles.length} profiles matching search term',
      );
      return profiles;
    } on FirebaseException catch (e) {
      _logger.e('Firebase error searching profiles: ${e.message}');
      throw Exception('Failed to search profiles: ${e.message}');
    } catch (e) {
      _logger.e('Unexpected error searching profiles: $e');
      throw Exception('Failed to search profiles: $e');
    }
  }

  /// Check if a profile exists for a user
  Future<bool> profileExists(String userId) async {
    try {
      _logger.i('Checking if profile exists for user ID: $userId');

      final docSnapshot = await _firestore
          .collection(_profilesCollection)
          .doc(userId)
          .get();

      final exists = docSnapshot.exists;
      _logger.i('Profile exists for user ID $userId: $exists');
      return exists;
    } on FirebaseException catch (e) {
      _logger.e('Firebase error checking profile existence: ${e.message}');
      throw Exception('Failed to check profile existence: ${e.message}');
    } catch (e) {
      _logger.e('Unexpected error checking profile existence: $e');
      throw Exception('Failed to check profile existence: $e');
    }
  }
}
