import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_symbols_icons/symbols.dart';
import '../models/uploaded_file_model.dart';
import '../../digital_library/models/file_model.dart';
import '../../digital_library/services/file_viewer_service.dart';

/// Tile widget for displaying uploaded files in submission screens
class SubmissionFileTile extends StatelessWidget {
  final UploadedFileModel uploadedFile;
  final VoidCallback onRemove;

  const SubmissionFileTile({
    super.key,
    required this.uploadedFile,
    required this.onRemove,
  });

  /// Handle file tap - open file in viewer
  void _handleFileTap(BuildContext context) {
    final fileViewerService = FileViewerService();
    final fileModel = FileModel.fromLocalFile(uploadedFile.file);

    fileViewerService.openFile(context, fileModel);
  }

  IconData _getFileIcon(String extension) {
    switch (extension) {
      case 'pdf':
        return Symbols.picture_as_pdf;
      case 'doc':
      case 'docx':
        return Symbols.description;
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
        return Symbols.image;
      case 'mp4':
      case 'mov':
      case 'avi':
        return Symbols.videocam;
      default:
        return Symbols.attach_file;
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final extension = uploadedFile.fileExtension;

    return InkWell(
      onTap: () => _handleFileTap(context),
      borderRadius: BorderRadius.circular(8.r),
      child: Container(
        padding: EdgeInsets.all(12.w),
        decoration: BoxDecoration(
          color: colorScheme.surface,
          border: Border.all(color: colorScheme.outline.withValues(alpha: 0.2)),
          borderRadius: BorderRadius.circular(8.r),
        ),
        child: Row(
          children: [
            Icon(
              _getFileIcon(extension),
              size: 24.sp,
              color: colorScheme.primary,
            ),
            SizedBox(width: 12.w),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    uploadedFile.fileName,
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: colorScheme.onSurface,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                  SizedBox(height: 2.h),
                  Text(
                    uploadedFile.fileSize,
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: colorScheme.onSurface.withValues(alpha: 0.6),
                    ),
                  ),
                ],
              ),
            ),
            IconButton(
              onPressed: onRemove,
              icon: Icon(Symbols.close, size: 20.sp, color: colorScheme.error),
              padding: EdgeInsets.zero,
              constraints: BoxConstraints(minWidth: 32.w, minHeight: 32.h),
            ),
          ],
        ),
      ),
    );
  }
}
