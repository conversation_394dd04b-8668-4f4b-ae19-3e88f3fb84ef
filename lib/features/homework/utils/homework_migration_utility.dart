import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';

import '../../../core/constants/firebase_collections.dart';
import '../../../core/enums/homework/assignment_type.dart';

/// Utility class for migrating existing homework data to support new assignment types
class HomeworkMigrationUtility {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static const String _homeworkCollection = FirebaseCollections.homeworks;

  /// Migrate existing homework documents to include assignment type fields
  static Future<void> migrateHomeworkData() async {
    try {
      debugPrint('Starting homework data migration...');

      // Get all homework documents
      final querySnapshot = await _firestore
          .collection(_homeworkCollection)
          .get();

      debugPrint(
        'Found ${querySnapshot.docs.length} homework documents to migrate',
      );

      int migratedCount = 0;
      int skippedCount = 0;

      for (final doc in querySnapshot.docs) {
        try {
          final data = doc.data();

          // Check if document already has assignment type fields
          if (data.containsKey('assignmentType') &&
              data.containsKey('assignedUserIds')) {
            debugPrint('Skipping ${doc.id} - already migrated');
            skippedCount++;
            continue;
          }

          // Prepare migration data
          final migrationData = <String, dynamic>{};

          // Add assignmentType field (default to classAssignment for backward compatibility)
          if (!data.containsKey('assignmentType')) {
            migrationData['assignmentType'] =
                AssignmentType.classAssignment.name;
          }

          // Add assignedUserIds field (empty list for class assignments)
          if (!data.containsKey('assignedUserIds')) {
            migrationData['assignedUserIds'] = <String>[];
          }

          // Update the document if there's data to migrate
          if (migrationData.isNotEmpty) {
            await doc.reference.update(migrationData);
            debugPrint('Migrated homework: ${doc.id}');
            migratedCount++;
          }
        } catch (e) {
          debugPrint('Error migrating homework ${doc.id}: $e');
        }
      }

      debugPrint(
        'Migration completed: $migratedCount migrated, $skippedCount skipped',
      );
    } catch (e) {
      debugPrint('Error during homework migration: $e');
      rethrow;
    }
  }

  /// Validate that all homework documents have the required fields
  static Future<bool> validateMigration() async {
    try {
      debugPrint('Validating homework migration...');

      final querySnapshot = await _firestore
          .collection(_homeworkCollection)
          .get();

      int validCount = 0;
      int invalidCount = 0;

      for (final doc in querySnapshot.docs) {
        final data = doc.data();

        final hasAssignmentType = data.containsKey('assignmentType');
        final hasAssignedUserIds = data.containsKey('assignedUserIds');

        if (hasAssignmentType && hasAssignedUserIds) {
          validCount++;
        } else {
          invalidCount++;
          debugPrint(
            'Invalid homework document ${doc.id}: missing assignmentType=$hasAssignmentType, assignedUserIds=$hasAssignedUserIds',
          );
        }
      }

      final isValid = invalidCount == 0;
      debugPrint(
        'Migration validation: $validCount valid, $invalidCount invalid documents',
      );

      return isValid;
    } catch (e) {
      debugPrint('Error validating migration: $e');
      return false;
    }
  }

  /// Create sample individual and group assignments for testing
  static Future<void> createSampleAssignments() async {
    try {
      debugPrint('Creating sample individual and group assignments...');

      // Sample individual assignment
      final individualAssignment = {
        'id': 'sample_individual_001',
        'subject': 'Mathematics',
        'title': 'Personal Math Practice',
        'description':
            'Complete the personalized math exercises based on your recent test results.',
        'assignedAt': DateTime.now()
            .subtract(const Duration(days: 2))
            .toIso8601String(),
        'dueAt': DateTime.now().add(const Duration(days: 5)).toIso8601String(),
        'requiresSubmission': true,
        'submissionType': 'online',
        'status': 'pending',
        'resourceUrls': ['https://example.com/math_practice.pdf'],
        'teacherNote': 'Focus on algebra and geometry problems',
        'teacherId': 'teacher_001',
        'assignmentType': AssignmentType.individual.name,
        'assignedUserIds': [
          'current_user_id',
        ], // This would be the actual user ID
      };

      // Sample group assignment
      final groupAssignment = {
        'id': 'sample_group_001',
        'subject': 'Science',
        'title': 'Group Lab Report',
        'description':
            'Work with your assigned lab partners to complete the chemistry experiment report.',
        'assignedAt': DateTime.now()
            .subtract(const Duration(days: 5))
            .toIso8601String(),
        'dueAt': DateTime.now().add(const Duration(days: 10)).toIso8601String(),
        'requiresSubmission': true,
        'submissionType': 'online',
        'status': 'pending',
        'resourceUrls': [
          'https://example.com/lab_instructions.pdf',
          'https://example.com/report_template.docx',
        ],
        'teacherNote':
            'Each group member should contribute equally to the report',
        'teacherId': 'teacher_003',
        'assignmentType': AssignmentType.group.name,
        'assignedUserIds': [
          'current_user_id',
          'student_002',
          'student_003',
          'student_004',
        ],
      };

      // Create the sample assignments
      await _firestore
          .collection(_homeworkCollection)
          .doc(individualAssignment['id'] as String)
          .set(individualAssignment);

      await _firestore
          .collection(_homeworkCollection)
          .doc(groupAssignment['id'] as String)
          .set(groupAssignment);

      debugPrint('Sample assignments created successfully');
    } catch (e) {
      debugPrint('Error creating sample assignments: $e');
      rethrow;
    }
  }

  /// Remove sample assignments (for cleanup)
  static Future<void> removeSampleAssignments() async {
    try {
      debugPrint('Removing sample assignments...');

      await _firestore
          .collection(_homeworkCollection)
          .doc('sample_individual_001')
          .delete();

      await _firestore
          .collection(_homeworkCollection)
          .doc('sample_group_001')
          .delete();

      debugPrint('Sample assignments removed successfully');
    } catch (e) {
      debugPrint('Error removing sample assignments: $e');
      rethrow;
    }
  }

  /// Get migration statistics
  static Future<Map<String, int>> getMigrationStats() async {
    try {
      final querySnapshot = await _firestore
          .collection(_homeworkCollection)
          .get();

      int classAssignments = 0;
      int individualAssignments = 0;
      int groupAssignments = 0;
      int customAssignments = 0;
      int unmigrated = 0;

      for (final doc in querySnapshot.docs) {
        final data = doc.data();

        if (!data.containsKey('assignmentType')) {
          unmigrated++;
          continue;
        }

        final assignmentType = data['assignmentType'] as String;
        switch (assignmentType) {
          case 'classAssignment':
            classAssignments++;
            break;
          case 'individual':
            individualAssignments++;
            break;
          case 'group':
            groupAssignments++;
            break;
          case 'custom':
            customAssignments++;
            break;
        }
      }

      return {
        'total': querySnapshot.docs.length,
        'classAssignments': classAssignments,
        'individualAssignments': individualAssignments,
        'groupAssignments': groupAssignments,
        'customAssignments': customAssignments,
        'unmigrated': unmigrated,
      };
    } catch (e) {
      debugPrint('Error getting migration stats: $e');
      return {};
    }
  }

  /// Run complete migration process
  static Future<bool> runCompleteMigration() async {
    try {
      debugPrint('Running complete homework migration process...');

      // Step 1: Migrate existing data
      await migrateHomeworkData();

      // Step 2: Validate migration
      final isValid = await validateMigration();
      if (!isValid) {
        debugPrint('Migration validation failed');
        return false;
      }

      // Step 3: Get final stats
      final stats = await getMigrationStats();
      debugPrint('Final migration stats: $stats');

      debugPrint('Complete migration process finished successfully');
      return true;
    } catch (e) {
      debugPrint('Error during complete migration: $e');
      return false;
    }
  }
}
