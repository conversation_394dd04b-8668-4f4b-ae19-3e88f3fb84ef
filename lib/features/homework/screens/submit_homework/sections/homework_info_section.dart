import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_symbols_icons/symbols.dart';
import '../../../../../core/theme/app_colors.dart';
import '../../../../../core/enums/homework/submission_type.dart';
import '../../../models/homework_model.dart';

/// Homework info section for submit homework screen
class HomeworkInfoSection extends StatelessWidget {
  final HomeworkModel homework;

  const HomeworkInfoSection({super.key, required this.homework});

  /// Format date for display
  String _formatDate(DateTime date) {
    const months = [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec',
    ];

    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final dateDay = DateTime(date.year, date.month, date.day);

    if (dateDay == today) {
      return 'Today at ${_formatTime(date)}';
    } else if (dateDay == today.subtract(const Duration(days: 1))) {
      return 'Yesterday at ${_formatTime(date)}';
    } else if (dateDay == today.add(const Duration(days: 1))) {
      return 'Tomorrow at ${_formatTime(date)}';
    } else {
      return '${months[date.month - 1]} ${date.day}, ${date.year} at ${_formatTime(date)}';
    }
  }

  /// Format time for display
  String _formatTime(DateTime date) {
    final hour = date.hour == 0
        ? 12
        : date.hour > 12
            ? date.hour - 12
            : date.hour;
    final minute = date.minute.toString().padLeft(2, '0');
    final period = date.hour >= 12 ? 'PM' : 'AM';
    return '$hour:$minute $period';
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: isDark ? AppColors.surfaceDark : AppColors.surfaceLight,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(
          color: isDark ? AppColors.borderDark : AppColors.borderLight,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title
          Text(
            homework.title,
            style: theme.textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(height: 8.h),

          // Subject
          Text(
            homework.subject,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.primary,
              fontWeight: FontWeight.w500,
            ),
          ),
          SizedBox(height: 16.h),

          // Due date if available
          if (homework.dueAt != null) ...[
            Row(
              children: [
                Icon(
                  Symbols.schedule,
                  size: 16.sp,
                  color: isDark
                      ? AppColors.textSecondaryDark
                      : AppColors.textSecondaryLight,
                ),
                SizedBox(width: 8.w),
                Text(
                  'Due: ${_formatDate(homework.dueAt!)}',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: isDark
                        ? AppColors.textSecondaryDark
                        : AppColors.textSecondaryLight,
                  ),
                ),
              ],
            ),
            SizedBox(height: 12.h),
          ],

          // Submission type
          Row(
            children: [
              Icon(
                homework.submissionType == SubmissionType.online
                    ? Symbols.cloud_upload
                    : Symbols.assignment,
                size: 16.sp,
                color: isDark
                    ? AppColors.textSecondaryDark
                    : AppColors.textSecondaryLight,
              ),
              SizedBox(width: 8.w),
              Text(
                homework.submissionType == SubmissionType.online
                    ? 'Online submission'
                    : 'Offline submission',
                style: theme.textTheme.bodySmall?.copyWith(
                  color: isDark
                      ? AppColors.textSecondaryDark
                      : AppColors.textSecondaryLight,
                ),
              ),
            ],
          ),

          // Description if available
          if (homework.description != null) ...[
            SizedBox(height: 16.h),
            Text(
              'Description',
              style: theme.textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: 8.h),
            Text(
              homework.description!,
              style: theme.textTheme.bodyMedium,
            ),
          ],
        ],
      ),
    );
  }
}
