import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_symbols_icons/symbols.dart';
import '../../../models/uploaded_file_model.dart';
import '../../../widgets/submission_file_tile.dart';

/// File upload section for submit homework screen
class FileUploadSection extends StatelessWidget {
  final List<UploadedFileModel> uploadedFiles;
  final Function(int) onRemoveFile;

  const FileUploadSection({
    super.key,
    required this.uploadedFiles,
    required this.onRemoveFile,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Uploaded Files',
          style: theme.textTheme.titleMedium?.copyWith(
            color: colorScheme.onSurface,
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: 16.h),
        if (uploadedFiles.isEmpty)
          Container(
            width: double.infinity,
            padding: EdgeInsets.all(24.w),
            decoration: BoxDecoration(
              border: Border.all(
                color: colorScheme.outline.withValues(alpha: 0.3),
                style: BorderStyle.solid,
              ),
              borderRadius: BorderRadius.circular(12.r),
            ),
            child: Column(
              children: [
                Icon(
                  Symbols.upload_file,
                  size: 48.sp,
                  color: colorScheme.onSurface.withValues(alpha: 0.5),
                ),
                SizedBox(height: 12.h),
                Text(
                  'No files uploaded yet',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: colorScheme.onSurface.withValues(alpha: 0.7),
                  ),
                ),
                SizedBox(height: 4.h),
                Text(
                  'Tap the + button to add files',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: colorScheme.onSurface.withValues(alpha: 0.5),
                  ),
                ),
              ],
            ),
          )
        else
          ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: uploadedFiles.length,
            separatorBuilder: (context, index) => SizedBox(height: 8.h),
            itemBuilder: (context, index) => SubmissionFileTile(
              uploadedFile: uploadedFiles[index],
              onRemove: () => onRemoveFile(index),
            ),
          ),
      ],
    );
  }
}
