import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:material_symbols_icons/symbols.dart';

import '../../../../core/services/file_picker_service.dart';
import '../../controllers/homework_controller.dart';
import '../../models/homework_model.dart';
import '../../models/homework_submission_model.dart';
import '../../models/uploaded_file_model.dart';

import 'sections/homework_info_section.dart';
import 'sections/file_upload_section.dart';
import 'sections/notes_input_section.dart';
import 'sections/submit_button_section.dart';

/// Screen for submitting homework assignments
class SubmitHomeworkScreen extends ConsumerStatefulWidget {
  /// The homework to submit (can be passed via navigation arguments)
  final HomeworkModel? homework;

  /// The homework ID if homework is not provided
  final String? homeworkId;

  const SubmitHomeworkScreen({super.key, this.homework, this.homeworkId});

  @override
  ConsumerState<SubmitHomeworkScreen> createState() =>
      _SubmitHomeworkScreenState();
}

class _SubmitHomeworkScreenState extends ConsumerState<SubmitHomeworkScreen> {
  final TextEditingController _noteController = TextEditingController();
  final List<UploadedFileModel> _uploadedFiles = [];

  bool _isUploading = false;
  bool _isSubmitting = false;

  @override
  void initState() {
    super.initState();
    // Load existing submission data if editing
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadExistingSubmission();
    });
  }

  @override
  void dispose() {
    _noteController.dispose();
    super.dispose();
  }

  /// Load existing submission data if this is an edit operation
  Future<void> _loadExistingSubmission() async {
    try {
      final currentUserId = ref.read(currentUserIdProvider);
      final homeworkId = widget.homework?.id ?? widget.homeworkId;

      if (homeworkId == null) return;

      final existingSubmission = await ref.read(
        submissionProvider((
          homeworkId: homeworkId,
          userId: currentUserId,
        )).future,
      );

      if (existingSubmission != null) {
        // Pre-fill the note
        _noteController.text = existingSubmission.studentNote ?? '';

        // Convert file URLs to UploadedFileModel for display
        final uploadedFiles = existingSubmission.fileUrls.map((url) {
          final fileName = url.split('/').last;
          return UploadedFileModel(
            id: DateTime.now().millisecondsSinceEpoch.toString(),
            file: File(url), // This is just for display
            fileName: fileName,
            fileSize: 'Unknown size',
            fileExtension: fileName.split('.').last.toLowerCase(),
            uploadedAt: existingSubmission.submittedAt,
            fileType: _getFileTypeFromExtension(
              fileName.split('.').last.toLowerCase(),
            ),
          );
        }).toList();

        if (mounted) {
          setState(() {
            _uploadedFiles.addAll(uploadedFiles);
          });
        }
      }
    } catch (e) {
      // No existing submission found, this is a new submission
      debugPrint('No existing submission found, creating new submission');
    }
  }

  /// Get file type from extension
  FileType _getFileTypeFromExtension(String extension) {
    if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].contains(extension)) {
      return FileType.image;
    } else if ([
      'mp4',
      'mov',
      'avi',
      'mkv',
      'wmv',
      'flv',
      '3gp',
    ].contains(extension)) {
      return FileType.video;
    } else if ([
      'pdf',
      'doc',
      'docx',
      'txt',
      'rtf',
      'odt',
    ].contains(extension)) {
      return FileType.document;
    } else {
      return FileType.other;
    }
  }

  /// Check if we're editing an existing submission
  bool _isEditingExistingSubmission() {
    return _uploadedFiles.isNotEmpty || _noteController.text.isNotEmpty;
  }

  /// Show file upload options bottom sheet
  void _showFileUploadOptions() {
    showModalBottomSheet(
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16.r)),
      ),
      builder: (context) => _FileUploadOptionsSheet(
        onFilesSelected: _addFiles,
        onCameraCapture: _captureFromCamera,
        isUploading: _isUploading,
      ),
    );
  }

  /// Add files to the uploaded files list
  void _addFiles(List<File> files) async {
    setState(() {
      _isUploading = true;
    });

    try {
      final uploadedFiles = files
          .map((file) => UploadedFileModel.fromFile(file))
          .toList();

      setState(() {
        _uploadedFiles.addAll(uploadedFiles);
        _isUploading = false;
      });

      if (mounted) {
        Navigator.of(context).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${files.length} file(s) added successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      setState(() {
        _isUploading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error adding files: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Capture photo/video from camera
  void _captureFromCamera(File file) async {
    try {
      final uploadedFile = UploadedFileModel.fromFile(file);

      setState(() {
        _uploadedFiles.add(uploadedFile);
      });

      if (mounted) {
        Navigator.of(context).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('File captured successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error capturing file: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Remove a file from the uploaded files list
  void _removeFile(int index) {
    setState(() {
      _uploadedFiles.removeAt(index);
    });
  }

  /// Submit the homework
  Future<void> _submitHomework(HomeworkModel homework) async {
    if (_isSubmitting) return;

    setState(() {
      _isSubmitting = true;
    });

    try {
      final currentUserId = ref.read(currentUserIdProvider);

      // Create submission model with unique ID
      final submissionId =
          'sub_${DateTime.now().millisecondsSinceEpoch}_${currentUserId.hashCode.abs()}';
      final submission = HomeworkSubmissionModel(
        id: submissionId,
        homeworkId: homework.id,
        userId: currentUserId,
        fileUrls: _uploadedFiles.map((file) => file.file.path).toList(),
        studentNote: _noteController.text.trim().isEmpty
            ? null
            : _noteController.text.trim(),
        submittedAt: DateTime.now(),
      );

      // Submit homework using provider
      await ref.read(submitHomeworkProvider(submission).future);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Homework submitted successfully!'),
            backgroundColor: Colors.green,
          ),
        );

        // Navigate back after submission
        context.pop();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error submitting homework: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSubmitting = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    // Get homework data
    final homework = widget.homework;
    final homeworkId = homework?.id ?? widget.homeworkId;

    if (homework != null) {
      // Homework provided directly
      return _buildScreen(context, theme, colorScheme, homework);
    } else if (homeworkId != null) {
      // Homework ID provided, fetch homework data
      final homeworkAsync = ref.watch(homeworkDetailProvider(homeworkId));

      return homeworkAsync.when(
        loading: () => Scaffold(
          appBar: AppBar(
            title: const Text('Loading...'),
            backgroundColor: colorScheme.surface,
            elevation: 0,
          ),
          body: const Center(child: CircularProgressIndicator()),
        ),
        error: (error, stackTrace) => Scaffold(
          appBar: AppBar(
            title: const Text('Error'),
            backgroundColor: colorScheme.surface,
            elevation: 0,
          ),
          body: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text('Error loading homework: ${error.toString()}'),
                ElevatedButton(
                  onPressed: () =>
                      ref.invalidate(homeworkDetailProvider(homeworkId)),
                  child: const Text('Retry'),
                ),
              ],
            ),
          ),
        ),
        data: (homeworkData) {
          if (homeworkData == null) {
            return Scaffold(
              appBar: AppBar(
                title: const Text('Not Found'),
                backgroundColor: colorScheme.surface,
                elevation: 0,
              ),
              body: const Center(child: Text('Homework not found')),
            );
          }
          return _buildScreen(context, theme, colorScheme, homeworkData);
        },
      );
    } else {
      // Neither homework nor homeworkId provided
      return Scaffold(
        appBar: AppBar(
          title: const Text('Error'),
          backgroundColor: colorScheme.surface,
          elevation: 0,
        ),
        body: const Center(child: Text('No homework data provided')),
      );
    }
  }

  Widget _buildScreen(
    BuildContext context,
    ThemeData theme,
    ColorScheme colorScheme,
    HomeworkModel homework,
  ) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          _isEditingExistingSubmission()
              ? 'Edit Submission'
              : 'Submit Homework',
          style: theme.textTheme.titleLarge?.copyWith(
            color: colorScheme.onSurface,
          ),
        ),
        backgroundColor: colorScheme.surface,
        elevation: 0,
      ),
      body: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              padding: EdgeInsets.all(16.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  HomeworkInfoSection(homework: homework),
                  SizedBox(height: 24.h),
                  FileUploadSection(
                    uploadedFiles: _uploadedFiles,
                    onRemoveFile: _removeFile,
                  ),
                  SizedBox(height: 16.h),
                  NotesInputSection(controller: _noteController),
                  SizedBox(height: 24.h),
                ],
              ),
            ),
          ),
          SubmitButtonSection(
            onSubmit: () => _submitHomework(homework),
            isEnabled: !_isSubmitting,
            buttonText: _isEditingExistingSubmission()
                ? (_isSubmitting ? 'Updating...' : 'Update Submission')
                : (_isSubmitting ? 'Submitting...' : 'Submit Homework'),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _showFileUploadOptions,
        tooltip: 'Add files',
        child: const Icon(Symbols.add),
      ),
    );
  }
}

/// File upload options bottom sheet
class _FileUploadOptionsSheet extends StatelessWidget {
  final Function(List<File>) onFilesSelected;
  final Function(File) onCameraCapture;
  final bool isUploading;

  const _FileUploadOptionsSheet({
    required this.onFilesSelected,
    required this.onCameraCapture,
    required this.isUploading,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Container(
      padding: EdgeInsets.all(16.w),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 40.w,
            height: 4.h,
            decoration: BoxDecoration(
              color: colorScheme.onSurface.withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(2.r),
            ),
          ),
          SizedBox(height: 16.h),
          Text(
            'Add Files',
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(height: 24.h),
          if (isUploading)
            const Center(child: CircularProgressIndicator())
          else
            Column(
              children: [
                _OptionTile(
                  icon: Symbols.photo_library,
                  title: 'Choose from Gallery',
                  subtitle: 'Select photos and videos',
                  onTap: () async {
                    final files = await FilePickerService().pickImages();
                    if (files != null && files.isNotEmpty) {
                      onFilesSelected(files);
                    }
                  },
                ),
                SizedBox(height: 12.h),
                _OptionTile(
                  icon: Symbols.description,
                  title: 'Choose Documents',
                  subtitle: 'Select PDF, Word, and other documents',
                  onTap: () async {
                    final files = await FilePickerService().pickDocuments();
                    if (files != null && files.isNotEmpty) {
                      onFilesSelected(files);
                    }
                  },
                ),
                SizedBox(height: 12.h),
                _OptionTile(
                  icon: Symbols.photo_camera,
                  title: 'Take Photo',
                  subtitle: 'Capture with camera',
                  onTap: () async {
                    final file = await FilePickerService().capturePhoto();
                    if (file != null) {
                      onCameraCapture(file);
                    }
                  },
                ),
                SizedBox(height: 12.h),
                _OptionTile(
                  icon: Symbols.videocam,
                  title: 'Record Video',
                  subtitle: 'Record with camera',
                  onTap: () async {
                    final file = await FilePickerService().captureVideo();
                    if (file != null) {
                      onCameraCapture(file);
                    }
                  },
                ),
              ],
            ),
          SizedBox(height: 16.h),
        ],
      ),
    );
  }
}

/// Option tile for file upload options
class _OptionTile extends StatelessWidget {
  final IconData icon;
  final String title;
  final String subtitle;
  final VoidCallback onTap;

  const _OptionTile({
    required this.icon,
    required this.title,
    required this.subtitle,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12.r),
      child: Container(
        padding: EdgeInsets.all(16.w),
        decoration: BoxDecoration(
          border: Border.all(color: colorScheme.outline.withValues(alpha: 0.2)),
          borderRadius: BorderRadius.circular(12.r),
        ),
        child: Row(
          children: [
            Icon(icon, size: 24.sp, color: colorScheme.primary),
            SizedBox(width: 16.w),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: theme.textTheme.bodyLarge?.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  SizedBox(height: 2.h),
                  Text(
                    subtitle,
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: colorScheme.onSurface.withValues(alpha: 0.6),
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Symbols.chevron_right,
              size: 20.sp,
              color: colorScheme.onSurface.withValues(alpha: 0.4),
            ),
          ],
        ),
      ),
    );
  }
}
