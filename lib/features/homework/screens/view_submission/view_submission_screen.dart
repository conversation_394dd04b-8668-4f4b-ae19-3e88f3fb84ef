import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../controllers/homework_controller.dart';

import '../../models/homework_submission_model.dart';
import 'sections/homework_overview_section.dart';
import 'sections/file_list_section.dart';
import 'sections/note_display_section.dart';
import 'sections/teacher_remark_section.dart';
import 'sections/edit_submission_button_section.dart';
import 'sections/error_state_section.dart';
import 'sections/no_submission_state_section.dart';

/// Screen for viewing homework submission details
class ViewSubmissionScreen extends ConsumerWidget {
  /// The homework submission to view
  final HomeworkSubmissionModel? submission;

  /// The homework ID to find the submission for
  final String? homeworkId;

  const ViewSubmissionScreen({super.key, this.submission, this.homeworkId});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final currentUserId = ref.read(currentUserIdProvider);

    if (submission != null) {
      // Submission provided directly
      return _buildSubmissionView(
        context,
        ref,
        theme,
        colorScheme,
        submission!,
        null,
      );
    } else if (homeworkId != null) {
      // Homework ID provided, fetch submission data
      final submissionAsync = ref.watch(
        submissionProvider((homeworkId: homeworkId!, userId: currentUserId)),
      );

      return submissionAsync.when(
        loading: () => Scaffold(
          appBar: AppBar(
            title: Text(
              'Your Submission',
              style: theme.textTheme.titleLarge?.copyWith(
                color: colorScheme.onSurface,
              ),
            ),
            backgroundColor: colorScheme.surface,
            elevation: 0,
          ),
          body: const Center(child: CircularProgressIndicator()),
        ),
        error: (error, stackTrace) => Scaffold(
          appBar: AppBar(
            title: Text(
              'Your Submission',
              style: theme.textTheme.titleLarge?.copyWith(
                color: colorScheme.onSurface,
              ),
            ),
            backgroundColor: colorScheme.surface,
            elevation: 0,
          ),
          body: ErrorStateSection(
            error: error.toString(),
            onRetry: () => ref.invalidate(
              submissionProvider((
                homeworkId: homeworkId!,
                userId: currentUserId,
              )),
            ),
          ),
        ),
        data: (submissionData) {
          if (submissionData == null) {
            return const NoSubmissionStateSection();
          }
          return _buildSubmissionView(
            context,
            ref,
            theme,
            colorScheme,
            submissionData,
            homeworkId,
          );
        },
      );
    } else {
      // Neither submission nor homeworkId provided
      return const NoSubmissionStateSection();
    }
  }

  Widget _buildSubmissionView(
    BuildContext context,
    WidgetRef ref,
    ThemeData theme,
    ColorScheme colorScheme,
    HomeworkSubmissionModel submissionData,
    String? hwId,
  ) {
    // Get homework data
    final homeworkAsync = ref.watch(
      homeworkDetailProvider(submissionData.homeworkId),
    );

    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Your Submission',
          style: theme.textTheme.titleLarge?.copyWith(
            color: colorScheme.onSurface,
          ),
        ),
        backgroundColor: colorScheme.surface,
        elevation: 0,
      ),
      body: homeworkAsync.when(
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (error, stackTrace) => ErrorStateSection(
          error: error.toString(),
          onRetry: () =>
              ref.invalidate(homeworkDetailProvider(submissionData.homeworkId)),
        ),
        data: (homeworkData) {
          return Column(
            children: [
              Expanded(
                child: SingleChildScrollView(
                  padding: EdgeInsets.all(16.w),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Homework Overview Section
                      HomeworkOverviewSection(
                        homework: homeworkData,
                        submission: submissionData,
                      ),

                      SizedBox(height: 24.h),

                      // File List Section
                      FileListSection(fileUrls: submissionData.fileUrls),

                      SizedBox(height: 24.h),

                      // Note Display Section
                      NoteDisplaySection(
                        studentNote: submissionData.studentNote,
                      ),

                      SizedBox(height: 24.h),

                      // Teacher's Remark Section
                      if (submissionData.teacherRemark != null)
                        TeacherRemarkSection(
                          teacherRemark: submissionData.teacherRemark!,
                          reviewedAt: submissionData.reviewedAt,
                        ),

                      if (submissionData.teacherRemark != null)
                        SizedBox(height: 24.h),
                    ],
                  ),
                ),
              ),

              // Edit Button
              EditSubmissionButtonSection(
                homeworkId: submissionData.homeworkId,
                homework: homeworkData,
              ),
            ],
          );
        },
      ),
    );
  }
}
