import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_symbols_icons/symbols.dart';
import '../../../../../core/theme/app_colors.dart';

import '../../../models/homework_model.dart';
import '../../../models/homework_submission_model.dart';

/// Homework overview section for view submission screen
class HomeworkOverviewSection extends StatelessWidget {
  final HomeworkModel? homework;
  final HomeworkSubmissionModel submission;

  const HomeworkOverviewSection({
    super.key,
    required this.homework,
    required this.submission,
  });

  /// Format date and time for display
  String _formatDateTime(DateTime dateTime) {
    const months = [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec',
    ];

    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final dateDay = DateTime(dateTime.year, dateTime.month, dateTime.day);

    String dateStr;
    if (dateDay == today) {
      dateStr = 'Today';
    } else if (dateDay == today.subtract(const Duration(days: 1))) {
      dateStr = 'Yesterday';
    } else if (dateDay == today.add(const Duration(days: 1))) {
      dateStr = 'Tomorrow';
    } else {
      dateStr =
          '${months[dateTime.month - 1]} ${dateTime.day}, ${dateTime.year}';
    }

    final hour = dateTime.hour == 0
        ? 12
        : dateTime.hour > 12
        ? dateTime.hour - 12
        : dateTime.hour;
    final minute = dateTime.minute.toString().padLeft(2, '0');
    final period = dateTime.hour >= 12 ? 'PM' : 'AM';

    return '$dateStr at $hour:$minute $period';
  }

  /// Get submission status color
  Color _getSubmissionStatusColor(ThemeData theme) {
    if (submission.teacherRemark != null) {
      // Determine if accepted or rejected based on context
      // This is a simplified approach - in a real app you might have explicit status
      return theme.colorScheme.primary;
    }
    return theme.colorScheme.secondary;
  }

  /// Get submission status text
  String _getSubmissionStatusText() {
    if (submission.reviewedAt != null && submission.teacherRemark != null) {
      return 'Reviewed';
    }
    return 'Submitted';
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: isDark ? AppColors.surfaceDark : AppColors.surfaceLight,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(
          color: isDark ? AppColors.borderDark : AppColors.borderLight,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Homework title and subject
          if (homework != null) ...[
            Text(
              homework!.title,
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: 4.h),
            Text(
              homework!.subject,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.primary,
                fontWeight: FontWeight.w500,
              ),
            ),
            SizedBox(height: 16.h),
          ],

          // Submission status
          Row(
            children: [
              Container(
                padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
                decoration: BoxDecoration(
                  color: _getSubmissionStatusColor(
                    theme,
                  ).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(6.r),
                ),
                child: Text(
                  _getSubmissionStatusText(),
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: _getSubmissionStatusColor(theme),
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: 16.h),

          // Submission details
          _DetailRow(
            icon: Symbols.schedule,
            label: 'Submitted',
            value: _formatDateTime(submission.submittedAt),
            theme: theme,
            isDark: isDark,
          ),

          if (submission.reviewedAt != null) ...[
            SizedBox(height: 12.h),
            _DetailRow(
              icon: Symbols.rate_review,
              label: 'Reviewed',
              value: _formatDateTime(submission.reviewedAt!),
              theme: theme,
              isDark: isDark,
            ),
          ],

          if (homework?.dueAt != null) ...[
            SizedBox(height: 12.h),
            _DetailRow(
              icon: Symbols.event,
              label: 'Due Date',
              value: _formatDateTime(homework!.dueAt!),
              theme: theme,
              isDark: isDark,
            ),
          ],

          // File count
          if (submission.fileUrls.isNotEmpty) ...[
            SizedBox(height: 12.h),
            _DetailRow(
              icon: Symbols.attach_file,
              label: 'Files',
              value:
                  '${submission.fileUrls.length} file${submission.fileUrls.length == 1 ? '' : 's'}',
              theme: theme,
              isDark: isDark,
            ),
          ],
        ],
      ),
    );
  }
}

/// Detail row widget for displaying submission information
class _DetailRow extends StatelessWidget {
  final IconData icon;
  final String label;
  final String value;
  final ThemeData theme;
  final bool isDark;

  const _DetailRow({
    required this.icon,
    required this.label,
    required this.value,
    required this.theme,
    required this.isDark,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Icon(
          icon,
          size: 16.sp,
          color: isDark
              ? AppColors.textSecondaryDark
              : AppColors.textSecondaryLight,
        ),
        SizedBox(width: 8.w),
        Text(
          '$label: ',
          style: theme.textTheme.bodySmall?.copyWith(
            color: isDark
                ? AppColors.textSecondaryDark
                : AppColors.textSecondaryLight,
            fontWeight: FontWeight.w500,
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: theme.textTheme.bodySmall?.copyWith(
              color: isDark
                  ? AppColors.textSecondaryDark
                  : AppColors.textSecondaryLight,
            ),
          ),
        ),
      ],
    );
  }
}
