import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_symbols_icons/symbols.dart';
import '../../../../../core/theme/app_colors.dart';
import '../../../../digital_library/models/file_model.dart';
import '../../../../digital_library/services/file_viewer_service.dart';

/// File list section for view submission screen
class FileListSection extends StatelessWidget {
  final List<String> fileUrls;

  const FileListSection({super.key, required this.fileUrls});

  /// Handle file tap - open file in viewer
  void _handleFileTap(BuildContext context, String fileUrl) {
    final fileViewerService = FileViewerService();
    final fileName = fileUrl.split('/').last;
    final fileModel = FileModel.fromUrl(fileUrl, customFileName: fileName);

    fileViewerService.openFile(context, fileModel);
  }

  /// Get file icon based on extension
  IconData _getFileIcon(String fileName) {
    final extension = fileName.split('.').last.toLowerCase();

    switch (extension) {
      case 'pdf':
        return Symbols.picture_as_pdf;
      case 'doc':
      case 'docx':
        return Symbols.description;
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
        return Symbols.image;
      case 'mp4':
      case 'mov':
      case 'avi':
        return Symbols.videocam;
      default:
        return Symbols.attach_file;
    }
  }

  /// Get file name from URL
  String _getFileName(String fileUrl) {
    return fileUrl.split('/').last;
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    if (fileUrls.isEmpty) {
      return Container(
        width: double.infinity,
        padding: EdgeInsets.all(24.w),
        decoration: BoxDecoration(
          color: isDark ? AppColors.surfaceDark : AppColors.surfaceLight,
          borderRadius: BorderRadius.circular(12.r),
          border: Border.all(
            color: isDark ? AppColors.borderDark : AppColors.borderLight,
          ),
        ),
        child: Column(
          children: [
            Icon(
              Symbols.folder_open,
              size: 48.sp,
              color: isDark
                  ? AppColors.textSecondaryDark
                  : AppColors.textSecondaryLight,
            ),
            SizedBox(height: 12.h),
            Text(
              'No files submitted',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: isDark
                    ? AppColors.textSecondaryDark
                    : AppColors.textSecondaryLight,
              ),
            ),
          ],
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Submitted Files (${fileUrls.length})',
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: 16.h),
        ListView.separated(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: fileUrls.length,
          separatorBuilder: (context, index) => SizedBox(height: 8.h),
          itemBuilder: (context, index) {
            final fileUrl = fileUrls[index];
            final fileName = _getFileName(fileUrl);

            return _FileListTile(
              fileName: fileName,
              fileUrl: fileUrl,
              icon: _getFileIcon(fileName),
              onTap: () => _handleFileTap(context, fileUrl),
              theme: theme,
              isDark: isDark,
            );
          },
        ),
      ],
    );
  }
}

/// File list tile widget
class _FileListTile extends StatelessWidget {
  final String fileName;
  final String fileUrl;
  final IconData icon;
  final VoidCallback onTap;
  final ThemeData theme;
  final bool isDark;

  const _FileListTile({
    required this.fileName,
    required this.fileUrl,
    required this.icon,
    required this.onTap,
    required this.theme,
    required this.isDark,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8.r),
      child: Container(
        padding: EdgeInsets.all(12.w),
        decoration: BoxDecoration(
          color: isDark ? AppColors.surfaceDark : AppColors.surfaceLight,
          border: Border.all(
            color: isDark ? AppColors.borderDark : AppColors.borderLight,
          ),
          borderRadius: BorderRadius.circular(8.r),
        ),
        child: Row(
          children: [
            Icon(icon, size: 24.sp, color: theme.colorScheme.primary),
            SizedBox(width: 12.w),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    fileName,
                    style: theme.textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                  SizedBox(height: 2.h),
                  Text(
                    'Tap to view',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: isDark
                          ? AppColors.textSecondaryDark
                          : AppColors.textSecondaryLight,
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Symbols.open_in_new,
              size: 16.sp,
              color: isDark
                  ? AppColors.textSecondaryDark
                  : AppColors.textSecondaryLight,
            ),
          ],
        ),
      ),
    );
  }
}
