import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import '../../../../../core/routes/app_routes.dart';

import '../../../models/homework_model.dart';

/// Edit submission button section for view submission screen
class EditSubmissionButtonSection extends StatelessWidget {
  final String homeworkId;
  final HomeworkModel? homework;

  const EditSubmissionButtonSection({
    super.key,
    required this.homeworkId,
    required this.homework,
  });

  /// Check if submission can be edited
  bool _canEditSubmission() {
    if (homework == null) return false;

    // Check if homework status allows editing
    // In a real app, you might have more complex logic here
    // For now, we'll assume submissions can be edited unless explicitly marked as reviewed/accepted
    return true;
  }

  /// Handle edit button press
  void _handleEditPress(BuildContext context) {
    context.pushNamed(
      RouteNames.submitHomework,
      pathParameters: {'id': homeworkId},
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    if (!_canEditSubmission()) {
      return Container(
        width: double.infinity,
        padding: EdgeInsets.all(16.w),
        child: Container(
          padding: EdgeInsets.symmetric(vertical: 16.h),
          decoration: BoxDecoration(
            color: colorScheme.surfaceContainerHighest,
            borderRadius: BorderRadius.circular(12.r),
          ),
          child: Text(
            'Submission cannot be edited',
            textAlign: TextAlign.center,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: colorScheme.onSurfaceVariant,
            ),
          ),
        ),
      );
    }

    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(16.w),
      child: ElevatedButton(
        onPressed: () => _handleEditPress(context),
        style: ElevatedButton.styleFrom(
          backgroundColor: colorScheme.primary,
          foregroundColor: colorScheme.onPrimary,
          padding: EdgeInsets.symmetric(vertical: 16.h),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12.r),
          ),
          elevation: 0,
        ),
        child: Text(
          'Edit Submission',
          style: theme.textTheme.labelLarge?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
    );
  }
}
