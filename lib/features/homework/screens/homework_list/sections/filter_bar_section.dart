import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:scholara_student/features/homework/widgets/class_filter_widget.dart';
import 'package:scholara_student/features/homework/widgets/date_selector_bar.dart';
import 'package:scholara_student/features/homework/widgets/unified_assignment_type_filter_bar.dart';

/// Filter bar section containing date selector, assignment type filter, and class filter
class FilterBarSection extends StatelessWidget {
  final DateTime? selectedDate;
  final ValueChanged<DateTime?> onDateSelected;

  const FilterBarSection({
    super.key,
    required this.selectedDate,
    required this.onDateSelected,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Date selector bar
        DateSelectorBar(
          selectedDate: selectedDate,
          onDateSelected: onDateSelected,
        ),

        // Unified assignment type filter bar
        const UnifiedAssignmentTypeFilterBar(),

        // Class filter widget (only shown for class assignments)
        const ClassFilterWidget(),

        SizedBox(height: 8.h),
      ],
    );
  }
}
