import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:material_symbols_icons/symbols.dart';
import '../../controllers/unified_homework_controller.dart';
import 'sections/filter_bar_section.dart';
import 'sections/homework_list_section.dart';
import 'sections/empty_state_section.dart';
import 'sections/error_state_section.dart';

/// Screen displaying the list of homework assignments
class HomeworkListScreen extends ConsumerStatefulWidget {
  /// Optional classroom ID for filtering homework to a specific classroom
  final String? classroomId;

  const HomeworkListScreen({super.key, this.classroomId});

  @override
  ConsumerState<HomeworkListScreen> createState() => _HomeworkListScreenState();
}

class _HomeworkListScreenState extends ConsumerState<HomeworkListScreen> {
  DateTime? _selectedDate; // null means 'All'

  @override
  void initState() {
    super.initState();
    // Set classroom context if provided
    if (widget.classroomId != null) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        ref.read(classroomContextProvider.notifier).state = widget.classroomId;
      });
    }
  }

  @override
  void dispose() {
    // Clear classroom context when leaving the screen
    if (widget.classroomId != null) {
      ref.read(classroomContextProvider.notifier).state = null;
    }
    super.dispose();
  }

  /// Handle date selection
  void _onDateSelected(DateTime? date) {
    setState(() {
      _selectedDate = date;
    });
  }

  /// Show date range picker
  void _showDateRangePicker() async {
    final DateTimeRange? picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime.now().subtract(const Duration(days: 365)),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      initialDateRange: _selectedDate != null
          ? DateTimeRange(start: _selectedDate!, end: _selectedDate!)
          : null,
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: Theme.of(context).colorScheme.copyWith(
              primary: Theme.of(context).colorScheme.primary,
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null) {
      // For now, just select the start date of the range
      // In a full implementation, you might want to filter by the entire range
      setState(() {
        _selectedDate = picked.start;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final classroomContext = ref.watch(classroomContextProvider);

    // Get homework data based on selected date and filters using unified providers
    final homeworkAsync = _selectedDate == null
        ? ref.watch(unifiedFilteredHomeworkProvider)
        : ref.watch(unifiedFilteredHomeworkByDateProvider(_selectedDate!));

    // Determine title based on context
    final title = classroomContext != null ? 'Classroom Homework' : 'Homework';

    return Scaffold(
      appBar: AppBar(
        title: Text(title, style: theme.textTheme.titleLarge),
        actions: [
          IconButton(
            onPressed: _showDateRangePicker,
            icon: const Icon(Symbols.date_range),
            tooltip: 'Select date range',
          ),
        ],
      ),
      body: SafeArea(
        child: Column(
          children: [
            // Filter bar section
            FilterBarSection(
              selectedDate: _selectedDate,
              onDateSelected: _onDateSelected,
            ),

            // Homework list
            Expanded(
              child: homeworkAsync.when(
                loading: () => const Center(child: CircularProgressIndicator()),
                error: (error, stackTrace) => ErrorStateSection(
                  error: error.toString(),
                  onRetry: () {
                    // Invalidate the unified providers
                    ref.invalidate(unifiedFilteredHomeworkProvider);
                    ref.invalidate(unifiedFilteredHomeworkByDateProvider);
                    ref.invalidate(unifiedHomeworkProvider);
                  },
                ),
                data: (homeworkList) {
                  if (homeworkList.isEmpty) {
                    return EmptyStateSection(selectedDate: _selectedDate);
                  }

                  return HomeworkListSection(homeworkList: homeworkList);
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}
