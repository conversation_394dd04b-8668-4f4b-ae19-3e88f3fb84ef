import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_symbols_icons/symbols.dart';
import 'package:go_router/go_router.dart';
import '../../../../core/routes/app_routes.dart';
import '../../controllers/homework_controller.dart';
import 'sections/general_info_section.dart';
import 'sections/resource_list_section.dart';
import 'sections/teacher_notes_section.dart';
import 'sections/personal_notes_section.dart';
import 'sections/action_button_section.dart';
import 'sections/error_state_section.dart';
import 'sections/not_found_state_section.dart';

/// Screen displaying detailed information about a homework assignment
class HomeworkDetailScreen extends ConsumerStatefulWidget {
  /// The ID of the homework to display
  final String homeworkId;

  const HomeworkDetailScreen({super.key, required this.homeworkId});

  @override
  ConsumerState<HomeworkDetailScreen> createState() =>
      _HomeworkDetailScreenState();
}

class _HomeworkDetailScreenState extends ConsumerState<HomeworkDetailScreen> {
  String? personalNotes;

  /// Show three-dot menu options
  void _showMoreOptions() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: EdgeInsets.all(16.w),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Symbols.chat),
              title: const Text('Discuss'),
              subtitle: const Text('Chat about this homework'),
              onTap: () {
                Navigator.pop(context);
                _navigateToHomeworkChat();
              },
            ),
            ListTile(
              leading: const Icon(Symbols.share),
              title: const Text('Share'),
              onTap: () {
                Navigator.pop(context);
              },
            ),
            ListTile(
              leading: const Icon(Symbols.flag),
              title: const Text('Report Issue'),
              onTap: () {
                Navigator.pop(context);
              },
            ),
          ],
        ),
      ),
    );
  }

  /// Navigate to homework-specific chat
  void _navigateToHomeworkChat() {
    // Navigate to chat list with homework context
    context.pushNamed(
      RouteNames.chatList,
      queryParameters: {'homeworkId': widget.homeworkId, 'context': 'homework'},
    );
  }

  /// Show personal notes editor modal
  void _showNotesEditor() {
    final controller = TextEditingController(text: personalNotes ?? '');

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Your Notes'),
        content: SizedBox(
          width: double.maxFinite,
          child: TextField(
            controller: controller,
            maxLines: 8,
            decoration: const InputDecoration(
              hintText: 'Add your personal notes here...',
              border: OutlineInputBorder(),
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              setState(() {
                personalNotes = controller.text.trim().isEmpty
                    ? null
                    : controller.text.trim();
              });
              Navigator.pop(context);
            },
            child: const Text('Save'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final currentUserId = ref.read(currentUserIdProvider);

    // Watch homework data
    final homeworkAsync = ref.watch(homeworkDetailProvider(widget.homeworkId));

    return Scaffold(
      appBar: AppBar(
        title: homeworkAsync.when(
          loading: () => const Text('Loading...'),
          error: (_, __) => const Text('Error'),
          data: (homework) => Text(
            homework?.subject ?? 'Homework',
            style: theme.textTheme.titleLarge,
          ),
        ),
        actions: [
          IconButton(
            onPressed: _showMoreOptions,
            icon: const Icon(Symbols.more_vert),
            tooltip: 'More options',
          ),
        ],
      ),
      body: homeworkAsync.when(
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (error, stackTrace) => ErrorStateSection(
          error: error.toString(),
          onRetry: () =>
              ref.invalidate(homeworkDetailProvider(widget.homeworkId)),
        ),
        data: (homework) {
          if (homework == null) {
            return const NotFoundStateSection();
          }

          return SingleChildScrollView(
            child: Column(
              children: [
                Padding(
                  padding: EdgeInsets.all(16.w),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      GeneralInfoSection(homework: homework),
                      SizedBox(height: 24.h),

                      // Resources Section
                      if (homework.resourceUrls.isNotEmpty) ...[
                        ResourceListSection(
                          resourceUrls: homework.resourceUrls,
                        ),
                        SizedBox(height: 24.h),
                      ],

                      // Teacher Notes Section
                      if (homework.teacherNote != null) ...[
                        TeacherNotesSection(note: homework.teacherNote!),
                        SizedBox(height: 24.h),
                      ],

                      // Personal Notes Section
                      PersonalNotesSection(
                        notes: personalNotes,
                        onEditPressed: _showNotesEditor,
                      ),

                      // Add bottom padding to account for FAB
                      SizedBox(height: 80.h),
                    ],
                  ),
                ),
              ],
            ),
          );
        },
      ),
      floatingActionButton: homeworkAsync.when(
        loading: () => null,
        error: (_, __) => null,
        data: (homework) {
          if (homework == null) return null;

          final submissionAsync = ref.watch(
            submissionProvider((
              homeworkId: homework.id,
              userId: currentUserId,
            )),
          );

          final statusAsync = homework.requiresSubmission
              ? const AsyncValue.data(false)
              : ref.watch(homeworkStatusProvider(homework.id));

          return submissionAsync.when(
            loading: () => FloatingActionButton.extended(
              onPressed: null,
              icon: const CircularProgressIndicator(),
              label: const Text('Loading...'),
            ),
            error: (_, __) => FloatingActionButton.extended(
              onPressed: () => ref.invalidate(
                submissionProvider((
                  homeworkId: homework.id,
                  userId: currentUserId,
                )),
              ),
              icon: const Icon(Symbols.refresh),
              label: const Text('Retry'),
            ),
            data: (submission) {
              return statusAsync.when(
                loading: () => FloatingActionButton.extended(
                  onPressed: null,
                  icon: const CircularProgressIndicator(),
                  label: const Text('Loading...'),
                ),
                error: (_, __) => FloatingActionButton.extended(
                  onPressed: () =>
                      ref.invalidate(homeworkStatusProvider(homework.id)),
                  icon: const Icon(Symbols.refresh),
                  label: const Text('Retry'),
                ),
                data: (isDone) {
                  return ActionButtonSection(
                    homework: homework,
                    submission: submission,
                    isDone: isDone,
                  );
                },
              );
            },
          );
        },
      ),
    );
  }
}
