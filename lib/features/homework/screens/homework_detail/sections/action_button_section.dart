import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:material_symbols_icons/symbols.dart';
import '../../../../../core/routes/app_routes.dart';
import '../../../../../core/enums/homework/submission_type.dart';
import '../../../controllers/homework_controller.dart';
import '../../../models/homework_model.dart';
import '../../../models/homework_submission_model.dart';

/// Action button section for homework detail screen
class ActionButtonSection extends ConsumerWidget {
  final HomeworkModel homework;
  final HomeworkSubmissionModel? submission;
  final bool isDone;

  const ActionButtonSection({
    super.key,
    required this.homework,
    required this.submission,
    required this.isDone,
  });

  /// Handle action button press
  Future<void> _handleActionButton(
    BuildContext context,
    WidgetRef ref,
  ) async {
    if (homework.requiresSubmission &&
        homework.submissionType == SubmissionType.online) {
      // Online submission logic
      if (submission == null) {
        // No submission exists - navigate to submit screen
        context.pushNamed(
          RouteNames.submitHomework,
          pathParameters: {'id': homework.id},
        );
      } else {
        // Submission exists - navigate to view submission screen
        context.pushNamed(
          RouteNames.viewSubmission,
          pathParameters: {'id': homework.id},
        );
      }
    } else {
      // Offline submission or no submission required - toggle done status
      final currentStatus = await ref.read(
        homeworkStatusProvider(homework.id).future,
      );

      try {
        await ref.read(
          markCurrentUserHomeworkDoneProvider((
            homeworkId: homework.id,
            isDone: !currentStatus,
          )).future,
        );

        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                currentStatus
                    ? 'Homework marked as undone!'
                    : 'Homework marked as done!',
              ),
            ),
          );
        }
      } catch (e) {
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Error: ${e.toString()}'),
              backgroundColor: Theme.of(context).colorScheme.error,
            ),
          );
        }
      }
    }
  }

  /// Get action button text based on homework state
  String _getActionButtonText() {
    if (homework.requiresSubmission &&
        homework.submissionType == SubmissionType.online) {
      // Online submission logic
      return submission == null ? 'Submit' : 'View Submission';
    } else {
      // Offline submission or no submission required
      return isDone ? 'Mark as Undone' : 'Mark as Done';
    }
  }

  /// Get action button icon based on homework state
  Icon _getActionButtonIcon() {
    if (homework.requiresSubmission &&
        homework.submissionType == SubmissionType.online) {
      // Online submission logic
      return submission == null
          ? const Icon(Symbols.upload)
          : const Icon(Symbols.visibility);
    } else {
      // Offline submission or no submission required
      return isDone ? const Icon(Symbols.undo) : const Icon(Symbols.check);
    }
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);

    return FloatingActionButton.extended(
      onPressed: () => _handleActionButton(context, ref),
      icon: _getActionButtonIcon(),
      label: Text(
        _getActionButtonText(),
        style: theme.textTheme.titleSmall?.copyWith(
          color: theme.colorScheme.onPrimary,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }
}
