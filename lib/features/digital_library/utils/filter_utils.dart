import '../models/library_file_model.dart';
import '../models/library_folder_model.dart';
import '../enums/file_access_type.dart';
import '../enums/folder_type.dart';
import '../enums/library_file_type.dart';
import '../enums/offline_access.dart';
import '../enums/upload_status.dart';

/// Utility class for filtering files and folders
class FilterUtils {
  /// Filter files by multiple criteria
  static List<LibraryFileModel> filterFiles(
    List<LibraryFileModel> files, {
    String? searchQuery,
    FileAccessType? accessType,
    LibraryFileType? fileType,
    OfflineAccess? offlineAccess,
    UploadStatus? uploadStatus,
    List<String>? tags,
    DateTime? createdAfter,
    DateTime? createdBefore,
    DateTime? modifiedAfter,
    DateTime? modifiedBefore,
    int? minSizeBytes,
    int? maxSizeBytes,
    bool? isFavorite,
    String? uploaderId,
    String? folderId,
    String? classId,
    String? subject,
  }) {
    var filteredFiles = files;

    // Search query filter
    if (searchQuery != null && searchQuery.isNotEmpty) {
      filteredFiles = filteredFiles
          .where((file) => _matchesSearchQuery(file, searchQuery))
          .toList();
    }

    // Access type filter
    if (accessType != null) {
      filteredFiles = filteredFiles
          .where((file) => file.accessType == accessType)
          .toList();
    }

    // File type filter
    if (fileType != null) {
      filteredFiles = filteredFiles
          .where((file) => file.fileType == fileType)
          .toList();
    }

    // Offline access filter
    if (offlineAccess != null) {
      filteredFiles = filteredFiles
          .where((file) => file.offlineAccess == offlineAccess)
          .toList();
    }

    // Upload status filter
    if (uploadStatus != null) {
      filteredFiles = filteredFiles
          .where((file) => file.uploadStatus == uploadStatus)
          .toList();
    }

    // Tags filter
    if (tags != null && tags.isNotEmpty) {
      filteredFiles = filteredFiles
          .where((file) => tags.any((tag) => file.tags.contains(tag)))
          .toList();
    }

    // Date filters
    if (createdAfter != null) {
      filteredFiles = filteredFiles
          .where((file) => file.createdAt.isAfter(createdAfter))
          .toList();
    }

    if (createdBefore != null) {
      filteredFiles = filteredFiles
          .where((file) => file.createdAt.isBefore(createdBefore))
          .toList();
    }

    if (modifiedAfter != null) {
      filteredFiles = filteredFiles
          .where((file) => file.modifiedAt?.isAfter(modifiedAfter) ?? false)
          .toList();
    }

    if (modifiedBefore != null) {
      filteredFiles = filteredFiles
          .where((file) => file.modifiedAt?.isBefore(modifiedBefore) ?? false)
          .toList();
    }

    // Size filters
    if (minSizeBytes != null) {
      filteredFiles = filteredFiles
          .where((file) => (file.fileSizeBytes ?? 0) >= minSizeBytes)
          .toList();
    }

    if (maxSizeBytes != null) {
      filteredFiles = filteredFiles
          .where((file) => (file.fileSizeBytes ?? 0) <= maxSizeBytes)
          .toList();
    }

    // Favorite filter
    if (isFavorite != null) {
      filteredFiles = filteredFiles
          .where((file) => file.isFavorite == isFavorite)
          .toList();
    }

    // Uploader filter
    if (uploaderId != null) {
      filteredFiles = filteredFiles
          .where((file) => file.uploaderId == uploaderId)
          .toList();
    }

    // Folder filter
    if (folderId != null) {
      filteredFiles = filteredFiles
          .where((file) => file.folderId == folderId)
          .toList();
    }

    // Class filter
    if (classId != null) {
      filteredFiles = filteredFiles
          .where((file) => file.classId == classId)
          .toList();
    }

    // Subject filter
    if (subject != null) {
      filteredFiles = filteredFiles
          .where((file) => file.subject == subject)
          .toList();
    }

    return filteredFiles;
  }

  /// Filter folders by multiple criteria
  static List<LibraryFolderModel> filterFolders(
    List<LibraryFolderModel> folders, {
    String? searchQuery,
    FolderType? folderType,
    FileAccessType? accessType,
    List<String>? tags,
    DateTime? createdAfter,
    DateTime? createdBefore,
    DateTime? modifiedAfter,
    DateTime? modifiedBefore,
    bool? isFavorite,
    bool? isPinned,
    bool? isArchived,
    String? creatorId,
    String? parentFolderId,
    String? classId,
    String? subject,
    int? minFileCount,
    int? maxFileCount,
  }) {
    var filteredFolders = folders;

    // Search query filter
    if (searchQuery != null && searchQuery.isNotEmpty) {
      filteredFolders = filteredFolders
          .where((folder) => _matchesFolderSearchQuery(folder, searchQuery))
          .toList();
    }

    // Folder type filter
    if (folderType != null) {
      filteredFolders = filteredFolders
          .where((folder) => folder.folderType == folderType)
          .toList();
    }

    // Access type filter
    if (accessType != null) {
      filteredFolders = filteredFolders
          .where((folder) => folder.accessType == accessType)
          .toList();
    }

    // Tags filter
    if (tags != null && tags.isNotEmpty) {
      filteredFolders = filteredFolders
          .where((folder) => tags.any((tag) => folder.tags.contains(tag)))
          .toList();
    }

    // Date filters
    if (createdAfter != null) {
      filteredFolders = filteredFolders
          .where((folder) => folder.createdAt.isAfter(createdAfter))
          .toList();
    }

    if (createdBefore != null) {
      filteredFolders = filteredFolders
          .where((folder) => folder.createdAt.isBefore(createdBefore))
          .toList();
    }

    if (modifiedAfter != null) {
      filteredFolders = filteredFolders
          .where((folder) => folder.modifiedAt?.isAfter(modifiedAfter) ?? false)
          .toList();
    }

    if (modifiedBefore != null) {
      filteredFolders = filteredFolders
          .where(
            (folder) => folder.modifiedAt?.isBefore(modifiedBefore) ?? false,
          )
          .toList();
    }

    // Boolean filters
    if (isFavorite != null) {
      filteredFolders = filteredFolders
          .where((folder) => folder.isFavorite == isFavorite)
          .toList();
    }

    if (isPinned != null) {
      filteredFolders = filteredFolders
          .where((folder) => folder.isPinned == isPinned)
          .toList();
    }

    if (isArchived != null) {
      filteredFolders = filteredFolders
          .where((folder) => folder.isArchived == isArchived)
          .toList();
    }

    // Creator filter
    if (creatorId != null) {
      filteredFolders = filteredFolders
          .where((folder) => folder.creatorId == creatorId)
          .toList();
    }

    // Parent folder filter
    if (parentFolderId != null) {
      filteredFolders = filteredFolders
          .where((folder) => folder.parentFolderId == parentFolderId)
          .toList();
    }

    // Class filter
    if (classId != null) {
      filteredFolders = filteredFolders
          .where((folder) => folder.classId == classId)
          .toList();
    }

    // Subject filter
    if (subject != null) {
      filteredFolders = filteredFolders
          .where((folder) => folder.subject == subject)
          .toList();
    }

    // File count filters
    if (minFileCount != null) {
      filteredFolders = filteredFolders
          .where((folder) => folder.fileCount >= minFileCount)
          .toList();
    }

    if (maxFileCount != null) {
      filteredFolders = filteredFolders
          .where((folder) => folder.fileCount <= maxFileCount)
          .toList();
    }

    return filteredFolders;
  }

  /// Check if file matches search query
  static bool _matchesSearchQuery(LibraryFileModel file, String query) {
    final searchQuery = query.toLowerCase();

    return file.fileName.toLowerCase().contains(searchQuery) ||
        (file.title?.toLowerCase().contains(searchQuery) ?? false) ||
        (file.description?.toLowerCase().contains(searchQuery) ?? false) ||
        file.tags.any((tag) => tag.toLowerCase().contains(searchQuery)) ||
        file.uploaderName.toLowerCase().contains(searchQuery) ||
        file.fileExtension.toLowerCase().contains(searchQuery) ||
        file.fileType.label.toLowerCase().contains(searchQuery);
  }

  /// Check if folder matches search query
  static bool _matchesFolderSearchQuery(
    LibraryFolderModel folder,
    String query,
  ) {
    final searchQuery = query.toLowerCase();

    return folder.name.toLowerCase().contains(searchQuery) ||
        (folder.description?.toLowerCase().contains(searchQuery) ?? false) ||
        folder.tags.any((tag) => tag.toLowerCase().contains(searchQuery)) ||
        folder.creatorName.toLowerCase().contains(searchQuery) ||
        folder.folderType.label.toLowerCase().contains(searchQuery);
  }

  /// Get unique tags from files
  static List<String> getUniqueTagsFromFiles(List<LibraryFileModel> files) {
    final tags = <String>{};
    for (final file in files) {
      tags.addAll(file.tags);
    }
    return tags.toList()..sort();
  }

  /// Get unique tags from folders
  static List<String> getUniqueTagsFromFolders(
    List<LibraryFolderModel> folders,
  ) {
    final tags = <String>{};
    for (final folder in folders) {
      tags.addAll(folder.tags);
    }
    return tags.toList()..sort();
  }

  /// Get unique uploaders from files
  static List<String> getUniqueUploadersFromFiles(
    List<LibraryFileModel> files,
  ) {
    final uploaders = <String>{};
    for (final file in files) {
      uploaders.add(file.uploaderName);
    }
    return uploaders.toList()..sort();
  }

  /// Get unique creators from folders
  static List<String> getUniqueCreatorsFromFolders(
    List<LibraryFolderModel> folders,
  ) {
    final creators = <String>{};
    for (final folder in folders) {
      creators.add(folder.creatorName);
    }
    return creators.toList()..sort();
  }

  /// Get file type distribution
  static Map<LibraryFileType, int> getFileTypeDistribution(
    List<LibraryFileModel> files,
  ) {
    final distribution = <LibraryFileType, int>{};

    for (final file in files) {
      distribution[file.fileType] = (distribution[file.fileType] ?? 0) + 1;
    }

    return distribution;
  }

  /// Get folder type distribution
  static Map<FolderType, int> getFolderTypeDistribution(
    List<LibraryFolderModel> folders,
  ) {
    final distribution = <FolderType, int>{};

    for (final folder in folders) {
      distribution[folder.folderType] =
          (distribution[folder.folderType] ?? 0) + 1;
    }

    return distribution;
  }

  /// Get access type distribution for files
  static Map<FileAccessType, int> getFileAccessTypeDistribution(
    List<LibraryFileModel> files,
  ) {
    final distribution = <FileAccessType, int>{};

    for (final file in files) {
      distribution[file.accessType] = (distribution[file.accessType] ?? 0) + 1;
    }

    return distribution;
  }

  /// Get access type distribution for folders
  static Map<FileAccessType, int> getFolderAccessTypeDistribution(
    List<LibraryFolderModel> folders,
  ) {
    final distribution = <FileAccessType, int>{};

    for (final folder in folders) {
      distribution[folder.accessType] =
          (distribution[folder.accessType] ?? 0) + 1;
    }

    return distribution;
  }

  /// Calculate total size of files
  static int calculateTotalFileSize(List<LibraryFileModel> files) {
    return files.fold<int>(0, (sum, file) => sum + (file.fileSizeBytes ?? 0));
  }

  /// Get files by date range
  static List<LibraryFileModel> getFilesByDateRange(
    List<LibraryFileModel> files,
    DateTime startDate,
    DateTime endDate,
  ) {
    return files
        .where(
          (file) =>
              file.createdAt.isAfter(startDate) &&
              file.createdAt.isBefore(endDate),
        )
        .toList();
  }

  /// Get folders by date range
  static List<LibraryFolderModel> getFoldersByDateRange(
    List<LibraryFolderModel> folders,
    DateTime startDate,
    DateTime endDate,
  ) {
    return folders
        .where(
          (folder) =>
              folder.createdAt.isAfter(startDate) &&
              folder.createdAt.isBefore(endDate),
        )
        .toList();
  }

  /// Get recently modified files
  static List<LibraryFileModel> getRecentlyModifiedFiles(
    List<LibraryFileModel> files, {
    int daysBack = 7,
  }) {
    final cutoffDate = DateTime.now().subtract(Duration(days: daysBack));

    return files.where((file) {
      final modifiedDate = file.modifiedAt ?? file.createdAt;
      return modifiedDate.isAfter(cutoffDate);
    }).toList();
  }

  /// Get recently modified folders
  static List<LibraryFolderModel> getRecentlyModifiedFolders(
    List<LibraryFolderModel> folders, {
    int daysBack = 7,
  }) {
    final cutoffDate = DateTime.now().subtract(Duration(days: daysBack));

    return folders.where((folder) {
      final modifiedDate = folder.modifiedAt ?? folder.createdAt;
      return modifiedDate.isAfter(cutoffDate);
    }).toList();
  }

  /// Get files with errors
  static List<LibraryFileModel> getFilesWithErrors(
    List<LibraryFileModel> files,
  ) {
    return files.where((file) => file.hasError).toList();
  }

  /// Get offline available files
  static List<LibraryFileModel> getOfflineAvailableFiles(
    List<LibraryFileModel> files,
  ) {
    return files.where((file) => file.offlineAccess.canAccessOffline).toList();
  }

  /// Get favorite files
  static List<LibraryFileModel> getFavoriteFiles(List<LibraryFileModel> files) {
    return files.where((file) => file.isFavorite).toList();
  }

  /// Get favorite folders
  static List<LibraryFolderModel> getFavoriteFolders(
    List<LibraryFolderModel> folders,
  ) {
    return folders.where((folder) => folder.isFavorite).toList();
  }

  /// Get pinned folders
  static List<LibraryFolderModel> getPinnedFolders(
    List<LibraryFolderModel> folders,
  ) {
    return folders.where((folder) => folder.isPinned).toList();
  }

  /// Get archived folders
  static List<LibraryFolderModel> getArchivedFolders(
    List<LibraryFolderModel> folders,
  ) {
    return folders.where((folder) => folder.isArchived).toList();
  }
}
