import 'dart:io';
import 'dart:typed_data';
import 'package:logger/logger.dart';

import '../enums/library_file_type.dart';
import 'file_utils.dart';

/// Utility class for file validation
class FileValidationUtils {
  static final Logger _logger = Logger();

  /// Validation result model
  static const int maxFileSize = 100 * 1024 * 1024; // 100MB
  static const int maxFilesPerUpload = 10;
  static const List<String> blockedExtensions = [
    'exe', 'bat', 'cmd', 'com', 'pif', 'scr', 'vbs', 'js', 'jar', 'msi'
  ];

  /// Validate a single file
  static FileValidationResult validateFile(File file) {
    try {
      final fileName = file.path.split('/').last;
      
      // Check if file exists
      if (!file.existsSync()) {
        return FileValidationResult(
          isValid: false,
          error: 'File does not exist',
          fileName: fileName,
        );
      }

      // Validate file name
      if (!FileUtils.isValidFileName(fileName)) {
        return FileValidationResult(
          isValid: false,
          error: 'Invalid file name',
          fileName: fileName,
        );
      }

      // Check file size
      final fileSize = file.lengthSync();
      if (!FileUtils.isValidFileSize(fileSize, maxSizeBytes: maxFileSize)) {
        return FileValidationResult(
          isValid: false,
          error: 'File size exceeds maximum allowed size (${FileUtils.formatFileSize(maxFileSize)})',
          fileName: fileName,
          fileSize: fileSize,
        );
      }

      // Check for blocked extensions
      final extension = FileUtils.getFileExtension(fileName);
      if (blockedExtensions.contains(extension.toLowerCase())) {
        return FileValidationResult(
          isValid: false,
          error: 'File type not allowed for security reasons',
          fileName: fileName,
          fileSize: fileSize,
        );
      }

      // Check if file is readable
      try {
        file.readAsBytesSync();
      } catch (e) {
        return FileValidationResult(
          isValid: false,
          error: 'File is not readable or corrupted',
          fileName: fileName,
          fileSize: fileSize,
        );
      }

      // Detect file type
      final fileType = FileUtils.detectFileType(fileName);
      
      // Additional validation based on file type
      final typeValidation = _validateFileType(file, fileType);
      if (!typeValidation.isValid) {
        return typeValidation.copyWith(fileName: fileName, fileSize: fileSize);
      }

      return FileValidationResult(
        isValid: true,
        fileName: fileName,
        fileSize: fileSize,
        fileType: fileType,
      );
    } catch (e, stackTrace) {
      _logger.e('Error validating file: $e', error: e, stackTrace: stackTrace);
      return FileValidationResult(
        isValid: false,
        error: 'Unexpected error during validation: $e',
        fileName: file.path.split('/').last,
      );
    }
  }

  /// Validate file from bytes
  static FileValidationResult validateFileFromBytes(
    Uint8List bytes,
    String fileName,
  ) {
    try {
      // Validate file name
      if (!FileUtils.isValidFileName(fileName)) {
        return FileValidationResult(
          isValid: false,
          error: 'Invalid file name',
          fileName: fileName,
        );
      }

      // Check file size
      if (!FileUtils.isValidFileSize(bytes.length, maxSizeBytes: maxFileSize)) {
        return FileValidationResult(
          isValid: false,
          error: 'File size exceeds maximum allowed size (${FileUtils.formatFileSize(maxFileSize)})',
          fileName: fileName,
          fileSize: bytes.length,
        );
      }

      // Check for blocked extensions
      final extension = FileUtils.getFileExtension(fileName);
      if (blockedExtensions.contains(extension.toLowerCase())) {
        return FileValidationResult(
          isValid: false,
          error: 'File type not allowed for security reasons',
          fileName: fileName,
          fileSize: bytes.length,
        );
      }

      // Check if bytes are not empty
      if (bytes.isEmpty) {
        return FileValidationResult(
          isValid: false,
          error: 'File is empty',
          fileName: fileName,
          fileSize: bytes.length,
        );
      }

      // Detect file type
      final fileType = FileUtils.detectFileType(fileName);
      
      // Additional validation based on file type
      final typeValidation = _validateFileTypeFromBytes(bytes, fileType);
      if (!typeValidation.isValid) {
        return typeValidation.copyWith(fileName: fileName, fileSize: bytes.length);
      }

      return FileValidationResult(
        isValid: true,
        fileName: fileName,
        fileSize: bytes.length,
        fileType: fileType,
      );
    } catch (e, stackTrace) {
      _logger.e('Error validating file from bytes: $e', error: e, stackTrace: stackTrace);
      return FileValidationResult(
        isValid: false,
        error: 'Unexpected error during validation: $e',
        fileName: fileName,
      );
    }
  }

  /// Validate multiple files
  static MultipleFileValidationResult validateMultipleFiles(List<File> files) {
    try {
      // Check number of files
      if (files.length > maxFilesPerUpload) {
        return MultipleFileValidationResult(
          isValid: false,
          error: 'Too many files selected. Maximum allowed: $maxFilesPerUpload',
          totalFiles: files.length,
        );
      }

      final validFiles = <File>[];
      final invalidFiles = <FileValidationResult>[];
      int totalSize = 0;

      for (final file in files) {
        final validation = validateFile(file);
        
        if (validation.isValid) {
          validFiles.add(file);
          totalSize += validation.fileSize ?? 0;
        } else {
          invalidFiles.add(validation);
        }
      }

      // Check total size
      if (totalSize > maxFileSize * 2) { // Allow up to 2x max size for multiple files
        return MultipleFileValidationResult(
          isValid: false,
          error: 'Total file size exceeds maximum allowed size',
          totalFiles: files.length,
          totalSize: totalSize,
        );
      }

      return MultipleFileValidationResult(
        isValid: invalidFiles.isEmpty,
        validFiles: validFiles,
        invalidFiles: invalidFiles,
        totalFiles: files.length,
        totalSize: totalSize,
        error: invalidFiles.isNotEmpty ? 'Some files failed validation' : null,
      );
    } catch (e, stackTrace) {
      _logger.e('Error validating multiple files: $e', error: e, stackTrace: stackTrace);
      return MultipleFileValidationResult(
        isValid: false,
        error: 'Unexpected error during validation: $e',
        totalFiles: files.length,
      );
    }
  }

  /// Validate file type specific requirements
  static FileValidationResult _validateFileType(File file, LibraryFileType fileType) {
    switch (fileType) {
      case LibraryFileType.image:
        return _validateImageFile(file);
      case LibraryFileType.video:
        return _validateVideoFile(file);
      case LibraryFileType.audio:
        return _validateAudioFile(file);
      case LibraryFileType.pdf:
        return _validatePdfFile(file);
      case LibraryFileType.document:
        return _validateDocumentFile(file);
      default:
        return const FileValidationResult(isValid: true);
    }
  }

  /// Validate file type from bytes
  static FileValidationResult _validateFileTypeFromBytes(Uint8List bytes, LibraryFileType fileType) {
    switch (fileType) {
      case LibraryFileType.image:
        return _validateImageFileFromBytes(bytes);
      case LibraryFileType.video:
        return _validateVideoFileFromBytes(bytes);
      case LibraryFileType.audio:
        return _validateAudioFileFromBytes(bytes);
      case LibraryFileType.pdf:
        return _validatePdfFileFromBytes(bytes);
      case LibraryFileType.document:
        return _validateDocumentFileFromBytes(bytes);
      default:
        return const FileValidationResult(isValid: true);
    }
  }

  /// Validate image file
  static FileValidationResult _validateImageFile(File file) {
    try {
      // Basic validation - check file signature
      final bytes = file.readAsBytesSync();
      return _validateImageFileFromBytes(bytes);
    } catch (e) {
      return const FileValidationResult(
        isValid: false,
        error: 'Invalid image file',
      );
    }
  }

  /// Validate image file from bytes
  static FileValidationResult _validateImageFileFromBytes(Uint8List bytes) {
    if (bytes.length < 4) {
      return const FileValidationResult(
        isValid: false,
        error: 'Invalid image file - too small',
      );
    }

    // Check common image file signatures
    final signature = bytes.take(4).toList();
    
    // JPEG
    if (signature[0] == 0xFF && signature[1] == 0xD8) {
      return const FileValidationResult(isValid: true);
    }
    
    // PNG
    if (signature[0] == 0x89 && signature[1] == 0x50 && 
        signature[2] == 0x4E && signature[3] == 0x47) {
      return const FileValidationResult(isValid: true);
    }
    
    // GIF
    if (signature[0] == 0x47 && signature[1] == 0x49 && signature[2] == 0x46) {
      return const FileValidationResult(isValid: true);
    }
    
    // BMP
    if (signature[0] == 0x42 && signature[1] == 0x4D) {
      return const FileValidationResult(isValid: true);
    }

    // For other formats, assume valid (WebP, TIFF, etc. have complex signatures)
    return const FileValidationResult(isValid: true);
  }

  /// Validate video file
  static FileValidationResult _validateVideoFile(File file) {
    // Basic validation - for now just check if file is readable
    try {
      file.readAsBytesSync();
      return const FileValidationResult(isValid: true);
    } catch (e) {
      return const FileValidationResult(
        isValid: false,
        error: 'Invalid video file',
      );
    }
  }

  /// Validate video file from bytes
  static FileValidationResult _validateVideoFileFromBytes(Uint8List bytes) {
    if (bytes.length < 8) {
      return const FileValidationResult(
        isValid: false,
        error: 'Invalid video file - too small',
      );
    }
    
    // Basic validation - assume valid for now
    return const FileValidationResult(isValid: true);
  }

  /// Validate audio file
  static FileValidationResult _validateAudioFile(File file) {
    try {
      file.readAsBytesSync();
      return const FileValidationResult(isValid: true);
    } catch (e) {
      return const FileValidationResult(
        isValid: false,
        error: 'Invalid audio file',
      );
    }
  }

  /// Validate audio file from bytes
  static FileValidationResult _validateAudioFileFromBytes(Uint8List bytes) {
    if (bytes.length < 4) {
      return const FileValidationResult(
        isValid: false,
        error: 'Invalid audio file - too small',
      );
    }
    
    return const FileValidationResult(isValid: true);
  }

  /// Validate PDF file
  static FileValidationResult _validatePdfFile(File file) {
    try {
      final bytes = file.readAsBytesSync();
      return _validatePdfFileFromBytes(bytes);
    } catch (e) {
      return const FileValidationResult(
        isValid: false,
        error: 'Invalid PDF file',
      );
    }
  }

  /// Validate PDF file from bytes
  static FileValidationResult _validatePdfFileFromBytes(Uint8List bytes) {
    if (bytes.length < 4) {
      return const FileValidationResult(
        isValid: false,
        error: 'Invalid PDF file - too small',
      );
    }

    // Check PDF signature
    final signature = String.fromCharCodes(bytes.take(4));
    if (signature != '%PDF') {
      return const FileValidationResult(
        isValid: false,
        error: 'Invalid PDF file - missing PDF signature',
      );
    }

    return const FileValidationResult(isValid: true);
  }

  /// Validate document file
  static FileValidationResult _validateDocumentFile(File file) {
    try {
      file.readAsBytesSync();
      return const FileValidationResult(isValid: true);
    } catch (e) {
      return const FileValidationResult(
        isValid: false,
        error: 'Invalid document file',
      );
    }
  }

  /// Validate document file from bytes
  static FileValidationResult _validateDocumentFileFromBytes(Uint8List bytes) {
    if (bytes.isEmpty) {
      return const FileValidationResult(
        isValid: false,
        error: 'Invalid document file - empty',
      );
    }
    
    return const FileValidationResult(isValid: true);
  }
}

/// File validation result model
class FileValidationResult {
  final bool isValid;
  final String? error;
  final String? fileName;
  final int? fileSize;
  final LibraryFileType? fileType;

  const FileValidationResult({
    required this.isValid,
    this.error,
    this.fileName,
    this.fileSize,
    this.fileType,
  });

  FileValidationResult copyWith({
    bool? isValid,
    String? error,
    String? fileName,
    int? fileSize,
    LibraryFileType? fileType,
  }) {
    return FileValidationResult(
      isValid: isValid ?? this.isValid,
      error: error ?? this.error,
      fileName: fileName ?? this.fileName,
      fileSize: fileSize ?? this.fileSize,
      fileType: fileType ?? this.fileType,
    );
  }
}

/// Multiple file validation result model
class MultipleFileValidationResult {
  final bool isValid;
  final String? error;
  final List<File>? validFiles;
  final List<FileValidationResult>? invalidFiles;
  final int totalFiles;
  final int? totalSize;

  const MultipleFileValidationResult({
    required this.isValid,
    this.error,
    this.validFiles,
    this.invalidFiles,
    required this.totalFiles,
    this.totalSize,
  });

  int get validFileCount => validFiles?.length ?? 0;
  int get invalidFileCount => invalidFiles?.length ?? 0;
  bool get hasValidFiles => validFileCount > 0;
  bool get hasInvalidFiles => invalidFileCount > 0;
}
