import 'dart:io';
import 'dart:typed_data';
import 'package:path/path.dart' as path;

import '../enums/library_file_type.dart';

/// Utility class for file operations and validations
class FileUtils {
  /// Detect file type from file extension
  static LibraryFileType detectFileType(String fileName) {
    final extension = path.extension(fileName).toLowerCase();

    for (final fileType in LibraryFileType.values) {
      if (fileType.extensions.contains(extension.substring(1))) {
        return fileType;
      }
    }

    return LibraryFileType.other;
  }

  /// Get MIME type from file extension
  static String? getMimeType(String fileName) {
    final extension = path.extension(fileName).toLowerCase();

    const mimeTypes = {
      // Documents
      '.pdf': 'application/pdf',
      '.doc': 'application/msword',
      '.docx':
          'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      '.xls': 'application/vnd.ms-excel',
      '.xlsx':
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      '.ppt': 'application/vnd.ms-powerpoint',
      '.pptx':
          'application/vnd.openxmlformats-officedocument.presentationml.presentation',
      '.odt': 'application/vnd.oasis.opendocument.text',
      '.ods': 'application/vnd.oasis.opendocument.spreadsheet',
      '.odp': 'application/vnd.oasis.opendocument.presentation',

      // Text files
      '.txt': 'text/plain',
      '.rtf': 'application/rtf',
      '.md': 'text/markdown',
      '.csv': 'text/csv',
      '.json': 'application/json',
      '.xml': 'application/xml',
      '.html': 'text/html',
      '.htm': 'text/html',

      // Images
      '.jpg': 'image/jpeg',
      '.jpeg': 'image/jpeg',
      '.png': 'image/png',
      '.gif': 'image/gif',
      '.bmp': 'image/bmp',
      '.webp': 'image/webp',
      '.svg': 'image/svg+xml',
      '.tiff': 'image/tiff',
      '.tif': 'image/tiff',
      '.ico': 'image/x-icon',

      // Videos
      '.mp4': 'video/mp4',
      '.avi': 'video/x-msvideo',
      '.mov': 'video/quicktime',
      '.wmv': 'video/x-ms-wmv',
      '.flv': 'video/x-flv',
      '.webm': 'video/webm',
      '.mkv': 'video/x-matroska',
      '.m4v': 'video/x-m4v',

      // Audio
      '.mp3': 'audio/mpeg',
      '.wav': 'audio/wav',
      '.flac': 'audio/flac',
      '.aac': 'audio/aac',
      '.ogg': 'audio/ogg',
      '.wma': 'audio/x-ms-wma',
      '.m4a': 'audio/x-m4a',

      // Archives
      '.zip': 'application/zip',
      '.rar': 'application/x-rar-compressed',
      '.7z': 'application/x-7z-compressed',
      '.tar': 'application/x-tar',
      '.gz': 'application/gzip',
      '.bz2': 'application/x-bzip2',

      // Code files
      '.js': 'text/javascript',
      '.ts': 'text/typescript',
      '.dart': 'text/x-dart',
      '.py': 'text/x-python',
      '.java': 'text/x-java-source',
      '.cpp': 'text/x-c++src',
      '.c': 'text/x-csrc',
      '.h': 'text/x-chdr',
      '.css': 'text/css',
      '.scss': 'text/x-scss',
      '.less': 'text/x-less',
      '.php': 'text/x-php',
      '.rb': 'text/x-ruby',
      '.go': 'text/x-go',
      '.rs': 'text/x-rust',
      '.swift': 'text/x-swift',
      '.kt': 'text/x-kotlin',
      '.scala': 'text/x-scala',
      '.sh': 'text/x-shellscript',
      '.bat': 'text/x-msdos-batch',
      '.ps1': 'text/x-powershell',

      // Other
      '.epub': 'application/epub+zip',
      '.mobi': 'application/x-mobipocket-ebook',
      '.apk': 'application/vnd.android.package-archive',
      '.dmg': 'application/x-apple-diskimage',
      '.iso': 'application/x-iso9660-image',
    };

    return mimeTypes[extension];
  }

  /// Format file size in bytes to human-readable string
  static String formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    }
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }

  /// Validate file name
  static bool isValidFileName(String fileName) {
    if (fileName.isEmpty) return false;
    if (fileName.length > 255) return false;

    // Check for invalid characters
    const invalidChars = ['<', '>', ':', '"', '/', '\\', '|', '?', '*'];
    for (final char in invalidChars) {
      if (fileName.contains(char)) return false;
    }

    // Check for reserved names (Windows)
    const reservedNames = [
      'CON',
      'PRN',
      'AUX',
      'NUL',
      'COM1',
      'COM2',
      'COM3',
      'COM4',
      'COM5',
      'COM6',
      'COM7',
      'COM8',
      'COM9',
      'LPT1',
      'LPT2',
      'LPT3',
      'LPT4',
      'LPT5',
      'LPT6',
      'LPT7',
      'LPT8',
      'LPT9',
    ];

    final nameWithoutExtension = path
        .basenameWithoutExtension(fileName)
        .toUpperCase();
    if (reservedNames.contains(nameWithoutExtension)) return false;

    return true;
  }

  /// Sanitize file name for storage
  static String sanitizeFileName(String fileName) {
    // Replace invalid characters with underscores
    String sanitized = fileName
        .replaceAll(RegExp(r'[<>:"/\\|?*]'), '_')
        .replaceAll(RegExp(r'\s+'), '_');

    // Ensure it's not too long
    if (sanitized.length > 255) {
      final extension = path.extension(sanitized);
      final nameWithoutExtension = path.basenameWithoutExtension(sanitized);
      final maxNameLength = 255 - extension.length;
      sanitized = nameWithoutExtension.substring(0, maxNameLength) + extension;
    }

    return sanitized;
  }

  /// Generate unique file name to avoid conflicts
  static String generateUniqueFileName(String originalFileName) {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final extension = path.extension(originalFileName);
    final nameWithoutExtension = path.basenameWithoutExtension(
      originalFileName,
    );

    return '${nameWithoutExtension}_$timestamp$extension';
  }

  /// Check if file is an image
  static bool isImageFile(String fileName) {
    return detectFileType(fileName) == LibraryFileType.image;
  }

  /// Check if file is a video
  static bool isVideoFile(String fileName) {
    return detectFileType(fileName) == LibraryFileType.video;
  }

  /// Check if file is an audio file
  static bool isAudioFile(String fileName) {
    return detectFileType(fileName) == LibraryFileType.audio;
  }

  /// Check if file is a document
  static bool isDocumentFile(String fileName) {
    final fileType = detectFileType(fileName);
    return fileType == LibraryFileType.document ||
        fileType == LibraryFileType.pdf ||
        fileType == LibraryFileType.spreadsheet ||
        fileType == LibraryFileType.presentation;
  }

  /// Check if file is a text file
  static bool isTextFile(String fileName) {
    final fileType = detectFileType(fileName);
    return fileType == LibraryFileType.document ||
        fileType == LibraryFileType.note;
  }

  /// Check if file is a code file
  static bool isCodeFile(String fileName) {
    return detectFileType(fileName) == LibraryFileType.code;
  }

  /// Check if file is an archive
  static bool isArchiveFile(String fileName) {
    return detectFileType(fileName) == LibraryFileType.archive;
  }

  /// Check if file can be previewed
  static bool canPreviewFile(String fileName) {
    const previewableExtensions = [
      'pdf',
      'txt',
      'md',
      'json',
      'xml',
      'html',
      'htm',
      'csv',
      'jpg',
      'jpeg',
      'png',
      'gif',
      'webp',
      'svg',
      'mp4',
      'webm',
      'ogg',
      'mp3',
      'wav',
      'ogg',
    ];

    final extension = path.extension(fileName).toLowerCase().substring(1);
    return previewableExtensions.contains(extension);
  }

  /// Check if file can be edited
  static bool canEditFile(String fileName) {
    const editableExtensions = [
      'txt',
      'md',
      'json',
      'xml',
      'html',
      'htm',
      'csv',
      'js',
      'ts',
      'dart',
      'py',
      'java',
      'cpp',
      'c',
      'h',
      'css',
      'scss',
      'less',
      'php',
      'rb',
      'go',
      'rs',
      'swift',
      'kt',
      'scala',
      'sh',
      'bat',
      'ps1',
    ];

    final extension = path.extension(fileName).toLowerCase().substring(1);
    return editableExtensions.contains(extension);
  }

  /// Validate file size
  static bool isValidFileSize(
    int sizeBytes, {
    int maxSizeBytes = 100 * 1024 * 1024,
  }) {
    return sizeBytes > 0 && sizeBytes <= maxSizeBytes;
  }

  /// Check if file exists
  static Future<bool> fileExists(String filePath) async {
    try {
      return await File(filePath).exists();
    } catch (e) {
      return false;
    }
  }

  /// Get file size
  static Future<int?> getFileSize(String filePath) async {
    try {
      final file = File(filePath);
      if (await file.exists()) {
        return await file.length();
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  /// Get file last modified date
  static Future<DateTime?> getFileLastModified(String filePath) async {
    try {
      final file = File(filePath);
      if (await file.exists()) {
        final stat = await file.stat();
        return stat.modified;
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  /// Read file as bytes
  static Future<Uint8List?> readFileAsBytes(String filePath) async {
    try {
      final file = File(filePath);
      if (await file.exists()) {
        return await file.readAsBytes();
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  /// Read file as string (for text files)
  static Future<String?> readFileAsString(String filePath) async {
    try {
      final file = File(filePath);
      if (await file.exists()) {
        return await file.readAsString();
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  /// Write bytes to file
  static Future<bool> writeBytesToFile(String filePath, Uint8List bytes) async {
    try {
      final file = File(filePath);
      await file.parent.create(recursive: true);
      await file.writeAsBytes(bytes);
      return true;
    } catch (e) {
      return false;
    }
  }

  /// Write string to file
  static Future<bool> writeStringToFile(String filePath, String content) async {
    try {
      final file = File(filePath);
      await file.parent.create(recursive: true);
      await file.writeAsString(content);
      return true;
    } catch (e) {
      return false;
    }
  }

  /// Copy file
  static Future<bool> copyFile(
    String sourcePath,
    String destinationPath,
  ) async {
    try {
      final sourceFile = File(sourcePath);
      if (await sourceFile.exists()) {
        final destinationFile = File(destinationPath);
        await destinationFile.parent.create(recursive: true);
        await sourceFile.copy(destinationPath);
        return true;
      }
      return false;
    } catch (e) {
      return false;
    }
  }

  /// Move file
  static Future<bool> moveFile(
    String sourcePath,
    String destinationPath,
  ) async {
    try {
      final sourceFile = File(sourcePath);
      if (await sourceFile.exists()) {
        final destinationFile = File(destinationPath);
        await destinationFile.parent.create(recursive: true);
        await sourceFile.rename(destinationPath);
        return true;
      }
      return false;
    } catch (e) {
      return false;
    }
  }

  /// Delete file
  static Future<bool> deleteFile(String filePath) async {
    try {
      final file = File(filePath);
      if (await file.exists()) {
        await file.delete();
        return true;
      }
      return false;
    } catch (e) {
      return false;
    }
  }

  /// Get file extension without dot
  static String getFileExtension(String fileName) {
    final extension = path.extension(fileName);
    return extension.isNotEmpty ? extension.substring(1).toLowerCase() : '';
  }

  /// Get file name without extension
  static String getFileNameWithoutExtension(String fileName) {
    return path.basenameWithoutExtension(fileName);
  }

  /// Check if two files have the same content
  static Future<bool> filesHaveSameContent(
    String filePath1,
    String filePath2,
  ) async {
    try {
      final file1 = File(filePath1);
      final file2 = File(filePath2);

      if (!await file1.exists() || !await file2.exists()) {
        return false;
      }

      final size1 = await file1.length();
      final size2 = await file2.length();

      if (size1 != size2) {
        return false;
      }

      final bytes1 = await file1.readAsBytes();
      final bytes2 = await file2.readAsBytes();

      if (bytes1.length != bytes2.length) {
        return false;
      }

      for (int i = 0; i < bytes1.length; i++) {
        if (bytes1[i] != bytes2[i]) {
          return false;
        }
      }

      return true;
    } catch (e) {
      return false;
    }
  }
}
