import '../enums/file_access_type.dart';
import '../enums/library_file_type.dart';
import '../enums/upload_status.dart';

/// Model representing an upload session for tracking file uploads
/// Supports batch uploads, progress tracking, and retry mechanisms
class UploadSessionModel {
  /// Unique identifier for the upload session
  final String id;

  /// ID of the user performing the upload
  final String userId;

  /// Name of the user performing the upload
  final String userName;

  /// ID of the target folder (null for root level)
  final String? folderId;

  /// List of files being uploaded in this session
  final List<UploadFileItem> files;

  /// Overall status of the upload session
  final UploadStatus status;

  /// Overall progress of the upload session (0.0 to 1.0)
  final double progress;

  /// When the upload session was created
  final DateTime createdAt;

  /// When the upload session was started
  final DateTime? startedAt;

  /// When the upload session was completed
  final DateTime? completedAt;

  /// Error message if the session failed
  final String? errorMessage;

  /// Whether the session can be retried
  final bool canRetry;

  /// Number of retry attempts made
  final int retryCount;

  /// Maximum number of retry attempts allowed
  final int maxRetries;

  /// Additional metadata for the upload session
  final Map<String, dynamic> metadata;

  const UploadSessionModel({
    required this.id,
    required this.userId,
    required this.userName,
    this.folderId,
    required this.files,
    required this.status,
    this.progress = 0.0,
    required this.createdAt,
    this.startedAt,
    this.completedAt,
    this.errorMessage,
    this.canRetry = true,
    this.retryCount = 0,
    this.maxRetries = 3,
    this.metadata = const {},
  });

  /// Create an UploadSessionModel from JSON
  factory UploadSessionModel.fromJson(Map<String, dynamic> json) {
    return UploadSessionModel(
      id: json['id'] as String,
      userId: json['userId'] as String,
      userName: json['userName'] as String,
      folderId: json['folderId'] as String?,
      files: (json['files'] as List)
          .map((item) => UploadFileItem.fromJson(item as Map<String, dynamic>))
          .toList(),
      status: UploadStatusExtension.fromString(json['status'] as String),
      progress: (json['progress'] as num?)?.toDouble() ?? 0.0,
      createdAt: DateTime.parse(json['createdAt'] as String),
      startedAt: json['startedAt'] != null
          ? DateTime.parse(json['startedAt'] as String)
          : null,
      completedAt: json['completedAt'] != null
          ? DateTime.parse(json['completedAt'] as String)
          : null,
      errorMessage: json['errorMessage'] as String?,
      canRetry: json['canRetry'] as bool? ?? true,
      retryCount: json['retryCount'] as int? ?? 0,
      maxRetries: json['maxRetries'] as int? ?? 3,
      metadata: Map<String, dynamic>.from(json['metadata'] as Map? ?? {}),
    );
  }

  /// Convert UploadSessionModel to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'userName': userName,
      'folderId': folderId,
      'files': files.map((file) => file.toJson()).toList(),
      'status': status.value,
      'progress': progress,
      'createdAt': createdAt.toIso8601String(),
      'startedAt': startedAt?.toIso8601String(),
      'completedAt': completedAt?.toIso8601String(),
      'errorMessage': errorMessage,
      'canRetry': canRetry,
      'retryCount': retryCount,
      'maxRetries': maxRetries,
      'metadata': metadata,
    };
  }

  /// Get the number of completed files
  int get completedFileCount {
    return files.where((file) => file.status == UploadStatus.completed).length;
  }

  /// Get the number of failed files
  int get failedFileCount {
    return files.where((file) => file.status == UploadStatus.failed).length;
  }

  /// Get the total number of files
  int get totalFileCount => files.length;

  /// Whether all files have been uploaded successfully
  bool get isAllCompleted => completedFileCount == totalFileCount;

  /// Whether any files have failed
  bool get hasFailures => failedFileCount > 0;

  /// Whether the session is currently active
  bool get isActive {
    return status == UploadStatus.uploading || status == UploadStatus.processing;
  }

  /// Whether the session can be retried
  bool get canRetrySession {
    return canRetry && retryCount < maxRetries && hasFailures;
  }

  /// Get the total size of all files in bytes
  int get totalSizeBytes {
    return files.fold(0, (sum, file) => sum + (file.fileSizeBytes ?? 0));
  }

  /// Get the total size as a human-readable string
  String get totalSizeString {
    return _formatFileSize(totalSizeBytes);
  }

  /// Create a copy of this UploadSessionModel with updated properties
  UploadSessionModel copyWith({
    String? id,
    String? userId,
    String? userName,
    String? folderId,
    List<UploadFileItem>? files,
    UploadStatus? status,
    double? progress,
    DateTime? createdAt,
    DateTime? startedAt,
    DateTime? completedAt,
    String? errorMessage,
    bool? canRetry,
    int? retryCount,
    int? maxRetries,
    Map<String, dynamic>? metadata,
  }) {
    return UploadSessionModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      userName: userName ?? this.userName,
      folderId: folderId ?? this.folderId,
      files: files ?? this.files,
      status: status ?? this.status,
      progress: progress ?? this.progress,
      createdAt: createdAt ?? this.createdAt,
      startedAt: startedAt ?? this.startedAt,
      completedAt: completedAt ?? this.completedAt,
      errorMessage: errorMessage ?? this.errorMessage,
      canRetry: canRetry ?? this.canRetry,
      retryCount: retryCount ?? this.retryCount,
      maxRetries: maxRetries ?? this.maxRetries,
      metadata: metadata ?? this.metadata,
    );
  }

  /// Format file size in bytes to human-readable string
  static String _formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    }
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }

  @override
  String toString() {
    return 'UploadSessionModel(id: $id, status: $status, '
        'progress: $progress, fileCount: $totalFileCount)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UploadSessionModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// Model representing a single file item within an upload session
class UploadFileItem {
  /// Unique identifier for this file item
  final String id;

  /// Original filename
  final String fileName;

  /// Optional custom title
  final String? title;

  /// Optional description
  final String? description;

  /// File type
  final LibraryFileType fileType;

  /// File size in bytes
  final int? fileSizeBytes;

  /// File extension
  final String fileExtension;

  /// Access type for this file
  final FileAccessType accessType;

  /// Tags for this file
  final List<String> tags;

  /// Local file path
  final String localPath;

  /// Upload status for this specific file
  final UploadStatus status;

  /// Upload progress for this specific file (0.0 to 1.0)
  final double progress;

  /// Error message if this file failed to upload
  final String? errorMessage;

  /// Cloud URL after successful upload
  final String? cloudUrl;

  /// Thumbnail URL after processing
  final String? thumbnailUrl;

  const UploadFileItem({
    required this.id,
    required this.fileName,
    this.title,
    this.description,
    required this.fileType,
    this.fileSizeBytes,
    required this.fileExtension,
    required this.accessType,
    this.tags = const [],
    required this.localPath,
    this.status = UploadStatus.pending,
    this.progress = 0.0,
    this.errorMessage,
    this.cloudUrl,
    this.thumbnailUrl,
  });

  /// Create an UploadFileItem from JSON
  factory UploadFileItem.fromJson(Map<String, dynamic> json) {
    return UploadFileItem(
      id: json['id'] as String,
      fileName: json['fileName'] as String,
      title: json['title'] as String?,
      description: json['description'] as String?,
      fileType: LibraryFileTypeExtension.fromString(json['fileType'] as String),
      fileSizeBytes: json['fileSizeBytes'] as int?,
      fileExtension: json['fileExtension'] as String,
      accessType: FileAccessTypeExtension.fromString(json['accessType'] as String),
      tags: List<String>.from(json['tags'] as List? ?? []),
      localPath: json['localPath'] as String,
      status: UploadStatusExtension.fromString(json['status'] as String),
      progress: (json['progress'] as num?)?.toDouble() ?? 0.0,
      errorMessage: json['errorMessage'] as String?,
      cloudUrl: json['cloudUrl'] as String?,
      thumbnailUrl: json['thumbnailUrl'] as String?,
    );
  }

  /// Convert UploadFileItem to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'fileName': fileName,
      'title': title,
      'description': description,
      'fileType': fileType.value,
      'fileSizeBytes': fileSizeBytes,
      'fileExtension': fileExtension,
      'accessType': accessType.value,
      'tags': tags,
      'localPath': localPath,
      'status': status.value,
      'progress': progress,
      'errorMessage': errorMessage,
      'cloudUrl': cloudUrl,
      'thumbnailUrl': thumbnailUrl,
    };
  }

  /// Get the display title (title if provided, otherwise fileName)
  String get displayTitle => title ?? fileName;

  /// Get the file size as a human-readable string
  String get fileSizeString {
    if (fileSizeBytes == null) return 'Unknown size';
    return UploadSessionModel._formatFileSize(fileSizeBytes!);
  }

  /// Create a copy of this UploadFileItem with updated properties
  UploadFileItem copyWith({
    String? id,
    String? fileName,
    String? title,
    String? description,
    LibraryFileType? fileType,
    int? fileSizeBytes,
    String? fileExtension,
    FileAccessType? accessType,
    List<String>? tags,
    String? localPath,
    UploadStatus? status,
    double? progress,
    String? errorMessage,
    String? cloudUrl,
    String? thumbnailUrl,
  }) {
    return UploadFileItem(
      id: id ?? this.id,
      fileName: fileName ?? this.fileName,
      title: title ?? this.title,
      description: description ?? this.description,
      fileType: fileType ?? this.fileType,
      fileSizeBytes: fileSizeBytes ?? this.fileSizeBytes,
      fileExtension: fileExtension ?? this.fileExtension,
      accessType: accessType ?? this.accessType,
      tags: tags ?? this.tags,
      localPath: localPath ?? this.localPath,
      status: status ?? this.status,
      progress: progress ?? this.progress,
      errorMessage: errorMessage ?? this.errorMessage,
      cloudUrl: cloudUrl ?? this.cloudUrl,
      thumbnailUrl: thumbnailUrl ?? this.thumbnailUrl,
    );
  }

  @override
  String toString() {
    return 'UploadFileItem(id: $id, fileName: $fileName, status: $status, progress: $progress)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UploadFileItem && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
