import 'dart:io';
import 'library_file_model.dart';
import '../enums/library_file_type.dart';
import '../enums/download_state.dart';
import '../enums/viewer_type.dart';
import '../enums/file_access_type.dart';
import '../enums/file_usage_type.dart';
import '../enums/offline_access.dart';
import '../enums/upload_status.dart';

/// Legacy FileModel for backward compatibility with files feature
/// This is a simplified wrapper around LibraryFileModel
class FileModel {
  /// Unique identifier for the file
  final String id;

  /// File name with extension
  final String fileName;

  /// File size in bytes (null if unknown)
  final int? fileSizeBytes;

  /// File extension (without dot)
  final String fileExtension;

  /// File type based on extension
  final FileType fileType;

  /// URL for remote files (null for local files)
  final String? url;

  /// Local file path (null for remote files that haven't been downloaded)
  final String? localPath;

  /// Whether this is a local file or remote file
  final bool isLocal;

  /// Current download state (only relevant for remote files)
  final DownloadState downloadState;

  /// Download progress (0.0 to 1.0, only relevant when downloading)
  final double downloadProgress;

  /// Error message if download failed
  final String? downloadError;

  /// When the file was created/uploaded
  final DateTime? createdAt;

  /// When the file was last modified
  final DateTime? modifiedAt;

  /// MIME type of the file
  final String? mimeType;

  const FileModel({
    required this.id,
    required this.fileName,
    this.fileSizeBytes,
    required this.fileExtension,
    required this.fileType,
    this.url,
    this.localPath,
    required this.isLocal,
    this.downloadState = DownloadState.notDownloaded,
    this.downloadProgress = 0.0,
    this.downloadError,
    this.createdAt,
    this.modifiedAt,
    this.mimeType,
  });

  /// Create a FileModel from a local file
  factory FileModel.fromLocalFile(File file) {
    final fileName = file.path.split('/').last;
    final extension = fileName.split('.').last.toLowerCase();
    final fileType = FileTypeDetector.fromExtension(extension);

    int? fileSize;
    DateTime? modifiedAt;

    try {
      fileSize = file.lengthSync();
      modifiedAt = file.lastModifiedSync();
    } catch (e) {
      // File might not exist or be accessible
    }

    return FileModel(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      fileName: fileName,
      fileSizeBytes: fileSize,
      fileExtension: extension,
      fileType: fileType,
      localPath: file.path,
      isLocal: true,
      downloadState: DownloadState.downloaded,
      downloadProgress: 1.0,
      modifiedAt: modifiedAt,
    );
  }

  /// Create a FileModel from a remote URL
  factory FileModel.fromUrl(String url, {String? customFileName}) {
    final fileName = customFileName ?? url.split('/').last;
    final extension = fileName.split('.').last.toLowerCase();
    final fileType = FileTypeDetector.fromExtension(extension);

    return FileModel(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      fileName: fileName,
      fileExtension: extension,
      fileType: fileType,
      url: url,
      isLocal: false,
      downloadState: DownloadState.notDownloaded,
      downloadProgress: 0.0,
      createdAt: DateTime.now(),
    );
  }

  /// Create a FileModel from LibraryFileModel
  factory FileModel.fromLibraryFile(LibraryFileModel libraryFile) {
    // Convert LibraryFileType to FileType
    FileType fileType;
    switch (libraryFile.fileType) {
      case LibraryFileType.image:
        fileType = FileType.image;
        break;
      case LibraryFileType.pdf:
        fileType = FileType.pdf;
        break;
      case LibraryFileType.video:
        fileType = FileType.video;
        break;
      case LibraryFileType.document:
      case LibraryFileType.presentation:
      case LibraryFileType.spreadsheet:
      case LibraryFileType.note:
        fileType = FileType.document;
        break;
      case LibraryFileType.audio:
        fileType = FileType.audio;
        break;
      case LibraryFileType.archive:
        fileType = FileType.archive;
        break;
      default:
        fileType = FileType.other;
    }

    // Convert OfflineAccess to DownloadState
    DownloadState downloadState;
    switch (libraryFile.offlineAccess) {
      case OfflineAccess.available:
        downloadState = DownloadState.downloaded;
        break;
      case OfflineAccess.downloading:
        downloadState = DownloadState.downloading;
        break;
      case OfflineAccess.failed:
        downloadState = DownloadState.failed;
        break;
      default:
        downloadState = DownloadState.notDownloaded;
    }

    return FileModel(
      id: libraryFile.id,
      fileName: libraryFile.fileName,
      fileSizeBytes: libraryFile.fileSizeBytes,
      fileExtension: libraryFile.fileExtension,
      fileType: fileType,
      url: libraryFile.cloudUrl,
      localPath: libraryFile.localPath,
      isLocal: libraryFile.localPath != null,
      downloadState: downloadState,
      downloadProgress: libraryFile.uploadProgress,
      downloadError: libraryFile.errorMessage,
      createdAt: libraryFile.createdAt,
      modifiedAt: libraryFile.modifiedAt,
      mimeType: libraryFile.mimeType,
    );
  }

  /// Convert to LibraryFileModel
  LibraryFileModel toLibraryFile({
    String? uploaderId,
    String? uploaderName,
    String? title,
    String? description,
  }) {
    return LibraryFileModel(
      id: id,
      fileName: fileName,
      title: title,
      description: description,
      fileType: fileType.toLibraryFileType(),
      fileSizeBytes: fileSizeBytes,
      fileExtension: fileExtension,
      mimeType: mimeType,
      accessType: FileAccessType.private,
      usageType: FileUsageType.library,
      offlineAccess: _downloadStateToOfflineAccess(downloadState),
      uploadStatus: isLocal ? UploadStatus.completed : UploadStatus.pending,
      uploadProgress: downloadProgress,
      uploaderId: uploaderId ?? 'unknown',
      uploaderName: uploaderName ?? 'Unknown User',
      cloudUrl: url,
      localPath: localPath,
      createdAt: createdAt ?? DateTime.now(),
      modifiedAt: modifiedAt,
      errorMessage: downloadError,
    );
  }

  /// Convert DownloadState to OfflineAccess
  OfflineAccess _downloadStateToOfflineAccess(DownloadState state) {
    switch (state) {
      case DownloadState.downloaded:
        return OfflineAccess.available;
      case DownloadState.downloading:
        return OfflineAccess.downloading;
      case DownloadState.failed:
        return OfflineAccess.failed;
      default:
        return OfflineAccess.none;
    }
  }

  /// Whether the file is available for viewing (either local or downloaded)
  bool get isAvailableForViewing {
    return isLocal || downloadState == DownloadState.downloaded;
  }

  /// Get the file size as a human-readable string
  String get fileSizeString {
    if (fileSizeBytes == null) return 'Unknown size';
    return _formatFileSize(fileSizeBytes!);
  }

  /// Get the appropriate viewer type for this file
  ViewerType get viewerType {
    if (fileType.canViewNatively) {
      switch (fileType) {
        case FileType.image:
          return ViewerType.image;
        case FileType.pdf:
          return ViewerType.pdf;
        default:
          return ViewerType.placeholder;
      }
    }
    return ViewerType.placeholder;
  }

  /// Format file size in bytes to human-readable string
  String _formatFileSize(int bytes) {
    if (bytes < 1024) {
      return '$bytes B';
    } else if (bytes < 1024 * 1024) {
      return '${(bytes / 1024).toStringAsFixed(1)} KB';
    } else if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    }
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }

  /// Create a copy of this FileModel with updated properties
  FileModel copyWith({
    String? id,
    String? fileName,
    int? fileSizeBytes,
    String? fileExtension,
    FileType? fileType,
    String? url,
    String? localPath,
    bool? isLocal,
    DownloadState? downloadState,
    double? downloadProgress,
    String? downloadError,
    DateTime? createdAt,
    DateTime? modifiedAt,
    String? mimeType,
  }) {
    return FileModel(
      id: id ?? this.id,
      fileName: fileName ?? this.fileName,
      fileSizeBytes: fileSizeBytes ?? this.fileSizeBytes,
      fileExtension: fileExtension ?? this.fileExtension,
      fileType: fileType ?? this.fileType,
      url: url ?? this.url,
      localPath: localPath ?? this.localPath,
      isLocal: isLocal ?? this.isLocal,
      downloadState: downloadState ?? this.downloadState,
      downloadProgress: downloadProgress ?? this.downloadProgress,
      downloadError: downloadError ?? this.downloadError,
      createdAt: createdAt ?? this.createdAt,
      modifiedAt: modifiedAt ?? this.modifiedAt,
      mimeType: mimeType ?? this.mimeType,
    );
  }

  @override
  String toString() {
    return 'FileModel(id: $id, fileName: $fileName, fileType: $fileType, isLocal: $isLocal, downloadState: $downloadState)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is FileModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
