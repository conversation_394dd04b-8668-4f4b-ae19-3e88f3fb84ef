import '../enums/download_state.dart';

/// Model representing download progress for a file
class DownloadProgressModel {
  /// Unique identifier for the file being downloaded
  final String fileId;

  /// Current download state
  final DownloadState state;

  /// Download progress (0.0 to 1.0)
  final double progress;

  /// Number of bytes downloaded
  final int bytesDownloaded;

  /// Total number of bytes to download (null if unknown)
  final int? totalBytes;

  /// Download speed in bytes per second (null if unknown)
  final double? speedBytesPerSecond;

  /// Estimated time remaining in seconds (null if unknown)
  final int? estimatedTimeRemainingSeconds;

  /// Error message if download failed
  final String? errorMessage;

  /// When the download started
  final DateTime? startTime;

  /// When the download completed or failed
  final DateTime? endTime;

  const DownloadProgressModel({
    required this.fileId,
    required this.state,
    this.progress = 0.0,
    this.bytesDownloaded = 0,
    this.totalBytes,
    this.speedBytesPerSecond,
    this.estimatedTimeRemainingSeconds,
    this.errorMessage,
    this.startTime,
    this.endTime,
  });

  /// Create a progress model for a download that just started
  factory DownloadProgressModel.started(String fileId) {
    return DownloadProgressModel(
      fileId: fileId,
      state: DownloadState.downloading,
      progress: 0.0,
      bytesDownloaded: 0,
      startTime: DateTime.now(),
    );
  }

  /// Create a progress model for a download in progress
  factory DownloadProgressModel.inProgress({
    required String fileId,
    required double progress,
    required int bytesDownloaded,
    int? totalBytes,
    double? speedBytesPerSecond,
    int? estimatedTimeRemainingSeconds,
  }) {
    return DownloadProgressModel(
      fileId: fileId,
      state: DownloadState.downloading,
      progress: progress,
      bytesDownloaded: bytesDownloaded,
      totalBytes: totalBytes,
      speedBytesPerSecond: speedBytesPerSecond,
      estimatedTimeRemainingSeconds: estimatedTimeRemainingSeconds,
    );
  }

  /// Create a progress model for a completed download
  factory DownloadProgressModel.completed(String fileId, int totalBytes) {
    return DownloadProgressModel(
      fileId: fileId,
      state: DownloadState.downloaded,
      progress: 1.0,
      bytesDownloaded: totalBytes,
      totalBytes: totalBytes,
      endTime: DateTime.now(),
    );
  }

  /// Create a progress model for a failed download
  factory DownloadProgressModel.failed(String fileId, String errorMessage) {
    return DownloadProgressModel(
      fileId: fileId,
      state: DownloadState.failed,
      errorMessage: errorMessage,
      endTime: DateTime.now(),
    );
  }

  /// Create a progress model for a cancelled download
  factory DownloadProgressModel.cancelled(String fileId) {
    return DownloadProgressModel(
      fileId: fileId,
      state: DownloadState.cancelled,
      endTime: DateTime.now(),
    );
  }

  /// Create a progress model for a paused download
  factory DownloadProgressModel.paused({
    required String fileId,
    required double progress,
    required int bytesDownloaded,
    int? totalBytes,
  }) {
    return DownloadProgressModel(
      fileId: fileId,
      state: DownloadState.paused,
      progress: progress,
      bytesDownloaded: bytesDownloaded,
      totalBytes: totalBytes,
    );
  }

  /// Whether the download is currently active
  bool get isActive {
    return state == DownloadState.downloading;
  }

  /// Whether the download is in progress
  bool get isInProgress {
    return state == DownloadState.downloading || state == DownloadState.paused;
  }

  /// Whether the download is complete
  bool get isComplete {
    return state == DownloadState.downloaded;
  }

  /// Whether the download has failed
  bool get hasFailed {
    return state == DownloadState.failed || state == DownloadState.cancelled;
  }

  /// Whether the download can be retried
  bool get canRetry {
    return state.canRetry;
  }

  /// Whether the download can be paused
  bool get canPause {
    return state.canPause;
  }

  /// Whether the download can be resumed
  bool get canResume {
    return state.canResume;
  }

  /// Whether the download can be cancelled
  bool get canCancel {
    return state.canCancel;
  }

  /// Get formatted download speed
  String get formattedSpeed {
    if (speedBytesPerSecond == null) return 'Unknown';

    final speed = speedBytesPerSecond!;
    if (speed < 1024) {
      return '${speed.toStringAsFixed(0)} B/s';
    } else if (speed < 1024 * 1024) {
      return '${(speed / 1024).toStringAsFixed(1)} KB/s';
    } else {
      return '${(speed / (1024 * 1024)).toStringAsFixed(1)} MB/s';
    }
  }

  /// Get formatted estimated time remaining
  String get formattedTimeRemaining {
    if (estimatedTimeRemainingSeconds == null) return 'Unknown';

    final seconds = estimatedTimeRemainingSeconds!;
    if (seconds < 60) {
      return '${seconds}s';
    } else if (seconds < 3600) {
      final minutes = (seconds / 60).floor();
      return '${minutes}m ${seconds % 60}s';
    } else {
      final hours = (seconds / 3600).floor();
      final minutes = ((seconds % 3600) / 60).floor();
      return '${hours}h ${minutes}m';
    }
  }

  /// Get formatted file size
  String get formattedSize {
    if (totalBytes == null) return 'Unknown size';

    final bytes = totalBytes!;
    if (bytes < 1024) {
      return '$bytes B';
    } else if (bytes < 1024 * 1024) {
      return '${(bytes / 1024).toStringAsFixed(1)} KB';
    } else if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    } else {
      return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
    }
  }

  /// Create a copy of this model with updated properties
  DownloadProgressModel copyWith({
    String? fileId,
    DownloadState? state,
    double? progress,
    int? bytesDownloaded,
    int? totalBytes,
    double? speedBytesPerSecond,
    int? estimatedTimeRemainingSeconds,
    String? errorMessage,
    DateTime? startTime,
    DateTime? endTime,
  }) {
    return DownloadProgressModel(
      fileId: fileId ?? this.fileId,
      state: state ?? this.state,
      progress: progress ?? this.progress,
      bytesDownloaded: bytesDownloaded ?? this.bytesDownloaded,
      totalBytes: totalBytes ?? this.totalBytes,
      speedBytesPerSecond: speedBytesPerSecond ?? this.speedBytesPerSecond,
      estimatedTimeRemainingSeconds:
          estimatedTimeRemainingSeconds ?? this.estimatedTimeRemainingSeconds,
      errorMessage: errorMessage ?? this.errorMessage,
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
    );
  }

  @override
  String toString() {
    return 'DownloadProgressModel(fileId: $fileId, state: $state, progress: $progress)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is DownloadProgressModel && other.fileId == fileId;
  }

  @override
  int get hashCode => fileId.hashCode;
}
