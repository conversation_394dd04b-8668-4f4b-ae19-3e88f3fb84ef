import '../enums/file_access_type.dart';
import '../enums/file_usage_type.dart';
import '../enums/folder_type.dart';

/// Model representing a folder in the digital library
/// Supports nested folder structure and access control
class LibraryFolderModel {
  /// Unique identifier for the folder
  final String id;

  /// Name of the folder
  final String name;

  /// Optional description of the folder content
  final String? description;

  /// Type of folder (general, subject, project, etc.)
  final FolderType folderType;

  /// Access type controlling who can view the folder and its contents
  final FileAccessType accessType;

  /// Usage type categorizing the folder's purpose across app features
  final FileUsageType usageType;

  /// ID of the parent folder (null for root level folders)
  final String? parentFolderId;

  /// ID of the user who created the folder
  final String creatorId;

  /// Name of the user who created the folder
  final String creatorName;

  /// ID of the class/classroom this folder belongs to (optional)
  final String? classId;

  /// Subject/course this folder is related to (optional)
  final String? subject;

  /// List of user IDs who have shared access (used for shared folders and restricted access)
  final List<String> sharedUserIds;

  /// Custom icon identifier for the folder
  final String? iconId;

  /// Custom color for the folder (hex color code)
  final String? colorHex;

  /// Tags for categorization and search
  final List<String> tags;

  /// When the folder was created
  final DateTime createdAt;

  /// When the folder was last modified
  final DateTime? modifiedAt;

  /// When the folder was last accessed
  final DateTime? lastAccessedAt;

  /// Number of files directly in this folder
  final int fileCount;

  /// Number of subfolders directly in this folder
  final int subfolderCount;

  /// Total size of all files in this folder and subfolders (in bytes)
  final int? totalSizeBytes;

  /// Whether the folder is marked as favorite by the current user
  final bool isFavorite;

  /// Whether the folder is pinned to the top of the list
  final bool isPinned;

  /// Whether the folder is archived (hidden from normal view)
  final bool isArchived;

  /// Additional metadata as key-value pairs
  final Map<String, dynamic> metadata;

  const LibraryFolderModel({
    required this.id,
    required this.name,
    this.description,
    required this.folderType,
    required this.accessType,
    this.usageType = FileUsageType.library,
    this.parentFolderId,
    required this.creatorId,
    required this.creatorName,
    this.classId,
    this.subject,
    this.sharedUserIds = const [],
    this.iconId,
    this.colorHex,
    this.tags = const [],
    required this.createdAt,
    this.modifiedAt,
    this.lastAccessedAt,
    this.fileCount = 0,
    this.subfolderCount = 0,
    this.totalSizeBytes,
    this.isFavorite = false,
    this.isPinned = false,
    this.isArchived = false,
    this.metadata = const {},
  });

  /// Create a LibraryFolderModel from JSON
  factory LibraryFolderModel.fromJson(Map<String, dynamic> json) {
    return LibraryFolderModel(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String?,
      folderType: FolderTypeExtension.fromString(json['folderType'] as String),
      accessType: FileAccessTypeExtension.fromString(
        json['accessType'] as String,
      ),
      usageType: FileUsageTypeExtension.fromString(
        json['usageType'] as String? ?? 'library',
      ),
      parentFolderId: json['parentFolderId'] as String?,
      creatorId: json['creatorId'] as String,
      creatorName: json['creatorName'] as String,
      classId: json['classId'] as String?,
      subject: json['subject'] as String?,
      sharedUserIds: List<String>.from(
        json['sharedUserIds'] as List? ??
            json['restrictedUserIds'] as List? ??
            [],
      ),
      iconId: json['iconId'] as String?,
      colorHex: json['colorHex'] as String?,
      tags: List<String>.from(json['tags'] as List? ?? []),
      createdAt: DateTime.parse(json['createdAt'] as String),
      modifiedAt: json['modifiedAt'] != null
          ? DateTime.parse(json['modifiedAt'] as String)
          : null,
      lastAccessedAt: json['lastAccessedAt'] != null
          ? DateTime.parse(json['lastAccessedAt'] as String)
          : null,
      fileCount: json['fileCount'] as int? ?? 0,
      subfolderCount: json['subfolderCount'] as int? ?? 0,
      totalSizeBytes: json['totalSizeBytes'] as int?,
      isFavorite: json['isFavorite'] as bool? ?? false,
      isPinned: json['isPinned'] as bool? ?? false,
      isArchived: json['isArchived'] as bool? ?? false,
      metadata: Map<String, dynamic>.from(json['metadata'] as Map? ?? {}),
    );
  }

  /// Convert LibraryFolderModel to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'folderType': folderType.value,
      'accessType': accessType.value,
      'usageType': usageType.value,
      'parentFolderId': parentFolderId,
      'creatorId': creatorId,
      'creatorName': creatorName,
      'classId': classId,
      'subject': subject,
      'sharedUserIds': sharedUserIds,
      'iconId': iconId,
      'colorHex': colorHex,
      'tags': tags,
      'createdAt': createdAt.toIso8601String(),
      'modifiedAt': modifiedAt?.toIso8601String(),
      'lastAccessedAt': lastAccessedAt?.toIso8601String(),
      'fileCount': fileCount,
      'subfolderCount': subfolderCount,
      'totalSizeBytes': totalSizeBytes,
      'isFavorite': isFavorite,
      'isPinned': isPinned,
      'isArchived': isArchived,
      'metadata': metadata,
    };
  }

  /// Get the total size as a human-readable string
  String get totalSizeString {
    if (totalSizeBytes == null) return 'Unknown size';
    return _formatFileSize(totalSizeBytes!);
  }

  /// Get the total item count (files + subfolders)
  int get totalItemCount => fileCount + subfolderCount;

  /// Whether the folder is empty
  bool get isEmpty => totalItemCount == 0;

  /// Whether the folder is a root level folder
  bool get isRootLevel => parentFolderId == null;

  /// Whether the folder has any content
  bool get hasContent => fileCount > 0 || subfolderCount > 0;

  /// Get the folder path as a list of folder names (for breadcrumb navigation)
  /// Note: This would need to be calculated by traversing parent folders
  String get displayPath {
    // This is a simplified version - in practice, you'd need to traverse
    // the parent folder hierarchy to build the full path
    return isRootLevel ? name : '.../$name';
  }

  /// Create a copy of this LibraryFolderModel with updated properties
  LibraryFolderModel copyWith({
    String? id,
    String? name,
    String? description,
    FolderType? folderType,
    FileAccessType? accessType,
    FileUsageType? usageType,
    String? parentFolderId,
    String? creatorId,
    String? creatorName,
    String? classId,
    String? subject,
    List<String>? sharedUserIds,
    String? iconId,
    String? colorHex,
    List<String>? tags,
    DateTime? createdAt,
    DateTime? modifiedAt,
    DateTime? lastAccessedAt,
    int? fileCount,
    int? subfolderCount,
    int? totalSizeBytes,
    bool? isFavorite,
    bool? isPinned,
    bool? isArchived,
    Map<String, dynamic>? metadata,
  }) {
    return LibraryFolderModel(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      folderType: folderType ?? this.folderType,
      accessType: accessType ?? this.accessType,
      usageType: usageType ?? this.usageType,
      parentFolderId: parentFolderId ?? this.parentFolderId,
      creatorId: creatorId ?? this.creatorId,
      creatorName: creatorName ?? this.creatorName,
      classId: classId ?? this.classId,
      subject: subject ?? this.subject,
      sharedUserIds: sharedUserIds ?? this.sharedUserIds,
      iconId: iconId ?? this.iconId,
      colorHex: colorHex ?? this.colorHex,
      tags: tags ?? this.tags,
      createdAt: createdAt ?? this.createdAt,
      modifiedAt: modifiedAt ?? this.modifiedAt,
      lastAccessedAt: lastAccessedAt ?? this.lastAccessedAt,
      fileCount: fileCount ?? this.fileCount,
      subfolderCount: subfolderCount ?? this.subfolderCount,
      totalSizeBytes: totalSizeBytes ?? this.totalSizeBytes,
      isFavorite: isFavorite ?? this.isFavorite,
      isPinned: isPinned ?? this.isPinned,
      isArchived: isArchived ?? this.isArchived,
      metadata: metadata ?? this.metadata,
    );
  }

  /// Format file size in bytes to human-readable string
  static String _formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    }
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }

  @override
  String toString() {
    return 'LibraryFolderModel(id: $id, name: $name, folderType: $folderType, '
        'accessType: $accessType, fileCount: $fileCount, subfolderCount: $subfolderCount)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is LibraryFolderModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
