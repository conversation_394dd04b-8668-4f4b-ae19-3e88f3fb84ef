import '../enums/file_access_type.dart';
import '../enums/file_usage_type.dart';
import '../enums/library_file_type.dart';
import '../enums/offline_access.dart';
import '../enums/upload_status.dart';

/// Model representing a file in the digital library
/// Supports comprehensive metadata, privacy controls, and offline access
class LibraryFileModel {
  /// Unique identifier for the file
  final String id;

  /// Original filename with extension
  final String fileName;

  /// Optional custom title for the file (defaults to fileName if not provided)
  final String? title;

  /// Optional description of the file content
  final String? description;

  /// File type based on extension and content
  final LibraryFileType fileType;

  /// File size in bytes
  final int? fileSizeBytes;

  /// File extension (without dot)
  final String fileExtension;

  /// MIME type of the file
  final String? mimeType;

  /// Access type controlling who can view/download the file
  final FileAccessType accessType;

  /// Usage type categorizing the file's purpose across app features
  final FileUsageType usageType;

  /// Current offline access state
  final OfflineAccess offlineAccess;

  /// Current upload status
  final UploadStatus uploadStatus;

  /// Upload progress (0.0 to 1.0)
  final double uploadProgress;

  /// Tags for categorization and search
  final List<String> tags;

  /// ID of the folder containing this file (null for root level)
  final String? folderId;

  /// ID of the user who uploaded the file
  final String uploaderId;

  /// Name of the user who uploaded the file
  final String uploaderName;

  /// ID of the class/classroom this file belongs to (optional)
  final String? classId;

  /// Subject/course this file is related to (optional)
  final String? subject;

  /// List of user IDs who have shared access (used for shared files and restricted access)
  final List<String> sharedUserIds;

  /// URL for cloud-stored files
  final String? cloudUrl;

  /// Local file path for offline access
  final String? localPath;

  /// Thumbnail URL for preview (images, videos, documents)
  final String? thumbnailUrl;

  /// When the file was uploaded/created
  final DateTime createdAt;

  /// When the file was last modified
  final DateTime? modifiedAt;

  /// When the file was last accessed
  final DateTime? lastAccessedAt;

  /// Number of times the file has been viewed
  final int viewCount;

  /// Number of times the file has been downloaded
  final int downloadCount;

  /// Whether the file is marked as favorite by the current user
  final bool isFavorite;

  /// Error message if upload/download failed
  final String? errorMessage;

  /// Additional metadata as key-value pairs
  final Map<String, dynamic> metadata;

  const LibraryFileModel({
    required this.id,
    required this.fileName,
    this.title,
    this.description,
    required this.fileType,
    this.fileSizeBytes,
    required this.fileExtension,
    this.mimeType,
    required this.accessType,
    this.usageType = FileUsageType.library,
    this.offlineAccess = OfflineAccess.none,
    this.uploadStatus = UploadStatus.pending,
    this.uploadProgress = 0.0,
    this.tags = const [],
    this.folderId,
    required this.uploaderId,
    required this.uploaderName,
    this.classId,
    this.subject,
    this.sharedUserIds = const [],
    this.cloudUrl,
    this.localPath,
    this.thumbnailUrl,
    required this.createdAt,
    this.modifiedAt,
    this.lastAccessedAt,
    this.viewCount = 0,
    this.downloadCount = 0,
    this.isFavorite = false,
    this.errorMessage,
    this.metadata = const {},
  });

  /// Create a LibraryFileModel from JSON
  factory LibraryFileModel.fromJson(Map<String, dynamic> json) {
    return LibraryFileModel(
      id: json['id'] as String,
      fileName: json['fileName'] as String,
      title: json['title'] as String?,
      description: json['description'] as String?,
      fileType: LibraryFileTypeExtension.fromString(json['fileType'] as String),
      fileSizeBytes: json['fileSizeBytes'] as int?,
      fileExtension: json['fileExtension'] as String,
      mimeType: json['mimeType'] as String?,
      accessType: FileAccessTypeExtension.fromString(
        json['accessType'] as String,
      ),
      usageType: FileUsageTypeExtension.fromString(
        json['usageType'] as String? ?? 'library',
      ),
      offlineAccess: OfflineAccessExtension.fromString(
        json['offlineAccess'] as String? ?? 'none',
      ),
      uploadStatus: UploadStatusExtension.fromString(
        json['uploadStatus'] as String? ?? 'pending',
      ),
      uploadProgress: (json['uploadProgress'] as num?)?.toDouble() ?? 0.0,
      tags: List<String>.from(json['tags'] as List? ?? []),
      folderId: json['folderId'] as String?,
      uploaderId: json['uploaderId'] as String,
      uploaderName: json['uploaderName'] as String,
      classId: json['classId'] as String?,
      subject: json['subject'] as String?,
      sharedUserIds: List<String>.from(
        json['sharedUserIds'] as List? ??
            json['restrictedUserIds'] as List? ??
            [],
      ),
      cloudUrl: json['cloudUrl'] as String?,
      localPath: json['localPath'] as String?,
      thumbnailUrl: json['thumbnailUrl'] as String?,
      createdAt: DateTime.parse(json['createdAt'] as String),
      modifiedAt: json['modifiedAt'] != null
          ? DateTime.parse(json['modifiedAt'] as String)
          : null,
      lastAccessedAt: json['lastAccessedAt'] != null
          ? DateTime.parse(json['lastAccessedAt'] as String)
          : null,
      viewCount: json['viewCount'] as int? ?? 0,
      downloadCount: json['downloadCount'] as int? ?? 0,
      isFavorite: json['isFavorite'] as bool? ?? false,
      errorMessage: json['errorMessage'] as String?,
      metadata: Map<String, dynamic>.from(json['metadata'] as Map? ?? {}),
    );
  }

  /// Convert LibraryFileModel to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'fileName': fileName,
      'title': title,
      'description': description,
      'fileType': fileType.value,
      'fileSizeBytes': fileSizeBytes,
      'fileExtension': fileExtension,
      'mimeType': mimeType,
      'accessType': accessType.value,
      'usageType': usageType.value,
      'offlineAccess': offlineAccess.value,
      'uploadStatus': uploadStatus.value,
      'uploadProgress': uploadProgress,
      'tags': tags,
      'folderId': folderId,
      'uploaderId': uploaderId,
      'uploaderName': uploaderName,
      'classId': classId,
      'subject': subject,
      'sharedUserIds': sharedUserIds,
      'cloudUrl': cloudUrl,
      'localPath': localPath,
      'thumbnailUrl': thumbnailUrl,
      'createdAt': createdAt.toIso8601String(),
      'modifiedAt': modifiedAt?.toIso8601String(),
      'lastAccessedAt': lastAccessedAt?.toIso8601String(),
      'viewCount': viewCount,
      'downloadCount': downloadCount,
      'isFavorite': isFavorite,
      'errorMessage': errorMessage,
      'metadata': metadata,
    };
  }

  /// Get the display title (title if provided, otherwise fileName)
  String get displayTitle => title ?? fileName;

  /// Get the file size as a human-readable string
  String get fileSizeString {
    if (fileSizeBytes == null) return 'Unknown size';
    return _formatFileSize(fileSizeBytes!);
  }

  /// Whether the file is available for viewing
  bool get isAvailableForViewing {
    return uploadStatus == UploadStatus.completed &&
        (offlineAccess.canAccessOffline || cloudUrl != null);
  }

  /// Whether the file can be downloaded by the current user
  bool get canDownload {
    return uploadStatus == UploadStatus.completed && accessType.isDownloadable;
  }

  /// Whether the file is stored locally
  bool get isStoredLocally {
    return localPath != null && offlineAccess.canAccessOffline;
  }

  /// Whether the file upload is in progress
  bool get isUploading {
    return uploadStatus == UploadStatus.uploading ||
        uploadStatus == UploadStatus.processing;
  }

  /// Whether the file has an error
  bool get hasError {
    return uploadStatus == UploadStatus.failed || offlineAccess.hasError;
  }

  /// Create a copy of this LibraryFileModel with updated properties
  LibraryFileModel copyWith({
    String? id,
    String? fileName,
    String? title,
    String? description,
    LibraryFileType? fileType,
    int? fileSizeBytes,
    String? fileExtension,
    String? mimeType,
    FileAccessType? accessType,
    FileUsageType? usageType,
    OfflineAccess? offlineAccess,
    UploadStatus? uploadStatus,
    double? uploadProgress,
    List<String>? tags,
    String? folderId,
    String? uploaderId,
    String? uploaderName,
    String? classId,
    String? subject,
    List<String>? sharedUserIds,
    String? cloudUrl,
    String? localPath,
    String? thumbnailUrl,
    DateTime? createdAt,
    DateTime? modifiedAt,
    DateTime? lastAccessedAt,
    int? viewCount,
    int? downloadCount,
    bool? isFavorite,
    String? errorMessage,
    Map<String, dynamic>? metadata,
  }) {
    return LibraryFileModel(
      id: id ?? this.id,
      fileName: fileName ?? this.fileName,
      title: title ?? this.title,
      description: description ?? this.description,
      fileType: fileType ?? this.fileType,
      fileSizeBytes: fileSizeBytes ?? this.fileSizeBytes,
      fileExtension: fileExtension ?? this.fileExtension,
      mimeType: mimeType ?? this.mimeType,
      accessType: accessType ?? this.accessType,
      usageType: usageType ?? this.usageType,
      offlineAccess: offlineAccess ?? this.offlineAccess,
      uploadStatus: uploadStatus ?? this.uploadStatus,
      uploadProgress: uploadProgress ?? this.uploadProgress,
      tags: tags ?? this.tags,
      folderId: folderId ?? this.folderId,
      uploaderId: uploaderId ?? this.uploaderId,
      uploaderName: uploaderName ?? this.uploaderName,
      classId: classId ?? this.classId,
      subject: subject ?? this.subject,
      sharedUserIds: sharedUserIds ?? this.sharedUserIds,
      cloudUrl: cloudUrl ?? this.cloudUrl,
      localPath: localPath ?? this.localPath,
      thumbnailUrl: thumbnailUrl ?? this.thumbnailUrl,
      createdAt: createdAt ?? this.createdAt,
      modifiedAt: modifiedAt ?? this.modifiedAt,
      lastAccessedAt: lastAccessedAt ?? this.lastAccessedAt,
      viewCount: viewCount ?? this.viewCount,
      downloadCount: downloadCount ?? this.downloadCount,
      isFavorite: isFavorite ?? this.isFavorite,
      errorMessage: errorMessage ?? this.errorMessage,
      metadata: metadata ?? this.metadata,
    );
  }

  /// Format file size in bytes to human-readable string
  static String _formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    }
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }

  @override
  String toString() {
    return 'LibraryFileModel(id: $id, fileName: $fileName, fileType: $fileType, '
        'accessType: $accessType, uploadStatus: $uploadStatus)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is LibraryFileModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
