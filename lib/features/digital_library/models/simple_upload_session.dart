import '../enums/upload_status.dart';

/// Simple model for tracking individual file uploads in the upload screen
class SimpleUploadSession {
  final String id;
  final String fileName;
  final String filePath;
  final UploadStatus uploadStatus;
  final double uploadProgress;
  final String? errorMessage;
  final DateTime createdAt;

  const SimpleUploadSession({
    required this.id,
    required this.fileName,
    required this.filePath,
    required this.uploadStatus,
    required this.uploadProgress,
    this.errorMessage,
    required this.createdAt,
  });

  SimpleUploadSession copyWith({
    String? id,
    String? fileName,
    String? filePath,
    UploadStatus? uploadStatus,
    double? uploadProgress,
    String? errorMessage,
    DateTime? createdAt,
  }) {
    return SimpleUploadSession(
      id: id ?? this.id,
      fileName: fileName ?? this.fileName,
      filePath: filePath ?? this.filePath,
      uploadStatus: uploadStatus ?? this.uploadStatus,
      uploadProgress: uploadProgress ?? this.uploadProgress,
      errorMessage: errorMessage ?? this.errorMessage,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is SimpleUploadSession && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'SimpleUploadSession(id: $id, fileName: $fileName, uploadStatus: $uploadStatus, uploadProgress: $uploadProgress)';
  }
}
