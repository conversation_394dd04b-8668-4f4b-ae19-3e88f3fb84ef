# Digital Library - Scholara

The **Digital Library** module is the core file management system for Scholara, enabling students, teachers, and admins to upload, organize, and access learning resources such as documents, videos, images, and notes. It provides a unified platform for all file-related activities across the application with support for folder-based organization, sharing controls, and offline access.

---

## Use Cases

The Digital Library supports the following primary use cases:

### 1. Personal Files

- **Purpose**: Users upload files for personal use and reference
- **Access**: Only accessible to the uploader
- **Examples**: Personal notes, study materials, draft assignments
- **Storage**: Can be cloud-stored or local-only based on user preference

### 2. Shared Files

- **Purpose**: Users upload files to share with specific other users
- **Access**: Accessible to uploader and selected users (sharedUserIds)
- **Examples**: Group project files, peer study materials, collaborative documents
- **Storage**: Cloud-stored for accessibility across devices

### 3. Class Resources

- **Purpose**: Teachers/admins upload files for specific classes
- **Access**: Accessible to all members of the specified class
- **Examples**: Lecture slides, syllabus, reading materials, assignment templates
- **Storage**: Cloud-stored with class-level permissions
- **Requirements**: classId must be specified

### 4. Future Extensions

- Organization-wide public files
- Department-specific resources
- Time-limited access files
- Version-controlled documents

---

## Core Features

- **File Management**: Upload, organize, view, and download files with comprehensive metadata
- **Folder Structure**: Create nested folders and subfolders for organization
- **Access Control**: Granular permissions based on use case (personal, shared, class)
- **Search & Filter**: Find files by title, type, tags, filename, and access level
- **Offline Access**: Mark files for offline availability
- **Upload Tracking**: Real-time progress monitoring with retry capability
- **File Types**: Support for PDFs, videos, images, documents, and links

---

## Core Screens

### 1. Main Library Screen

- Displays:
  - Featured folders grid (2x2/2x3 layout)
  - Recent items section
  - Quick access to personal, shared, and class files
- Widgets:
  - `search_bar`
  - `FeaturedFoldersSection`
  - `RecentItemsSection`

---

### 2. Folder Detail Screen

- Folder info: Name, access type, last updated
- Subfolders and files within the folder
- View/download options based on access permissions
- Widgets:
  - `FolderHeaderBar`: Folder name, access type, last updated
  - `search_bar`
  - `SubfolderListTile`
  - `FileListTile`

---

### 3. Upload File Screen

- Features:
  - Select folder destination
  - Choose access type (Personal/Shared/Class)
  - Select shared users (for shared files)
  - Select class (for class resources)
  - Enter title, description, and tags
  - Real-time upload tracking with status and retry
- Widgets:
  - `FolderDropdownSelector`
  - `AccessTypeSelector`
  - `SharedUsersSelector`
  - `ClassSelector`
  - `UploadFileCard`

---

### 4. File Detail Screen

- File info: Title, uploaded by, date, size, tags, access type
- View/download options based on permissions
- Widgets:
  - `FileHeaderBar`
  - `ViewDownloadButtons`

---

### 5. Create Folder Screen

- Features:
  - Select parent folder
  - Enter folder name and description
  - Choose folder type (Personal, Shared, Class)
  - Set access permissions
- Widgets:
  - `FolderDropdownSelector`
  - `AccessTypeSelector`
  - `FolderTypeDropdown`
  - `CreateFolderCard`

---

### 6. Search Screen

- Features:
  - Search bar with filters
  - Filter tabs: All, Personal, Shared, Class
  - Sort options: Name, Date Created, Date Modified, Size, Type
  - Search results: Folders and files
- Widgets:
  - `LibrarySearchBar`
  - `FilterTabs`
  - `SortDropdown`
  - `SearchResultsList`

---

## Technical Requirements

- **Required Metadata**: filename and type (title, description, tags are optional)
- **Access Control**: Traditional access types (public, private, restricted, local-only) control who can view/download
- **Usage Types**: New categorization system for file purposes across app features:
  - `personal`: Personal files for individual use
  - `shared`: Files shared with specific users (uses sharedUserIds)
  - `classResource`: Educational resources for class members
  - `homeworkSubmission`: Files submitted as homework assignments
  - `noticeAttachment`: Files attached to notices/announcements
  - `classroomActivity`: Files related to classroom activities
  - `profile`: Files used in user profiles
  - `library`: General library files (default)
- **Storage Options**: Cloud storage (default) or local-only for personal files
- **File Organization**: Hierarchical folder structure with nested support
- **Offline Support**: Mark files for offline access with local caching
- **Upload Management**: Progress tracking, retry mechanism, error handling
- **Shared Users**: Files can be shared with specific users via sharedUserIds (replaces restrictedUserIds)

---

## Additional Considerations

- **Migration**: Existing 'files' feature will be migrated into digital library
- **Integration**: Classroom resources are essentially digital library files with class-level access
- **Scalability**: Architecture supports future extensions like organization-wide files
- **Performance**: Efficient file indexing and search capabilities
- **Security**: Proper access control and permission validation
- **Follow all instructions given in the `lib/README.md` file**

---
