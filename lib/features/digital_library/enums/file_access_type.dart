import 'package:flutter/material.dart';
import 'package:material_symbols_icons/symbols.dart';

/// Enum representing different access types for files in the digital library
/// Defines who can access and download files
enum FileAccessType {
  /// File is accessible to everyone in the organization and downloadable by everyone
  /// This is the default setting
  publicDownloadable,

  /// File is accessible to everyone in the organization but not downloadable
  /// Users can view but cannot download the file
  publicViewOnly,

  /// File is accessible only to the uploader (private use)
  /// Not accessible to anyone else
  private,

  /// File is accessible to only specific users defined by the uploader
  /// Restricted access to selected individuals
  restricted,

  /// File is accessible to everyone but stored locally only
  /// Not uploaded to cloud, accessible only from the device it was uploaded from
  localOnly,
}

/// Extension to provide additional functionality for FileAccessType
extension FileAccessTypeExtension on FileAccessType {
  /// Returns a human-readable label for the access type
  String get label {
    switch (this) {
      case FileAccessType.publicDownloadable:
        return 'Public & Downloadable';
      case FileAccessType.publicViewOnly:
        return 'Public View Only';
      case FileAccessType.private:
        return 'Private';
      case FileAccessType.restricted:
        return 'Restricted Access';
      case FileAccessType.localOnly:
        return 'Local Only';
    }
  }

  /// Returns a detailed description of the access type
  String get description {
    switch (this) {
      case FileAccessType.publicDownloadable:
        return 'Accessible to everyone in the organization and downloadable by everyone';
      case FileAccessType.publicViewOnly:
        return 'Accessible to everyone in the organization but not downloadable';
      case FileAccessType.private:
        return 'Accessible only to you for personal use';
      case FileAccessType.restricted:
        return 'Accessible only to specific users you select';
      case FileAccessType.localOnly:
        return 'Stored locally on your device only, not uploaded to cloud';
    }
  }

  /// Returns the appropriate icon for the access type
  IconData get icon {
    switch (this) {
      case FileAccessType.publicDownloadable:
        return Symbols.public;
      case FileAccessType.publicViewOnly:
        return Symbols.visibility;
      case FileAccessType.private:
        return Symbols.lock;
      case FileAccessType.restricted:
        return Symbols.group;
      case FileAccessType.localOnly:
        return Symbols.offline_pin;
    }
  }

  /// Returns the color associated with the access type
  Color get color {
    switch (this) {
      case FileAccessType.publicDownloadable:
        return Colors.green;
      case FileAccessType.publicViewOnly:
        return Colors.blue;
      case FileAccessType.private:
        return Colors.orange;
      case FileAccessType.restricted:
        return Colors.purple;
      case FileAccessType.localOnly:
        return Colors.grey;
    }
  }

  /// Whether the file can be downloaded by others
  bool get isDownloadable {
    switch (this) {
      case FileAccessType.publicDownloadable:
      case FileAccessType.restricted: // Restricted users can download
        return true;
      case FileAccessType.publicViewOnly:
      case FileAccessType.private:
      case FileAccessType.localOnly:
        return false;
    }
  }

  /// Whether the file is stored in the cloud
  bool get isCloudStored {
    switch (this) {
      case FileAccessType.publicDownloadable:
      case FileAccessType.publicViewOnly:
      case FileAccessType.private:
      case FileAccessType.restricted:
        return true;
      case FileAccessType.localOnly:
        return false;
    }
  }

  /// Whether the file is publicly accessible
  bool get isPublic {
    switch (this) {
      case FileAccessType.publicDownloadable:
      case FileAccessType.publicViewOnly:
        return true;
      case FileAccessType.private:
      case FileAccessType.restricted:
      case FileAccessType.localOnly:
        return false;
    }
  }

  /// Get the string value for JSON serialization
  String get value {
    switch (this) {
      case FileAccessType.publicDownloadable:
        return 'public_downloadable';
      case FileAccessType.publicViewOnly:
        return 'public_view_only';
      case FileAccessType.private:
        return 'private';
      case FileAccessType.restricted:
        return 'restricted';
      case FileAccessType.localOnly:
        return 'local_only';
    }
  }

  /// Create FileAccessType from string value
  static FileAccessType fromString(String value) {
    switch (value.toLowerCase()) {
      case 'public_downloadable':
        return FileAccessType.publicDownloadable;
      case 'public_view_only':
        return FileAccessType.publicViewOnly;
      case 'private':
        return FileAccessType.private;
      case 'restricted':
        return FileAccessType.restricted;
      case 'local_only':
        return FileAccessType.localOnly;
      default:
        return FileAccessType.publicDownloadable; // Default
    }
  }
}
