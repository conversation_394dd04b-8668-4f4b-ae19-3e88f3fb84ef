/// Enum representing different viewer types for files
enum ViewerType {
  /// Native image viewer for image files
  image,

  /// Native PDF viewer for PDF files
  pdf,

  /// Placeholder viewer for unsupported file types
  placeholder,

  /// External viewer (system default app)
  external,
}

/// Extension to provide additional functionality for ViewerType
extension ViewerTypeExtension on ViewerType {
  /// Returns a human-readable label for the viewer type
  String get label {
    switch (this) {
      case ViewerType.image:
        return 'Image Viewer';
      case ViewerType.pdf:
        return 'PDF Viewer';
      case ViewerType.placeholder:
        return 'File Preview';
      case ViewerType.external:
        return 'External App';
    }
  }

  /// Returns whether this viewer type supports full-screen viewing
  bool get supportsFullScreen {
    switch (this) {
      case ViewerType.image:
      case ViewerType.pdf:
        return true;
      case ViewerType.placeholder:
      case ViewerType.external:
        return false;
    }
  }

  /// Returns whether this viewer type supports navigation between files
  bool get supportsNavigation {
    switch (this) {
      case ViewerType.image:
      case ViewerType.pdf:
        return true;
      case ViewerType.placeholder:
      case ViewerType.external:
        return false;
    }
  }

  /// Returns whether this viewer type supports zoom functionality
  bool get supportsZoom {
    switch (this) {
      case ViewerType.image:
        return true;
      case ViewerType.pdf:
      case ViewerType.placeholder:
      case ViewerType.external:
        return false;
    }
  }

  /// Returns whether this viewer type supports sharing functionality
  bool get supportsSharing {
    switch (this) {
      case ViewerType.image:
      case ViewerType.pdf:
        return true;
      case ViewerType.placeholder:
      case ViewerType.external:
        return false;
    }
  }
}
