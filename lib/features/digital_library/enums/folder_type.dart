import 'package:flutter/material.dart';
import 'package:material_symbols_icons/symbols.dart';

/// Enum representing different types of folders in the digital library
enum FolderType {
  /// General purpose folder for organizing files
  general,

  /// Subject-specific folder (Math, Science, English, etc.)
  subject,

  /// Project-specific folder for collaborative work
  project,

  /// Personal folder for individual use
  personal,

  /// Shared folder accessible to multiple users
  shared,

  /// Archive folder for old/completed materials
  archive,

  /// Resource folder for reference materials
  resource,

  /// Assignment folder for homework and tasks
  assignment,

  /// Notes folder for study materials
  notes,

  /// Media folder for images, videos, audio
  media,

  /// Template folder for reusable documents
  template,

  /// Smart virtual folder based on metadata filtering
  smart,
}

/// Extension to provide additional functionality for FolderType
extension FolderTypeExtension on FolderType {
  /// Returns a human-readable label for the folder type
  String get label {
    switch (this) {
      case FolderType.general:
        return 'General';
      case FolderType.subject:
        return 'Subject';
      case FolderType.project:
        return 'Project';
      case FolderType.personal:
        return 'Personal';
      case FolderType.shared:
        return 'Shared';
      case FolderType.archive:
        return 'Archive';
      case FolderType.resource:
        return 'Resource';
      case FolderType.assignment:
        return 'Assignment';
      case FolderType.notes:
        return 'Notes';
      case FolderType.media:
        return 'Media';
      case FolderType.template:
        return 'Template';
      case FolderType.smart:
        return 'Smart Folder';
    }
  }

  /// Returns a detailed description of the folder type
  String get description {
    switch (this) {
      case FolderType.general:
        return 'General purpose folder for organizing files';
      case FolderType.subject:
        return 'Subject-specific folder for academic materials';
      case FolderType.project:
        return 'Project-specific folder for collaborative work';
      case FolderType.personal:
        return 'Personal folder for individual use';
      case FolderType.shared:
        return 'Shared folder accessible to multiple users';
      case FolderType.archive:
        return 'Archive folder for old or completed materials';
      case FolderType.resource:
        return 'Resource folder for reference materials';
      case FolderType.assignment:
        return 'Assignment folder for homework and tasks';
      case FolderType.notes:
        return 'Notes folder for study materials';
      case FolderType.media:
        return 'Media folder for images, videos, and audio';
      case FolderType.template:
        return 'Template folder for reusable documents';
      case FolderType.smart:
        return 'Smart virtual folder based on metadata filtering';
    }
  }

  /// Returns the appropriate icon for the folder type
  IconData get icon {
    switch (this) {
      case FolderType.general:
        return Symbols.folder;
      case FolderType.subject:
        return Symbols.school;
      case FolderType.project:
        return Symbols.work;
      case FolderType.personal:
        return Symbols.person;
      case FolderType.shared:
        return Symbols.group;
      case FolderType.archive:
        return Symbols.archive;
      case FolderType.resource:
        return Symbols.library_books;
      case FolderType.assignment:
        return Symbols.assignment;
      case FolderType.notes:
        return Symbols.note;
      case FolderType.media:
        return Symbols.perm_media;
      case FolderType.template:
        return Symbols.content_copy;
      case FolderType.smart:
        return Symbols.auto_awesome;
    }
  }

  /// Returns the color associated with the folder type
  Color get color {
    switch (this) {
      case FolderType.general:
        return Colors.blue;
      case FolderType.subject:
        return Colors.green;
      case FolderType.project:
        return Colors.orange;
      case FolderType.personal:
        return Colors.purple;
      case FolderType.shared:
        return Colors.teal;
      case FolderType.archive:
        return Colors.grey;
      case FolderType.smart:
        return Colors.purple;
      case FolderType.resource:
        return Colors.indigo;
      case FolderType.assignment:
        return Colors.red;
      case FolderType.notes:
        return Colors.amber;
      case FolderType.media:
        return Colors.pink;
      case FolderType.template:
        return Colors.cyan;
    }
  }

  /// Whether this folder type typically allows public access
  bool get defaultPublicAccess {
    switch (this) {
      case FolderType.shared:
      case FolderType.resource:
      case FolderType.template:
      case FolderType.smart:
        return true;
      case FolderType.general:
      case FolderType.subject:
      case FolderType.project:
      case FolderType.personal:
      case FolderType.archive:
      case FolderType.assignment:
      case FolderType.notes:
      case FolderType.media:
        return false;
    }
  }

  /// Whether this folder type is typically used for collaboration
  bool get isCollaborative {
    switch (this) {
      case FolderType.project:
      case FolderType.shared:
      case FolderType.assignment:
        return true;
      case FolderType.general:
      case FolderType.subject:
      case FolderType.personal:
      case FolderType.archive:
      case FolderType.resource:
      case FolderType.notes:
      case FolderType.media:
      case FolderType.template:
      case FolderType.smart:
        return false;
    }
  }

  /// Get the string value for JSON serialization
  String get value {
    switch (this) {
      case FolderType.general:
        return 'general';
      case FolderType.subject:
        return 'subject';
      case FolderType.project:
        return 'project';
      case FolderType.personal:
        return 'personal';
      case FolderType.shared:
        return 'shared';
      case FolderType.archive:
        return 'archive';
      case FolderType.resource:
        return 'resource';
      case FolderType.assignment:
        return 'assignment';
      case FolderType.notes:
        return 'notes';
      case FolderType.media:
        return 'media';
      case FolderType.template:
        return 'template';
      case FolderType.smart:
        return 'smart';
    }
  }

  /// Create FolderType from string value
  static FolderType fromString(String value) {
    switch (value.toLowerCase()) {
      case 'general':
        return FolderType.general;
      case 'subject':
        return FolderType.subject;
      case 'project':
        return FolderType.project;
      case 'personal':
        return FolderType.personal;
      case 'shared':
        return FolderType.shared;
      case 'archive':
        return FolderType.archive;
      case 'resource':
        return FolderType.resource;
      case 'assignment':
        return FolderType.assignment;
      case 'notes':
        return FolderType.notes;
      case 'media':
        return FolderType.media;
      case 'template':
        return FolderType.template;
      default:
        return FolderType.general; // Default
    }
  }
}
