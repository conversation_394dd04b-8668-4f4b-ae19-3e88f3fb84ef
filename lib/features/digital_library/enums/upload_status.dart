import 'package:flutter/material.dart';
import 'package:material_symbols_icons/symbols.dart';

/// Enum representing different upload states for files in the digital library
enum UploadStatus {
  /// Upload is pending/queued but not started yet
  pending,
  
  /// File is currently being uploaded
  uploading,
  
  /// Upload completed successfully
  completed,
  
  /// Upload failed due to an error
  failed,
  
  /// Upload was cancelled by the user
  cancelled,
  
  /// Upload is paused (can be resumed)
  paused,
  
  /// File is being processed after upload (e.g., virus scan, metadata extraction)
  processing,
}

/// Extension to provide additional functionality for UploadStatus
extension UploadStatusExtension on UploadStatus {
  /// Returns a human-readable label for the upload status
  String get label {
    switch (this) {
      case UploadStatus.pending:
        return 'Pending';
      case UploadStatus.uploading:
        return 'Uploading';
      case UploadStatus.completed:
        return 'Completed';
      case UploadStatus.failed:
        return 'Failed';
      case UploadStatus.cancelled:
        return 'Cancelled';
      case UploadStatus.paused:
        return 'Paused';
      case UploadStatus.processing:
        return 'Processing';
    }
  }

  /// Returns a detailed description of the upload status
  String get description {
    switch (this) {
      case UploadStatus.pending:
        return 'Upload is queued and waiting to start';
      case UploadStatus.uploading:
        return 'File is currently being uploaded';
      case UploadStatus.completed:
        return 'Upload completed successfully';
      case UploadStatus.failed:
        return 'Upload failed due to an error';
      case UploadStatus.cancelled:
        return 'Upload was cancelled by user';
      case UploadStatus.paused:
        return 'Upload is paused and can be resumed';
      case UploadStatus.processing:
        return 'File is being processed after upload';
    }
  }

  /// Returns the appropriate icon for the upload status
  IconData get icon {
    switch (this) {
      case UploadStatus.pending:
        return Symbols.schedule;
      case UploadStatus.uploading:
        return Symbols.cloud_upload;
      case UploadStatus.completed:
        return Symbols.check_circle;
      case UploadStatus.failed:
        return Symbols.error;
      case UploadStatus.cancelled:
        return Symbols.cancel;
      case UploadStatus.paused:
        return Symbols.pause_circle;
      case UploadStatus.processing:
        return Symbols.hourglass_top;
    }
  }

  /// Returns the color associated with the upload status
  Color get color {
    switch (this) {
      case UploadStatus.pending:
        return Colors.grey;
      case UploadStatus.uploading:
        return Colors.blue;
      case UploadStatus.completed:
        return Colors.green;
      case UploadStatus.failed:
        return Colors.red;
      case UploadStatus.cancelled:
        return Colors.orange;
      case UploadStatus.paused:
        return Colors.amber;
      case UploadStatus.processing:
        return Colors.purple;
    }
  }

  /// Whether the upload can be retried
  bool get canRetry {
    switch (this) {
      case UploadStatus.failed:
      case UploadStatus.cancelled:
        return true;
      case UploadStatus.pending:
      case UploadStatus.uploading:
      case UploadStatus.completed:
      case UploadStatus.paused:
      case UploadStatus.processing:
        return false;
    }
  }

  /// Whether the upload can be cancelled
  bool get canCancel {
    switch (this) {
      case UploadStatus.pending:
      case UploadStatus.uploading:
      case UploadStatus.paused:
        return true;
      case UploadStatus.completed:
      case UploadStatus.failed:
      case UploadStatus.cancelled:
      case UploadStatus.processing:
        return false;
    }
  }

  /// Whether the upload can be paused
  bool get canPause {
    switch (this) {
      case UploadStatus.uploading:
        return true;
      case UploadStatus.pending:
      case UploadStatus.completed:
      case UploadStatus.failed:
      case UploadStatus.cancelled:
      case UploadStatus.paused:
      case UploadStatus.processing:
        return false;
    }
  }

  /// Whether the upload can be resumed
  bool get canResume {
    switch (this) {
      case UploadStatus.paused:
        return true;
      case UploadStatus.pending:
      case UploadStatus.uploading:
      case UploadStatus.completed:
      case UploadStatus.failed:
      case UploadStatus.cancelled:
      case UploadStatus.processing:
        return false;
    }
  }

  /// Whether the upload is in a final state (completed, failed, or cancelled)
  bool get isFinal {
    switch (this) {
      case UploadStatus.completed:
      case UploadStatus.failed:
      case UploadStatus.cancelled:
        return true;
      case UploadStatus.pending:
      case UploadStatus.uploading:
      case UploadStatus.paused:
      case UploadStatus.processing:
        return false;
    }
  }

  /// Get the string value for JSON serialization
  String get value {
    switch (this) {
      case UploadStatus.pending:
        return 'pending';
      case UploadStatus.uploading:
        return 'uploading';
      case UploadStatus.completed:
        return 'completed';
      case UploadStatus.failed:
        return 'failed';
      case UploadStatus.cancelled:
        return 'cancelled';
      case UploadStatus.paused:
        return 'paused';
      case UploadStatus.processing:
        return 'processing';
    }
  }

  /// Create UploadStatus from string value
  static UploadStatus fromString(String value) {
    switch (value.toLowerCase()) {
      case 'pending':
        return UploadStatus.pending;
      case 'uploading':
        return UploadStatus.uploading;
      case 'completed':
        return UploadStatus.completed;
      case 'failed':
        return UploadStatus.failed;
      case 'cancelled':
        return UploadStatus.cancelled;
      case 'paused':
        return UploadStatus.paused;
      case 'processing':
        return UploadStatus.processing;
      default:
        return UploadStatus.pending; // Default
    }
  }
}
