import 'package:flutter/material.dart';
import 'package:material_symbols_icons/symbols.dart';

/// Enum representing different offline access states for files in the digital library
enum OfflineAccess {
  /// File is not available offline
  none,
  
  /// File is marked for offline download but not yet downloaded
  pending,
  
  /// File is currently being downloaded for offline access
  downloading,
  
  /// File is available offline (downloaded and cached)
  available,
  
  /// Offline download failed
  failed,
  
  /// File was available offline but cache has expired
  expired,
  
  /// File is being synced with the server
  syncing,
}

/// Extension to provide additional functionality for OfflineAccess
extension OfflineAccessExtension on OfflineAccess {
  /// Returns a human-readable label for the offline access state
  String get label {
    switch (this) {
      case OfflineAccess.none:
        return 'Online Only';
      case OfflineAccess.pending:
        return 'Download Pending';
      case OfflineAccess.downloading:
        return 'Downloading';
      case OfflineAccess.available:
        return 'Available Offline';
      case OfflineAccess.failed:
        return 'Download Failed';
      case OfflineAccess.expired:
        return 'Cache Expired';
      case OfflineAccess.syncing:
        return 'Syncing';
    }
  }

  /// Returns a detailed description of the offline access state
  String get description {
    switch (this) {
      case OfflineAccess.none:
        return 'File is only available when connected to the internet';
      case OfflineAccess.pending:
        return 'File is queued for offline download';
      case OfflineAccess.downloading:
        return 'File is currently being downloaded for offline access';
      case OfflineAccess.available:
        return 'File is downloaded and available for offline viewing';
      case OfflineAccess.failed:
        return 'Failed to download file for offline access';
      case OfflineAccess.expired:
        return 'Offline cache has expired and needs to be refreshed';
      case OfflineAccess.syncing:
        return 'File is being synchronized with the server';
    }
  }

  /// Returns the appropriate icon for the offline access state
  IconData get icon {
    switch (this) {
      case OfflineAccess.none:
        return Symbols.cloud;
      case OfflineAccess.pending:
        return Symbols.download_for_offline;
      case OfflineAccess.downloading:
        return Symbols.downloading;
      case OfflineAccess.available:
        return Symbols.offline_pin;
      case OfflineAccess.failed:
        return Symbols.cloud_off;
      case OfflineAccess.expired:
        return Symbols.update;
      case OfflineAccess.syncing:
        return Symbols.sync;
    }
  }

  /// Returns the color associated with the offline access state
  Color get color {
    switch (this) {
      case OfflineAccess.none:
        return Colors.grey;
      case OfflineAccess.pending:
        return Colors.orange;
      case OfflineAccess.downloading:
        return Colors.blue;
      case OfflineAccess.available:
        return Colors.green;
      case OfflineAccess.failed:
        return Colors.red;
      case OfflineAccess.expired:
        return Colors.amber;
      case OfflineAccess.syncing:
        return Colors.purple;
    }
  }

  /// Whether the file can be accessed offline
  bool get canAccessOffline {
    switch (this) {
      case OfflineAccess.available:
        return true;
      case OfflineAccess.none:
      case OfflineAccess.pending:
      case OfflineAccess.downloading:
      case OfflineAccess.failed:
      case OfflineAccess.expired:
      case OfflineAccess.syncing:
        return false;
    }
  }

  /// Whether the offline download can be retried
  bool get canRetryDownload {
    switch (this) {
      case OfflineAccess.failed:
      case OfflineAccess.expired:
        return true;
      case OfflineAccess.none:
      case OfflineAccess.pending:
      case OfflineAccess.downloading:
      case OfflineAccess.available:
      case OfflineAccess.syncing:
        return false;
    }
  }

  /// Whether the offline download can be cancelled
  bool get canCancelDownload {
    switch (this) {
      case OfflineAccess.pending:
      case OfflineAccess.downloading:
        return true;
      case OfflineAccess.none:
      case OfflineAccess.available:
      case OfflineAccess.failed:
      case OfflineAccess.expired:
      case OfflineAccess.syncing:
        return false;
    }
  }

  /// Whether the file can be marked for offline access
  bool get canMarkForOffline {
    switch (this) {
      case OfflineAccess.none:
      case OfflineAccess.failed:
      case OfflineAccess.expired:
        return true;
      case OfflineAccess.pending:
      case OfflineAccess.downloading:
      case OfflineAccess.available:
      case OfflineAccess.syncing:
        return false;
    }
  }

  /// Whether the offline cache can be cleared
  bool get canClearCache {
    switch (this) {
      case OfflineAccess.available:
      case OfflineAccess.expired:
        return true;
      case OfflineAccess.none:
      case OfflineAccess.pending:
      case OfflineAccess.downloading:
      case OfflineAccess.failed:
      case OfflineAccess.syncing:
        return false;
    }
  }

  /// Whether the file is in a downloading state
  bool get isDownloading {
    switch (this) {
      case OfflineAccess.downloading:
      case OfflineAccess.syncing:
        return true;
      case OfflineAccess.none:
      case OfflineAccess.pending:
      case OfflineAccess.available:
      case OfflineAccess.failed:
      case OfflineAccess.expired:
        return false;
    }
  }

  /// Whether the offline access state indicates an error
  bool get hasError {
    switch (this) {
      case OfflineAccess.failed:
        return true;
      case OfflineAccess.none:
      case OfflineAccess.pending:
      case OfflineAccess.downloading:
      case OfflineAccess.available:
      case OfflineAccess.expired:
      case OfflineAccess.syncing:
        return false;
    }
  }

  /// Get the string value for JSON serialization
  String get value {
    switch (this) {
      case OfflineAccess.none:
        return 'none';
      case OfflineAccess.pending:
        return 'pending';
      case OfflineAccess.downloading:
        return 'downloading';
      case OfflineAccess.available:
        return 'available';
      case OfflineAccess.failed:
        return 'failed';
      case OfflineAccess.expired:
        return 'expired';
      case OfflineAccess.syncing:
        return 'syncing';
    }
  }

  /// Create OfflineAccess from string value
  static OfflineAccess fromString(String value) {
    switch (value.toLowerCase()) {
      case 'none':
        return OfflineAccess.none;
      case 'pending':
        return OfflineAccess.pending;
      case 'downloading':
        return OfflineAccess.downloading;
      case 'available':
        return OfflineAccess.available;
      case 'failed':
        return OfflineAccess.failed;
      case 'expired':
        return OfflineAccess.expired;
      case 'syncing':
        return OfflineAccess.syncing;
      default:
        return OfflineAccess.none; // Default
    }
  }
}
