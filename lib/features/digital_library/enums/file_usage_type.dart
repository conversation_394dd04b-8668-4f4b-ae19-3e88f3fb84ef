import 'package:flutter/material.dart';
import 'package:material_symbols_icons/symbols.dart';

/// Enum representing different usage types for files in the digital library
/// Categorizes files based on their purpose across different app features
enum FileUsageType {
  /// Personal files uploaded by users for their own use
  /// Examples: Personal notes, study materials, draft assignments
  personal,
  
  /// Files shared between specific users
  /// Examples: Group project files, peer study materials, collaborative documents
  shared,
  
  /// Files uploaded as class resources by teachers/admins
  /// Examples: Lecture slides, syllabus, reading materials, assignment templates
  classResource,
  
  /// Files attached to homework submissions
  /// Examples: Completed assignments, project files, submission documents
  homeworkSubmission,
  
  /// Files attached to notices/announcements
  /// Examples: Notice attachments, announcement documents, circular files
  noticeAttachment,
  
  /// Files uploaded as general classroom activities
  /// Examples: Activity instructions, reference materials, supplementary content
  classroomActivity,
  
  /// Files used in the profile section
  /// Examples: Profile pictures, certificates, portfolio items
  profile,
  
  /// General library files that don't fit other categories
  /// Examples: Reference materials, general resources
  library,
}

/// Extension to provide additional functionality for FileUsageType
extension FileUsageTypeExtension on FileUsageType {
  /// Returns a human-readable label for the usage type
  String get label {
    switch (this) {
      case FileUsageType.personal:
        return 'Personal';
      case FileUsageType.shared:
        return 'Shared';
      case FileUsageType.classResource:
        return 'Class Resource';
      case FileUsageType.homeworkSubmission:
        return 'Homework Submission';
      case FileUsageType.noticeAttachment:
        return 'Notice Attachment';
      case FileUsageType.classroomActivity:
        return 'Classroom Activity';
      case FileUsageType.profile:
        return 'Profile';
      case FileUsageType.library:
        return 'Library';
    }
  }

  /// Returns a detailed description of the usage type
  String get description {
    switch (this) {
      case FileUsageType.personal:
        return 'Files uploaded for personal use and reference';
      case FileUsageType.shared:
        return 'Files shared with specific users for collaboration';
      case FileUsageType.classResource:
        return 'Educational resources provided for class members';
      case FileUsageType.homeworkSubmission:
        return 'Files submitted as part of homework assignments';
      case FileUsageType.noticeAttachment:
        return 'Files attached to notices and announcements';
      case FileUsageType.classroomActivity:
        return 'Files related to classroom activities and exercises';
      case FileUsageType.profile:
        return 'Files used in user profiles and portfolios';
      case FileUsageType.library:
        return 'General library files and reference materials';
    }
  }

  /// Returns the appropriate icon for the usage type
  IconData get icon {
    switch (this) {
      case FileUsageType.personal:
        return Symbols.person;
      case FileUsageType.shared:
        return Symbols.group;
      case FileUsageType.classResource:
        return Symbols.school;
      case FileUsageType.homeworkSubmission:
        return Symbols.assignment;
      case FileUsageType.noticeAttachment:
        return Symbols.campaign;
      case FileUsageType.classroomActivity:
        return Symbols.local_activity;
      case FileUsageType.profile:
        return Symbols.account_circle;
      case FileUsageType.library:
        return Symbols.library_books;
    }
  }

  /// Returns the color associated with the usage type
  Color get color {
    switch (this) {
      case FileUsageType.personal:
        return Colors.blue;
      case FileUsageType.shared:
        return Colors.green;
      case FileUsageType.classResource:
        return Colors.purple;
      case FileUsageType.homeworkSubmission:
        return Colors.orange;
      case FileUsageType.noticeAttachment:
        return Colors.red;
      case FileUsageType.classroomActivity:
        return Colors.teal;
      case FileUsageType.profile:
        return Colors.indigo;
      case FileUsageType.library:
        return Colors.brown;
    }
  }

  /// Whether this usage type requires a classId
  bool get requiresClassId {
    switch (this) {
      case FileUsageType.classResource:
      case FileUsageType.homeworkSubmission:
      case FileUsageType.classroomActivity:
        return true;
      case FileUsageType.personal:
      case FileUsageType.shared:
      case FileUsageType.noticeAttachment:
      case FileUsageType.profile:
      case FileUsageType.library:
        return false;
    }
  }

  /// Whether this usage type supports sharing with specific users
  bool get supportsSharing {
    switch (this) {
      case FileUsageType.shared:
      case FileUsageType.library:
        return true;
      case FileUsageType.personal:
      case FileUsageType.classResource:
      case FileUsageType.homeworkSubmission:
      case FileUsageType.noticeAttachment:
      case FileUsageType.classroomActivity:
      case FileUsageType.profile:
        return false;
    }
  }

  /// Get the string value for JSON serialization
  String get value {
    switch (this) {
      case FileUsageType.personal:
        return 'personal';
      case FileUsageType.shared:
        return 'shared';
      case FileUsageType.classResource:
        return 'class_resource';
      case FileUsageType.homeworkSubmission:
        return 'homework_submission';
      case FileUsageType.noticeAttachment:
        return 'notice_attachment';
      case FileUsageType.classroomActivity:
        return 'classroom_activity';
      case FileUsageType.profile:
        return 'profile';
      case FileUsageType.library:
        return 'library';
    }
  }

  /// Create FileUsageType from string value
  static FileUsageType fromString(String value) {
    switch (value.toLowerCase()) {
      case 'personal':
        return FileUsageType.personal;
      case 'shared':
        return FileUsageType.shared;
      case 'class_resource':
        return FileUsageType.classResource;
      case 'homework_submission':
        return FileUsageType.homeworkSubmission;
      case 'notice_attachment':
        return FileUsageType.noticeAttachment;
      case 'classroom_activity':
        return FileUsageType.classroomActivity;
      case 'profile':
        return FileUsageType.profile;
      case 'library':
        return FileUsageType.library;
      default:
        return FileUsageType.library; // Default
    }
  }
}
