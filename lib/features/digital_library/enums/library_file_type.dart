import 'package:flutter/material.dart';
import 'package:material_symbols_icons/symbols.dart';

/// Enhanced enum representing different file types for the digital library
/// Extends the basic file types with more specific categories for educational content
enum LibraryFileType {
  /// Image files (JPG, PNG, GIF, WebP, etc.)
  image,

  /// PDF documents
  pdf,

  /// Video files (MP4, MOV, AVI, etc.)
  video,

  /// Text documents (DOC, DOCX, TXT, RTF, etc.)
  document,

  /// Audio files (MP3, WAV, AAC, etc.)
  audio,

  /// Archive files (ZIP, RAR, 7Z, etc.)
  archive,

  /// Presentation files (PPT, PPTX, KEY, etc.)
  presentation,

  /// Spreadsheet files (XLS, XLSX, CSV, etc.)
  spreadsheet,

  /// Code files (JS, PY, JAVA, etc.)
  code,

  /// Web links and URLs
  link,

  /// Study notes and text files
  note,

  /// E-book files (EPUB, MOBI, etc.)
  ebook,

  /// 3D model files (OBJ, STL, etc.)
  model3d,

  /// Font files (TTF, OTF, WOFF, etc.)
  font,

  /// Other/unknown file types
  other,
}

/// Legacy FileType enum for backward compatibility with files feature
/// This maps to LibraryFileType for seamless migration
enum FileType {
  /// Image files (JPG, PNG, GIF, WebP, etc.)
  image,

  /// PDF documents
  pdf,

  /// Video files (MP4, MOV, AVI, etc.)
  video,

  /// Document files (DOC, DOCX, TXT, RTF, etc.)
  document,

  /// Audio files (MP3, WAV, AAC, etc.)
  audio,

  /// Archive files (ZIP, RAR, 7Z, etc.)
  archive,

  /// Other/unknown file types
  other,
}

/// Extension to provide additional functionality for FileType (legacy compatibility)
extension FileTypeExtension on FileType {
  /// Returns a human-readable label for the file type
  String get label {
    switch (this) {
      case FileType.image:
        return 'Image';
      case FileType.pdf:
        return 'PDF';
      case FileType.video:
        return 'Video';
      case FileType.document:
        return 'Document';
      case FileType.audio:
        return 'Audio';
      case FileType.archive:
        return 'Archive';
      case FileType.other:
        return 'File';
    }
  }

  /// Returns the icon for the file type
  IconData get icon {
    switch (this) {
      case FileType.image:
        return Symbols.image;
      case FileType.pdf:
        return Symbols.picture_as_pdf;
      case FileType.video:
        return Symbols.videocam;
      case FileType.document:
        return Symbols.description;
      case FileType.audio:
        return Symbols.audiotrack;
      case FileType.archive:
        return Symbols.folder_zip;
      case FileType.other:
        return Symbols.insert_drive_file;
    }
  }

  /// Returns whether this file type can be viewed natively in the app
  bool get canViewNatively {
    switch (this) {
      case FileType.image:
      case FileType.pdf:
        return true;
      case FileType.video:
      case FileType.document:
      case FileType.audio:
      case FileType.archive:
      case FileType.other:
        return false;
    }
  }

  /// Returns the list of file extensions for this file type
  List<String> get extensions {
    switch (this) {
      case FileType.image:
        return ['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp', 'svg'];
      case FileType.pdf:
        return ['pdf'];
      case FileType.video:
        return ['mp4', 'mov', 'avi', 'mkv', 'wmv', 'flv', 'webm'];
      case FileType.document:
        return ['doc', 'docx', 'txt', 'rtf', 'odt', 'pages', 'ppt', 'pptx'];
      case FileType.audio:
        return ['mp3', 'wav', 'aac', 'flac', 'ogg', 'm4a'];
      case FileType.archive:
        return ['zip', 'rar', '7z', 'tar', 'gz', 'bz2'];
      case FileType.other:
        return [];
    }
  }

  /// Returns the color hex string for the file type
  String get colorHex {
    switch (this) {
      case FileType.image:
        return '#4CAF50';
      case FileType.pdf:
        return '#F44336';
      case FileType.video:
        return '#FF5722';
      case FileType.document:
        return '#2196F3';
      case FileType.audio:
        return '#9C27B0';
      case FileType.archive:
        return '#FF9800';
      case FileType.other:
        return '#9E9E9E';
    }
  }

  /// Convert to LibraryFileType
  LibraryFileType toLibraryFileType() {
    switch (this) {
      case FileType.image:
        return LibraryFileType.image;
      case FileType.pdf:
        return LibraryFileType.pdf;
      case FileType.video:
        return LibraryFileType.video;
      case FileType.document:
        return LibraryFileType.document;
      case FileType.audio:
        return LibraryFileType.audio;
      case FileType.archive:
        return LibraryFileType.archive;
      case FileType.other:
        return LibraryFileType.other;
    }
  }
}

/// Utility class for file type detection (legacy compatibility)
class FileTypeDetector {
  /// Detect file type from file extension
  static FileType fromExtension(String extension) {
    final ext = extension.toLowerCase();

    for (final fileType in FileType.values) {
      if (fileType.extensions.contains(ext)) {
        return fileType;
      }
    }

    return FileType.other;
  }

  /// Detect file type from file name
  static FileType fromFileName(String fileName) {
    final parts = fileName.split('.');
    if (parts.length < 2) return FileType.other;

    final extension = parts.last;
    return fromExtension(extension);
  }

  /// Detect file type from file path or URL
  static FileType fromPath(String path) {
    final fileName = path.split('/').last;
    return fromFileName(fileName);
  }
}

/// Extension to provide additional functionality for LibraryFileType
extension LibraryFileTypeExtension on LibraryFileType {
  /// Returns a human-readable label for the file type
  String get label {
    switch (this) {
      case LibraryFileType.image:
        return 'Image';
      case LibraryFileType.pdf:
        return 'PDF';
      case LibraryFileType.video:
        return 'Video';
      case LibraryFileType.document:
        return 'Document';
      case LibraryFileType.audio:
        return 'Audio';
      case LibraryFileType.archive:
        return 'Archive';
      case LibraryFileType.presentation:
        return 'Presentation';
      case LibraryFileType.spreadsheet:
        return 'Spreadsheet';
      case LibraryFileType.code:
        return 'Code';
      case LibraryFileType.link:
        return 'Link';
      case LibraryFileType.note:
        return 'Note';
      case LibraryFileType.ebook:
        return 'E-book';
      case LibraryFileType.model3d:
        return '3D Model';
      case LibraryFileType.font:
        return 'Font';
      case LibraryFileType.other:
        return 'File';
    }
  }

  /// Returns the appropriate icon for the file type
  IconData get icon {
    switch (this) {
      case LibraryFileType.image:
        return Symbols.image;
      case LibraryFileType.pdf:
        return Symbols.picture_as_pdf;
      case LibraryFileType.video:
        return Symbols.videocam;
      case LibraryFileType.document:
        return Symbols.description;
      case LibraryFileType.audio:
        return Symbols.audiotrack;
      case LibraryFileType.archive:
        return Symbols.folder_zip;
      case LibraryFileType.presentation:
        return Symbols.slideshow;
      case LibraryFileType.spreadsheet:
        return Symbols.table_chart;
      case LibraryFileType.code:
        return Symbols.code;
      case LibraryFileType.link:
        return Symbols.link;
      case LibraryFileType.note:
        return Symbols.note;
      case LibraryFileType.ebook:
        return Symbols.menu_book;
      case LibraryFileType.model3d:
        return Symbols.view_in_ar;
      case LibraryFileType.font:
        return Symbols.font_download;
      case LibraryFileType.other:
        return Symbols.insert_drive_file;
    }
  }

  /// Returns the primary color for the file type
  Color get color {
    switch (this) {
      case LibraryFileType.image:
        return Colors.green;
      case LibraryFileType.pdf:
        return Colors.red;
      case LibraryFileType.video:
        return Colors.deepOrange;
      case LibraryFileType.document:
        return Colors.blue;
      case LibraryFileType.audio:
        return Colors.purple;
      case LibraryFileType.archive:
        return Colors.orange;
      case LibraryFileType.presentation:
        return Colors.amber;
      case LibraryFileType.spreadsheet:
        return Colors.teal;
      case LibraryFileType.code:
        return Colors.indigo;
      case LibraryFileType.link:
        return Colors.cyan;
      case LibraryFileType.note:
        return Colors.lime;
      case LibraryFileType.ebook:
        return Colors.brown;
      case LibraryFileType.model3d:
        return Colors.pink;
      case LibraryFileType.font:
        return Colors.deepPurple;
      case LibraryFileType.other:
        return Colors.grey;
    }
  }

  /// Returns the color hex string for the file type
  String get colorHex {
    switch (this) {
      case LibraryFileType.image:
        return '#4CAF50';
      case LibraryFileType.pdf:
        return '#F44336';
      case LibraryFileType.video:
        return '#FF5722';
      case LibraryFileType.document:
        return '#2196F3';
      case LibraryFileType.audio:
        return '#9C27B0';
      case LibraryFileType.archive:
        return '#FF9800';
      case LibraryFileType.presentation:
        return '#FFC107';
      case LibraryFileType.spreadsheet:
        return '#009688';
      case LibraryFileType.code:
        return '#3F51B5';
      case LibraryFileType.link:
        return '#00BCD4';
      case LibraryFileType.note:
        return '#CDDC39';
      case LibraryFileType.ebook:
        return '#795548';
      case LibraryFileType.model3d:
        return '#E91E63';
      case LibraryFileType.font:
        return '#673AB7';
      case LibraryFileType.other:
        return '#9E9E9E';
    }
  }

  /// Returns whether this file type can be viewed natively in the app
  bool get canViewNatively {
    switch (this) {
      case LibraryFileType.image:
      case LibraryFileType.pdf:
      case LibraryFileType.note:
        return true;
      case LibraryFileType.video:
      case LibraryFileType.document:
      case LibraryFileType.audio:
      case LibraryFileType.archive:
      case LibraryFileType.presentation:
      case LibraryFileType.spreadsheet:
      case LibraryFileType.code:
      case LibraryFileType.link:
      case LibraryFileType.ebook:
      case LibraryFileType.model3d:
      case LibraryFileType.font:
      case LibraryFileType.other:
        return false;
    }
  }

  /// Returns the list of file extensions for this file type
  List<String> get extensions {
    switch (this) {
      case LibraryFileType.image:
        return ['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp', 'svg', 'ico'];
      case LibraryFileType.pdf:
        return ['pdf'];
      case LibraryFileType.video:
        return ['mp4', 'mov', 'avi', 'mkv', 'wmv', 'flv', 'webm', 'm4v'];
      case LibraryFileType.document:
        return ['doc', 'docx', 'txt', 'rtf', 'odt', 'pages'];
      case LibraryFileType.audio:
        return ['mp3', 'wav', 'aac', 'flac', 'ogg', 'm4a', 'wma'];
      case LibraryFileType.archive:
        return ['zip', 'rar', '7z', 'tar', 'gz', 'bz2', 'xz'];
      case LibraryFileType.presentation:
        return ['ppt', 'pptx', 'key', 'odp'];
      case LibraryFileType.spreadsheet:
        return ['xls', 'xlsx', 'csv', 'ods', 'numbers'];
      case LibraryFileType.code:
        return [
          'js',
          'py',
          'java',
          'cpp',
          'c',
          'html',
          'css',
          'dart',
          'swift',
          'kt',
        ];
      case LibraryFileType.link:
        return ['url', 'webloc'];
      case LibraryFileType.note:
        return ['md', 'txt', 'note'];
      case LibraryFileType.ebook:
        return ['epub', 'mobi', 'azw', 'fb2'];
      case LibraryFileType.model3d:
        return ['obj', 'stl', 'ply', 'dae', 'fbx'];
      case LibraryFileType.font:
        return ['ttf', 'otf', 'woff', 'woff2', 'eot'];
      case LibraryFileType.other:
        return [];
    }
  }

  /// Whether this file type is typically used for educational content
  bool get isEducational {
    switch (this) {
      case LibraryFileType.pdf:
      case LibraryFileType.document:
      case LibraryFileType.presentation:
      case LibraryFileType.spreadsheet:
      case LibraryFileType.note:
      case LibraryFileType.ebook:
        return true;
      case LibraryFileType.image:
      case LibraryFileType.video:
      case LibraryFileType.audio:
      case LibraryFileType.archive:
      case LibraryFileType.code:
      case LibraryFileType.link:
      case LibraryFileType.model3d:
      case LibraryFileType.font:
      case LibraryFileType.other:
        return false;
    }
  }

  /// Get the string value for JSON serialization
  String get value {
    return name;
  }

  /// Create LibraryFileType from string value
  static LibraryFileType fromString(String value) {
    try {
      return LibraryFileType.values.firstWhere(
        (type) => type.name == value.toLowerCase(),
      );
    } catch (e) {
      return LibraryFileType.other; // Default
    }
  }
}

/// Utility class for library file type detection
class LibraryFileTypeDetector {
  /// Detect file type from file extension
  static LibraryFileType fromExtension(String extension) {
    final ext = extension.toLowerCase();

    for (final fileType in LibraryFileType.values) {
      if (fileType.extensions.contains(ext)) {
        return fileType;
      }
    }

    return LibraryFileType.other;
  }

  /// Detect file type from file name
  static LibraryFileType fromFileName(String fileName) {
    final parts = fileName.split('.');
    if (parts.length < 2) return LibraryFileType.other;

    final extension = parts.last;
    return fromExtension(extension);
  }

  /// Detect file type from file path or URL
  static LibraryFileType fromPath(String path) {
    final fileName = path.split('/').last;
    return fromFileName(fileName);
  }

  /// Check if a URL is a web link
  static bool isWebLink(String url) {
    return url.startsWith('http://') || url.startsWith('https://');
  }
}
