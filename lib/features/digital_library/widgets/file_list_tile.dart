import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_symbols_icons/symbols.dart';

import '../../../core/theme/app_colors.dart';
import '../enums/library_file_type.dart';
import '../enums/file_access_type.dart';
import '../enums/upload_status.dart';
import '../enums/offline_access.dart';
import '../models/library_file_model.dart';

/// Tile widget for displaying file information in lists
class FileListTile extends StatelessWidget {
  final LibraryFileModel file;
  final VoidCallback? onTap;
  final VoidCallback? onLongPress;
  final VoidCallback? onDownload;
  final VoidCallback? onOfflineToggle;
  final VoidCallback? onMoreOptions;
  final bool showThumbnail;
  final bool showProgress;

  const FileListTile({
    super.key,
    required this.file,
    this.onTap,
    this.onLongPress,
    this.onDownload,
    this.onOfflineToggle,
    this.onMoreOptions,
    this.showThumbnail = true,
    this.showProgress = true,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Container(
      margin: EdgeInsets.symmetric(vertical: 4.h),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: theme.dividerColor, width: 1),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          onLongPress: onLongPress,
          borderRadius: BorderRadius.circular(12.r),
          child: Padding(
            padding: EdgeInsets.all(16.w),
            child: Column(
              children: [
                Row(
                  children: [
                    // File icon/thumbnail
                    _buildFileIcon(theme),

                    SizedBox(width: 12.w),

                    // File info
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // File name and badges
                          Row(
                            children: [
                              Expanded(
                                child: Text(
                                  file.displayTitle,
                                  style: theme.textTheme.bodyLarge?.copyWith(
                                    fontWeight: FontWeight.w600,
                                    color: theme.colorScheme.onSurface,
                                  ),
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),

                              // Status badges
                              ..._buildStatusBadges(theme),
                            ],
                          ),

                          SizedBox(height: 4.h),

                          // File details
                          Row(
                            children: [
                              Text(
                                file.fileType.label,
                                style: theme.textTheme.bodySmall?.copyWith(
                                  color: file.fileType.color,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),

                              SizedBox(width: 8.w),

                              Container(
                                width: 4.w,
                                height: 4.w,
                                decoration: BoxDecoration(
                                  color: isDark
                                      ? AppColors.textSecondaryDark
                                      : AppColors.textSecondaryLight,
                                  shape: BoxShape.circle,
                                ),
                              ),

                              SizedBox(width: 8.w),

                              Text(
                                _formatDate(file.createdAt),
                                style: theme.textTheme.bodySmall?.copyWith(
                                  color: isDark
                                      ? AppColors.textSecondaryDark
                                      : AppColors.textSecondaryLight,
                                ),
                              ),
                            ],
                          ),

                          // Tags
                          if (file.tags.isNotEmpty) ...[
                            SizedBox(height: 8.h),
                            Wrap(
                              spacing: 6.w,
                              runSpacing: 4.h,
                              children: file.tags
                                  .take(3)
                                  .map(
                                    (tag) => Container(
                                      padding: EdgeInsets.symmetric(
                                        horizontal: 6.w,
                                        vertical: 2.h,
                                      ),
                                      decoration: BoxDecoration(
                                        color:
                                            (isDark
                                                    ? AppColors.primaryDark
                                                    : AppColors.primaryLight)
                                                .withValues(alpha: 0.1),
                                        borderRadius: BorderRadius.circular(
                                          4.r,
                                        ),
                                      ),
                                      child: Text(
                                        tag,
                                        style: theme.textTheme.bodySmall
                                            ?.copyWith(
                                              color: isDark
                                                  ? AppColors.primaryDark
                                                  : AppColors.primaryLight,
                                              fontSize: 10.sp,
                                            ),
                                      ),
                                    ),
                                  )
                                  .toList(),
                            ),
                          ],
                        ],
                      ),
                    ),

                    // Action buttons
                    _buildActionButtons(context, isDark),
                  ],
                ),

                // Upload progress
                if (showProgress && file.isUploading) ...[
                  SizedBox(height: 12.h),
                  _buildProgressBar(context, isDark),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildFileIcon(ThemeData theme) {
    return Container(
      width: 48.w,
      height: 48.w,
      decoration: BoxDecoration(
        color: file.fileType.color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: showThumbnail && file.thumbnailUrl != null
          ? ClipRRect(
              borderRadius: BorderRadius.circular(12.r),
              child: Image.network(
                file.thumbnailUrl!,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) => Icon(
                  file.fileType.icon,
                  color: file.fileType.color,
                  size: 24.sp,
                ),
              ),
            )
          : Icon(file.fileType.icon, color: file.fileType.color, size: 24.sp),
    );
  }

  List<Widget> _buildStatusBadges(ThemeData theme) {
    final badges = <Widget>[];

    // Favorite badge
    if (file.isFavorite) {
      badges.add(
        Icon(Symbols.favorite, size: 16.sp, color: theme.colorScheme.secondary),
      );
      badges.add(SizedBox(width: 4.w));
    }

    // Offline badge
    if (file.offlineAccess.canAccessOffline) {
      badges.add(
        Icon(
          Symbols.offline_pin,
          size: 16.sp,
          color: theme.colorScheme.primary,
        ),
      );
      badges.add(SizedBox(width: 4.w));
    }

    // Access type badge
    badges.add(
      Container(
        padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 2.h),
        decoration: BoxDecoration(
          color: file.accessType.color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(4.r),
        ),
        child: Icon(
          file.accessType.icon,
          size: 12.sp,
          color: file.accessType.color,
        ),
      ),
    );

    return badges;
  }

  Widget _buildActionButtons(BuildContext context, bool isDark) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Offline toggle
        if (onOfflineToggle != null)
          IconButton(
            onPressed: onOfflineToggle,
            icon: Icon(
              file.offlineAccess.canAccessOffline
                  ? Symbols.offline_pin
                  : Symbols.download_for_offline,
              size: 20.sp,
              color: file.offlineAccess.canAccessOffline
                  ? (isDark ? AppColors.successDark : AppColors.successLight)
                  : (isDark
                        ? AppColors.textSecondaryDark
                        : AppColors.textSecondaryLight),
            ),
          ),

        // Download button
        if (onDownload != null && file.canDownload)
          IconButton(
            onPressed: onDownload,
            icon: Icon(
              Symbols.download,
              size: 20.sp,
              color: isDark
                  ? AppColors.textSecondaryDark
                  : AppColors.textSecondaryLight,
            ),
          ),

        // More options
        IconButton(
          onPressed: onMoreOptions != null
              ? () => _showMoreOptions(context)
              : null,
          icon: Icon(
            Symbols.more_vert,
            size: 20.sp,
            color: isDark
                ? AppColors.textSecondaryDark
                : AppColors.textSecondaryLight,
          ),
        ),
      ],
    );
  }

  Widget _buildProgressBar(BuildContext context, bool isDark) {
    final theme = Theme.of(context);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              file.uploadStatus.label,
              style: theme.textTheme.bodySmall?.copyWith(
                color: file.uploadStatus.color,
                fontWeight: FontWeight.w500,
              ),
            ),
            const Spacer(),
            Text(
              '${(file.uploadProgress * 100).toInt()}%',
              style: theme.textTheme.bodySmall?.copyWith(
                color: isDark
                    ? AppColors.textSecondaryDark
                    : AppColors.textSecondaryLight,
              ),
            ),
          ],
        ),

        SizedBox(height: 4.h),

        LinearProgressIndicator(
          value: file.uploadProgress,
          backgroundColor:
              (isDark ? AppColors.borderDark : AppColors.borderLight)
                  .withValues(alpha: 0.3),
          valueColor: AlwaysStoppedAnimation<Color>(file.uploadStatus.color),
        ),
      ],
    );
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays == 0) {
      return 'Today';
    } else if (difference.inDays == 1) {
      return 'Yesterday';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}d ago';
    } else if (difference.inDays < 30) {
      final weeks = (difference.inDays / 7).floor();
      return '${weeks}w ago';
    } else if (difference.inDays < 365) {
      final months = (difference.inDays / 30).floor();
      return '${months}m ago';
    } else {
      final years = (difference.inDays / 365).floor();
      return '${years}y ago';
    }
  }

  /// Show more options menu for the file
  void _showMoreOptions(BuildContext context) {
    showModalBottomSheet(
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20.r)),
      ),
      builder: (context) => _buildMoreOptionsSheet(context),
    );
  }

  /// Build the more options bottom sheet
  Widget _buildMoreOptionsSheet(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      padding: EdgeInsets.symmetric(vertical: 20.h),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Header
          Container(
            width: 40.w,
            height: 4.h,
            decoration: BoxDecoration(
              color: theme.colorScheme.outline,
              borderRadius: BorderRadius.circular(2.r),
            ),
          ),
          SizedBox(height: 20.h),

          // File info
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 20.w),
            child: Row(
              children: [
                Icon(
                  file.fileType.icon,
                  size: 24.sp,
                  color: theme.colorScheme.primary,
                ),
                SizedBox(width: 12.w),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        file.title ?? file.fileName,
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      if (file.description?.isNotEmpty == true) ...[
                        SizedBox(height: 2.h),
                        Text(
                          file.description!,
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: theme.colorScheme.onSurfaceVariant,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ],
                  ),
                ),
              ],
            ),
          ),

          SizedBox(height: 20.h),
          Divider(height: 1.h),

          // Options
          if (onDownload != null)
            _buildOptionTile(
              context,
              icon: Symbols.download,
              title: 'Download',
              onTap: () {
                Navigator.pop(context);
                onDownload!();
              },
            ),

          if (onOfflineToggle != null)
            _buildOptionTile(
              context,
              icon: file.offlineAccess.canAccessOffline
                  ? Symbols.cloud_off
                  : Symbols.offline_pin,
              title: file.offlineAccess.canAccessOffline
                  ? 'Remove from offline'
                  : 'Make available offline',
              onTap: () {
                Navigator.pop(context);
                onOfflineToggle!();
              },
            ),

          _buildOptionTile(
            context,
            icon: Symbols.share,
            title: 'Share',
            onTap: () {
              Navigator.pop(context);
              // TODO: Implement share functionality
            },
          ),

          _buildOptionTile(
            context,
            icon: Symbols.info,
            title: 'Details',
            onTap: () {
              Navigator.pop(context);
              if (onMoreOptions != null) onMoreOptions!();
            },
          ),
        ],
      ),
    );
  }

  /// Build an option tile for the bottom sheet
  Widget _buildOptionTile(
    BuildContext context, {
    required IconData icon,
    required String title,
    required VoidCallback onTap,
  }) {
    final theme = Theme.of(context);

    return ListTile(
      leading: Icon(icon, size: 24.sp, color: theme.colorScheme.onSurface),
      title: Text(title, style: theme.textTheme.bodyLarge),
      onTap: onTap,
      contentPadding: EdgeInsets.symmetric(horizontal: 20.w),
    );
  }
}
