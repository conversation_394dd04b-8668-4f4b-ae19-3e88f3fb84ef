// ignore_for_file: deprecated_member_use

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_symbols_icons/symbols.dart';
import 'package:share_plus/share_plus.dart';
import '../models/file_model.dart';
import '../enums/library_file_type.dart';
import '../enums/download_state.dart';
import '../services/file_viewer_service.dart';
import '../services/file_download_service.dart';

/// Widget for displaying unsupported file types with file info and actions
class PlaceholderViewerWidget extends StatefulWidget {
  /// The file model to display
  final FileModel fileModel;

  const PlaceholderViewerWidget({super.key, required this.fileModel});

  @override
  State<PlaceholderViewerWidget> createState() =>
      _PlaceholderViewerWidgetState();
}

class _PlaceholderViewerWidgetState extends State<PlaceholderViewerWidget> {
  final FileViewerService _viewerService = FileViewerService();
  final FileDownloadService _downloadService = FileDownloadService();
  bool _isDownloading = false;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final fileIcon = _viewerService.getFileIcon(widget.fileModel.fileType);
    final fileColor = _viewerService.getFileColor(widget.fileModel.fileType);

    return Container(
      color: Colors.black,
      child: Center(
        child: SingleChildScrollView(
          padding: EdgeInsets.all(24.w),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // File icon
              Container(
                width: 120.w,
                height: 120.w,
                decoration: BoxDecoration(
                  color: fileColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(16.r),
                  border: Border.all(
                    color: fileColor.withValues(alpha: 0.3),
                    width: 2,
                  ),
                ),
                child: Icon(fileIcon, size: 64.sp, color: fileColor),
              ),

              SizedBox(height: 24.h),

              // File name
              Text(
                widget.fileModel.fileName,
                style: theme.textTheme.headlineSmall?.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                ),
                textAlign: TextAlign.center,
              ),

              SizedBox(height: 8.h),

              // File type
              Container(
                padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
                decoration: BoxDecoration(
                  color: fileColor.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(16.r),
                ),
                child: Text(
                  widget.fileModel.fileType.label.toUpperCase(),
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: fileColor,
                    fontWeight: FontWeight.w600,
                    letterSpacing: 1.2,
                  ),
                ),
              ),

              SizedBox(height: 24.h),

              // File details
              _buildFileDetails(theme),

              SizedBox(height: 32.h),

              // Action buttons
              _buildActionButtons(theme),

              SizedBox(height: 24.h),

              // Info message
              Container(
                padding: EdgeInsets.all(16.w),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12.r),
                ),
                child: Row(
                  children: [
                    Icon(Symbols.info, color: Colors.white70, size: 20.sp),
                    SizedBox(width: 12.w),
                    Expanded(
                      child: Text(
                        'This file type cannot be previewed in the app. You can download it or open it with an external app.',
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: Colors.white70,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFileDetails(ThemeData theme) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: Colors.white.withValues(alpha: 0.1)),
      ),
      child: Column(
        children: [
          // File size
          if (widget.fileModel.fileSizeBytes != null)
            _buildDetailRow(
              'Size',
              widget.fileModel.fileSizeString,
              Symbols.storage,
              theme,
            ),

          // File extension
          _buildDetailRow(
            'Type',
            widget.fileModel.fileExtension.toUpperCase(),
            Symbols.description,
            theme,
          ),

          // Download state
          if (!widget.fileModel.isLocal)
            _buildDetailRow(
              'Status',
              widget.fileModel.downloadState.label,
              _getDownloadStateIcon(widget.fileModel.downloadState),
              theme,
            ),

          // Created date
          if (widget.fileModel.createdAt != null)
            _buildDetailRow(
              'Created',
              _formatDate(widget.fileModel.createdAt!),
              Symbols.schedule,
              theme,
            ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(
    String label,
    String value,
    IconData icon,
    ThemeData theme,
  ) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 8.h),
      child: Row(
        children: [
          Icon(icon, color: Colors.white70, size: 18.sp),
          SizedBox(width: 12.w),
          Text(
            '$label:',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: Colors.white70,
              fontWeight: FontWeight.w500,
            ),
          ),
          const Spacer(),
          Text(
            value,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons(ThemeData theme) {
    return Column(
      children: [
        // Download button (for remote files)
        if (!widget.fileModel.isLocal && 
            widget.fileModel.downloadState != DownloadState.downloaded)
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: _isDownloading ? null : _downloadFile,
              icon: _isDownloading
                  ? SizedBox(
                      width: 16.w,
                      height: 16.w,
                      child: const CircularProgressIndicator(strokeWidth: 2),
                    )
                  : const Icon(Symbols.download),
              label: Text(_isDownloading ? 'Downloading...' : 'Download'),
              style: ElevatedButton.styleFrom(
                padding: EdgeInsets.symmetric(vertical: 16.h),
              ),
            ),
          ),

        SizedBox(height: 12.h),

        // Open externally button
        SizedBox(
          width: double.infinity,
          child: OutlinedButton.icon(
            onPressed: _openExternally,
            icon: const Icon(Symbols.open_in_new),
            label: const Text('Open with External App'),
            style: OutlinedButton.styleFrom(
              padding: EdgeInsets.symmetric(vertical: 16.h),
              side: const BorderSide(color: Colors.white54),
              foregroundColor: Colors.white,
            ),
          ),
        ),

        SizedBox(height: 12.h),

        // Share button
        SizedBox(
          width: double.infinity,
          child: OutlinedButton.icon(
            onPressed: _shareFile,
            icon: const Icon(Symbols.share),
            label: const Text('Share'),
            style: OutlinedButton.styleFrom(
              padding: EdgeInsets.symmetric(vertical: 16.h),
              side: const BorderSide(color: Colors.white54),
              foregroundColor: Colors.white,
            ),
          ),
        ),
      ],
    );
  }

  IconData _getDownloadStateIcon(DownloadState state) {
    switch (state) {
      case DownloadState.notDownloaded:
        return Symbols.cloud_download;
      case DownloadState.downloading:
        return Symbols.downloading;
      case DownloadState.downloaded:
        return Symbols.download_done;
      case DownloadState.failed:
        return Symbols.error;
      case DownloadState.cancelled:
        return Symbols.cancel;
      case DownloadState.paused:
        return Symbols.pause;
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  void _downloadFile() async {
    setState(() {
      _isDownloading = true;
    });

    try {
      await _downloadService.downloadFile(widget.fileModel);
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Download failed: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isDownloading = false;
        });
      }
    }
  }

  void _openExternally() async {
    try {
      await _viewerService.openFileExternally(widget.fileModel);
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to open file: $e')),
        );
      }
    }
  }

  void _shareFile() async {
    try {
      if (widget.fileModel.isLocal && widget.fileModel.localPath != null) {
        await Share.shareXFiles([XFile(widget.fileModel.localPath!)]);
      } else if (widget.fileModel.url != null) {
        await Share.share(widget.fileModel.url!);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to share file: $e')),
        );
      }
    }
  }
}
