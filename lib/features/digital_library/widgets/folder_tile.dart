import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_symbols_icons/symbols.dart';

import '../enums/folder_type.dart';
import '../enums/file_access_type.dart';
import '../models/library_folder_model.dart';

/// Tile widget for displaying folder information in lists
class FolderTile extends StatelessWidget {
  final LibraryFolderModel folder;
  final VoidCallback? onTap;
  final VoidCallback? onLongPress;
  final bool showAccessType;
  final bool showStats;

  const FolderTile({
    super.key,
    required this.folder,
    this.onTap,
    this.onLongPress,
    this.showAccessType = true,
    this.showStats = true,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      margin: EdgeInsets.symmetric(vertical: 4.h),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: theme.dividerColor, width: 1),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          onLongPress: onLongPress,
          borderRadius: BorderRadius.circular(12.r),
          child: Padding(
            padding: EdgeInsets.all(16.w),
            child: Row(
              children: [
                // Folder icon with standard design
                Container(
                  width: 48.w,
                  height: 48.w,
                  decoration: BoxDecoration(
                    color: theme.colorScheme.primaryContainer.withValues(
                      alpha: 0.3,
                    ),
                    borderRadius: BorderRadius.circular(12.r),
                  ),
                  child: Icon(
                    Symbols.folder,
                    color: theme.colorScheme.primary,
                    size: 24.sp,
                  ),
                ),

                SizedBox(width: 12.w),

                // Folder info
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Folder name and badges
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              folder.name,
                              style: theme.textTheme.bodyLarge?.copyWith(
                                fontWeight: FontWeight.w600,
                                color: theme.colorScheme.onSurface,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),

                          // Badges
                          if (folder.isPinned) ...[
                            SizedBox(width: 8.w),
                            Icon(
                              Symbols.push_pin,
                              size: 16.sp,
                              color: theme.colorScheme.primary,
                            ),
                          ],

                          if (folder.isFavorite) ...[
                            SizedBox(width: 8.w),
                            Icon(
                              Symbols.favorite,
                              size: 16.sp,
                              color: theme.colorScheme.secondary,
                            ),
                          ],
                        ],
                      ),

                      SizedBox(height: 4.h),

                      // Description or folder type
                      Text(
                        folder.description ?? folder.folderType.description,
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.colorScheme.onSurface.withValues(
                            alpha: 0.7,
                          ),
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),

                      if (showStats) ...[
                        SizedBox(height: 8.h),

                        // Stats row
                        Row(
                          children: [
                            // File count
                            Icon(
                              Symbols.description,
                              size: 14.sp,
                              color: theme.colorScheme.onSurface.withValues(
                                alpha: 0.6,
                              ),
                            ),
                            SizedBox(width: 4.w),
                            Text(
                              '${folder.fileCount} files',
                              style: theme.textTheme.bodySmall?.copyWith(
                                color: theme.colorScheme.onSurface.withValues(
                                  alpha: 0.7,
                                ),
                              ),
                            ),

                            if (folder.subfolderCount > 0) ...[
                              SizedBox(width: 12.w),
                              Icon(
                                Symbols.folder,
                                size: 14.sp,
                                color: theme.colorScheme.onSurface.withValues(
                                  alpha: 0.6,
                                ),
                              ),
                              SizedBox(width: 4.w),
                              Text(
                                '${folder.subfolderCount} folders',
                                style: theme.textTheme.bodySmall?.copyWith(
                                  color: theme.colorScheme.onSurface.withValues(
                                    alpha: 0.7,
                                  ),
                                ),
                              ),
                            ],

                            const Spacer(),

                            // Last modified
                            if (folder.modifiedAt != null)
                              Text(
                                _formatDate(folder.modifiedAt!),
                                style: theme.textTheme.bodySmall?.copyWith(
                                  color: theme.colorScheme.onSurface.withValues(
                                    alpha: 0.7,
                                  ),
                                ),
                              ),
                          ],
                        ),
                      ],
                    ],
                  ),
                ),

                // Access type indicator
                if (showAccessType) ...[
                  SizedBox(width: 8.w),
                  Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: 8.w,
                      vertical: 4.h,
                    ),
                    decoration: BoxDecoration(
                      color: folder.accessType.color.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(6.r),
                    ),
                    child: Icon(
                      folder.accessType.icon,
                      size: 16.sp,
                      color: folder.accessType.color,
                    ),
                  ),
                ],

                // Chevron
                SizedBox(width: 8.w),
                Icon(
                  Symbols.chevron_right,
                  size: 20.sp,
                  color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays == 0) {
      return 'Today';
    } else if (difference.inDays == 1) {
      return 'Yesterday';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} days ago';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }
}

/// Compact folder tile for use in dropdowns or smaller spaces
class CompactFolderTile extends StatelessWidget {
  final LibraryFolderModel folder;
  final VoidCallback? onTap;
  final bool isSelected;

  const CompactFolderTile({
    super.key,
    required this.folder,
    this.onTap,
    this.isSelected = false,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        child: Container(
          padding: EdgeInsets.symmetric(vertical: 12.h),
          decoration: BoxDecoration(
            color: isSelected
                ? theme.colorScheme.primary.withValues(alpha: 0.1)
                : Colors.transparent,
          ),
          child: Row(
            children: [
              Icon(
                Symbols.folder,
                color: theme.colorScheme.primary,
                size: 20.sp,
              ),

              SizedBox(width: 12.w),

              Expanded(
                child: Text(
                  folder.name,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: theme.colorScheme.onSurface,
                    fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),

              if (isSelected)
                Icon(
                  Symbols.check,
                  size: 20.sp,
                  color: theme.colorScheme.primary,
                ),
            ],
          ),
        ),
      ),
    );
  }
}
