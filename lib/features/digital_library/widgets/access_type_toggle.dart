import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../core/theme/app_colors.dart';
import '../enums/file_access_type.dart';

/// Toggle widget for selecting file access type
class AccessTypeToggle extends StatelessWidget {
  final FileAccessType selectedType;
  final ValueChanged<FileAccessType> onChanged;
  final List<FileAccessType> availableTypes;
  final bool showDescriptions;

  const AccessTypeToggle({
    super.key,
    required this.selectedType,
    required this.onChanged,
    this.availableTypes = const [
      FileAccessType.publicDownloadable,
      FileAccessType.publicViewOnly,
      FileAccessType.private,
      FileAccessType.restricted,
    ],
    this.showDescriptions = true,
  });

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Access Type',
          style: Theme.of(context).textTheme.bodyLarge?.copyWith(
            fontWeight: FontWeight.w600,
            color: isDark
                ? AppColors.textPrimaryDark
                : AppColors.textPrimaryLight,
          ),
        ),

        SizedBox(height: 12.h),

        ...availableTypes.map(
          (type) => _buildAccessTypeOption(context, type, isDark),
        ),
      ],
    );
  }

  Widget _buildAccessTypeOption(
    BuildContext context,
    FileAccessType type,
    bool isDark,
  ) {
    final isSelected = selectedType == type;

    return Container(
      margin: EdgeInsets.only(bottom: 8.h),
      decoration: BoxDecoration(
        color: isSelected
            ? (isDark ? AppColors.primaryDark : AppColors.primaryLight)
                  .withValues(alpha: 0.1)
            : Colors.transparent,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(
          color: isSelected
              ? (isDark ? AppColors.primaryDark : AppColors.primaryLight)
              : (isDark ? AppColors.borderDark : AppColors.borderLight),
          width: isSelected ? 2 : 1,
        ),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => onChanged(type),
          borderRadius: BorderRadius.circular(12.r),
          child: Padding(
            padding: EdgeInsets.all(16.w),
            child: Row(
              children: [
                // Access type icon
                Container(
                  width: 40.w,
                  height: 40.w,
                  decoration: BoxDecoration(
                    color: type.color.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8.r),
                  ),
                  child: Icon(type.icon, color: type.color, size: 20.sp),
                ),

                SizedBox(width: 12.w),

                // Access type info
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        type.label,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                          color: isDark
                              ? AppColors.textPrimaryDark
                              : AppColors.textPrimaryLight,
                        ),
                      ),

                      if (showDescriptions) ...[
                        SizedBox(height: 4.h),
                        Text(
                          type.description,
                          style: Theme.of(context).textTheme.bodySmall
                              ?.copyWith(
                                color: isDark
                                    ? AppColors.textSecondaryDark
                                    : AppColors.textSecondaryLight,
                              ),
                        ),
                      ],
                    ],
                  ),
                ),

                // Selection indicator
                Container(
                  width: 20.w,
                  height: 20.w,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: isSelected
                          ? (isDark
                                ? AppColors.primaryDark
                                : AppColors.primaryLight)
                          : (isDark
                                ? AppColors.borderDark
                                : AppColors.borderLight),
                      width: 2,
                    ),
                    color: isSelected
                        ? (isDark
                              ? AppColors.primaryDark
                              : AppColors.primaryLight)
                        : Colors.transparent,
                  ),
                  child: isSelected
                      ? Icon(Icons.check, size: 12.sp, color: Colors.white)
                      : null,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

/// Compact access type toggle for smaller spaces
class CompactAccessTypeToggle extends StatelessWidget {
  final FileAccessType selectedType;
  final ValueChanged<FileAccessType> onChanged;
  final List<FileAccessType> availableTypes;

  const CompactAccessTypeToggle({
    super.key,
    required this.selectedType,
    required this.onChanged,
    this.availableTypes = const [
      FileAccessType.publicDownloadable,
      FileAccessType.private,
    ],
  });

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Container(
      height: 48.h,
      decoration: BoxDecoration(
        color: isDark ? AppColors.surfaceDark : AppColors.surfaceLight,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(
          color: isDark ? AppColors.borderDark : AppColors.borderLight,
          width: 1,
        ),
      ),
      child: Row(
        children: availableTypes.asMap().entries.map((entry) {
          final index = entry.key;
          final type = entry.value;
          final isSelected = selectedType == type;
          final isFirst = index == 0;
          final isLast = index == availableTypes.length - 1;

          return Expanded(
            child: GestureDetector(
              onTap: () => onChanged(type),
              child: Container(
                decoration: BoxDecoration(
                  color: isSelected
                      ? (isDark
                            ? AppColors.primaryDark
                            : AppColors.primaryLight)
                      : Colors.transparent,
                  borderRadius: BorderRadius.horizontal(
                    left: isFirst ? Radius.circular(11.r) : Radius.zero,
                    right: isLast ? Radius.circular(11.r) : Radius.zero,
                  ),
                ),
                child: Center(
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        type.icon,
                        size: 16.sp,
                        color: isSelected ? Colors.white : type.color,
                      ),
                      SizedBox(width: 6.w),
                      Text(
                        type.label,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: isSelected
                              ? Colors.white
                              : (isDark
                                    ? AppColors.textSecondaryDark
                                    : AppColors.textSecondaryLight),
                          fontWeight: isSelected
                              ? FontWeight.w600
                              : FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          );
        }).toList(),
      ),
    );
  }
}

/// Dropdown style access type selector
class AccessTypeDropdown extends StatelessWidget {
  final FileAccessType selectedType;
  final ValueChanged<FileAccessType> onChanged;
  final List<FileAccessType> availableTypes;

  const AccessTypeDropdown({
    super.key,
    required this.selectedType,
    required this.onChanged,
    this.availableTypes = const [
      FileAccessType.publicDownloadable,
      FileAccessType.publicViewOnly,
      FileAccessType.private,
      FileAccessType.restricted,
    ],
  });

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 4.h),
      decoration: BoxDecoration(
        color: isDark ? AppColors.surfaceDark : AppColors.surfaceLight,
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(
          color: isDark ? AppColors.borderDark : AppColors.borderLight,
          width: 1,
        ),
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<FileAccessType>(
          value: selectedType,
          onChanged: (type) {
            if (type != null) onChanged(type);
          },
          icon: Icon(
            Icons.keyboard_arrow_down,
            color: isDark
                ? AppColors.textSecondaryDark
                : AppColors.textSecondaryLight,
          ),
          items: availableTypes
              .map(
                (type) => DropdownMenuItem(
                  value: type,
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(type.icon, size: 16.sp, color: type.color),
                      SizedBox(width: 8.w),
                      Text(
                        type.label,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: isDark
                              ? AppColors.textPrimaryDark
                              : AppColors.textPrimaryLight,
                        ),
                      ),
                    ],
                  ),
                ),
              )
              .toList(),
        ),
      ),
    );
  }
}
