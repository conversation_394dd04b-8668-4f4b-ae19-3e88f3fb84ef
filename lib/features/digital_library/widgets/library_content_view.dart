import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_symbols_icons/symbols.dart';

import '../models/library_folder_model.dart';
import '../models/library_file_model.dart';
import '../enums/file_usage_type.dart';
import '../controllers/digital_library_controller.dart';
import 'folder_tile.dart';
import 'file_list_tile.dart';
import 'filter_tabs.dart';

/// Unified content view for displaying folders and files
/// Used by both AllItemsScreen and FolderDetailScreen
class LibraryContentView extends ConsumerStatefulWidget {
  final String? parentFolderId; // null for root level (all items)
  final bool showTabs; // whether to show folders/files tabs
  final bool showFilters; // whether to show usage type filters
  final String? initialFilter; // 'folders', 'files', or null
  final FileUsageType? initialUsageType;
  final Function(String folderId)? onFolderTap;
  final Function(String fileId)? onFileTap;
  final Widget? header; // optional header widget (for folder details)

  const LibraryContentView({
    super.key,
    this.parentFolderId,
    this.showTabs = true,
    this.showFilters = true,
    this.initialFilter,
    this.initialUsageType,
    this.onFolderTap,
    this.onFileTap,
    this.header,
  });

  @override
  ConsumerState<LibraryContentView> createState() => _LibraryContentViewState();
}

class _LibraryContentViewState extends ConsumerState<LibraryContentView>
    with SingleTickerProviderStateMixin {
  TabController? _tabController;
  int _selectedFilterIndex = 0;
  FileUsageType? _selectedUsageType;

  @override
  void initState() {
    super.initState();
    
    if (widget.showTabs) {
      _tabController = TabController(length: 2, vsync: this);
      
      // Set initial tab based on initialFilter
      if (widget.initialFilter == 'folders') {
        _tabController!.index = 0;
      } else if (widget.initialFilter == 'files') {
        _tabController!.index = 1;
      }
    }

    // Set initial usage type filter
    _selectedUsageType = widget.initialUsageType;
    if (_selectedUsageType != null) {
      _selectedFilterIndex = _getFilterIndexFromUsageType(_selectedUsageType);
    }
  }

  @override
  void dispose() {
    _tabController?.dispose();
    super.dispose();
  }

  void _onFilterChanged(int index) {
    setState(() {
      _selectedFilterIndex = index;
      _selectedUsageType = _getUsageTypeFromIndex(index);
    });
  }

  FileUsageType? _getUsageTypeFromIndex(int index) {
    switch (index) {
      case 1: return FileUsageType.personal;
      case 2: return FileUsageType.shared;
      case 3: return FileUsageType.classResource;
      case 4: return FileUsageType.homeworkSubmission;
      case 5: return FileUsageType.library;
      default: return null; // All
    }
  }

  int _getFilterIndexFromUsageType(FileUsageType? usageType) {
    if (usageType == null) return 0;
    switch (usageType) {
      case FileUsageType.personal: return 1;
      case FileUsageType.shared: return 2;
      case FileUsageType.classResource: return 3;
      case FileUsageType.homeworkSubmission: return 4;
      case FileUsageType.library: return 5;
      default: return 0;
    }
  }

  @override
  Widget build(BuildContext context) {
    if (widget.showTabs) {
      return _buildTabbedView();
    } else {
      return _buildSingleView();
    }
  }

  Widget _buildTabbedView() {
    return Column(
      children: [
        if (widget.header != null) widget.header!,
        
        if (widget.showFilters)
          Padding(
            padding: EdgeInsets.symmetric(vertical: 16.h),
            child: FilterTabs(
              tabs: const ['All', 'Personal', 'Shared', 'Class', 'Homework', 'Library'],
              selectedIndex: _selectedFilterIndex,
              onTabSelected: _onFilterChanged,
            ),
          ),

        Expanded(
          child: TabBarView(
            controller: _tabController!,
            children: [
              _buildFoldersContent(),
              _buildFilesContent(),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildSingleView() {
    return Column(
      children: [
        if (widget.header != null) widget.header!,
        
        if (widget.showFilters)
          Padding(
            padding: EdgeInsets.symmetric(vertical: 16.h),
            child: FilterTabs(
              tabs: const ['All', 'Personal', 'Shared', 'Class', 'Homework', 'Library'],
              selectedIndex: _selectedFilterIndex,
              onTabSelected: _onFilterChanged,
            ),
          ),

        Expanded(
          child: _buildMixedContent(),
        ),
      ],
    );
  }

  Widget _buildFoldersContent() {
    final foldersAsync = _getFoldersProvider();
    
    return foldersAsync.when(
      data: (folders) => _buildFoldersList(folders),
      loading: () => _buildLoadingState(),
      error: (error, stack) => _buildErrorState('Failed to load folders'),
    );
  }

  Widget _buildFilesContent() {
    final filesAsync = _getFilesProvider();
    
    return filesAsync.when(
      data: (files) => _buildFilesList(files),
      loading: () => _buildLoadingState(),
      error: (error, stack) => _buildErrorState('Failed to load files'),
    );
  }

  Widget _buildMixedContent() {
    final foldersAsync = _getFoldersProvider();
    final filesAsync = _getFilesProvider();
    
    return foldersAsync.when(
      data: (folders) => filesAsync.when(
        data: (files) => _buildMixedList(folders, files),
        loading: () => _buildLoadingState(),
        error: (error, stack) => _buildErrorState('Failed to load files'),
      ),
      loading: () => _buildLoadingState(),
      error: (error, stack) => _buildErrorState('Failed to load content'),
    );
  }

  AsyncValue<List<LibraryFolderModel>> _getFoldersProvider() {
    if (widget.parentFolderId != null) {
      return ref.watch(subFoldersProvider(widget.parentFolderId!));
    } else if (_selectedUsageType != null) {
      return ref.watch(foldersByUsageTypeProvider(_selectedUsageType!));
    } else {
      return ref.watch(rootFoldersProvider);
    }
  }

  AsyncValue<List<LibraryFileModel>> _getFilesProvider() {
    if (widget.parentFolderId != null) {
      return ref.watch(folderFilesProvider(widget.parentFolderId!));
    } else if (_selectedUsageType != null) {
      return ref.watch(filesByUsageTypeProvider(_selectedUsageType!));
    } else {
      return ref.watch(userFilesProvider);
    }
  }

  Widget _buildFoldersList(List<LibraryFolderModel> folders) {
    if (folders.isEmpty) {
      return _buildEmptyState('No folders found');
    }

    return ListView.builder(
      padding: EdgeInsets.all(16.w),
      itemCount: folders.length,
      itemBuilder: (context, index) {
        final folder = folders[index];
        return Padding(
          padding: EdgeInsets.only(bottom: 12.h),
          child: FolderTile(
            folder: folder,
            onTap: () => widget.onFolderTap?.call(folder.id),
          ),
        );
      },
    );
  }

  Widget _buildFilesList(List<LibraryFileModel> files) {
    if (files.isEmpty) {
      return _buildEmptyState('No files found');
    }

    return ListView.builder(
      padding: EdgeInsets.all(16.w),
      itemCount: files.length,
      itemBuilder: (context, index) {
        final file = files[index];
        return Padding(
          padding: EdgeInsets.only(bottom: 8.h),
          child: FileListTile(
            file: file,
            onTap: () => widget.onFileTap?.call(file.id),
          ),
        );
      },
    );
  }

  Widget _buildMixedList(List<LibraryFolderModel> folders, List<LibraryFileModel> files) {
    if (folders.isEmpty && files.isEmpty) {
      return _buildEmptyState('No items found');
    }

    return ListView.builder(
      padding: EdgeInsets.all(16.w),
      itemCount: folders.length + files.length,
      itemBuilder: (context, index) {
        if (index < folders.length) {
          final folder = folders[index];
          return Padding(
            padding: EdgeInsets.only(bottom: 12.h),
            child: FolderTile(
              folder: folder,
              onTap: () => widget.onFolderTap?.call(folder.id),
            ),
          );
        } else {
          final file = files[index - folders.length];
          return Padding(
            padding: EdgeInsets.only(bottom: 8.h),
            child: FileListTile(
              file: file,
              onTap: () => widget.onFileTap?.call(file.id),
            ),
          );
        }
      },
    );
  }

  Widget _buildLoadingState() {
    return const Center(child: CircularProgressIndicator());
  }

  Widget _buildErrorState(String message) {
    final theme = Theme.of(context);

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Symbols.error, size: 48.sp, color: theme.colorScheme.error),
          SizedBox(height: 16.h),
          Text(
            message,
            style: theme.textTheme.bodyLarge?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 16.h),
          ElevatedButton(
            onPressed: () {
              ref.invalidate(userFoldersProvider);
              ref.invalidate(userFilesProvider);
              ref.invalidate(rootFoldersProvider);
            },
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState(String message) {
    final theme = Theme.of(context);

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Symbols.folder_open,
            size: 48.sp,
            color: theme.colorScheme.onSurfaceVariant,
          ),
          SizedBox(height: 16.h),
          Text(
            message,
            style: theme.textTheme.bodyLarge?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
