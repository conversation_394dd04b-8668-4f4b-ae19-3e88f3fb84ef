import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_pdfview/flutter_pdfview.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_symbols_icons/symbols.dart';
import '../models/file_model.dart';

/// Widget for viewing PDF files
class PDFViewerWidget extends StatefulWidget {
  /// The file model containing the PDF to view
  final FileModel fileModel;

  const PDFViewerWidget({
    super.key,
    required this.fileModel,
  });

  @override
  State<PDFViewerWidget> createState() => _PDFViewerWidgetState();
}

class _PDFViewerWidgetState extends State<PDFViewerWidget> {
  PDFViewController? _pdfViewController;
  bool _isLoading = true;
  String? _errorMessage;
  int _currentPage = 1;
  int _totalPages = 0;
  bool _showControls = true;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    // Check if we have a valid local path for the PDF
    if (widget.fileModel.localPath == null) {
      return _buildErrorView('PDF file not available locally');
    }

    if (!File(widget.fileModel.localPath!).existsSync()) {
      return _buildErrorView('PDF file not found');
    }

    return Stack(
      children: [
        // PDF Viewer
        PDFView(
          filePath: widget.fileModel.localPath!,
          enableSwipe: true,
          swipeHorizontal: false,
          autoSpacing: true,
          pageFling: true,
          pageSnap: true,
          defaultPage: 0,
          fitPolicy: FitPolicy.BOTH,
          preventLinkNavigation: false,
          backgroundColor: Colors.black,
          onRender: (pages) {
            setState(() {
              _totalPages = pages ?? 0;
              _isLoading = false;
            });
          },
          onError: (error) {
            setState(() {
              _isLoading = false;
              _errorMessage = 'Failed to load PDF: $error';
            });
          },
          onPageError: (page, error) {
            setState(() {
              _errorMessage = 'Error loading page $page: $error';
            });
          },
          onViewCreated: (PDFViewController pdfViewController) {
            _pdfViewController = pdfViewController;
          },
          onPageChanged: (page, total) {
            setState(() {
              _currentPage = (page ?? 0) + 1;
              _totalPages = total ?? 0;
            });
          },
        ),

        // Loading overlay
        if (_isLoading)
          Container(
            color: Colors.black54,
            child: const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(color: Colors.white),
                  SizedBox(height: 16),
                  Text(
                    'Loading PDF...',
                    style: TextStyle(color: Colors.white),
                  ),
                ],
              ),
            ),
          ),

        // Error overlay
        if (_errorMessage != null)
          Container(
            color: Colors.black54,
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(
                    Symbols.error,
                    size: 64,
                    color: Colors.white54,
                  ),
                  SizedBox(height: 16.h),
                  Text(
                    'Error Loading PDF',
                    style: theme.textTheme.titleLarge?.copyWith(
                      color: Colors.white,
                    ),
                  ),
                  SizedBox(height: 8.h),
                  Text(
                    _errorMessage!,
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: Colors.white70,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ),

        // Controls overlay
        if (_showControls && !_isLoading && _errorMessage == null)
          Positioned(
            bottom: 20,
            left: 20,
            right: 20,
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
              decoration: BoxDecoration(
                color: Colors.black54,
                borderRadius: BorderRadius.circular(8.r),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  // Previous page button
                  IconButton(
                    onPressed: _currentPage > 1 ? _goToPreviousPage : null,
                    icon: const Icon(Symbols.chevron_left),
                    color: Colors.white,
                    iconSize: 24.sp,
                  ),

                  // Page indicator
                  Container(
                    padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
                    decoration: BoxDecoration(
                      color: Colors.black26,
                      borderRadius: BorderRadius.circular(4.r),
                    ),
                    child: Text(
                      '$_currentPage / $_totalPages',
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),

                  // Next page button
                  IconButton(
                    onPressed: _currentPage < _totalPages ? _goToNextPage : null,
                    icon: const Icon(Symbols.chevron_right),
                    color: Colors.white,
                    iconSize: 24.sp,
                  ),
                ],
              ),
            ),
          ),

        // Tap to toggle controls
        GestureDetector(
          onTap: _toggleControls,
          behavior: HitTestBehavior.translucent,
          child: Container(),
        ),
      ],
    );
  }

  /// Build error view
  Widget _buildErrorView(String message) {
    final theme = Theme.of(context);
    
    return Container(
      color: Colors.black,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Symbols.picture_as_pdf,
              size: 64,
              color: Colors.white54,
            ),
            SizedBox(height: 16.h),
            Text(
              'Cannot Load PDF',
              style: theme.textTheme.titleLarge?.copyWith(
                color: Colors.white,
              ),
            ),
            SizedBox(height: 8.h),
            Text(
              message,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: Colors.white70,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  /// Go to previous page
  void _goToPreviousPage() {
    if (_pdfViewController != null && _currentPage > 1) {
      _pdfViewController!.setPage(_currentPage - 2); // 0-based index
    }
  }

  /// Go to next page
  void _goToNextPage() {
    if (_pdfViewController != null && _currentPage < _totalPages) {
      _pdfViewController!.setPage(_currentPage); // 0-based index
    }
  }

  /// Toggle controls visibility
  void _toggleControls() {
    setState(() {
      _showControls = !_showControls;
    });
  }

  /// Go to specific page
  void goToPage(int page) {
    if (_pdfViewController != null && page >= 1 && page <= _totalPages) {
      _pdfViewController!.setPage(page - 1); // 0-based index
    }
  }

  /// Get current page number
  int get currentPage => _currentPage;

  /// Get total pages
  int get totalPages => _totalPages;

  /// Check if PDF is loaded
  bool get isLoaded => !_isLoading && _errorMessage == null;
}
