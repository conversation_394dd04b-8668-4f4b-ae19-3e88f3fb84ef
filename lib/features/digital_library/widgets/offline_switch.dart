import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_symbols_icons/symbols.dart';

/// Switch widget for enabling/disabling offline access
class OfflineSwitch extends StatelessWidget {
  final bool value;
  final ValueChanged<bool> onChanged;
  final String? title;
  final String? description;
  final bool showIcon;
  final bool enabled;

  const OfflineSwitch({
    super.key,
    required this.value,
    required this.onChanged,
    this.title = 'Enable Offline Access',
    this.description = 'Download file for offline viewing',
    this.showIcon = true,
    this.enabled = true,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: theme.dividerColor, width: 1),
      ),
      child: Row(
        children: [
          // Icon
          if (showIcon) ...[
            Container(
              width: 40.w,
              height: 40.w,
              decoration: BoxDecoration(
                color: value
                    ? theme.colorScheme.primary.withValues(alpha: 0.1)
                    : theme.colorScheme.onSurface.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8.r),
              ),
              child: Icon(
                value ? Symbols.offline_pin : Symbols.download_for_offline,
                color: value
                    ? theme.colorScheme.primary
                    : theme.colorScheme.onSurface.withValues(alpha: 0.6),
                size: 20.sp,
              ),
            ),
            SizedBox(width: 12.w),
          ],

          // Text content
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (title != null)
                  Text(
                    title!,
                    style: theme.textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: enabled
                          ? theme.colorScheme.onSurface
                          : theme.colorScheme.onSurface.withValues(alpha: 0.6),
                    ),
                  ),

                if (description != null) ...[
                  SizedBox(height: 4.h),
                  Text(
                    description!,
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                    ),
                  ),
                ],
              ],
            ),
          ),

          // Switch
          Switch(
            value: value,
            onChanged: enabled ? onChanged : null,
            activeColor: theme.colorScheme.primary,
            inactiveThumbColor: theme.dividerColor,
            inactiveTrackColor: theme.dividerColor.withValues(alpha: 0.3),
          ),
        ],
      ),
    );
  }
}

/// Compact offline switch for smaller spaces
class CompactOfflineSwitch extends StatelessWidget {
  final bool value;
  final ValueChanged<bool> onChanged;
  final bool enabled;

  const CompactOfflineSwitch({
    super.key,
    required this.value,
    required this.onChanged,
    this.enabled = true,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(
          value ? Symbols.offline_pin : Symbols.download_for_offline,
          color: value
              ? theme.colorScheme.primary
              : theme.colorScheme.onSurface.withValues(alpha: 0.6),
          size: 20.sp,
        ),

        SizedBox(width: 8.w),

        Text(
          'Offline',
          style: theme.textTheme.bodySmall?.copyWith(
            color: enabled
                ? theme.colorScheme.onSurface
                : theme.colorScheme.onSurface.withValues(alpha: 0.6),
            fontWeight: FontWeight.w500,
          ),
        ),

        SizedBox(width: 8.w),

        Switch(
          value: value,
          onChanged: enabled ? onChanged : null,
          activeColor: theme.colorScheme.primary,
          inactiveThumbColor: theme.dividerColor,
          inactiveTrackColor: theme.dividerColor.withValues(alpha: 0.3),
          materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
        ),
      ],
    );
  }
}

/// Offline switch with storage info
class OfflineSwitchWithInfo extends StatelessWidget {
  final bool value;
  final ValueChanged<bool> onChanged;
  final String? storageUsed;
  final String? estimatedSize;
  final bool enabled;

  const OfflineSwitchWithInfo({
    super.key,
    required this.value,
    required this.onChanged,
    this.storageUsed,
    this.estimatedSize,
    this.enabled = true,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: theme.dividerColor, width: 1),
      ),
      child: Column(
        children: [
          Row(
            children: [
              // Icon
              Container(
                width: 40.w,
                height: 40.w,
                decoration: BoxDecoration(
                  color: value
                      ? theme.colorScheme.primary.withValues(alpha: 0.1)
                      : theme.colorScheme.onSurface.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8.r),
                ),
                child: Icon(
                  value ? Symbols.offline_pin : Symbols.download_for_offline,
                  color: value
                      ? theme.colorScheme.primary
                      : theme.colorScheme.onSurface.withValues(alpha: 0.6),
                  size: 20.sp,
                ),
              ),

              SizedBox(width: 12.w),

              // Text content
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Enable Offline Access',
                      style: theme.textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: enabled
                            ? theme.colorScheme.onSurface
                            : theme.colorScheme.onSurface.withValues(
                                alpha: 0.6,
                              ),
                      ),
                    ),

                    SizedBox(height: 4.h),

                    Text(
                      value
                          ? 'File will be available offline'
                          : 'Download file for offline viewing',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.onSurface.withValues(
                          alpha: 0.6,
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              // Switch
              Switch(
                value: value,
                onChanged: enabled ? onChanged : null,
                activeColor: theme.colorScheme.primary,
                inactiveThumbColor: theme.dividerColor,
                inactiveTrackColor: theme.dividerColor.withValues(alpha: 0.3),
              ),
            ],
          ),

          // Storage info
          if (value && (storageUsed != null || estimatedSize != null)) ...[
            SizedBox(height: 12.h),
            Container(
              padding: EdgeInsets.all(12.w),
              decoration: BoxDecoration(
                color: theme.colorScheme.primary.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8.r),
              ),
              child: Row(
                children: [
                  Icon(
                    Symbols.storage,
                    size: 16.sp,
                    color: theme.colorScheme.primary,
                  ),

                  SizedBox(width: 8.w),

                  Expanded(
                    child: Text(
                      storageUsed != null
                          ? 'Storage used: $storageUsed'
                          : 'Estimated size: ${estimatedSize ?? 'Unknown'}',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.primary,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }
}
