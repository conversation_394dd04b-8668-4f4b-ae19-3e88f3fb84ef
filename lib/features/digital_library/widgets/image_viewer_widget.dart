import 'dart:io';
import 'package:flutter/material.dart';
import 'package:photo_view/photo_view.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:material_symbols_icons/symbols.dart';
import '../models/file_model.dart';

/// Widget for viewing images with zoom and pan capabilities
class ImageViewerWidget extends StatefulWidget {
  /// The file model containing the image to view
  final FileModel fileModel;

  const ImageViewerWidget({super.key, required this.fileModel});

  @override
  State<ImageViewerWidget> createState() => _ImageViewerWidgetState();
}

class _ImageViewerWidgetState extends State<ImageViewerWidget> {
  late PhotoViewController _photoViewController;
  bool _isLoading = true;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _photoViewController = PhotoViewController();
  }

  @override
  void dispose() {
    _photoViewController.dispose();
    super.dispose();
  }

  /// Build image provider based on file type (local or remote)
  ImageProvider _buildImageProvider() {
    if (widget.fileModel.isLocal && widget.fileModel.localPath != null) {
      return FileImage(File(widget.fileModel.localPath!));
    } else if (widget.fileModel.url != null) {
      return CachedNetworkImageProvider(widget.fileModel.url!);
    } else {
      throw Exception('No valid image source available');
    }
  }

  /// Handle image load error
  void _onImageError(Object error) {
    if (mounted) {
      setState(() {
        _isLoading = false;
        _errorMessage = 'Failed to load image: $error';
      });
    }
  }

  /// Reset zoom to fit screen
  void _resetZoom() {
    _photoViewController.reset();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        // Main image viewer
        PhotoView(
          imageProvider: _buildImageProvider(),
          controller: _photoViewController,
          backgroundDecoration: const BoxDecoration(color: Colors.black),
          minScale: PhotoViewComputedScale.contained,
          maxScale: PhotoViewComputedScale.covered * 3.0,
          initialScale: PhotoViewComputedScale.contained,
          heroAttributes: PhotoViewHeroAttributes(tag: widget.fileModel.id),
          loadingBuilder: (context, event) {
            _isLoading = true;
            return const Center(
              child: CircularProgressIndicator(color: Colors.white),
            );
          },
          errorBuilder: (context, error, stackTrace) {
            _onImageError(error);
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(
                    Symbols.broken_image,
                    size: 64,
                    color: Colors.white54,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Failed to load image',
                    style: Theme.of(
                      context,
                    ).textTheme.bodyLarge?.copyWith(color: Colors.white54),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    _errorMessage ?? 'Unknown error',
                    style: Theme.of(
                      context,
                    ).textTheme.bodySmall?.copyWith(color: Colors.white38),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            );
          },
          onTapUp: (context, details, controllerValue) {
            // Handle tap to show/hide controls
            // This can be implemented based on your UI needs
          },
        ),

        // Zoom controls overlay
        if (!_isLoading && _errorMessage == null)
          Positioned(
            bottom: 20,
            right: 20,
            child: Column(
              children: [
                FloatingActionButton.small(
                  heroTag: 'zoom_in',
                  onPressed: () {
                    final currentScale = _photoViewController.scale ?? 1.0;
                    _photoViewController.scale = (currentScale * 1.5).clamp(
                      PhotoViewComputedScale.contained.multiplier,
                      PhotoViewComputedScale.covered.multiplier * 3.0,
                    );
                  },
                  backgroundColor: Colors.black54,
                  foregroundColor: Colors.white,
                  child: const Icon(Symbols.zoom_in),
                ),
                const SizedBox(height: 8),
                FloatingActionButton.small(
                  heroTag: 'zoom_out',
                  onPressed: () {
                    final currentScale = _photoViewController.scale ?? 1.0;
                    _photoViewController.scale = (currentScale / 1.5).clamp(
                      PhotoViewComputedScale.contained.multiplier,
                      PhotoViewComputedScale.covered.multiplier * 3.0,
                    );
                  },
                  backgroundColor: Colors.black54,
                  foregroundColor: Colors.white,
                  child: const Icon(Symbols.zoom_out),
                ),
                const SizedBox(height: 8),
                FloatingActionButton.small(
                  heroTag: 'reset_zoom',
                  onPressed: _resetZoom,
                  backgroundColor: Colors.black54,
                  foregroundColor: Colors.white,
                  child: const Icon(Symbols.fit_screen),
                ),
              ],
            ),
          ),

        // Loading indicator
        if (_isLoading)
          const Center(
            child: CircularProgressIndicator(color: Colors.white),
          ),
      ],
    );
  }
}

/// Gallery image viewer for multiple images
class GalleryImageViewerWidget extends StatefulWidget {
  /// List of file models containing images
  final List<FileModel> imageFiles;
  
  /// Initial index to display
  final int initialIndex;

  const GalleryImageViewerWidget({
    super.key,
    required this.imageFiles,
    this.initialIndex = 0,
  });

  @override
  State<GalleryImageViewerWidget> createState() => _GalleryImageViewerWidgetState();
}

class _GalleryImageViewerWidgetState extends State<GalleryImageViewerWidget> {
  late PageController _pageController;
  late int _currentIndex;

  @override
  void initState() {
    super.initState();
    _currentIndex = widget.initialIndex;
    _pageController = PageController(initialPage: widget.initialIndex);
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        // Image gallery
        PageView.builder(
          controller: _pageController,
          itemCount: widget.imageFiles.length,
          onPageChanged: (index) {
            setState(() {
              _currentIndex = index;
            });
          },
          itemBuilder: (context, index) {
            return ImageViewerWidget(
              fileModel: widget.imageFiles[index],
            );
          },
        ),

        // Image counter
        if (widget.imageFiles.length > 1)
          Positioned(
            top: 40,
            left: 20,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: Colors.black54,
                borderRadius: BorderRadius.circular(16),
              ),
              child: Text(
                '${_currentIndex + 1} / ${widget.imageFiles.length}',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),

        // Navigation arrows
        if (widget.imageFiles.length > 1) ...[
          // Previous button
          if (_currentIndex > 0)
            Positioned(
              left: 20,
              top: 0,
              bottom: 0,
              child: Center(
                child: FloatingActionButton.small(
                  heroTag: 'previous',
                  onPressed: () {
                    _pageController.previousPage(
                      duration: const Duration(milliseconds: 300),
                      curve: Curves.easeInOut,
                    );
                  },
                  backgroundColor: Colors.black54,
                  foregroundColor: Colors.white,
                  child: const Icon(Symbols.chevron_left),
                ),
              ),
            ),

          // Next button
          if (_currentIndex < widget.imageFiles.length - 1)
            Positioned(
              right: 20,
              top: 0,
              bottom: 0,
              child: Center(
                child: FloatingActionButton.small(
                  heroTag: 'next',
                  onPressed: () {
                    _pageController.nextPage(
                      duration: const Duration(milliseconds: 300),
                      curve: Curves.easeInOut,
                    );
                  },
                  backgroundColor: Colors.black54,
                  foregroundColor: Colors.white,
                  child: const Icon(Symbols.chevron_right),
                ),
              ),
            ),
        ],
      ],
    );
  }
}
