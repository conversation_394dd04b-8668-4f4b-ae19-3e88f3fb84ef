import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_symbols_icons/symbols.dart';

import '../models/library_folder_model.dart';
import '../enums/file_access_type.dart';
import '../enums/folder_type.dart';

/// Grid tile widget for displaying folders in a grid layout
/// Uses simplified icon system with consistent main folder icon and overlay badges
class FolderGridTile extends StatelessWidget {
  final LibraryFolderModel folder;
  final VoidCallback? onTap;
  final VoidCallback? onLongPress;

  const FolderGridTile({
    super.key,
    required this.folder,
    this.onTap,
    this.onLongPress,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(16.r),
        border: Border.all(color: theme.dividerColor, width: 1),
        boxShadow: [
          BoxShadow(
            color: theme.shadowColor.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          onLongPress: onLongPress,
          borderRadius: BorderRadius.circular(16.r),
          child: Padding(
            padding: EdgeInsets.all(16.w),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                // Folder icon with badges
                Row(
                  children: [
                    // Main folder icon
                    Stack(
                      children: [
                        Container(
                          width: 40.w,
                          height: 40.w,
                          decoration: BoxDecoration(
                            color: theme.colorScheme.primary.withValues(
                              alpha: 0.1,
                            ),
                            borderRadius: BorderRadius.circular(10.r),
                          ),
                          child: Icon(
                            Symbols.folder,
                            color: theme.colorScheme.primary,
                            size: 24.sp,
                          ),
                        ),

                        // Overlay badges
                        if (folder.accessType == FileAccessType.private)
                          Positioned(
                            right: -2,
                            top: -2,
                            child: Container(
                              width: 16.w,
                              height: 16.w,
                              decoration: BoxDecoration(
                                color: theme.colorScheme.surface,
                                shape: BoxShape.circle,
                                border: Border.all(
                                  color: theme.dividerColor,
                                  width: 1,
                                ),
                              ),
                              child: Icon(
                                Symbols.lock,
                                size: 10.sp,
                                color: theme.colorScheme.onSurface,
                              ),
                            ),
                          ),
                      ],
                    ),

                    const Spacer(),

                    // Additional badges
                    if (folder.isPinned)
                      Icon(
                        Symbols.push_pin,
                        size: 14.sp,
                        color: theme.colorScheme.primary,
                      ),
                  ],
                ),

                SizedBox(height: 8.h),

                // Folder name
                Flexible(
                  child: Text(
                    folder.name,
                    style: theme.textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: theme.colorScheme.onSurface,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),

                SizedBox(height: 2.h),

                // Folder stats
                Text(
                  '${folder.fileCount} files',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                  ),
                ),

                SizedBox(height: 6.h),

                // Folder type label
                Align(
                  alignment: Alignment.centerLeft,
                  child: Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: 6.w,
                      vertical: 2.h,
                    ),
                    decoration: BoxDecoration(
                      color: theme.colorScheme.surfaceContainerHighest
                          .withValues(alpha: 0.5),
                      borderRadius: BorderRadius.circular(6.r),
                    ),
                    child: Text(
                      folder.folderType.label,
                      style: theme.textTheme.labelSmall?.copyWith(
                        color: theme.colorScheme.onSurfaceVariant,
                        fontWeight: FontWeight.w500,
                        fontSize: 9.sp,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
