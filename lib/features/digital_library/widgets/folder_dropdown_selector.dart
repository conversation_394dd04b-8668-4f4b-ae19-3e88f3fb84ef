import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_symbols_icons/symbols.dart';

import '../../../core/theme/app_colors.dart';
import '../enums/folder_type.dart';
import '../enums/file_access_type.dart';
import '../models/library_folder_model.dart';
import '../widgets/folder_tile.dart';

/// Dropdown selector for choosing a folder destination
class FolderDropdownSelector extends StatelessWidget {
  final List<LibraryFolderModel> folders;
  final LibraryFolderModel? selectedFolder;
  final ValueChanged<LibraryFolderModel?> onChanged;
  final String? hint;
  final bool allowRootSelection;

  const FolderDropdownSelector({
    super.key,
    required this.folders,
    required this.selectedFolder,
    required this.onChanged,
    this.hint = 'Select folder',
    this.allowRootSelection = true,
  });

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Destination Folder',
          style: Theme.of(context).textTheme.bodyLarge?.copyWith(
            fontWeight: FontWeight.w600,
            color: isDark
                ? AppColors.textPrimaryDark
                : AppColors.textPrimaryLight,
          ),
        ),

        SizedBox(height: 8.h),

        Container(
          width: double.infinity,
          decoration: BoxDecoration(
            color: isDark ? AppColors.surfaceDark : AppColors.surfaceLight,
            borderRadius: BorderRadius.circular(12.r),
            border: Border.all(
              color: isDark ? AppColors.borderDark : AppColors.borderLight,
              width: 1,
            ),
          ),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              onTap: () => _showFolderSelector(context),
              borderRadius: BorderRadius.circular(12.r),
              child: Padding(
                padding: EdgeInsets.all(16.w),
                child: Row(
                  children: [
                    // Folder icon
                    Icon(
                      selectedFolder?.folderType.icon ?? Symbols.folder,
                      color:
                          selectedFolder?.folderType.color ??
                          (isDark
                              ? AppColors.textSecondaryDark
                              : AppColors.textSecondaryLight),
                      size: 24.sp,
                    ),

                    SizedBox(width: 12.w),

                    // Folder name or hint
                    Expanded(
                      child: Text(
                        selectedFolder?.name ?? hint ?? 'Select folder',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: selectedFolder != null
                              ? (isDark
                                    ? AppColors.textPrimaryDark
                                    : AppColors.textPrimaryLight)
                              : (isDark
                                    ? AppColors.textSecondaryDark
                                    : AppColors.textSecondaryLight),
                          fontWeight: selectedFolder != null
                              ? FontWeight.w500
                              : FontWeight.w400,
                        ),
                      ),
                    ),

                    // Dropdown arrow
                    Icon(
                      Symbols.keyboard_arrow_down,
                      color: isDark
                          ? AppColors.textSecondaryDark
                          : AppColors.textSecondaryLight,
                      size: 20.sp,
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  void _showFolderSelector(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => FolderSelectorBottomSheet(
        folders: folders,
        selectedFolder: selectedFolder,
        onChanged: onChanged,
        allowRootSelection: allowRootSelection,
      ),
    );
  }
}

/// Bottom sheet for folder selection
class FolderSelectorBottomSheet extends StatefulWidget {
  final List<LibraryFolderModel> folders;
  final LibraryFolderModel? selectedFolder;
  final ValueChanged<LibraryFolderModel?> onChanged;
  final bool allowRootSelection;

  const FolderSelectorBottomSheet({
    super.key,
    required this.folders,
    required this.selectedFolder,
    required this.onChanged,
    required this.allowRootSelection,
  });

  @override
  State<FolderSelectorBottomSheet> createState() =>
      _FolderSelectorBottomSheetState();
}

class _FolderSelectorBottomSheetState extends State<FolderSelectorBottomSheet> {
  late TextEditingController _searchController;
  List<LibraryFolderModel> _filteredFolders = [];

  @override
  void initState() {
    super.initState();
    _searchController = TextEditingController();
    _filteredFolders = widget.folders;
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _filterFolders(String query) {
    setState(() {
      if (query.isEmpty) {
        _filteredFolders = widget.folders;
      } else {
        _filteredFolders = widget.folders
            .where(
              (folder) =>
                  folder.name.toLowerCase().contains(query.toLowerCase()) ||
                  folder.description?.toLowerCase().contains(
                        query.toLowerCase(),
                      ) ==
                      true,
            )
            .toList();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Container(
      height: MediaQuery.of(context).size.height * 0.7,
      decoration: BoxDecoration(
        color: isDark ? AppColors.backgroundDark : AppColors.backgroundLight,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20.r)),
      ),
      child: Column(
        children: [
          // Handle
          Container(
            width: 40.w,
            height: 4.h,
            margin: EdgeInsets.only(top: 12.h),
            decoration: BoxDecoration(
              color: isDark ? AppColors.borderDark : AppColors.borderLight,
              borderRadius: BorderRadius.circular(2.r),
            ),
          ),

          // Header
          Padding(
            padding: EdgeInsets.all(16.w),
            child: Row(
              children: [
                Text(
                  'Select Folder',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: isDark
                        ? AppColors.textPrimaryDark
                        : AppColors.textPrimaryLight,
                  ),
                ),

                const Spacer(),

                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: Icon(
                    Symbols.close,
                    color: isDark
                        ? AppColors.textSecondaryDark
                        : AppColors.textSecondaryLight,
                  ),
                ),
              ],
            ),
          ),

          // Search bar
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            child: TextField(
              controller: _searchController,
              onChanged: _filterFolders,
              decoration: InputDecoration(
                hintText: 'Search folders...',
                prefixIcon: Icon(
                  Symbols.search,
                  color: isDark
                      ? AppColors.textSecondaryDark
                      : AppColors.textSecondaryLight,
                ),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12.r),
                  borderSide: BorderSide(
                    color: isDark
                        ? AppColors.borderDark
                        : AppColors.borderLight,
                  ),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12.r),
                  borderSide: BorderSide(
                    color: isDark
                        ? AppColors.borderDark
                        : AppColors.borderLight,
                  ),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12.r),
                  borderSide: BorderSide(
                    color: isDark
                        ? AppColors.primaryDark
                        : AppColors.primaryLight,
                    width: 2,
                  ),
                ),
                filled: true,
                fillColor: isDark
                    ? AppColors.surfaceDark
                    : AppColors.surfaceLight,
              ),
            ),
          ),

          SizedBox(height: 16.h),

          // Folder list
          Expanded(
            child: ListView(
              padding: EdgeInsets.symmetric(horizontal: 16.w),
              children: [
                // Root option
                if (widget.allowRootSelection)
                  CompactFolderTile(
                    folder: LibraryFolderModel(
                      id: '',
                      name: 'Root Folder',
                      folderType: FolderType.general,
                      accessType: FileAccessType.publicDownloadable,
                      creatorId: '',
                      creatorName: '',
                      createdAt: DateTime.now(),
                    ),
                    isSelected: widget.selectedFolder == null,
                    onTap: () {
                      widget.onChanged(null);
                      Navigator.of(context).pop();
                    },
                  ),

                // Folders
                ..._filteredFolders.map(
                  (folder) => CompactFolderTile(
                    folder: folder,
                    isSelected: widget.selectedFolder?.id == folder.id,
                    onTap: () {
                      widget.onChanged(folder);
                      Navigator.of(context).pop();
                    },
                  ),
                ),

                // Empty state
                if (_filteredFolders.isEmpty &&
                    _searchController.text.isNotEmpty)
                  Padding(
                    padding: EdgeInsets.all(32.w),
                    child: Column(
                      children: [
                        Icon(
                          Symbols.folder_off,
                          size: 48.sp,
                          color: isDark
                              ? AppColors.textSecondaryDark
                              : AppColors.textSecondaryLight,
                        ),
                        SizedBox(height: 16.h),
                        Text(
                          'No folders found',
                          style: Theme.of(context).textTheme.bodyLarge
                              ?.copyWith(
                                color: isDark
                                    ? AppColors.textSecondaryDark
                                    : AppColors.textSecondaryLight,
                              ),
                        ),
                        SizedBox(height: 8.h),
                        Text(
                          'Try adjusting your search terms',
                          style: Theme.of(context).textTheme.bodySmall
                              ?.copyWith(
                                color: isDark
                                    ? AppColors.textSecondaryDark
                                    : AppColors.textSecondaryLight,
                              ),
                        ),
                      ],
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
