import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_symbols_icons/symbols.dart';

import '../../controllers/digital_library_controller.dart';
import '../../models/library_folder_model.dart';
import '../../models/simple_upload_session.dart';
import '../../enums/file_access_type.dart';
import '../../enums/file_usage_type.dart';

import '../../enums/upload_status.dart';
import '../../widgets/folder_dropdown_selector.dart';
import '../../widgets/access_type_toggle.dart';
import '../../widgets/offline_switch.dart';
import '../../../debug/mock_data/generators/digital_library_mock_generator.dart';
import 'sections/file_selection_section.dart';
import 'sections/upload_progress_section.dart';
import 'sections/file_metadata_section.dart';

/// Screen for uploading files to the digital library
class UploadFileScreen extends ConsumerStatefulWidget {
  final String? initialFolderId;

  const UploadFileScreen({super.key, this.initialFolderId});

  @override
  ConsumerState<UploadFileScreen> createState() => _UploadFileScreenState();
}

class _UploadFileScreenState extends ConsumerState<UploadFileScreen> {
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _tagsController = TextEditingController();

  LibraryFolderModel? _selectedFolder;
  FileAccessType _accessType = FileAccessType.publicDownloadable;
  FileUsageType _usageType = FileUsageType.library;
  bool _offlineEnabled = false;
  List<SimpleUploadSession> _uploadSessions = [];
  List<LibraryFolderModel> _availableFolders = [];
  bool _isUploading = false;

  @override
  void initState() {
    super.initState();
    _loadFolders();
  }

  Future<void> _loadFolders() async {
    try {
      final foldersAsync = ref.read(userFoldersProvider);
      foldersAsync.when(
        data: (folders) {
          _availableFolders = folders;
          _setInitialFolder();
          setState(() {});
        },
        loading: () {
          // Keep loading state
        },
        error: (error, stack) {
          // Fallback to mock data on error
          _availableFolders = mockLibraryFolders.take(10).toList();
          _setInitialFolder();
          setState(() {});
        },
      );
    } catch (e) {
      // Fallback to mock data on error
      _availableFolders = mockLibraryFolders.take(10).toList();
      _setInitialFolder();
      setState(() {});
    }
  }

  /// Set the initial folder if initialFolderId is provided
  void _setInitialFolder() {
    if (widget.initialFolderId != null && _availableFolders.isNotEmpty) {
      try {
        _selectedFolder = _availableFolders.firstWhere(
          (folder) => folder.id == widget.initialFolderId,
        );
      } catch (e) {
        // Folder not found in available folders, keep _selectedFolder as null
        _selectedFolder = null;
      }
    }
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    _tagsController.dispose();
    super.dispose();
  }

  void _onFolderSelected(LibraryFolderModel? folder) {
    setState(() {
      _selectedFolder = folder;
    });
  }

  void _onAccessTypeChanged(FileAccessType accessType) {
    setState(() {
      _accessType = accessType;
    });
  }

  void _onUsageTypeChanged(FileUsageType? usageType) {
    if (usageType != null) {
      setState(() {
        _usageType = usageType;
      });
    }
  }

  void _onOfflineAccessChanged(bool enabled) {
    setState(() {
      _offlineEnabled = enabled;
    });
  }

  void _onFilesSelected(List<String> filePaths) {
    setState(() {
      _uploadSessions = filePaths.map((path) {
        final fileName = path.split('/').last;
        return SimpleUploadSession(
          id: 'upload_${DateTime.now().millisecondsSinceEpoch}_${fileName.hashCode}',
          fileName: fileName,
          filePath: path,
          uploadStatus: UploadStatus.pending,
          uploadProgress: 0.0,
          createdAt: DateTime.now(),
        );
      }).toList();
    });
  }

  void _onUploadStart() {
    if (_uploadSessions.isEmpty) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('Please select files to upload')));
      return;
    }

    setState(() {
      _isUploading = true;
    });

    // Start upload process for each file
    for (final session in _uploadSessions) {
      _startUpload(session);
    }
  }

  Future<void> _startUpload(SimpleUploadSession session) async {
    try {
      // Update status to uploading
      setState(() {
        final index = _uploadSessions.indexWhere((s) => s.id == session.id);
        if (index != -1) {
          _uploadSessions[index] = session.copyWith(
            uploadStatus: UploadStatus.uploading,
          );
        }
      });

      // Simulate upload progress
      for (int i = 0; i <= 100; i += 10) {
        await Future.delayed(Duration(milliseconds: 200));
        if (mounted) {
          setState(() {
            final index = _uploadSessions.indexWhere((s) => s.id == session.id);
            if (index != -1) {
              _uploadSessions[index] = _uploadSessions[index].copyWith(
                uploadProgress: i / 100.0,
              );
            }
          });
        }
      }

      // Mark as completed
      if (mounted) {
        setState(() {
          final index = _uploadSessions.indexWhere((s) => s.id == session.id);
          if (index != -1) {
            _uploadSessions[index] = _uploadSessions[index].copyWith(
              uploadStatus: UploadStatus.completed,
              uploadProgress: 1.0,
            );
          }
        });
      }
    } catch (e) {
      // Mark as failed
      if (mounted) {
        setState(() {
          final index = _uploadSessions.indexWhere((s) => s.id == session.id);
          if (index != -1) {
            _uploadSessions[index] = _uploadSessions[index].copyWith(
              uploadStatus: UploadStatus.failed,
              errorMessage: e.toString(),
            );
          }
        });
      }
    }

    // Check if all uploads are complete
    _checkUploadCompletion();
  }

  void _checkUploadCompletion() {
    final allComplete = _uploadSessions.every(
      (session) =>
          session.uploadStatus == UploadStatus.completed ||
          session.uploadStatus == UploadStatus.failed,
    );

    if (allComplete) {
      setState(() {
        _isUploading = false;
      });

      final successCount = _uploadSessions
          .where((session) => session.uploadStatus == UploadStatus.completed)
          .length;
      final failedCount = _uploadSessions
          .where((session) => session.uploadStatus == UploadStatus.failed)
          .length;

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            failedCount == 0
                ? 'Successfully uploaded $successCount file${successCount > 1 ? 's' : ''}'
                : 'Uploaded $successCount file${successCount > 1 ? 's' : ''}, $failedCount failed',
          ),
          backgroundColor: failedCount == 0 ? Colors.green : Colors.orange,
        ),
      );

      if (failedCount == 0) {
        // Navigate back after successful upload
        Future.delayed(Duration(seconds: 2), () {
          if (mounted) {
            Navigator.pop(context);
          }
        });
      }
    }
  }

  void _retryFailedUploads() {
    final failedSessions = _uploadSessions
        .where((session) => session.uploadStatus == UploadStatus.failed)
        .toList();

    for (final session in failedSessions) {
      _startUpload(
        session.copyWith(
          uploadStatus: UploadStatus.pending,
          uploadProgress: 0.0,
          errorMessage: null,
        ),
      );
    }
  }

  void _removeUploadSession(String sessionId) {
    setState(() {
      _uploadSessions.removeWhere((session) => session.id == sessionId);
    });
  }

  Widget _buildUsageTypeSelector() {
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'File Purpose',
          style: theme.textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: 8.h),
        Container(
          width: double.infinity,
          padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 4.h),
          decoration: BoxDecoration(
            border: Border.all(color: theme.colorScheme.outline),
            borderRadius: BorderRadius.circular(8.r),
          ),
          child: DropdownButtonHideUnderline(
            child: DropdownButton<FileUsageType>(
              value: _usageType,
              onChanged: _onUsageTypeChanged,
              items: FileUsageType.values.map((usageType) {
                return DropdownMenuItem<FileUsageType>(
                  value: usageType,
                  child: Row(
                    children: [
                      Icon(usageType.icon, size: 20.sp, color: usageType.color),
                      SizedBox(width: 12.w),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Text(
                              usageType.label,
                              style: theme.textTheme.bodyMedium?.copyWith(
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            Text(
                              usageType.description,
                              style: theme.textTheme.bodySmall?.copyWith(
                                color: theme.colorScheme.onSurfaceVariant,
                              ),
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                );
              }).toList(),
            ),
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: theme.colorScheme.surface,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        elevation: 0,
        title: Text(
          'Upload Files',
          style: theme.textTheme.headlineMedium?.copyWith(
            fontWeight: FontWeight.w700,
            color: theme.colorScheme.onSurface,
          ),
        ),
        actions: [
          if (_uploadSessions.any((s) => s.uploadStatus == UploadStatus.failed))
            IconButton(
              onPressed: _retryFailedUploads,
              icon: Icon(Symbols.refresh, color: theme.colorScheme.onSurface),
              tooltip: 'Retry failed uploads',
            ),
        ],
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // File selection
            FileSelectionSection(
              onFilesSelected: _onFilesSelected,
              isUploading: _isUploading,
            ),

            SizedBox(height: 24.h),

            // Upload settings
            if (_uploadSessions.isNotEmpty) ...[
              _buildSectionTitle(context, 'Upload Settings'),
              SizedBox(height: 16.h),

              // Folder selection
              FolderDropdownSelector(
                folders: _availableFolders,
                selectedFolder: _selectedFolder,
                onChanged: _onFolderSelected,
              ),

              SizedBox(height: 16.h),

              // Access type
              AccessTypeToggle(
                selectedType: _accessType,
                onChanged: _onAccessTypeChanged,
              ),

              SizedBox(height: 16.h),

              // Usage type
              _buildUsageTypeSelector(),

              SizedBox(height: 16.h),

              // Offline access
              OfflineSwitch(
                value: _offlineEnabled,
                onChanged: _onOfflineAccessChanged,
              ),

              SizedBox(height: 24.h),

              // File metadata
              FileMetadataSection(
                titleController: _titleController,
                descriptionController: _descriptionController,
                tagsController: _tagsController,
                enabled: !_isUploading,
              ),

              SizedBox(height: 24.h),
            ],

            // Upload progress
            if (_uploadSessions.isNotEmpty) ...[
              _buildSectionTitle(context, 'Upload Progress'),
              SizedBox(height: 16.h),
              UploadProgressSection(
                uploadSessions: _uploadSessions,
                onRemoveSession: _removeUploadSession,
                onRetrySession: (sessionId) {
                  final session = _uploadSessions.firstWhere(
                    (s) => s.id == sessionId,
                  );
                  _startUpload(
                    session.copyWith(
                      uploadStatus: UploadStatus.pending,
                      uploadProgress: 0.0,
                      errorMessage: null,
                    ),
                  );
                },
              ),
            ],
          ],
        ),
      ),
      bottomNavigationBar: _uploadSessions.isNotEmpty && !_isUploading
          ? Container(
              padding: EdgeInsets.all(16.w),
              decoration: BoxDecoration(
                color: theme.colorScheme.surface,
                border: Border(
                  top: BorderSide(color: theme.dividerColor, width: 1),
                ),
              ),
              child: SafeArea(
                child: SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: _onUploadStart,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: theme.colorScheme.primary,
                      foregroundColor: theme.colorScheme.onPrimary,
                      padding: EdgeInsets.symmetric(vertical: 16.h),
                    ),
                    child: Text(
                      'Start Upload',
                      style: theme.textTheme.bodyLarge?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: theme.colorScheme.onPrimary,
                      ),
                    ),
                  ),
                ),
              ),
            )
          : null,
    );
  }

  Widget _buildSectionTitle(BuildContext context, String title) {
    final theme = Theme.of(context);

    return Text(
      title,
      style: theme.textTheme.titleMedium?.copyWith(
        fontWeight: FontWeight.w700,
        color: theme.colorScheme.onSurface,
      ),
    );
  }
}
