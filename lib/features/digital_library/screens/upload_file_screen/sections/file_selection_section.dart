import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_symbols_icons/symbols.dart';
import 'package:file_picker/file_picker.dart';
import 'package:image_picker/image_picker.dart';

/// Section for selecting files to upload
class FileSelectionSection extends StatelessWidget {
  final ValueChanged<List<String>> onFilesSelected;
  final bool isUploading;

  const FileSelectionSection({
    super.key,
    required this.onFilesSelected,
    this.isUploading = false,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Select Files',
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w700,
            color: theme.colorScheme.onSurface,
          ),
        ),
        SizedBox(height: 16.h),

        // File selection options
        Row(
          children: [
            Expanded(
              child: _buildSelectionButton(
                context,
                icon: Symbols.photo_library,
                label: 'Photos',
                onTap: isUploading ? null : () => _selectPhotos(context),
              ),
            ),
            SizedBox(width: 12.w),
            Expanded(
              child: _buildSelectionButton(
                context,
                icon: Symbols.description,
                label: 'Documents',
                onTap: isUploading ? null : () => _selectDocuments(context),
              ),
            ),
            SizedBox(width: 12.w),
            Expanded(
              child: _buildSelectionButton(
                context,
                icon: Symbols.folder_open,
                label: 'Browse',
                onTap: isUploading ? null : () => _browseFiles(context),
              ),
            ),
          ],
        ),

        SizedBox(height: 16.h),

        // Drag and drop area
        _buildDragDropArea(context),
      ],
    );
  }

  Widget _buildSelectionButton(
    BuildContext context, {
    required IconData icon,
    required String label,
    required VoidCallback? onTap,
  }) {
    final theme = Theme.of(context);
    final isEnabled = onTap != null;

    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12.r),
        child: Container(
          padding: EdgeInsets.symmetric(vertical: 16.h, horizontal: 12.w),
          decoration: BoxDecoration(
            color: isEnabled
                ? theme.colorScheme.primary.withValues(alpha: 0.1)
                : theme.colorScheme.onSurface.withValues(alpha: 0.05),
            borderRadius: BorderRadius.circular(12.r),
            border: Border.all(
              color: isEnabled
                  ? theme.colorScheme.primary.withValues(alpha: 0.3)
                  : theme.dividerColor,
              width: 1,
            ),
          ),
          child: Column(
            children: [
              Icon(
                icon,
                size: 24.sp,
                color: isEnabled
                    ? theme.colorScheme.primary
                    : theme.colorScheme.onSurface.withValues(alpha: 0.4),
              ),
              SizedBox(height: 8.h),
              Text(
                label,
                style: theme.textTheme.bodySmall?.copyWith(
                  color: isEnabled
                      ? theme.colorScheme.onSurface
                      : theme.colorScheme.onSurface.withValues(alpha: 0.4),
                  fontWeight: FontWeight.w500,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDragDropArea(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(24.w),
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(16.r),
        border: Border.all(
          color: theme.dividerColor,
          width: 2,
          strokeAlign: BorderSide.strokeAlignInside,
        ),
      ),
      child: Column(
        children: [
          Icon(
            Symbols.cloud_upload,
            size: 48.sp,
            color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
          ),
          SizedBox(height: 16.h),
          Text(
            'Drag and drop files here',
            style: theme.textTheme.bodyLarge?.copyWith(
              color: theme.colorScheme.onSurface,
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            'or tap to browse files',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
            ),
          ),
          SizedBox(height: 16.h),
          OutlinedButton.icon(
            onPressed: isUploading ? null : () => _browseFiles(context),
            icon: Icon(Symbols.folder_open),
            label: Text('Browse Files'),
            style: OutlinedButton.styleFrom(
              foregroundColor: theme.colorScheme.primary,
              side: BorderSide(color: theme.colorScheme.primary),
            ),
          ),
        ],
      ),
    );
  }

  void _selectPhotos(BuildContext context) async {
    try {
      // Show options for camera or gallery
      final source = await _showImageSourceDialog(context);
      if (source == null) return;

      final ImagePicker picker = ImagePicker();
      List<String> selectedFiles = [];

      if (source == ImageSource.camera) {
        // Take photo with camera
        final XFile? photo = await picker.pickImage(source: ImageSource.camera);
        if (photo != null) {
          selectedFiles.add(photo.path);
        }
      } else {
        // Pick multiple images from gallery
        final List<XFile> images = await picker.pickMultiImage();
        selectedFiles = images.map((image) => image.path).toList();
      }

      if (selectedFiles.isNotEmpty) {
        onFilesSelected(selectedFiles);
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to select photos: $e'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }

  void _selectDocuments(BuildContext context) async {
    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['pdf', 'doc', 'docx', 'txt', 'rtf', 'odt'],
        allowMultiple: true,
      );

      if (result != null && result.files.isNotEmpty) {
        final selectedFiles = result.paths
            .where((path) => path != null)
            .cast<String>()
            .toList();

        if (selectedFiles.isNotEmpty) {
          onFilesSelected(selectedFiles);
        }
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to select documents: $e'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }

  void _browseFiles(BuildContext context) async {
    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.any,
        allowMultiple: true,
      );

      if (result != null && result.files.isNotEmpty) {
        final selectedFiles = result.paths
            .where((path) => path != null)
            .cast<String>()
            .toList();

        if (selectedFiles.isNotEmpty) {
          onFilesSelected(selectedFiles);
        }
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to browse files: $e'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }

  /// Show dialog to choose between camera and gallery for photo selection
  Future<ImageSource?> _showImageSourceDialog(BuildContext context) async {
    return showDialog<ImageSource>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Select Photo Source'),
        content: const Text('Choose how you want to select photos'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, ImageSource.camera),
            child: const Text('Camera'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, ImageSource.gallery),
            child: const Text('Gallery'),
          ),
        ],
      ),
    );
  }
}
