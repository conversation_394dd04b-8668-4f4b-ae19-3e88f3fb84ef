import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_symbols_icons/symbols.dart';

import '../../../models/simple_upload_session.dart';
import '../../../enums/upload_status.dart';

/// Section showing upload progress for multiple files
class UploadProgressSection extends StatelessWidget {
  final List<SimpleUploadSession> uploadSessions;
  final ValueChanged<String> onRemoveSession;
  final ValueChanged<String> onRetrySession;

  const UploadProgressSection({
    super.key,
    required this.uploadSessions,
    required this.onRemoveSession,
    required this.onRetrySession,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: uploadSessions.map((session) {
        return Padding(
          padding: EdgeInsets.only(bottom: 12.h),
          child: UploadProgressCard(
            session: session,
            onRemove: () => onRemoveSession(session.id),
            onRetry: () => onRetrySession(session.id),
          ),
        );
      }).toList(),
    );
  }
}

/// Card showing progress for a single file upload
class UploadProgressCard extends StatelessWidget {
  final SimpleUploadSession session;
  final VoidCallback onRemove;
  final VoidCallback onRetry;

  const UploadProgressCard({
    super.key,
    required this.session,
    required this.onRemove,
    required this.onRetry,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: _getBorderColor(theme), width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // File info row
          Row(
            children: [
              // File icon
              Container(
                width: 40.w,
                height: 40.h,
                decoration: BoxDecoration(
                  color: _getStatusColor(theme).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8.r),
                ),
                child: Icon(
                  _getFileIcon(),
                  size: 20.sp,
                  color: _getStatusColor(theme),
                ),
              ),

              SizedBox(width: 12.w),

              // File name and status
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      session.fileName,
                      style: theme.textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: theme.colorScheme.onSurface,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    SizedBox(height: 2.h),
                    Text(
                      _getStatusText(),
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: _getStatusColor(theme),
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),

              // Action button
              _buildActionButton(context),
            ],
          ),

          // Progress bar (only show for uploading)
          if (session.uploadStatus == UploadStatus.uploading) ...[
            SizedBox(height: 12.h),
            _buildProgressBar(theme),
          ],

          // Error message (only show for failed)
          if (session.uploadStatus == UploadStatus.failed &&
              session.errorMessage != null) ...[
            SizedBox(height: 8.h),
            Container(
              width: double.infinity,
              padding: EdgeInsets.all(8.w),
              decoration: BoxDecoration(
                color: Colors.red.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(6.r),
              ),
              child: Text(
                session.errorMessage!,
                style: theme.textTheme.bodySmall?.copyWith(color: Colors.red),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildProgressBar(ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Uploading...',
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
              ),
            ),
            Text(
              '${(session.uploadProgress * 100).toInt()}%',
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        SizedBox(height: 4.h),
        LinearProgressIndicator(
          value: session.uploadProgress,
          backgroundColor: theme.colorScheme.surfaceContainerHighest,
          valueColor: AlwaysStoppedAnimation<Color>(theme.colorScheme.primary),
          minHeight: 4.h,
        ),
      ],
    );
  }

  Widget _buildActionButton(BuildContext context) {
    final theme = Theme.of(context);

    switch (session.uploadStatus) {
      case UploadStatus.pending:
        return IconButton(
          onPressed: onRemove,
          icon: Icon(
            Symbols.close,
            size: 20.sp,
            color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
          ),
          tooltip: 'Remove',
        );

      case UploadStatus.uploading:
      case UploadStatus.processing:
        return SizedBox(
          width: 20.w,
          height: 20.h,
          child: CircularProgressIndicator(
            strokeWidth: 2,
            valueColor: AlwaysStoppedAnimation<Color>(
              theme.colorScheme.primary,
            ),
          ),
        );

      case UploadStatus.completed:
        return Icon(Symbols.check_circle, size: 24.sp, color: Colors.green);

      case UploadStatus.failed:
        return Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(
              onPressed: onRetry,
              icon: Icon(Symbols.refresh, size: 20.sp, color: Colors.orange),
              tooltip: 'Retry',
            ),
            IconButton(
              onPressed: onRemove,
              icon: Icon(Symbols.close, size: 20.sp, color: Colors.red),
              tooltip: 'Remove',
            ),
          ],
        );

      case UploadStatus.cancelled:
      case UploadStatus.paused:
        return IconButton(
          onPressed: onRemove,
          icon: Icon(
            Symbols.close,
            size: 20.sp,
            color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
          ),
          tooltip: 'Remove',
        );
    }
  }

  Color _getBorderColor(ThemeData theme) {
    switch (session.uploadStatus) {
      case UploadStatus.pending:
        return theme.dividerColor;
      case UploadStatus.uploading:
      case UploadStatus.processing:
        return theme.colorScheme.primary.withValues(alpha: 0.3);
      case UploadStatus.completed:
        return Colors.green.withValues(alpha: 0.3);
      case UploadStatus.failed:
        return Colors.red.withValues(alpha: 0.3);
      case UploadStatus.cancelled:
      case UploadStatus.paused:
        return Colors.orange.withValues(alpha: 0.3);
    }
  }

  Color _getStatusColor(ThemeData theme) {
    switch (session.uploadStatus) {
      case UploadStatus.pending:
        return theme.colorScheme.onSurface.withValues(alpha: 0.6);
      case UploadStatus.uploading:
      case UploadStatus.processing:
        return theme.colorScheme.primary;
      case UploadStatus.completed:
        return Colors.green;
      case UploadStatus.failed:
        return Colors.red;
      case UploadStatus.cancelled:
      case UploadStatus.paused:
        return Colors.orange;
    }
  }

  String _getStatusText() {
    switch (session.uploadStatus) {
      case UploadStatus.pending:
        return 'Ready to upload';
      case UploadStatus.uploading:
        return 'Uploading... ${(session.uploadProgress * 100).toInt()}%';
      case UploadStatus.processing:
        return 'Processing...';
      case UploadStatus.completed:
        return 'Upload complete';
      case UploadStatus.failed:
        return 'Upload failed';
      case UploadStatus.cancelled:
        return 'Upload cancelled';
      case UploadStatus.paused:
        return 'Upload paused';
    }
  }

  IconData _getFileIcon() {
    final extension = session.fileName.split('.').last.toLowerCase();

    switch (extension) {
      case 'pdf':
        return Symbols.picture_as_pdf;
      case 'doc':
      case 'docx':
        return Symbols.description;
      case 'xls':
      case 'xlsx':
        return Symbols.table_chart;
      case 'ppt':
      case 'pptx':
        return Symbols.slideshow;
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
        return Symbols.image;
      case 'mp4':
      case 'avi':
      case 'mov':
        return Symbols.movie;
      case 'mp3':
      case 'wav':
      case 'aac':
        return Symbols.audio_file;
      case 'zip':
      case 'rar':
      case '7z':
        return Symbols.folder_zip;
      default:
        return Symbols.description;
    }
  }
}
