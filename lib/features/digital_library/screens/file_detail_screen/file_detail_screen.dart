import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_symbols_icons/symbols.dart';

import '../../models/library_file_model.dart';
import '../../models/file_model.dart';
import '../../controllers/digital_library_controller.dart';
import '../../services/file_operations_service.dart';
import '../../services/file_viewer_service.dart';
import '../../services/file_sharing_service.dart';
import '../../enums/offline_access.dart';
import 'sections/file_header_section.dart';
import 'sections/file_preview_section.dart';
import 'sections/file_actions_section.dart';
import 'sections/file_metadata_section.dart';
import '../edit_file_screen/edit_file_screen.dart';
import '../select_folder_screen/select_folder_screen.dart';

/// Screen showing details of a specific file
class FileDetailScreen extends ConsumerStatefulWidget {
  final String fileId;

  const FileDetailScreen({super.key, required this.fileId});

  @override
  ConsumerState<FileDetailScreen> createState() => _FileDetailScreenState();
}

class _FileDetailScreenState extends ConsumerState<FileDetailScreen> {
  final FileOperationsService _fileOperationsService = FileOperationsService();
  final FileViewerService _fileViewerService = FileViewerService();
  final FileSharingService _fileSharingService = FileSharingService();

  double? _downloadProgress;

  Future<void> _onViewFile(LibraryFileModel file) async {
    try {
      final userId = ref.read(currentUserIdProvider);
      if (userId == 'anonymous') return;

      // Update view count
      await ref
          .read(fileOperationsProvider)
          .updateFileAccessCount(file.id, incrementView: true);

      // Get file for viewing
      await _fileOperationsService.getFileForViewing(
        file: file,
        userId: userId,
      );

      if (mounted) {
        // Convert LibraryFileModel to FileModel for the viewer service
        final fileModel = FileModel.fromLibraryFile(file);

        // Open file in the viewer
        await _fileViewerService.openFile(context, fileModel);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to open file: $e'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }

  Future<void> _onDownloadFile(LibraryFileModel file) async {
    try {
      final userId = ref.read(currentUserIdProvider);
      if (userId == 'anonymous') return;

      // Show loading indicator
      if (mounted) {
        setState(() {
          _downloadProgress = 0.0;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Starting download of ${file.displayTitle}...'),
          ),
        );
      }

      // Download file
      await _fileOperationsService.downloadFile(
        file: file,
        userId: userId,
        onProgress: (progress) {
          if (mounted) {
            setState(() {
              _downloadProgress = progress;
            });
          }
        },
      );

      // Reset download progress
      if (mounted) {
        setState(() {
          _downloadProgress = null;
        });
      }

      // Update download count
      await ref
          .read(fileOperationsProvider)
          .updateFileAccessCount(file.id, incrementDownload: true);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Downloaded ${file.displayTitle}'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to download file: $e'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }

  Future<void> _onShareFile(LibraryFileModel file) async {
    try {
      // Convert LibraryFileModel to FileModel for the sharing service
      final fileModel = FileModel.fromLibraryFile(file);

      // Share the file
      final result = await _fileSharingService.shareFile(
        fileModel,
        customText: 'Check out this file: ${file.displayTitle}',
        customSubject: file.displayTitle,
      );

      if (mounted && result != null) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Shared ${file.displayTitle}'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to share file: $e'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }

  Future<void> _onToggleFavorite(LibraryFileModel file) async {
    try {
      final newFavoriteStatus = !file.isFavorite;

      await ref
          .read(fileOperationsProvider)
          .toggleFileFavorite(file.id, newFavoriteStatus);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              newFavoriteStatus
                  ? 'Added to favorites'
                  : 'Removed from favorites',
            ),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to update favorite status: $e'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }

  Future<void> _onToggleOfflineAccess(LibraryFileModel file) async {
    try {
      final userId = ref.read(currentUserIdProvider);
      if (userId == 'anonymous') return;

      final newOfflineAccess = file.offlineAccess.canAccessOffline
          ? OfflineAccess.none
          : OfflineAccess.available;

      if (newOfflineAccess.canAccessOffline) {
        // Enable offline access
        await _fileOperationsService.enableOfflineAccess(
          file: file,
          userId: userId,
        );
      } else {
        // Disable offline access
        await _fileOperationsService.disableOfflineAccess(file: file);
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              newOfflineAccess.canAccessOffline
                  ? 'File available offline'
                  : 'File removed from offline access',
            ),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to update offline access: $e'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }

  void _showFileOptions(LibraryFileModel file) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => FileOptionsBottomSheet(
        file: file,
        onFileUpdated: (updatedFile) {
          // Invalidate the provider to refresh the data
          ref.invalidate(fileByIdProvider(widget.fileId));
        },
        onFileDeleted: () {
          Navigator.pop(context);
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final fileAsync = ref.watch(fileByIdProvider(widget.fileId));

    return Scaffold(
      backgroundColor: theme.colorScheme.surface,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        elevation: 0,
        title: Text(
          fileAsync.when(
            data: (file) => file?.displayTitle ?? 'File Details',
            loading: () => 'File Details',
            error: (_, __) => 'File Details',
          ),
          style: theme.textTheme.headlineMedium?.copyWith(
            fontWeight: FontWeight.w700,
            color: theme.colorScheme.onSurface,
          ),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        actions: fileAsync.when(
          data: (file) => file != null
              ? [
                  IconButton(
                    onPressed: () => _onToggleFavorite(file),
                    icon: Icon(
                      file.isFavorite
                          ? Symbols.favorite
                          : Symbols.favorite_border,
                      color: file.isFavorite
                          ? Colors.red
                          : theme.colorScheme.onSurface,
                    ),
                    tooltip: file.isFavorite
                        ? 'Remove from favorites'
                        : 'Add to favorites',
                  ),
                  IconButton(
                    onPressed: () => _showFileOptions(file),
                    icon: Icon(
                      Symbols.more_vert,
                      color: theme.colorScheme.onSurface,
                    ),
                  ),
                ]
              : <Widget>[],
          loading: () => <Widget>[],
          error: (_, __) => <Widget>[],
        ),
      ),
      body: _buildBody(fileAsync),
      bottomNavigationBar: fileAsync.when(
        data: (file) => file != null
            ? FileActionsSection(
                file: file,
                onView: () => _onViewFile(file),
                onDownload: () => _onDownloadFile(file),
                onShare: () => _onShareFile(file),
                onToggleOffline: () => _onToggleOfflineAccess(file),
              )
            : null,
        loading: () => null,
        error: (_, __) => null,
      ),
    );
  }

  Widget _buildBody(AsyncValue<LibraryFileModel?> fileAsync) {
    return fileAsync.when(
      data: (file) {
        if (file == null) {
          return const Center(child: Text('File not found'));
        }
        return _buildFileContent(file);
      },
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stackTrace) => Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Symbols.error,
              size: 64.sp,
              color: Theme.of(context).colorScheme.error,
            ),
            SizedBox(height: 16.h),
            Text(
              'Failed to load file: $error',
              style: Theme.of(context).textTheme.bodyLarge,
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 16.h),
            ElevatedButton(
              onPressed: () {
                ref.invalidate(fileByIdProvider(widget.fileId));
              },
              child: const Text('Retry'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFileContent(LibraryFileModel file) {
    return RefreshIndicator(
      onRefresh: () async {
        ref.invalidate(fileByIdProvider(widget.fileId));
      },
      child: SingleChildScrollView(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Download progress indicator
            if (_downloadProgress != null) ...[
              Container(
                padding: EdgeInsets.all(16.w),
                margin: EdgeInsets.only(bottom: 16.h),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primaryContainer,
                  borderRadius: BorderRadius.circular(8.r),
                ),
                child: Column(
                  children: [
                    Text(
                      'Downloading... ${(_downloadProgress! * 100).toInt()}%',
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                    SizedBox(height: 8.h),
                    LinearProgressIndicator(
                      value: _downloadProgress,
                      backgroundColor: Theme.of(
                        context,
                      ).colorScheme.outline.withValues(alpha: 0.3),
                      valueColor: AlwaysStoppedAnimation<Color>(
                        Theme.of(context).colorScheme.primary,
                      ),
                    ),
                  ],
                ),
              ),
            ],

            // File header
            FileHeaderSection(file: file),

            SizedBox(height: 24.h),

            // File preview
            FilePreviewSection(file: file),

            SizedBox(height: 24.h),

            // File metadata
            FileMetadataDetailSection(file: file),

            // Bottom padding for FAB
            SizedBox(height: 100.h),
          ],
        ),
      ),
    );
  }
}

/// Bottom sheet for file options
class FileOptionsBottomSheet extends ConsumerWidget {
  final LibraryFileModel file;
  final ValueChanged<LibraryFileModel>? onFileUpdated;
  final VoidCallback? onFileDeleted;

  const FileOptionsBottomSheet({
    super.key,
    required this.file,
    this.onFileUpdated,
    this.onFileDeleted,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);

    return Container(
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20.r)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Handle
          Container(
            width: 40.w,
            height: 4.h,
            margin: EdgeInsets.only(top: 12.h),
            decoration: BoxDecoration(
              color: theme.dividerColor,
              borderRadius: BorderRadius.circular(2.r),
            ),
          ),

          // Options
          ListTile(
            leading: Icon(Symbols.edit),
            title: Text('Edit Details'),
            onTap: () async {
              Navigator.pop(context);
              final result = await Navigator.push<LibraryFileModel>(
                context,
                MaterialPageRoute(
                  builder: (context) => EditFileScreen(file: file),
                ),
              );

              // If file was updated, notify parent and refresh
              if (result != null) {
                onFileUpdated?.call(result);
                ref.invalidate(fileByIdProvider(file.id));
              }
            },
          ),
          ListTile(
            leading: Icon(Symbols.drive_file_move),
            title: Text('Move to Folder'),
            onTap: () async {
              Navigator.pop(context);
              await _moveFileToFolder(context, ref, file);
            },
          ),
          ListTile(
            leading: Icon(Symbols.delete, color: Colors.red),
            title: Text('Delete', style: TextStyle(color: Colors.red)),
            onTap: () {
              Navigator.pop(context);
              onFileDeleted?.call();
            },
          ),

          SizedBox(height: MediaQuery.of(context).padding.bottom + 16.h),
        ],
      ),
    );
  }

  /// Move file to a different folder
  Future<void> _moveFileToFolder(
    BuildContext context,
    WidgetRef ref,
    LibraryFileModel file,
  ) async {
    try {
      // Navigate to folder selection screen
      final selectedFolderId = await Navigator.push<String?>(
        context,
        MaterialPageRoute(
          builder: (context) => SelectFolderScreen(
            currentFolderId: file.folderId,
            title: 'Move to Folder',
          ),
        ),
      );

      // If user cancelled or selected the same folder, do nothing
      if (selectedFolderId == file.folderId) {
        return;
      }

      if (context.mounted) {
        // Show loading indicator
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Moving ${file.displayTitle}...'),
            duration: const Duration(seconds: 1),
          ),
        );

        // Update file with new folder
        final updatedFile = file.copyWith(
          folderId: selectedFolderId,
          modifiedAt: DateTime.now(),
        );

        final fileOps = ref.read(fileOperationsProvider);
        final result = await fileOps.updateFile(updatedFile);

        if (context.mounted) {
          // Notify parent about the update
          onFileUpdated?.call(result);

          // Show success message
          final folderName = selectedFolderId == null
              ? 'Root folder'
              : await _getFolderName(ref, selectedFolderId);

          if (context.mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Moved ${file.displayTitle} to $folderName'),
                backgroundColor: Theme.of(context).colorScheme.primary,
              ),
            );
          }
        }
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to move file: $e'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }

  /// Get folder name by ID
  Future<String> _getFolderName(WidgetRef ref, String folderId) async {
    try {
      final folderAsync = ref.read(folderByIdProvider(folderId));
      return folderAsync.when(
        data: (folder) => folder?.name ?? 'Unknown folder',
        loading: () => 'Loading...',
        error: (error, stack) => 'Unknown folder',
      );
    } catch (e) {
      return 'Unknown folder';
    }
  }
}
