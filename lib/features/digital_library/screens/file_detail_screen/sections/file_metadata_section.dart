import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_symbols_icons/symbols.dart';

import '../../../controllers/digital_library_controller.dart';
import '../../../models/library_file_model.dart';
import '../../../enums/library_file_type.dart';
import '../../../enums/upload_status.dart';

/// Section showing detailed file metadata and tags
class FileMetadataDetailSection extends ConsumerWidget {
  final LibraryFileModel file;

  const FileMetadataDetailSection({super.key, required this.file});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);

    return Container(
      padding: EdgeInsets.all(20.w),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(16.r),
        border: Border.all(color: theme.dividerColor, width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section title
          Text(
            'File Details',
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w700,
              color: theme.colorScheme.onSurface,
            ),
          ),

          SizedBox(height: 16.h),

          // File properties
          _buildPropertyRow(
            context,
            icon: Symbols.description,
            label: 'File Type',
            value: file.fileType.label,
          ),
          SizedBox(height: 12.h),
          _buildPropertyRow(
            context,
            icon: Symbols.extension,
            label: 'Extension',
            value: '.${file.fileExtension}',
          ),
          if (file.mimeType != null) ...[
            SizedBox(height: 12.h),
            _buildPropertyRow(
              context,
              icon: Symbols.code,
              label: 'MIME Type',
              value: file.mimeType!,
            ),
          ],
          if (file.folderId != null) ...[
            SizedBox(height: 12.h),
            _buildFolderLocationRow(context, ref, file.folderId!),
          ],

          // Tags section
          if (file.tags.isNotEmpty) ...[
            SizedBox(height: 20.h),
            Text(
              'Tags',
              style: theme.textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.w600,
                color: theme.colorScheme.onSurface,
              ),
            ),
            SizedBox(height: 12.h),
            Wrap(
              spacing: 8.w,
              runSpacing: 8.h,
              children: file.tags
                  .map((tag) => _buildTagChip(context, tag))
                  .toList(),
            ),
          ],

          // Technical details
          SizedBox(height: 20.h),
          Text(
            'Technical Details',
            style: theme.textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.w600,
              color: theme.colorScheme.onSurface,
            ),
          ),
          SizedBox(height: 12.h),
          _buildPropertyRow(
            context,
            icon: Symbols.fingerprint,
            label: 'File ID',
            value: file.id,
            isMonospace: true,
          ),
          if (file.cloudUrl != null) ...[
            SizedBox(height: 12.h),
            _buildPropertyRow(
              context,
              icon: Symbols.cloud,
              label: 'Cloud Storage',
              value: 'Available',
            ),
          ],
          if (file.localPath != null) ...[
            SizedBox(height: 12.h),
            _buildPropertyRow(
              context,
              icon: Symbols.storage,
              label: 'Local Storage',
              value: 'Available',
            ),
          ],

          // Upload information
          SizedBox(height: 20.h),
          Text(
            'Upload Information',
            style: theme.textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.w600,
              color: theme.colorScheme.onSurface,
            ),
          ),
          SizedBox(height: 12.h),
          _buildPropertyRow(
            context,
            icon: Symbols.upload,
            label: 'Upload Status',
            value: file.uploadStatus.label,
          ),
          if (file.uploadProgress < 1.0) ...[
            SizedBox(height: 12.h),
            _buildPropertyRow(
              context,
              icon: Symbols.progress_activity,
              label: 'Progress',
              value: '${(file.uploadProgress * 100).toInt()}%',
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildPropertyRow(
    BuildContext context, {
    required IconData icon,
    required String label,
    required String value,
    bool isMonospace = false,
  }) {
    final theme = Theme.of(context);

    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(
          icon,
          size: 16.sp,
          color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
        ),
        SizedBox(width: 12.w),
        Expanded(
          flex: 2,
          child: Text(
            label,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
            ),
          ),
        ),
        SizedBox(width: 12.w),
        Expanded(
          flex: 3,
          child: Text(
            value,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurface,
              fontWeight: FontWeight.w500,
              fontFamily: isMonospace ? 'monospace' : null,
            ),
            textAlign: TextAlign.end,
          ),
        ),
      ],
    );
  }

  Widget _buildTagChip(BuildContext context, String tag) {
    final theme = Theme.of(context);

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
      decoration: BoxDecoration(
        color: theme.colorScheme.primary.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(16.r),
        border: Border.all(
          color: theme.colorScheme.primary.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Text(
        tag,
        style: theme.textTheme.bodySmall?.copyWith(
          color: theme.colorScheme.primary,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  /// Build folder location row with actual folder name
  Widget _buildFolderLocationRow(
    BuildContext context,
    WidgetRef ref,
    String folderId,
  ) {
    final folderAsync = ref.watch(folderByIdProvider(folderId));

    return folderAsync.when(
      data: (folder) {
        final folderName = folder?.name ?? 'Unknown folder';
        return _buildPropertyRow(
          context,
          icon: Symbols.folder,
          label: 'Location',
          value: 'In $folderName',
        );
      },
      loading: () => _buildPropertyRow(
        context,
        icon: Symbols.folder,
        label: 'Location',
        value: 'Loading...',
      ),
      error: (error, stack) => _buildPropertyRow(
        context,
        icon: Symbols.folder,
        label: 'Location',
        value: 'In folder',
      ),
    );
  }
}
