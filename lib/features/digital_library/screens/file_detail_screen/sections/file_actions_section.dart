import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_symbols_icons/symbols.dart';

import '../../../models/library_file_model.dart';
import '../../../enums/offline_access.dart';

/// Bottom action bar for file detail screen
class FileActionsSection extends StatelessWidget {
  final LibraryFileModel file;
  final VoidCallback onView;
  final VoidCallback onDownload;
  final VoidCallback onShare;
  final VoidCallback onToggleOffline;

  const FileActionsSection({
    super.key,
    required this.file,
    required this.onView,
    required this.onDownload,
    required this.onShare,
    required this.onToggleOffline,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        border: Border(top: BorderSide(color: theme.dividerColor, width: 1)),
      ),
      child: <PERSON><PERSON><PERSON>(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Primary action button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: file.isAvailableForViewing ? onView : null,
                icon: Icon(Symbols.visibility),
                label: Text(
                  'View File',
                  style: theme.textTheme.bodyLarge?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: theme.colorScheme.primary,
                  foregroundColor: theme.colorScheme.onPrimary,
                  padding: EdgeInsets.symmetric(vertical: 16.h),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12.r),
                  ),
                ),
              ),
            ),

            SizedBox(height: 12.h),

            // Secondary action buttons
            Row(
              children: [
                // Download button
                if (file.canDownload)
                  Expanded(
                    child: OutlinedButton.icon(
                      onPressed: onDownload,
                      icon: Icon(Symbols.download, size: 18.sp),
                      label: Text('Download'),
                      style: OutlinedButton.styleFrom(
                        foregroundColor: theme.colorScheme.primary,
                        side: BorderSide(color: theme.colorScheme.primary),
                        padding: EdgeInsets.symmetric(vertical: 12.h),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12.r),
                        ),
                      ),
                    ),
                  ),

                if (file.canDownload) SizedBox(width: 12.w),

                // Share button
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: onShare,
                    icon: Icon(Symbols.share, size: 18.sp),
                    label: Text('Share'),
                    style: OutlinedButton.styleFrom(
                      foregroundColor: theme.colorScheme.primary,
                      side: BorderSide(color: theme.colorScheme.primary),
                      padding: EdgeInsets.symmetric(vertical: 12.h),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12.r),
                      ),
                    ),
                  ),
                ),

                SizedBox(width: 12.w),

                // Offline toggle button
                OutlinedButton(
                  onPressed: onToggleOffline,
                  style: OutlinedButton.styleFrom(
                    foregroundColor: file.offlineAccess.canAccessOffline
                        ? Colors.green
                        : theme.colorScheme.primary,
                    side: BorderSide(
                      color: file.offlineAccess.canAccessOffline
                          ? Colors.green
                          : theme.colorScheme.primary,
                    ),
                    padding: EdgeInsets.symmetric(
                      vertical: 12.h,
                      horizontal: 16.w,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12.r),
                    ),
                  ),
                  child: Icon(
                    file.offlineAccess.canAccessOffline
                        ? Symbols.offline_pin
                        : Symbols.download_for_offline,
                    size: 20.sp,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
