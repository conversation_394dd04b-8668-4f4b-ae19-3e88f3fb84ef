import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_symbols_icons/symbols.dart';

import '../../../models/library_file_model.dart';
import '../../../enums/library_file_type.dart';

/// Section showing file preview or placeholder
class FilePreviewSection extends StatelessWidget {
  final LibraryFileModel file;

  const FilePreviewSection({super.key, required this.file});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(16.r),
        border: Border.all(color: theme.dividerColor, width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section header
          Padding(
            padding: EdgeInsets.all(16.w),
            child: Row(
              children: [
                Text(
                  'Preview',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w700,
                    color: theme.colorScheme.onSurface,
                  ),
                ),
                Spacer(),
                if (file.isAvailableForViewing)
                  TextButton.icon(
                    onPressed: () => _openFullPreview(context),
                    icon: Icon(Symbols.open_in_full, size: 18.sp),
                    label: Text('Full View'),
                    style: TextButton.styleFrom(
                      foregroundColor: theme.colorScheme.primary,
                    ),
                  ),
              ],
            ),
          ),

          // Preview content
          _buildPreviewContent(context),
        ],
      ),
    );
  }

  Widget _buildPreviewContent(BuildContext context) {
    if (!file.isAvailableForViewing) {
      return _buildUnavailablePreview(context);
    }

    switch (file.fileType) {
      case LibraryFileType.image:
        return _buildImagePreview(context);
      case LibraryFileType.pdf:
        return _buildPdfPreview(context);
      case LibraryFileType.video:
        return _buildVideoPreview(context);
      case LibraryFileType.audio:
        return _buildAudioPreview(context);
      case LibraryFileType.document:
      case LibraryFileType.spreadsheet:
      case LibraryFileType.presentation:
        return _buildDocumentPreview(context);
      case LibraryFileType.archive:
        return _buildArchivePreview(context);
      case LibraryFileType.code:
      case LibraryFileType.link:
      case LibraryFileType.note:
      case LibraryFileType.ebook:
      case LibraryFileType.model3d:
      case LibraryFileType.font:
      case LibraryFileType.other:
        return _buildGenericPreview(context);
    }
  }

  Widget _buildImagePreview(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      height: 200.h,
      margin: EdgeInsets.fromLTRB(16.w, 0, 16.w, 16.h),
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: file.thumbnailUrl != null
          ? ClipRRect(
              borderRadius: BorderRadius.circular(12.r),
              child: Image.network(
                file.thumbnailUrl!,
                width: double.infinity,
                height: double.infinity,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) =>
                    _buildPreviewPlaceholder(
                      context,
                      icon: Symbols.broken_image,
                      message: 'Failed to load image',
                    ),
              ),
            )
          : _buildPreviewPlaceholder(
              context,
              icon: Symbols.image,
              message: 'Image preview',
            ),
    );
  }

  Widget _buildPdfPreview(BuildContext context) {
    return Container(
      height: 200.h,
      margin: EdgeInsets.fromLTRB(16.w, 0, 16.w, 16.h),
      child: file.thumbnailUrl != null
          ? ClipRRect(
              borderRadius: BorderRadius.circular(12.r),
              child: Image.network(
                file.thumbnailUrl!,
                width: double.infinity,
                height: double.infinity,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) =>
                    _buildPreviewPlaceholder(
                      context,
                      icon: Symbols.picture_as_pdf,
                      message: 'PDF document',
                    ),
              ),
            )
          : _buildPreviewPlaceholder(
              context,
              icon: Symbols.picture_as_pdf,
              message: 'PDF document',
            ),
    );
  }

  Widget _buildVideoPreview(BuildContext context) {
    return Container(
      height: 200.h,
      margin: EdgeInsets.fromLTRB(16.w, 0, 16.w, 16.h),
      child: Stack(
        children: [
          file.thumbnailUrl != null
              ? ClipRRect(
                  borderRadius: BorderRadius.circular(12.r),
                  child: Image.network(
                    file.thumbnailUrl!,
                    width: double.infinity,
                    height: double.infinity,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) =>
                        _buildPreviewPlaceholder(
                          context,
                          icon: Symbols.movie,
                          message: 'Video file',
                        ),
                  ),
                )
              : _buildPreviewPlaceholder(
                  context,
                  icon: Symbols.movie,
                  message: 'Video file',
                ),
          // Play button overlay
          Positioned.fill(
            child: Center(
              child: Container(
                width: 60.w,
                height: 60.h,
                decoration: BoxDecoration(
                  color: Colors.black.withValues(alpha: 0.7),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Symbols.play_arrow,
                  size: 32.sp,
                  color: Colors.white,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAudioPreview(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      height: 120.h,
      margin: EdgeInsets.fromLTRB(16.w, 0, 16.w, 16.h),
      padding: EdgeInsets.all(20.w),
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Row(
        children: [
          Container(
            width: 60.w,
            height: 60.h,
            decoration: BoxDecoration(
              color: theme.colorScheme.primary.withValues(alpha: 0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(
              Symbols.audio_file,
              size: 28.sp,
              color: theme.colorScheme.primary,
            ),
          ),
          SizedBox(width: 16.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  'Audio File',
                  style: theme.textTheme.bodyLarge?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: theme.colorScheme.onSurface,
                  ),
                ),
                SizedBox(height: 4.h),
                Text(
                  'Tap to play audio',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                  ),
                ),
              ],
            ),
          ),
          Icon(
            Symbols.play_circle,
            size: 32.sp,
            color: theme.colorScheme.primary,
          ),
        ],
      ),
    );
  }

  Widget _buildDocumentPreview(BuildContext context) {
    return _buildPreviewPlaceholder(
      context,
      icon: file.fileType.icon,
      message: '${file.fileType.label} document',
    );
  }

  Widget _buildArchivePreview(BuildContext context) {
    return _buildPreviewPlaceholder(
      context,
      icon: Symbols.folder_zip,
      message: 'Archive file',
    );
  }

  Widget _buildGenericPreview(BuildContext context) {
    return _buildPreviewPlaceholder(
      context,
      icon: Symbols.description,
      message: 'File preview',
    );
  }

  Widget _buildUnavailablePreview(BuildContext context) {
    return _buildPreviewPlaceholder(
      context,
      icon: Symbols.cloud_off,
      message: 'Preview not available',
    );
  }

  Widget _buildPreviewPlaceholder(
    BuildContext context, {
    required IconData icon,
    required String message,
  }) {
    final theme = Theme.of(context);

    return Container(
      height: 150.h,
      margin: EdgeInsets.fromLTRB(16.w, 0, 16.w, 16.h),
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              size: 48.sp,
              color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
            ),
            SizedBox(height: 12.h),
            Text(
              message,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _openFullPreview(BuildContext context) {
    // TODO: Implement full preview
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Opening full preview for ${file.displayTitle}')),
    );
  }
}
