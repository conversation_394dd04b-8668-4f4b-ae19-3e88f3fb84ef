import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_symbols_icons/symbols.dart';
import 'package:scholara_student/features/digital_library/enums/library_file_type.dart';

import '../../../../core/providers/auth_providers.dart';
import '../../controllers/digital_library_controller.dart';
import '../../models/library_file_model.dart';
import '../../models/library_folder_model.dart';
import '../../enums/file_access_type.dart';
import '../../enums/file_usage_type.dart';
import '../../widgets/folder_dropdown_selector.dart';
import '../../widgets/access_type_toggle.dart';

/// Screen for editing file details
class EditFileScreen extends ConsumerStatefulWidget {
  final LibraryFileModel file;

  const EditFileScreen({super.key, required this.file});

  @override
  ConsumerState<EditFileScreen> createState() => _EditFileScreenState();
}

class _EditFileScreenState extends ConsumerState<EditFileScreen> {
  late final TextEditingController _titleController;
  late final TextEditingController _descriptionController;
  late final TextEditingController _tagsController;
  
  late FileAccessType _selectedAccessType;
  late FileUsageType _selectedUsageType;
  LibraryFolderModel? _selectedFolder;
  List<LibraryFolderModel> _availableFolders = [];
  bool _isLoading = false;
  bool _isSaving = false;

  @override
  void initState() {
    super.initState();
    _initializeControllers();
    _loadFolders();
  }

  void _initializeControllers() {
    _titleController = TextEditingController(text: widget.file.title);
    _descriptionController = TextEditingController(text: widget.file.description);
    _tagsController = TextEditingController(text: widget.file.tags.join(', '));
    _selectedAccessType = widget.file.accessType;
    _selectedUsageType = widget.file.usageType;
  }

  Future<void> _loadFolders() async {
    setState(() => _isLoading = true);
    
    try {
      final foldersAsync = ref.read(userFoldersProvider);
      foldersAsync.when(
        data: (folders) {
          _availableFolders = folders;
          // Find and set current folder
          if (widget.file.folderId != null) {
            try {
              _selectedFolder = folders.firstWhere(
                (folder) => folder.id == widget.file.folderId,
              );
            } catch (e) {
              _selectedFolder = null;
            }
          }
          setState(() => _isLoading = false);
        },
        loading: () {
          setState(() => _isLoading = true);
        },
        error: (error, stack) {
          setState(() => _isLoading = false);
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text('Failed to load folders: $error')),
            );
          }
        },
      );
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to load folders: $e')),
        );
      }
    }
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    _tagsController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Edit File Details',
          style: theme.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        centerTitle: true,
        actions: [
          if (_isSaving)
            Container(
              margin: EdgeInsets.only(right: 16.w),
              child: Center(
                child: SizedBox(
                  width: 20.w,
                  height: 20.h,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(
                      theme.colorScheme.primary,
                    ),
                  ),
                ),
              ),
            )
          else
            TextButton(
              onPressed: _saveChanges,
              child: Text(
                'Save',
                style: TextStyle(
                  fontWeight: FontWeight.w600,
                  color: theme.colorScheme.primary,
                ),
              ),
            ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: EdgeInsets.all(20.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // File info header
                  _buildFileInfoHeader(theme),
                  
                  SizedBox(height: 32.h),
                  
                  // Title field
                  _buildTextField(
                    controller: _titleController,
                    label: 'Title',
                    hint: 'Enter file title',
                    icon: Symbols.title,
                  ),
                  
                  SizedBox(height: 20.h),
                  
                  // Description field
                  _buildTextField(
                    controller: _descriptionController,
                    label: 'Description',
                    hint: 'Enter file description',
                    icon: Symbols.description,
                    maxLines: 3,
                  ),
                  
                  SizedBox(height: 20.h),
                  
                  // Tags field
                  _buildTextField(
                    controller: _tagsController,
                    label: 'Tags',
                    hint: 'Enter tags separated by commas',
                    icon: Symbols.tag,
                  ),
                  
                  SizedBox(height: 24.h),
                  
                  // Folder selection
                  _buildSectionTitle(theme, 'Location'),
                  SizedBox(height: 12.h),
                  FolderDropdownSelector(
                    folders: _availableFolders,
                    selectedFolder: _selectedFolder,
                    onChanged: (folder) {
                      setState(() => _selectedFolder = folder);
                    },
                  ),
                  
                  SizedBox(height: 24.h),
                  
                  // Access type
                  _buildSectionTitle(theme, 'Access Type'),
                  SizedBox(height: 12.h),
                  AccessTypeToggle(
                    selectedType: _selectedAccessType,
                    onChanged: (type) {
                      setState(() => _selectedAccessType = type);
                    },
                  ),
                  
                  SizedBox(height: 24.h),
                  
                  // Usage type
                  _buildSectionTitle(theme, 'Usage Type'),
                  SizedBox(height: 12.h),
                  _buildUsageTypeDropdown(theme),
                  
                  SizedBox(height: 40.h),
                ],
              ),
            ),
    );
  }

  Widget _buildFileInfoHeader(ThemeData theme) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceContainerHighest,
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Row(
        children: [
          Icon(
            widget.file.fileType.icon,
            size: 32.sp,
            color: widget.file.fileType.color,
          ),
          SizedBox(width: 16.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.file.fileName,
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                SizedBox(height: 4.h),
                Text(
                  '${widget.file.fileType.label} • ${widget.file.fileSizeString}',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    required IconData icon,
    int maxLines = 1,
  }) {
    final theme = Theme.of(context);
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: theme.textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.w600,
            color: theme.colorScheme.onSurface,
          ),
        ),
        SizedBox(height: 8.h),
        TextFormField(
          controller: controller,
          maxLines: maxLines,
          decoration: InputDecoration(
            hintText: hint,
            prefixIcon: Icon(icon, size: 20.sp),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12.r),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12.r),
              borderSide: BorderSide(
                color: theme.colorScheme.outline,
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12.r),
              borderSide: BorderSide(
                color: theme.colorScheme.primary,
                width: 2,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSectionTitle(ThemeData theme, String title) {
    return Text(
      title,
      style: theme.textTheme.titleSmall?.copyWith(
        fontWeight: FontWeight.w600,
        color: theme.colorScheme.onSurface,
      ),
    );
  }

  Widget _buildUsageTypeDropdown(ThemeData theme) {
    return DropdownButtonFormField<FileUsageType>(
      value: _selectedUsageType,
      decoration: InputDecoration(
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12.r),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12.r),
          borderSide: BorderSide(
            color: theme.colorScheme.outline,
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12.r),
          borderSide: BorderSide(
            color: theme.colorScheme.primary,
            width: 2,
          ),
        ),
      ),
      items: FileUsageType.values.map((type) {
        return DropdownMenuItem(
          value: type,
          child: Text(type.label),
        );
      }).toList(),
      onChanged: (type) {
        if (type != null) {
          setState(() => _selectedUsageType = type);
        }
      },
    );
  }

  Future<void> _saveChanges() async {
    if (_isSaving) return;

    setState(() => _isSaving = true);

    try {
      final currentUser = ref.read(currentUserProvider);
      final userId = currentUser.value?.id;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      // Parse tags
      final tags = _tagsController.text
          .split(',')
          .map((tag) => tag.trim())
          .where((tag) => tag.isNotEmpty)
          .toList();

      // Create updated file model
      final updatedFile = widget.file.copyWith(
        title: _titleController.text.trim().isEmpty 
            ? null 
            : _titleController.text.trim(),
        description: _descriptionController.text.trim().isEmpty 
            ? null 
            : _descriptionController.text.trim(),
        tags: tags,
        folderId: _selectedFolder?.id,
        accessType: _selectedAccessType,
        usageType: _selectedUsageType,
        modifiedAt: DateTime.now(),
      );

      // Save changes
      final fileOps = ref.read(fileOperationsProvider);
      await fileOps.updateFile(updatedFile);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('File details updated successfully'),
            backgroundColor: Theme.of(context).colorScheme.primary,
          ),
        );
        Navigator.pop(context, updatedFile);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to update file details: $e'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isSaving = false);
      }
    }
  }
}
