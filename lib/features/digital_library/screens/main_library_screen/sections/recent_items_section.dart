import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../controllers/digital_library_controller.dart';
import '../../../models/library_folder_model.dart';
import '../../../models/library_file_model.dart';
import '../../../enums/offline_access.dart';
import '../../../services/file_operations_service.dart';
import '../../../widgets/folder_tile.dart';
import '../../../widgets/file_list_tile.dart';

/// Recent items section displaying 10-15 recent files and folders
class RecentItemsSection extends ConsumerWidget {
  final List<LibraryFolderModel> folders;
  final List<LibraryFileModel> files;
  final ValueChanged<LibraryFolderModel> onFolderTap;
  final ValueChanged<LibraryFileModel> onFileTap;
  final VoidCallback? onShowAll;

  const RecentItemsSection({
    super.key,
    required this.folders,
    required this.files,
    required this.onFolderTap,
    required this.onFileTap,
    this.onShowAll,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final recentItems = _getRecentItems();

    if (recentItems.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Section header
        Padding(
          padding: EdgeInsets.symmetric(vertical: 4.h),
          child: Row(
            children: [
              Text(
                'Recent Files & Folders',
                style: theme.textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: theme.colorScheme.onSurface,
                ),
              ),
              const Spacer(),
              if (onShowAll != null)
                TextButton(
                  onPressed: onShowAll,
                  child: Text(
                    'Show All',
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: theme.colorScheme.primary,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
            ],
          ),
        ),

        // Recent items list
        ...recentItems.map((item) {
          if (item is LibraryFolderModel) {
            return FolderTile(
              folder: item,
              onTap: () => onFolderTap(item),
              showStats: false,
            );
          } else if (item is LibraryFileModel) {
            return FileListTile(
              file: item,
              onTap: () => onFileTap(item),
              onDownload: () => _downloadFile(context, ref, item),
              onOfflineToggle: () => _toggleOfflineAccess(context, ref, item),
            );
          }
          return const SizedBox.shrink();
        }),

        SizedBox(height: 16.h),
      ],
    );
  }

  /// Get recent items (folders and files) sorted by last accessed/modified date
  List<dynamic> _getRecentItems() {
    final allItems = <dynamic>[];

    // Add folders with recent access
    allItems.addAll(folders.where((f) => f.lastAccessedAt != null));

    // Add files with recent access
    allItems.addAll(files.where((f) => f.lastAccessedAt != null));

    // Sort by last accessed date (most recent first)
    allItems.sort((a, b) {
      final aDate = a is LibraryFolderModel
          ? a.lastAccessedAt ?? a.createdAt
          : (a as LibraryFileModel).lastAccessedAt ?? a.createdAt;
      final bDate = b is LibraryFolderModel
          ? b.lastAccessedAt ?? b.createdAt
          : (b as LibraryFileModel).lastAccessedAt ?? b.createdAt;
      return bDate.compareTo(aDate);
    });

    // Return up to 15 items
    return allItems.take(15).toList();
  }

  void _downloadFile(
    BuildContext context,
    WidgetRef ref,
    LibraryFileModel file,
  ) async {
    try {
      final userId = ref.read(currentUserIdProvider);
      if (userId == 'anonymous') {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Please sign in to download files')),
        );
        return;
      }

      // Show loading indicator
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Starting download of ${file.displayTitle}...')),
      );

      // Download file using FileOperationsService
      final fileOperationsService = FileOperationsService();
      await fileOperationsService.downloadFile(
        file: file,
        userId: userId,
        onProgress: (progress) {
          // Could show progress in a dialog or notification
        },
      );

      // Show success message
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Successfully downloaded ${file.displayTitle}'),
            backgroundColor: Theme.of(context).colorScheme.primary,
          ),
        );
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to download ${file.displayTitle}: $e'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }

  void _toggleOfflineAccess(
    BuildContext context,
    WidgetRef ref,
    LibraryFileModel file,
  ) async {
    try {
      final userId = ref.read(currentUserIdProvider);
      if (userId == 'anonymous') {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Please sign in to manage offline access'),
          ),
        );
        return;
      }

      final isCurrentlyOffline = file.offlineAccess.canAccessOffline;

      if (isCurrentlyOffline) {
        // Remove from offline access
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Removing ${file.displayTitle} from offline access...',
            ),
          ),
        );
        // TODO: Implement actual offline removal logic
      } else {
        // Add to offline access by downloading
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Making ${file.displayTitle} available offline...'),
          ),
        );

        final fileOperationsService = FileOperationsService();
        await fileOperationsService.downloadFile(
          file: file,
          userId: userId,
          forOfflineAccess: true,
        );
      }

      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              isCurrentlyOffline
                  ? 'Removed ${file.displayTitle} from offline access'
                  : 'Made ${file.displayTitle} available offline',
            ),
            backgroundColor: Theme.of(context).colorScheme.primary,
          ),
        );
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to update offline access: $e'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }
}
