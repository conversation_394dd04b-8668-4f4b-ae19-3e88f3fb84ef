import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_symbols_icons/symbols.dart';

import '../../../enums/offline_access.dart';
import '../../../widgets/folder_tile.dart';
import '../../../widgets/file_list_tile.dart';
import '../../../models/library_folder_model.dart';
import '../../../models/library_file_model.dart';

/// Content list for the digital library showing folders and files
class LibraryContentList extends StatelessWidget {
  final List<LibraryFolderModel> folders;
  final List<LibraryFileModel> files;
  final ValueChanged<LibraryFolderModel> onFolderTap;
  final ValueChanged<LibraryFileModel> onFileTap;
  final bool isLoading;
  final bool isEmpty;
  final String searchQuery;

  const LibraryContentList({
    super.key,
    required this.folders,
    required this.files,
    required this.onFolderTap,
    required this.onFileTap,
    this.isLoading = false,
    this.isEmpty = false,
    this.searchQuery = '',
  });

  @override
  Widget build(BuildContext context) {
    if (isLoading) {
      return const LibraryLoadingState();
    }

    if (isEmpty) {
      return LibraryEmptyState(searchQuery: searchQuery);
    }

    return Column(
      children: [
        // Folders section
        if (folders.isNotEmpty) ...[
          _buildSectionHeader(context, 'Folders', folders.length),
          ...folders.map(
            (folder) => FolderTile(
              folder: folder,
              onTap: () => onFolderTap(folder),
              onLongPress: () => _showFolderOptions(context, folder),
            ),
          ),
          SizedBox(height: 16.h),
        ],

        // Files section
        if (files.isNotEmpty) ...[
          _buildSectionHeader(context, 'Files', files.length),
          ...files.map(
            (file) => FileListTile(
              file: file,
              onTap: () => onFileTap(file),
              onLongPress: () => _showFileOptions(context, file),
              onDownload: () => _downloadFile(context, file),
              onOfflineToggle: () => _toggleOfflineAccess(context, file),
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildSectionHeader(BuildContext context, String title, int count) {
    final theme = Theme.of(context);

    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
      child: Row(
        children: [
          Text(
            title,
            style: theme.textTheme.bodyLarge?.copyWith(
              fontWeight: FontWeight.w600,
              color: theme.colorScheme.onSurface,
            ),
          ),
          SizedBox(width: 8.w),
          Container(
            padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 2.h),
            decoration: BoxDecoration(
              color: theme.colorScheme.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12.r),
            ),
            child: Text(
              count.toString(),
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.primary,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showFolderOptions(BuildContext context, LibraryFolderModel folder) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => FolderOptionsBottomSheet(folder: folder),
    );
  }

  void _showFileOptions(BuildContext context, LibraryFileModel file) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => FileOptionsBottomSheet(file: file),
    );
  }

  void _downloadFile(BuildContext context, LibraryFileModel file) {
    // Implement file download - show progress and completion
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Downloading ${file.displayTitle}...'),
        duration: const Duration(seconds: 2),
      ),
    );
    // In real app, this would trigger actual download
  }

  void _toggleOfflineAccess(BuildContext context, LibraryFileModel file) {
    // Implement offline access toggle - update file state
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          file.offlineAccess.canAccessOffline
              ? 'Removed ${file.displayTitle} from offline access'
              : 'Added ${file.displayTitle} to offline access',
        ),
        duration: const Duration(seconds: 2),
      ),
    );
    // In real app, this would update the file's offline status
  }
}

/// Loading state for the library content
class LibraryLoadingState extends StatelessWidget {
  const LibraryLoadingState({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(color: theme.colorScheme.primary),
          SizedBox(height: 16.h),
          Text(
            'Loading library...',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
            ),
          ),
        ],
      ),
    );
  }
}

/// Empty state for the library content
class LibraryEmptyState extends StatelessWidget {
  final String searchQuery;

  const LibraryEmptyState({super.key, this.searchQuery = ''});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isSearching = searchQuery.isNotEmpty;

    return Center(
      child: Padding(
        padding: EdgeInsets.all(32.w),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              isSearching ? Symbols.search_off : Symbols.folder_open,
              size: 64.sp,
              color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
            ),

            SizedBox(height: 24.h),

            Text(
              isSearching ? 'No results found' : 'Your library is empty',
              style: theme.textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.w600,
                color: theme.colorScheme.onSurface,
              ),
            ),

            SizedBox(height: 8.h),

            Text(
              isSearching
                  ? 'Try adjusting your search terms or filters'
                  : 'Upload your first file to get started',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
              ),
              textAlign: TextAlign.center,
            ),

            if (!isSearching) ...[
              SizedBox(height: 24.h),
              ElevatedButton.icon(
                onPressed: () {
                  Navigator.pushNamed(context, '/digital-library/upload');
                },
                icon: Icon(Symbols.upload),
                label: Text('Upload Files'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: theme.colorScheme.primary,
                  foregroundColor: theme.colorScheme.onPrimary,
                  padding: EdgeInsets.symmetric(
                    horizontal: 24.w,
                    vertical: 12.h,
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}

/// Bottom sheet for folder options
class FolderOptionsBottomSheet extends StatelessWidget {
  final LibraryFolderModel folder;

  const FolderOptionsBottomSheet({super.key, required this.folder});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20.r)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Handle
          Container(
            width: 40.w,
            height: 4.h,
            margin: EdgeInsets.only(top: 12.h),
            decoration: BoxDecoration(
              color: theme.dividerColor,
              borderRadius: BorderRadius.circular(2.r),
            ),
          ),

          // Options
          ListTile(
            leading: Icon(Symbols.info),
            title: Text('Folder Details'),
            onTap: () {
              Navigator.pop(context);
              // Show folder details - placeholder implementation
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('Showing details for ${folder.name}')),
              );
            },
          ),
          ListTile(
            leading: Icon(Symbols.edit),
            title: Text('Rename'),
            onTap: () {
              Navigator.pop(context);
              // Rename folder - placeholder implementation
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('Rename ${folder.name} - Coming soon')),
              );
            },
          ),
          ListTile(
            leading: Icon(Symbols.share),
            title: Text('Share'),
            onTap: () {
              Navigator.pop(context);
              // Share folder - placeholder implementation
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('Sharing ${folder.name} - Coming soon')),
              );
            },
          ),
          ListTile(
            leading: Icon(Symbols.delete, color: Colors.red),
            title: Text('Delete', style: TextStyle(color: Colors.red)),
            onTap: () {
              Navigator.pop(context);
              // Delete folder - placeholder implementation
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('Delete ${folder.name} - Coming soon')),
              );
            },
          ),

          SizedBox(height: MediaQuery.of(context).padding.bottom + 16.h),
        ],
      ),
    );
  }
}

/// Bottom sheet for file options
class FileOptionsBottomSheet extends StatelessWidget {
  final LibraryFileModel file;

  const FileOptionsBottomSheet({super.key, required this.file});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20.r)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Handle
          Container(
            width: 40.w,
            height: 4.h,
            margin: EdgeInsets.only(top: 12.h),
            decoration: BoxDecoration(
              color: theme.dividerColor,
              borderRadius: BorderRadius.circular(2.r),
            ),
          ),

          // Options
          ListTile(
            leading: Icon(Symbols.visibility),
            title: Text('View'),
            onTap: () {
              Navigator.pop(context);
              // View file - placeholder implementation
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('Opening ${file.displayTitle}')),
              );
            },
          ),
          if (file.canDownload)
            ListTile(
              leading: Icon(Symbols.download),
              title: Text('Download'),
              onTap: () {
                Navigator.pop(context);
                // Download file - implement directly
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('Downloading ${file.displayTitle}...'),
                    duration: const Duration(seconds: 2),
                  ),
                );
              },
            ),
          ListTile(
            leading: Icon(Symbols.share),
            title: Text('Share'),
            onTap: () {
              Navigator.pop(context);
              // Share file - placeholder implementation
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Sharing ${file.displayTitle} - Coming soon'),
                ),
              );
            },
          ),
          ListTile(
            leading: Icon(Symbols.edit),
            title: Text('Rename'),
            onTap: () {
              Navigator.pop(context);
              // Rename file - placeholder implementation
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Rename ${file.displayTitle} - Coming soon'),
                ),
              );
            },
          ),
          ListTile(
            leading: Icon(Symbols.delete, color: Colors.red),
            title: Text('Delete', style: TextStyle(color: Colors.red)),
            onTap: () {
              Navigator.pop(context);
              // Delete file - placeholder implementation
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Delete ${file.displayTitle} - Coming soon'),
                ),
              );
            },
          ),

          SizedBox(height: MediaQuery.of(context).padding.bottom + 16.h),
        ],
      ),
    );
  }
}
