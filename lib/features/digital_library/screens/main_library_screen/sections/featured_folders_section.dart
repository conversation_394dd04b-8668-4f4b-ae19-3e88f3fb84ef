import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../models/library_folder_model.dart';
import '../../../models/library_file_model.dart';
import '../../../enums/folder_type.dart';
import '../../../enums/file_access_type.dart';
import '../../../enums/file_usage_type.dart';
import '../../../enums/offline_access.dart';
import '../../../widgets/folder_grid_tile.dart';

/// Featured folders section displaying 4-6 folders in a grid layout
/// These can be actual folders or virtual folders based on metadata
class FeaturedFoldersSection extends StatelessWidget {
  final List<LibraryFolderModel> folders;
  final List<LibraryFileModel> files;
  final ValueChanged<LibraryFolderModel> onFolderTap;
  final VoidCallback? onShowAll;

  const FeaturedFoldersSection({
    super.key,
    required this.folders,
    required this.files,
    required this.onFolderTap,
    this.onShowAll,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final featuredFolders = _generateFeaturedFolders();

    if (featuredFolders.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Section header
        Padding(
          padding: EdgeInsets.symmetric(vertical: 8.h),
          child: Row(
            children: [
              Text(
                'Featured Folders',
                style: theme.textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: theme.colorScheme.onSurface,
                ),
              ),
              const Spacer(),
              if (onShowAll != null)
                TextButton(
                  onPressed: onShowAll,
                  child: Text(
                    'Show All',
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: theme.colorScheme.primary,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
            ],
          ),
        ),

        // Grid of featured folders
        GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: _getGridCrossAxisCount(featuredFolders.length),
            crossAxisSpacing: 8.w,
            mainAxisSpacing: 8.h,
            childAspectRatio: 1.2,
          ),
          itemCount: featuredFolders.length,
          itemBuilder: (context, index) {
            final folder = featuredFolders[index];
            return FolderGridTile(
              folder: folder,
              onTap: () => onFolderTap(folder),
            );
          },
        ),
      ],
    );
  }

  /// Generate featured folders with only 4 fixed folders
  /// Includes: Personal, Shared with me, Class Resources, Downloaded/Offline
  List<LibraryFolderModel> _generateFeaturedFolders() {
    return _generateFixedFolders();
  }

  /// Generate the 4 fixed folders that should always be present
  /// Personal, Shared with me, Class Resources, Downloaded/Offline
  /// Note: File counts are estimated since we don't have access to repository here
  List<LibraryFolderModel> _generateFixedFolders() {
    final fixedFolders = <LibraryFolderModel>[];
    final now = DateTime.now();

    // 1. Personal folder
    final personalFiles = files
        .where((f) => f.usageType == FileUsageType.personal)
        .length;
    fixedFolders.add(
      LibraryFolderModel(
        id: 'fixed_personal',
        name: 'Personal',
        description: 'Your personal files and documents',
        folderType: FolderType.personal,
        accessType: FileAccessType.private,
        usageType: FileUsageType.personal,
        fileCount: personalFiles,
        createdAt: now,
        lastAccessedAt: now,
        isPinned: true,
        tags: ['personal'],
        creatorId: 'system',
        creatorName: 'System',
      ),
    );

    // 2. Shared with me folder
    final sharedFiles = files
        .where((f) => f.usageType == FileUsageType.shared)
        .length;
    fixedFolders.add(
      LibraryFolderModel(
        id: 'fixed_shared',
        name: 'Shared with me',
        description: 'Files shared with you by others',
        folderType: FolderType.shared,
        accessType: FileAccessType.restricted,
        usageType: FileUsageType.shared,
        fileCount: sharedFiles,
        createdAt: now,
        lastAccessedAt: now,
        isPinned: true,
        tags: ['shared'],
        creatorId: 'system',
        creatorName: 'System',
      ),
    );

    // 3. Class Resources folder
    final classResourceFiles = files
        .where((f) => f.usageType == FileUsageType.classResource)
        .length;
    fixedFolders.add(
      LibraryFolderModel(
        id: 'fixed_class_resources',
        name: 'Class Resources',
        description: 'Educational materials and resources for classes',
        folderType: FolderType.resource,
        accessType: FileAccessType.publicDownloadable,
        usageType: FileUsageType.classResource,
        fileCount: classResourceFiles,
        createdAt: now,
        lastAccessedAt: now,
        isPinned: true,
        tags: ['class', 'resources'],
        creatorId: 'system',
        creatorName: 'System',
      ),
    );

    // 4. Downloaded/Offline folder
    final downloadedFiles = files
        .where((f) => f.offlineAccess == OfflineAccess.available)
        .length;
    fixedFolders.add(
      LibraryFolderModel(
        id: 'fixed_downloaded',
        name: 'Downloaded',
        description: 'Files available for offline access',
        folderType: FolderType.general,
        accessType: FileAccessType.publicDownloadable,
        usageType: FileUsageType.library,
        fileCount: downloadedFiles,
        createdAt: now,
        lastAccessedAt: now,
        isPinned: true,
        tags: ['downloaded', 'offline'],
        creatorId: 'system',
        creatorName: 'System',
      ),
    );

    return fixedFolders;
  }

  /// Determine the number of columns based on folder count
  /// 4 folders: 2x2, 5-6 folders: 2x3, 7-8 folders: 2x4
  int _getGridCrossAxisCount(int folderCount) {
    if (folderCount <= 4) {
      return 2; // 2x2 grid
    } else if (folderCount <= 6) {
      return 2; // 2x3 grid
    } else {
      return 2; // 2x4 grid (max 8 folders)
    }
  }
}
