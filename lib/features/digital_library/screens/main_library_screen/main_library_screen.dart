import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_symbols_icons/symbols.dart';
import 'package:go_router/go_router.dart';
import '../../../../core/routes/app_routes.dart';

import '../../controllers/digital_library_controller.dart';
import '../../controllers/upload_controller.dart';
import '../../enums/upload_status.dart';
import '../../models/upload_session_model.dart';
import 'sections/featured_folders_section.dart';
import 'sections/recent_items_section.dart';

/// Main digital library screen with tabs, search, and content listing
class MainLibraryScreen extends ConsumerWidget {
  const MainLibraryScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Watch providers for data
    final featuredFoldersAsync = ref.watch(featuredFoldersProvider);
    final recentFilesAsync = ref.watch(recentFilesProvider);
    final userFilesAsync = ref.watch(
      userFilesProvider,
    ); // Add this to get all user files
    final activeUploadsCount = ref.watch(activeUploadsCountProvider);
    final hasFailedUploads = ref.watch(hasFailedUploadsProvider);

    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface,
      appBar: _buildAppBar(context, ref, activeUploadsCount, hasFailedUploads),
      body: RefreshIndicator(
        onRefresh: () async {
          // Refresh data
          ref.invalidate(featuredFoldersProvider);
          ref.invalidate(recentFilesProvider);
          ref.invalidate(userFilesProvider);
          ref.invalidate(activeUploadSessionsProvider);
        },
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          padding: EdgeInsets.all(16.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Upload status banner
              if (activeUploadsCount > 0 || hasFailedUploads)
                _buildUploadStatusBanner(
                  context,
                  ref,
                  activeUploadsCount,
                  hasFailedUploads,
                ),

              // Featured folders section
              featuredFoldersAsync.when(
                data: (folders) {
                  debugPrint('📁 Featured folders loaded: ${folders.length}');
                  return userFilesAsync.when(
                    data: (files) {
                      debugPrint(
                        '📄 User files for folder counts: ${files.length}',
                      );
                      return FeaturedFoldersSection(
                        folders: folders,
                        files: files, // Pass actual files for count calculation
                        onFolderTap: (folder) =>
                            _onFolderTap(context, folder.id),
                        onShowAll: () => _onShowAllFolders(context),
                      );
                    },
                    loading: () => FeaturedFoldersSection(
                      folders: folders,
                      files: const [], // Empty while loading files
                      onFolderTap: (folder) => _onFolderTap(context, folder.id),
                      onShowAll: () => _onShowAllFolders(context),
                    ),
                    error: (_, __) => FeaturedFoldersSection(
                      folders: folders,
                      files: const [], // Empty on error
                      onFolderTap: (folder) => _onFolderTap(context, folder.id),
                      onShowAll: () => _onShowAllFolders(context),
                    ),
                  );
                },
                loading: () => _buildFoldersLoadingState(),
                error: (error, stack) {
                  debugPrint('❌ Error loading featured folders: $error');
                  debugPrint('❌ Stack trace: $stack');
                  return _buildErrorState(
                    context,
                    'Failed to load folders',
                    error.toString(),
                    () => ref.invalidate(featuredFoldersProvider),
                  );
                },
              ),

              // Recent items section
              recentFilesAsync.when(
                data: (files) {
                  debugPrint('📄 Recent files loaded: ${files.length}');
                  return RecentItemsSection(
                    folders: const [], // We'll get folders separately
                    files: files,
                    onFolderTap: (folder) => _onFolderTap(context, folder.id),
                    onFileTap: (file) => _onFileTap(context, file.id),
                    onShowAll: () => _onShowAllItems(context),
                  );
                },
                loading: () => _buildFilesLoadingState(),
                error: (error, stack) {
                  debugPrint('❌ Error loading recent files: $error');
                  debugPrint('❌ Stack trace: $stack');
                  return _buildErrorState(
                    context,
                    'Failed to load recent files',
                    error.toString(),
                    () => ref.invalidate(recentFilesProvider),
                  );
                },
              ),
            ],
          ),
        ),
      ),
      floatingActionButton: _buildFloatingActionButton(context),
    );
  }

  void _onUploadPressed(BuildContext context) {
    context.pushNamed(RouteNames.uploadFile);
  }

  void _onSearchPressed(BuildContext context) {
    context.pushNamed(RouteNames.librarySearch);
  }

  void _onFolderTap(BuildContext context, String folderId) {
    // Handle virtual/smart folders differently
    if (folderId.startsWith('fixed_') || folderId.startsWith('smart_')) {
      // Navigate to virtual folder detail screen
      context.pushNamed(
        RouteNames.folderDetail,
        pathParameters: {'id': folderId},
      );
    } else {
      // Navigate to actual folder detail
      context.pushNamed(
        RouteNames.folderDetail,
        pathParameters: {'id': folderId},
      );
    }
  }

  void _onFileTap(BuildContext context, String fileId) {
    context.pushNamed(RouteNames.fileDetail, pathParameters: {'id': fileId});
  }

  void _onShowAllFolders(BuildContext context) {
    context.pushNamed(
      RouteNames.libraryAllItems,
      queryParameters: {'filter': 'folders'},
    );
  }

  void _onShowAllItems(BuildContext context) {
    context.pushNamed(RouteNames.libraryAllItems);
  }

  AppBar _buildAppBar(
    BuildContext context,
    WidgetRef ref,
    int activeUploadsCount,
    bool hasFailedUploads,
  ) {
    return AppBar(
      backgroundColor: Theme.of(context).colorScheme.surface,
      elevation: 0,
      title: Text(
        'Digital Library',
        style: Theme.of(
          context,
        ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.w600),
      ),
      actions: [
        // Upload status indicator
        if (activeUploadsCount > 0)
          Container(
            margin: EdgeInsets.only(right: 8.w),
            child: Stack(
              children: [
                IconButton(
                  onPressed: () => _showUploadProgress(context, ref),
                  icon: Icon(
                    Symbols.cloud_upload,
                    size: 24.w,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ),
                if (activeUploadsCount > 0)
                  Positioned(
                    right: 6,
                    top: 6,
                    child: Container(
                      padding: EdgeInsets.all(2.w),
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.error,
                        borderRadius: BorderRadius.circular(8.r),
                      ),
                      constraints: BoxConstraints(
                        minWidth: 16.w,
                        minHeight: 16.h,
                      ),
                      child: Text(
                        activeUploadsCount.toString(),
                        style: Theme.of(context).textTheme.labelSmall?.copyWith(
                          color: Theme.of(context).colorScheme.onError,
                          fontSize: 10.sp,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),
              ],
            ),
          ),

        // Failed uploads indicator
        if (hasFailedUploads)
          IconButton(
            onPressed: () => _showFailedUploads(context, ref),
            icon: Icon(
              Symbols.error,
              size: 24.w,
              color: Theme.of(context).colorScheme.error,
            ),
          ),

        IconButton(
          onPressed: () => _onSearchPressed(context),
          icon: Icon(Symbols.search, size: 24.w),
        ),
        SizedBox(width: 8.w),
      ],
    );
  }

  Widget _buildFloatingActionButton(BuildContext context) {
    return FloatingActionButton(
      onPressed: () => _onUploadPressed(context),
      backgroundColor: Theme.of(context).colorScheme.primary,
      foregroundColor: Theme.of(context).colorScheme.onPrimary,
      child: Icon(Symbols.add, size: 24.w),
    );
  }

  Widget _buildUploadStatusBanner(
    BuildContext context,
    WidgetRef ref,
    int activeUploadsCount,
    bool hasFailedUploads,
  ) {
    return Container(
      margin: EdgeInsets.only(bottom: 16.h),
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: hasFailedUploads
            ? Theme.of(context).colorScheme.errorContainer
            : Theme.of(context).colorScheme.primaryContainer,
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Row(
        children: [
          Icon(
            hasFailedUploads ? Symbols.error : Symbols.cloud_upload,
            size: 20.w,
            color: hasFailedUploads
                ? Theme.of(context).colorScheme.onErrorContainer
                : Theme.of(context).colorScheme.onPrimaryContainer,
          ),
          SizedBox(width: 8.w),
          Expanded(
            child: Text(
              hasFailedUploads
                  ? 'Some uploads failed. Tap to retry.'
                  : '$activeUploadsCount file${activeUploadsCount == 1 ? '' : 's'} uploading...',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: hasFailedUploads
                    ? Theme.of(context).colorScheme.onErrorContainer
                    : Theme.of(context).colorScheme.onPrimaryContainer,
              ),
            ),
          ),
          TextButton(
            onPressed: () => hasFailedUploads
                ? _showFailedUploads(context, ref)
                : _showUploadProgress(context, ref),
            child: Text(
              hasFailedUploads ? 'View' : 'Details',
              style: TextStyle(
                color: hasFailedUploads
                    ? Theme.of(context).colorScheme.onErrorContainer
                    : Theme.of(context).colorScheme.onPrimaryContainer,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFoldersLoadingState() {
    return SizedBox(
      height: 200.h,
      child: Center(child: CircularProgressIndicator()),
    );
  }

  Widget _buildFilesLoadingState() {
    return SizedBox(
      height: 300.h,
      child: Center(child: CircularProgressIndicator()),
    );
  }

  Widget _buildErrorState(
    BuildContext context,
    String title,
    String message,
    VoidCallback onRetry,
  ) {
    return Container(
      padding: EdgeInsets.all(16.w),
      child: Column(
        children: [
          Icon(
            Symbols.error,
            size: 48.w,
            color: Theme.of(context).colorScheme.error,
          ),
          SizedBox(height: 16.h),
          Text(
            title,
            style: Theme.of(context).textTheme.titleMedium,
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 8.h),
          Text(
            message,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 16.h),
          ElevatedButton(onPressed: onRetry, child: Text('Retry')),
        ],
      ),
    );
  }

  void _showUploadProgress(BuildContext context, WidgetRef ref) {
    final activeUploads = ref.read(activeUploadSessionsProvider);

    activeUploads.when(
      data: (sessions) {
        if (sessions.isEmpty) {
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(const SnackBar(content: Text('No active uploads')));
          return;
        }

        _showUploadProgressDialog(context, ref, sessions);
      },
      loading: () {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Loading upload progress...')),
        );
      },
      error: (error, stack) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to load upload progress: $error')),
        );
      },
    );
  }

  void _showFailedUploads(BuildContext context, WidgetRef ref) {
    final failedUploads = ref.read(userUploadSessionsProvider);

    failedUploads.when(
      data: (sessions) {
        final failedSessions = sessions
            .where((session) => session.status == UploadStatus.failed)
            .toList();

        if (failedSessions.isEmpty) {
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(const SnackBar(content: Text('No failed uploads')));
          return;
        }

        _showFailedUploadsDialog(context, ref, failedSessions);
      },
      loading: () {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Loading failed uploads...')),
        );
      },
      error: (error, stack) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to load upload history: $error')),
        );
      },
    );
  }

  /// Show upload progress dialog
  void _showUploadProgressDialog(
    BuildContext context,
    WidgetRef ref,
    List<UploadSessionModel> sessions,
  ) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Active Uploads'),
        content: SizedBox(
          width: double.maxFinite,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                '${sessions.length} session(s) uploading',
                style: Theme.of(context).textTheme.bodyMedium,
              ),
              SizedBox(height: 16.h),
              ...sessions.expand(
                (session) => session.files.map(
                  (file) => Padding(
                    padding: EdgeInsets.only(bottom: 8.h),
                    child: _buildUploadProgressItem(context, session, file),
                  ),
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              context.push(AppRoutes.uploadFile);
            },
            child: const Text('View Details'),
          ),
        ],
      ),
    );
  }

  /// Show failed uploads dialog
  void _showFailedUploadsDialog(
    BuildContext context,
    WidgetRef ref,
    List<UploadSessionModel> sessions,
  ) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Failed Uploads'),
        content: SizedBox(
          width: double.maxFinite,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                '${sessions.length} session(s) failed',
                style: Theme.of(context).textTheme.bodyMedium,
              ),
              SizedBox(height: 16.h),
              ...sessions.expand(
                (session) => session.files.map(
                  (file) => Padding(
                    padding: EdgeInsets.only(bottom: 8.h),
                    child: _buildFailedUploadItem(context, ref, session, file),
                  ),
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              context.push(AppRoutes.uploadFile);
            },
            child: const Text('Retry All'),
          ),
        ],
      ),
    );
  }

  /// Build upload progress item
  Widget _buildUploadProgressItem(
    BuildContext context,
    UploadSessionModel session,
    UploadFileItem file,
  ) {
    final theme = Theme.of(context);

    return Container(
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceContainerHighest,
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Text(
                  file.fileName,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              Text(
                '${(file.progress * 100).toInt()}%',
                style: theme.textTheme.bodySmall?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          SizedBox(height: 8.h),
          LinearProgressIndicator(
            value: file.progress,
            backgroundColor: theme.colorScheme.surfaceContainerHighest,
            valueColor: AlwaysStoppedAnimation<Color>(
              theme.colorScheme.primary,
            ),
          ),
        ],
      ),
    );
  }

  /// Build failed upload item
  Widget _buildFailedUploadItem(
    BuildContext context,
    WidgetRef ref,
    UploadSessionModel session,
    UploadFileItem file,
  ) {
    final theme = Theme.of(context);

    return Container(
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: theme.colorScheme.errorContainer.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(
          color: theme.colorScheme.error.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Icon(Symbols.error, size: 20.sp, color: theme.colorScheme.error),
          SizedBox(width: 12.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  file.fileName,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                if (session.errorMessage != null) ...[
                  SizedBox(height: 2.h),
                  Text(
                    session.errorMessage!,
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.error,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ],
            ),
          ),
          IconButton(
            onPressed: () async {
              // Retry upload
              final uploadOps = ref.read(uploadOperationsProvider);
              try {
                await uploadOps.updateUploadProgress(
                  session.id,
                  0.0,
                  UploadStatus.pending,
                );
                if (context.mounted) {
                  Navigator.pop(context);
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('Upload retry initiated')),
                  );
                }
              } catch (e) {
                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text('Failed to retry upload: $e')),
                  );
                }
              }
            },
            icon: Icon(
              Symbols.refresh,
              size: 20.sp,
              color: theme.colorScheme.primary,
            ),
            tooltip: 'Retry',
          ),
        ],
      ),
    );
  }
}
