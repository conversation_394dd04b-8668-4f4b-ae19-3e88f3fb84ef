import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_symbols_icons/symbols.dart';
import 'package:go_router/go_router.dart';

import '../../models/library_folder_model.dart';
import '../../models/library_file_model.dart';
import '../../enums/file_usage_type.dart';
import '../../../debug/mock_data/generators/digital_library_mock_generator.dart';
import '../../widgets/filter_tabs.dart';
import '../../widgets/folder_tile.dart';
import '../../widgets/file_list_tile.dart';

/// Dedicated search page for the digital library
class LibrarySearchScreen extends StatefulWidget {
  const LibrarySearchScreen({super.key});

  @override
  State<LibrarySearchScreen> createState() => _LibrarySearchScreenState();
}

/// Enum for sorting options
enum SortOption { name, dateCreated, dateModified, size, type }

/// Extension for sort option labels
extension SortOptionExtension on SortOption {
  String get label {
    switch (this) {
      case SortOption.name:
        return 'Name';
      case SortOption.dateCreated:
        return 'Date Created';
      case SortOption.dateModified:
        return 'Date Modified';
      case SortOption.size:
        return 'Size';
      case SortOption.type:
        return 'Type';
    }
  }
}

class _LibrarySearchScreenState extends State<LibrarySearchScreen> {
  final TextEditingController _searchController = TextEditingController();
  final FocusNode _searchFocusNode = FocusNode();

  String _searchQuery = '';
  bool _isLoading = false;

  // Filter and sort state
  int _selectedFilterIndex = 0; // 0: All, 1: Public, 2: Private, 3: Offline
  SortOption _selectedSortOption = SortOption.name;
  bool _sortAscending = true;

  // Mock data - in real app this would come from providers
  List<LibraryFolderModel> _allFolders = [];
  List<LibraryFileModel> _allFiles = [];

  @override
  void initState() {
    super.initState();
    _loadMockData();
    // Auto-focus search field when page opens
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _searchFocusNode.requestFocus();
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    _searchFocusNode.dispose();
    super.dispose();
  }

  void _loadMockData() {
    setState(() {
      _isLoading = true;
    });

    // Simulate loading delay
    Future.delayed(const Duration(milliseconds: 500), () {
      if (mounted) {
        setState(() {
          _allFolders = mockLibraryFolders.take(15).toList();
          _allFiles = mockLibraryFiles.take(50).toList();
          _isLoading = false;
        });
      }
    });
  }

  void _onSearchChanged(String query) {
    setState(() {
      _searchQuery = query;
    });
  }

  void _onFilterChanged(int index) {
    setState(() {
      _selectedFilterIndex = index;
    });
  }

  void _showSortOptions() {
    showModalBottomSheet(
      context: context,
      builder: (context) => _buildSortOptionsSheet(),
    );
  }

  Widget _buildSortOptionsSheet() {
    final theme = Theme.of(context);

    return Container(
      padding: EdgeInsets.all(16.w),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Sort by',
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(height: 16.h),
          ...SortOption.values.map(
            (option) => ListTile(
              title: Text(option.label),
              trailing: _selectedSortOption == option
                  ? Icon(
                      _sortAscending
                          ? Symbols.arrow_upward
                          : Symbols.arrow_downward,
                      color: theme.colorScheme.primary,
                    )
                  : null,
              onTap: () {
                setState(() {
                  if (_selectedSortOption == option) {
                    _sortAscending = !_sortAscending;
                  } else {
                    _selectedSortOption = option;
                    _sortAscending = true;
                  }
                });
                Navigator.pop(context);
              },
            ),
          ),
          SizedBox(height: 16.h),
        ],
      ),
    );
  }

  void _onFolderTap(LibraryFolderModel folder) {
    context.pushNamed(
      '/digital-library/folder',
      pathParameters: {'id': folder.id},
    );
  }

  void _onFileTap(LibraryFileModel file) {
    context.pushNamed('/digital-library/file', pathParameters: {'id': file.id});
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final filteredFolders = _getFilteredFolders();
    final filteredFiles = _getFilteredFiles();
    final hasResults = filteredFolders.isNotEmpty || filteredFiles.isNotEmpty;
    final showResults = _searchQuery.isNotEmpty;

    return Scaffold(
      backgroundColor: theme.colorScheme.surface,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        elevation: 0,
        leading: IconButton(
          onPressed: () => context.pop(),
          icon: Icon(Symbols.arrow_back, color: theme.colorScheme.onSurface),
        ),
        title: TextField(
          controller: _searchController,
          focusNode: _searchFocusNode,
          onChanged: _onSearchChanged,
          style: theme.textTheme.bodyLarge?.copyWith(
            color: theme.colorScheme.onSurface,
          ),
          decoration: InputDecoration(
            hintText: 'Search files and folders...',
            hintStyle: theme.textTheme.bodyLarge?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
            ),
            border: InputBorder.none,
            suffixIcon: _searchQuery.isNotEmpty
                ? IconButton(
                    onPressed: () {
                      _searchController.clear();
                      _onSearchChanged('');
                    },
                    icon: Icon(
                      Symbols.close,
                      color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                    ),
                  )
                : null,
          ),
        ),
        actions: [
          IconButton(
            onPressed: _showSortOptions,
            icon: Icon(Symbols.tune, color: theme.colorScheme.onSurface),
          ),
        ],
      ),
      body: Column(
        children: [
          // Filter Tabs
          Padding(
            padding: EdgeInsets.symmetric(vertical: 16.h),
            child: FilterTabs(
              tabs: const [
                'All',
                'Personal',
                'Shared',
                'Class',
                'Homework',
                'Library',
              ],
              selectedIndex: _selectedFilterIndex,
              onTabSelected: _onFilterChanged,
            ),
          ),

          // Search Results
          Expanded(
            child: _isLoading
                ? _buildLoadingState()
                : showResults
                ? hasResults
                      ? _buildSearchResults(filteredFolders, filteredFiles)
                      : _buildEmptyState()
                : _buildInitialState(),
          ),
        ],
      ),
    );
  }

  List<LibraryFolderModel> _getFilteredFolders() {
    var folders = _allFolders;

    // Filter by access type
    if (_selectedFilterIndex != 0) {
      folders = folders.where((folder) {
        switch (_selectedFilterIndex) {
          case 1: // Personal
            return folder.usageType == FileUsageType.personal;
          case 2: // Shared
            return folder.usageType == FileUsageType.shared;
          case 3: // Class
            return folder.usageType == FileUsageType.classResource;
          case 4: // Homework
            return folder.usageType == FileUsageType.homeworkSubmission;
          case 5: // Library
            return folder.usageType == FileUsageType.library;
          default:
            return true;
        }
      }).toList();
    }

    // Filter by search
    if (_searchQuery.isNotEmpty) {
      folders = folders
          .where(
            (folder) =>
                folder.name.toLowerCase().contains(
                  _searchQuery.toLowerCase(),
                ) ||
                (folder.description?.toLowerCase().contains(
                      _searchQuery.toLowerCase(),
                    ) ??
                    false),
          )
          .toList();
    }

    // Sort folders
    folders.sort((a, b) {
      int comparison;
      switch (_selectedSortOption) {
        case SortOption.name:
          comparison = a.name.compareTo(b.name);
          break;
        case SortOption.dateCreated:
          comparison = a.createdAt.compareTo(b.createdAt);
          break;
        case SortOption.dateModified:
          comparison = (a.lastAccessedAt ?? a.createdAt).compareTo(
            b.lastAccessedAt ?? b.createdAt,
          );
          break;
        case SortOption.size:
          comparison = a.fileCount.compareTo(b.fileCount);
          break;
        case SortOption.type:
          comparison = a.folderType.toString().compareTo(
            b.folderType.toString(),
          );
          break;
      }
      return _sortAscending ? comparison : -comparison;
    });

    return folders;
  }

  List<LibraryFileModel> _getFilteredFiles() {
    var files = _allFiles;

    // Filter by access type
    if (_selectedFilterIndex != 0) {
      files = files.where((file) {
        switch (_selectedFilterIndex) {
          case 1: // Personal
            return file.usageType == FileUsageType.personal;
          case 2: // Shared
            return file.usageType == FileUsageType.shared;
          case 3: // Class
            return file.usageType == FileUsageType.classResource;
          case 4: // Homework
            return file.usageType == FileUsageType.homeworkSubmission;
          case 5: // Library
            return file.usageType == FileUsageType.library;
          default:
            return true;
        }
      }).toList();
    }

    // Filter by search
    if (_searchQuery.isNotEmpty) {
      files = files
          .where(
            (file) =>
                file.displayTitle.toLowerCase().contains(
                  _searchQuery.toLowerCase(),
                ) ||
                file.fileName.toLowerCase().contains(
                  _searchQuery.toLowerCase(),
                ) ||
                (file.description?.toLowerCase().contains(
                      _searchQuery.toLowerCase(),
                    ) ??
                    false) ||
                file.tags.any(
                  (tag) =>
                      tag.toLowerCase().contains(_searchQuery.toLowerCase()),
                ),
          )
          .toList();
    }

    // Sort files
    files.sort((a, b) {
      int comparison;
      switch (_selectedSortOption) {
        case SortOption.name:
          comparison = a.displayTitle.compareTo(b.displayTitle);
          break;
        case SortOption.dateCreated:
          comparison = a.createdAt.compareTo(b.createdAt);
          break;
        case SortOption.dateModified:
          comparison = (a.lastAccessedAt ?? a.createdAt).compareTo(
            b.lastAccessedAt ?? b.createdAt,
          );
          break;
        case SortOption.size:
          comparison = (a.fileSizeBytes ?? 0).compareTo(b.fileSizeBytes ?? 0);
          break;
        case SortOption.type:
          comparison = a.fileName
              .split('.')
              .last
              .compareTo(b.fileName.split('.').last);
          break;
      }
      return _sortAscending ? comparison : -comparison;
    });

    return files;
  }

  Widget _buildLoadingState() {
    final theme = Theme.of(context);
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(color: theme.colorScheme.primary),
          SizedBox(height: 16.h),
          Text(
            'Loading...',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInitialState() {
    final theme = Theme.of(context);
    return Center(
      child: Padding(
        padding: EdgeInsets.all(32.w),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Symbols.search,
              size: 64.sp,
              color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
            ),
            SizedBox(height: 24.h),
            Text(
              'Search your library',
              style: theme.textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.w600,
                color: theme.colorScheme.onSurface,
              ),
            ),
            SizedBox(height: 8.h),
            Text(
              'Enter keywords to find files and folders',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    final theme = Theme.of(context);
    return Center(
      child: Padding(
        padding: EdgeInsets.all(32.w),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Symbols.search_off,
              size: 64.sp,
              color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
            ),
            SizedBox(height: 24.h),
            Text(
              'No results found',
              style: theme.textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.w600,
                color: theme.colorScheme.onSurface,
              ),
            ),
            SizedBox(height: 8.h),
            Text(
              'Try adjusting your search terms or filters',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSearchResults(
    List<LibraryFolderModel> folders,
    List<LibraryFileModel> files,
  ) {
    return ListView(
      padding: EdgeInsets.all(16.w),
      children: [
        // Folders section
        if (folders.isNotEmpty) ...[
          _buildSectionHeader('Folders', folders.length),
          ...folders.map(
            (folder) =>
                FolderTile(folder: folder, onTap: () => _onFolderTap(folder)),
          ),
          SizedBox(height: 16.h),
        ],

        // Files section
        if (files.isNotEmpty) ...[
          _buildSectionHeader('Files', files.length),
          ...files.map(
            (file) => FileListTile(file: file, onTap: () => _onFileTap(file)),
          ),
        ],
      ],
    );
  }

  Widget _buildSectionHeader(String title, int count) {
    final theme = Theme.of(context);
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 8.h),
      child: Row(
        children: [
          Text(
            title,
            style: theme.textTheme.bodyLarge?.copyWith(
              fontWeight: FontWeight.w600,
              color: theme.colorScheme.onSurface,
            ),
          ),
          SizedBox(width: 8.w),
          Container(
            padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 2.h),
            decoration: BoxDecoration(
              color: theme.colorScheme.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12.r),
            ),
            child: Text(
              count.toString(),
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.primary,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
