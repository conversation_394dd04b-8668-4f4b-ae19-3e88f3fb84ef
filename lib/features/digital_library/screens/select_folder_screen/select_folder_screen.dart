import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_symbols_icons/symbols.dart';

import '../../controllers/digital_library_controller.dart';
import '../../models/library_folder_model.dart';
import '../../widgets/folder_tile.dart';

/// Screen for selecting a folder to move files to
class SelectFolderScreen extends ConsumerStatefulWidget {
  final String? currentFolderId;
  final String title;

  const SelectFolderScreen({
    super.key,
    this.currentFolderId,
    this.title = 'Select Folder',
  });

  @override
  ConsumerState<SelectFolderScreen> createState() => _SelectFolderScreenState();
}

class _SelectFolderScreenState extends ConsumerState<SelectFolderScreen> {
  String? _currentFolderId;
  List<LibraryFolderModel> _breadcrumbs = [];

  @override
  void initState() {
    super.initState();
    _currentFolderId = widget.currentFolderId;
    _loadBreadcrumbs();
  }

  Future<void> _loadBreadcrumbs() async {
    if (_currentFolderId == null) {
      _breadcrumbs = [];
      return;
    }

    try {
      // Build breadcrumb trail
      final breadcrumbs = <LibraryFolderModel>[];
      String? folderId = _currentFolderId;

      while (folderId != null) {
        final folderAsync = ref.read(folderByIdProvider(folderId));
        final folder = folderAsync.when(
          data: (folder) => folder,
          loading: () => null,
          error: (error, stack) => null,
        );
        if (folder != null) {
          breadcrumbs.insert(0, folder);
          folderId = folder.parentFolderId;
        } else {
          break;
        }
      }

      setState(() {
        _breadcrumbs = breadcrumbs;
      });
    } catch (e) {
      // Handle error silently
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final foldersAsync = _currentFolderId != null
        ? ref.watch(subFoldersProvider(_currentFolderId!))
        : ref.watch(rootFoldersProvider);

    return Scaffold(
      appBar: AppBar(
        title: Text(
          widget.title,
          style: theme.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        centerTitle: true,
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, _currentFolderId),
            child: Text(
              'Select',
              style: TextStyle(
                fontWeight: FontWeight.w600,
                color: theme.colorScheme.primary,
              ),
            ),
          ),
        ],
      ),
      body: Column(
        children: [
          // Breadcrumbs
          if (_breadcrumbs.isNotEmpty || _currentFolderId != null)
            _buildBreadcrumbs(theme),

          // Current location indicator
          Container(
            width: double.infinity,
            padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 12.h),
            color: theme.colorScheme.surfaceContainerHighest,
            child: Row(
              children: [
                Icon(
                  Symbols.folder,
                  size: 20.sp,
                  color: theme.colorScheme.primary,
                ),
                SizedBox(width: 8.w),
                Text(
                  _currentFolderId == null
                      ? 'Root Folder'
                      : _breadcrumbs.isNotEmpty
                      ? _breadcrumbs.last.name
                      : 'Current Folder',
                  style: theme.textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: theme.colorScheme.primary,
                  ),
                ),
                const Spacer(),
                Text(
                  'Tap to select this folder',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          ),

          // Folders list
          Expanded(
            child: foldersAsync.when(
              data: (folders) => _buildFoldersList(folders),
              loading: () => const Center(child: CircularProgressIndicator()),
              error: (error, stack) => _buildErrorState(error),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBreadcrumbs(ThemeData theme) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 12.h),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        border: Border(
          bottom: BorderSide(
            color: theme.colorScheme.outline.withValues(alpha: 0.2),
          ),
        ),
      ),
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Row(
          children: [
            // Root folder
            GestureDetector(
              onTap: () => _navigateToFolder(null),
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
                decoration: BoxDecoration(
                  color: _currentFolderId == null
                      ? theme.colorScheme.primaryContainer
                      : Colors.transparent,
                  borderRadius: BorderRadius.circular(16.r),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Symbols.home,
                      size: 16.sp,
                      color: _currentFolderId == null
                          ? theme.colorScheme.onPrimaryContainer
                          : theme.colorScheme.onSurfaceVariant,
                    ),
                    SizedBox(width: 4.w),
                    Text(
                      'Root',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: _currentFolderId == null
                            ? theme.colorScheme.onPrimaryContainer
                            : theme.colorScheme.onSurfaceVariant,
                        fontWeight: _currentFolderId == null
                            ? FontWeight.w600
                            : FontWeight.normal,
                      ),
                    ),
                  ],
                ),
              ),
            ),

            // Breadcrumb folders
            ..._breadcrumbs.asMap().entries.map((entry) {
              final index = entry.key;
              final folder = entry.value;
              final isLast = index == _breadcrumbs.length - 1;

              return Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Symbols.chevron_right,
                    size: 16.sp,
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                  GestureDetector(
                    onTap: () => _navigateToFolder(folder.id),
                    child: Container(
                      padding: EdgeInsets.symmetric(
                        horizontal: 12.w,
                        vertical: 6.h,
                      ),
                      decoration: BoxDecoration(
                        color: isLast
                            ? theme.colorScheme.primaryContainer
                            : Colors.transparent,
                        borderRadius: BorderRadius.circular(16.r),
                      ),
                      child: Text(
                        folder.name,
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: isLast
                              ? theme.colorScheme.onPrimaryContainer
                              : theme.colorScheme.onSurfaceVariant,
                          fontWeight: isLast
                              ? FontWeight.w600
                              : FontWeight.normal,
                        ),
                      ),
                    ),
                  ),
                ],
              );
            }),
          ],
        ),
      ),
    );
  }

  Widget _buildFoldersList(List<LibraryFolderModel> folders) {
    if (folders.isEmpty) {
      return _buildEmptyState();
    }

    return ListView.builder(
      padding: EdgeInsets.all(20.w),
      itemCount: folders.length,
      itemBuilder: (context, index) {
        final folder = folders[index];
        return Padding(
          padding: EdgeInsets.only(bottom: 12.h),
          child: FolderTile(
            folder: folder,
            onTap: () => _navigateToFolder(folder.id),
          ),
        );
      },
    );
  }

  Widget _buildEmptyState() {
    final theme = Theme.of(context);

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Symbols.folder_off,
            size: 64.sp,
            color: theme.colorScheme.onSurfaceVariant,
          ),
          SizedBox(height: 16.h),
          Text(
            'No subfolders',
            style: theme.textTheme.titleMedium?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            'This folder doesn\'t contain any subfolders',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(Object error) {
    final theme = Theme.of(context);

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Symbols.error, size: 64.sp, color: theme.colorScheme.error),
          SizedBox(height: 16.h),
          Text(
            'Error loading folders',
            style: theme.textTheme.titleMedium?.copyWith(
              color: theme.colorScheme.error,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            error.toString(),
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  void _navigateToFolder(String? folderId) {
    setState(() {
      _currentFolderId = folderId;
    });

    _loadBreadcrumbs();
  }
}
