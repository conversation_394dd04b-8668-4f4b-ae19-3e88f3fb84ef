import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:go_router/go_router.dart';

import '../../enums/file_usage_type.dart';
import '../../widgets/library_content_view.dart';
import '../../../../core/routes/app_routes.dart';

/// Screen showing all folders and files in the digital library
class AllItemsScreen extends ConsumerWidget {
  final String? initialFilter; // 'folders', 'files', or null for all
  final String? initialUsage; // 'personal', 'shared', 'class', etc.

  const AllItemsScreen({super.key, this.initialFilter, this.initialUsage});

  FileUsageType? _getUsageTypeFromString(String usage) {
    switch (usage.toLowerCase()) {
      case 'personal':
        return FileUsageType.personal;
      case 'shared':
        return FileUsageType.shared;
      case 'class':
        return FileUsageType.classResource;
      case 'homework':
        return FileUsageType.homeworkSubmission;
      case 'library':
        return FileUsageType.library;
      default:
        return null;
    }
  }

  void _onFolderTap(BuildContext context, String folderId) {
    context.pushNamed(
      RouteNames.folderDetail,
      pathParameters: {'id': folderId},
    );
  }

  void _onFileTap(BuildContext context, String fileId) {
    context.pushNamed(RouteNames.fileDetail, pathParameters: {'id': fileId});
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final initialUsageType = initialUsage != null
        ? _getUsageTypeFromString(initialUsage!)
        : null;

    return Scaffold(
      appBar: AppBar(
        title: Text(
          'All Items',
          style: theme.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        centerTitle: true,
      ),
      body: LibraryContentView(
        parentFolderId: null, // Root level
        showTabs: true,
        showFilters: true,
        initialFilter: initialFilter,
        initialUsageType: initialUsageType,
        onFolderTap: (folderId) => _onFolderTap(context, folderId),
        onFileTap: (fileId) => _onFileTap(context, fileId),
      ),
    );
  }
}
