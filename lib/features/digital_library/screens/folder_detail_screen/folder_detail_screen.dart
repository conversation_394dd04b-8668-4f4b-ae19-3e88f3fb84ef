import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_symbols_icons/symbols.dart';
import 'package:go_router/go_router.dart';

import '../../models/library_folder_model.dart';
import '../../models/library_file_model.dart';
import '../../enums/file_access_type.dart';
import '../../enums/file_usage_type.dart';
import '../../enums/folder_type.dart';
import '../../enums/offline_access.dart';
import '../../controllers/digital_library_controller.dart';

import '../../../../core/routes/app_routes.dart';

import 'sections/folder_header_section.dart';
import 'sections/folder_content_section.dart';

/// Screen showing details of a specific folder with its contents
class FolderDetailScreen extends ConsumerStatefulWidget {
  final String folderId;

  const FolderDetailScreen({super.key, required this.folderId});

  @override
  ConsumerState<FolderDetailScreen> createState() => _FolderDetailScreenState();
}

class _FolderDetailScreenState extends ConsumerState<FolderDetailScreen> {
  LibraryFolderModel? _folder;
  List<LibraryFolderModel> _subfolders = [];
  List<LibraryFileModel> _files = [];
  bool _isLoading = true;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _loadFolderData();
  }

  Future<void> _loadFolderData() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // Check if this is a virtual folder (fixed_ or smart_)
      if (widget.folderId.startsWith('fixed_') ||
          widget.folderId.startsWith('smart_')) {
        await _loadVirtualFolderData();
      } else {
        await _loadRealFolderData();
      }

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage = 'Failed to load folder: $e';
      });
    }
  }

  Future<void> _loadVirtualFolderData() async {
    final userId = ref.read(currentUserIdProvider);
    final repository = ref.read(digitalLibraryRepositoryProvider);
    final now = DateTime.now();

    // Debug: Check current user ID and total files
    debugPrint('🔍 Loading virtual folder: ${widget.folderId}');
    debugPrint('🔍 Current user ID: $userId');

    // Get total files for debugging
    final allUserFiles = await repository.getFilesForUser(userId);
    debugPrint('🔍 Total files for user: ${allUserFiles.length}');

    if (allUserFiles.isNotEmpty) {
      debugPrint(
        '🔍 Sample file usage types: ${allUserFiles.take(5).map((f) => f.usageType.value).toList()}',
      );
    }

    // Create virtual folder model and get filtered files based on folder type
    List<LibraryFileModel> filteredFiles = [];

    switch (widget.folderId) {
      case 'fixed_personal':
        _folder = LibraryFolderModel(
          id: 'fixed_personal',
          name: 'Personal',
          description: 'Your personal files and documents',
          folderType: FolderType.personal,
          accessType: FileAccessType.private,
          usageType: FileUsageType.personal,
          createdAt: now,
          lastAccessedAt: now,
          isPinned: true,
          tags: ['personal'],
          creatorId: 'system',
          creatorName: 'System',
        );
        // Use repository filtering for better performance
        filteredFiles = await repository.getFilesForUser(
          userId,
          usageType: FileUsageType.personal,
        );
        break;

      case 'fixed_shared':
        _folder = LibraryFolderModel(
          id: 'fixed_shared',
          name: 'Shared with me',
          description: 'Files shared with you by others',
          folderType: FolderType.shared,
          accessType: FileAccessType.restricted,
          usageType: FileUsageType.shared,
          createdAt: now,
          lastAccessedAt: now,
          isPinned: true,
          tags: ['shared'],
          creatorId: 'system',
          creatorName: 'System',
        );
        // Use repository filtering for better performance
        filteredFiles = await repository.getFilesForUser(
          userId,
          usageType: FileUsageType.shared,
        );
        break;

      case 'fixed_class_resources':
        _folder = LibraryFolderModel(
          id: 'fixed_class_resources',
          name: 'Class Resources',
          description: 'Educational materials and resources for classes',
          folderType: FolderType.resource,
          accessType: FileAccessType.publicDownloadable,
          usageType: FileUsageType.classResource,
          createdAt: now,
          lastAccessedAt: now,
          isPinned: true,
          tags: ['class', 'resources'],
          creatorId: 'system',
          creatorName: 'System',
        );
        // Use repository filtering for better performance
        filteredFiles = await repository.getFilesForUser(
          userId,
          usageType: FileUsageType.classResource,
        );
        break;

      case 'fixed_downloaded':
        _folder = LibraryFolderModel(
          id: 'fixed_downloaded',
          name: 'Downloaded',
          description: 'Files available for offline access',
          folderType: FolderType.general,
          accessType: FileAccessType.publicDownloadable,
          usageType: FileUsageType.library,
          createdAt: now,
          lastAccessedAt: now,
          isPinned: true,
          tags: ['downloaded', 'offline'],
          creatorId: 'system',
          creatorName: 'System',
        );
        // For offline files, we need to get all files and filter client-side
        // since there's no repository parameter for offline access
        final allFiles = await repository.getFilesForUser(userId);
        filteredFiles = allFiles
            .where((f) => f.offlineAccess == OfflineAccess.available)
            .toList();
        break;

      default:
        throw Exception('Unknown virtual folder: ${widget.folderId}');
    }

    // Update file count in folder model
    _folder = _folder!.copyWith(fileCount: filteredFiles.length);
    _files = filteredFiles;
    _subfolders = []; // Virtual folders don't have subfolders
  }

  Future<void> _loadRealFolderData() async {
    // Load folder data from repository
    final repository = ref.read(digitalLibraryRepositoryProvider);
    final folder = await repository.getFolderById(widget.folderId);

    if (folder != null) {
      _folder = folder;

      // Load subfolders and files for this folder
      final userId = ref.read(currentUserIdProvider);

      final subfolders = await repository.getFoldersForUser(
        userId,
        parentFolderId: widget.folderId,
      );

      final files = await repository.getFilesForUser(
        userId,
        folderId: widget.folderId,
      );

      _subfolders = subfolders;
      _files = files;
    } else {
      throw Exception('Folder not found');
    }
  }

  void _onSubfolderTap(LibraryFolderModel subfolder) {
    context.pushNamed(
      RouteNames.folderDetail,
      pathParameters: {'id': subfolder.id},
    );
  }

  void _onFileTap(LibraryFileModel file) {
    context.pushNamed(RouteNames.fileDetail, pathParameters: {'id': file.id});
  }

  void _onUploadToFolder() {
    context.pushNamed(RouteNames.uploadFile);
  }

  void _onCreateSubfolder() {
    _showCreateSubfolderDialog();
  }

  void _showCreateSubfolderDialog() {
    showDialog(
      context: context,
      builder: (context) => CreateSubfolderDialog(
        parentFolderId: widget.folderId,
        onFolderCreated: (folder) {
          setState(() {
            _subfolders.add(folder);
          });
        },
      ),
    );
  }

  void _showFolderOptions() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => FolderOptionsBottomSheet(
        folder: _folder!,
        onFolderUpdated: (updatedFolder) {
          setState(() {
            _folder = updatedFolder;
          });
        },
        onFolderDeleted: () {
          Navigator.pop(context);
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: theme.colorScheme.surface,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        elevation: 0,
        title: Text(
          _folder?.name ?? 'Folder',
          style: theme.textTheme.headlineMedium?.copyWith(
            fontWeight: FontWeight.w700,
            color: theme.colorScheme.onSurface,
          ),
        ),
        actions: [
          if (_folder != null)
            IconButton(
              onPressed: _showFolderOptions,
              icon: Icon(Symbols.more_vert, color: theme.colorScheme.onSurface),
            ),
        ],
      ),
      body: _buildBody(),
      floatingActionButton: _folder != null
          ? FloatingActionButton.extended(
              onPressed: _onUploadToFolder,
              backgroundColor: theme.colorScheme.primary,
              foregroundColor: theme.colorScheme.onPrimary,
              icon: Icon(Symbols.upload),
              label: Text(
                'Upload',
                style: theme.textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: theme.colorScheme.onPrimary,
                ),
              ),
            )
          : null,
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Symbols.error,
              size: 64.sp,
              color: Theme.of(context).colorScheme.error,
            ),
            SizedBox(height: 16.h),
            Text(
              _errorMessage!,
              style: Theme.of(context).textTheme.bodyLarge,
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 16.h),
            ElevatedButton(onPressed: _loadFolderData, child: Text('Retry')),
          ],
        ),
      );
    }

    if (_folder == null) {
      return const Center(child: Text('Folder not found'));
    }

    return RefreshIndicator(
      onRefresh: _loadFolderData,
      child: CustomScrollView(
        slivers: [
          // Folder header
          SliverToBoxAdapter(
            child: FolderHeaderSection(
              folder: _folder!,
              onCreateSubfolder: _onCreateSubfolder,
            ),
          ),

          SizedBox(height: 24.h).toSliver(),

          // Folder content
          SliverToBoxAdapter(
            child: FolderContentSection(
              subfolders: _subfolders,
              files: _files,
              onSubfolderTap: _onSubfolderTap,
              onFileTap: _onFileTap,
              isEmpty: _subfolders.isEmpty && _files.isEmpty,
            ),
          ),

          // Bottom padding
          SliverToBoxAdapter(child: SizedBox(height: 100.h)),
        ],
      ),
    );
  }
}

// Extension to convert SizedBox to Sliver
extension SizedBoxSliver on SizedBox {
  Widget toSliver() => SliverToBoxAdapter(child: this);
}

/// Dialog for creating a new subfolder
class CreateSubfolderDialog extends ConsumerStatefulWidget {
  final String parentFolderId;
  final ValueChanged<LibraryFolderModel> onFolderCreated;

  const CreateSubfolderDialog({
    super.key,
    required this.parentFolderId,
    required this.onFolderCreated,
  });

  @override
  ConsumerState<CreateSubfolderDialog> createState() =>
      _CreateSubfolderDialogState();
}

class _CreateSubfolderDialogState extends ConsumerState<CreateSubfolderDialog> {
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  bool _isCreating = false;

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  Future<void> _createFolder() async {
    if (_nameController.text.trim().isEmpty) return;

    setState(() => _isCreating = true);

    try {
      // Get current user ID
      final userId = ref.read(currentUserIdProvider);
      if (userId == 'anonymous') {
        throw Exception('User not authenticated');
      }

      // Create folder via repository
      final newFolder = LibraryFolderModel(
        id: 'folder_${DateTime.now().millisecondsSinceEpoch}',
        name: _nameController.text.trim(),
        description: _descriptionController.text.trim().isEmpty
            ? null
            : _descriptionController.text.trim(),
        folderType: FolderType.general,
        accessType: FileAccessType.private, // Default to private
        parentFolderId: widget.parentFolderId,
        createdAt: DateTime.now(),
        creatorId: userId,
        creatorName: 'Current User', // TODO: Get actual user name from profile
        fileCount: 0,
        subfolderCount: 0,
        totalSizeBytes: 0,
      );

      // Create folder in repository
      final repository = ref.read(digitalLibraryRepositoryProvider);
      final createdFolder = await repository.createFolder(newFolder);

      // Invalidate relevant providers to refresh the UI
      ref.invalidate(userFoldersProvider);
      ref.invalidate(subFoldersProvider(widget.parentFolderId));

      widget.onFolderCreated(createdFolder);
      if (mounted) {
        Navigator.pop(context);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Failed to create folder: $e')));
      }
    } finally {
      if (mounted) {
        setState(() => _isCreating = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text('Create Subfolder'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          TextField(
            controller: _nameController,
            decoration: InputDecoration(
              labelText: 'Folder Name',
              hintText: 'Enter folder name',
            ),
            autofocus: true,
          ),
          SizedBox(height: 16.h),
          TextField(
            controller: _descriptionController,
            decoration: InputDecoration(
              labelText: 'Description (Optional)',
              hintText: 'Enter folder description',
            ),
            maxLines: 3,
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: _isCreating ? null : () => Navigator.pop(context),
          child: Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: _isCreating ? null : _createFolder,
          child: _isCreating
              ? SizedBox(
                  width: 16.w,
                  height: 16.h,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : Text('Create'),
        ),
      ],
    );
  }
}

/// Bottom sheet for folder options
class FolderOptionsBottomSheet extends StatelessWidget {
  final LibraryFolderModel folder;
  final ValueChanged<LibraryFolderModel>? onFolderUpdated;
  final VoidCallback? onFolderDeleted;

  const FolderOptionsBottomSheet({
    super.key,
    required this.folder,
    this.onFolderUpdated,
    this.onFolderDeleted,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20.r)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Handle
          Container(
            width: 40.w,
            height: 4.h,
            margin: EdgeInsets.only(top: 12.h),
            decoration: BoxDecoration(
              color: theme.dividerColor,
              borderRadius: BorderRadius.circular(2.r),
            ),
          ),

          // Options
          ListTile(
            leading: Icon(Symbols.info),
            title: Text('Folder Details'),
            onTap: () {
              Navigator.pop(context);
              // TODO: Show folder details
            },
          ),
          ListTile(
            leading: Icon(Symbols.edit),
            title: Text('Rename'),
            onTap: () {
              Navigator.pop(context);
              // TODO: Rename folder
            },
          ),
          ListTile(
            leading: Icon(Symbols.share),
            title: Text('Share'),
            onTap: () {
              Navigator.pop(context);
              // TODO: Share folder
            },
          ),
          ListTile(
            leading: Icon(Symbols.delete, color: Colors.red),
            title: Text('Delete', style: TextStyle(color: Colors.red)),
            onTap: () {
              Navigator.pop(context);
              onFolderDeleted?.call();
            },
          ),

          SizedBox(height: MediaQuery.of(context).padding.bottom + 16.h),
        ],
      ),
    );
  }
}
