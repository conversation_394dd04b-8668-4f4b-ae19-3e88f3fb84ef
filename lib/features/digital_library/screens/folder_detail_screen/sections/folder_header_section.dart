import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_symbols_icons/symbols.dart';

import '../../../models/library_folder_model.dart';
import '../../../enums/file_access_type.dart';

/// Header section for folder detail screen showing folder info and actions
class FolderHeaderSection extends StatelessWidget {
  final LibraryFolderModel folder;
  final VoidCallback? onCreateSubfolder;

  const FolderHeaderSection({
    super.key,
    required this.folder,
    this.onCreateSubfolder,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      margin: EdgeInsets.all(16.w),
      padding: EdgeInsets.all(20.w),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(16.r),
        border: Border.all(color: theme.dividerColor, width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Folder icon and name
          Row(
            children: [
              Container(
                width: 48.w,
                height: 48.h,
                decoration: BoxDecoration(
                  color: theme.colorScheme.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12.r),
                ),
                child: Icon(
                  Symbols.folder,
                  size: 24.sp,
                  color: theme.colorScheme.primary,
                ),
              ),
              SizedBox(width: 16.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      folder.name,
                      style: theme.textTheme.headlineSmall?.copyWith(
                        fontWeight: FontWeight.w700,
                        color: theme.colorScheme.onSurface,
                      ),
                    ),
                    if (folder.description != null) ...[
                      SizedBox(height: 4.h),
                      Text(
                        folder.description!,
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: theme.colorScheme.onSurface.withValues(
                            alpha: 0.7,
                          ),
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ],
                ),
              ),
            ],
          ),

          SizedBox(height: 20.h),

          // Access type badge
          Row(
            children: [
              Container(
                padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
                decoration: BoxDecoration(
                  color: _getAccessTypeColor(
                    folder.accessType,
                    theme,
                  ).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(20.r),
                  border: Border.all(
                    color: _getAccessTypeColor(
                      folder.accessType,
                      theme,
                    ).withValues(alpha: 0.3),
                    width: 1,
                  ),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      _getAccessTypeIcon(folder.accessType),
                      size: 16.sp,
                      color: _getAccessTypeColor(folder.accessType, theme),
                    ),
                    SizedBox(width: 6.w),
                    Text(
                      folder.accessType.label,
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: _getAccessTypeColor(folder.accessType, theme),
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),
              Spacer(),
              if (onCreateSubfolder != null)
                TextButton.icon(
                  onPressed: onCreateSubfolder,
                  icon: Icon(Symbols.create_new_folder, size: 18.sp),
                  label: Text('New Folder'),
                  style: TextButton.styleFrom(
                    foregroundColor: theme.colorScheme.primary,
                  ),
                ),
            ],
          ),

          SizedBox(height: 20.h),

          // Folder stats
          Row(
            children: [
              Expanded(
                child: _buildStatItem(
                  context,
                  icon: Symbols.folder,
                  label: 'Subfolders',
                  value: folder.subfolderCount.toString(),
                ),
              ),
              SizedBox(width: 16.w),
              Expanded(
                child: _buildStatItem(
                  context,
                  icon: Symbols.description,
                  label: 'Files',
                  value: folder.fileCount.toString(),
                ),
              ),
              SizedBox(width: 16.w),
              Expanded(
                child: _buildStatItem(
                  context,
                  icon: Symbols.storage,
                  label: 'Size',
                  value: folder.totalSizeString,
                ),
              ),
            ],
          ),

          SizedBox(height: 20.h),

          // Folder metadata
          _buildMetadataRow(
            context,
            icon: Symbols.person,
            label: 'Created by',
            value: folder.creatorName,
          ),
          SizedBox(height: 8.h),
          _buildMetadataRow(
            context,
            icon: Symbols.schedule,
            label: 'Created',
            value: _formatDate(folder.createdAt),
          ),
          if (folder.modifiedAt != null) ...[
            SizedBox(height: 8.h),
            _buildMetadataRow(
              context,
              icon: Symbols.edit,
              label: 'Modified',
              value: _formatDate(folder.modifiedAt!),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildStatItem(
    BuildContext context, {
    required IconData icon,
    required String label,
    required String value,
  }) {
    final theme = Theme.of(context);

    return Container(
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Column(
        children: [
          Icon(icon, size: 20.sp, color: theme.colorScheme.primary),
          SizedBox(height: 8.h),
          Text(
            value,
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w700,
              color: theme.colorScheme.onSurface,
            ),
          ),
          SizedBox(height: 2.h),
          Text(
            label,
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMetadataRow(
    BuildContext context, {
    required IconData icon,
    required String label,
    required String value,
  }) {
    final theme = Theme.of(context);

    return Row(
      children: [
        Icon(
          icon,
          size: 16.sp,
          color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
        ),
        SizedBox(width: 8.w),
        Text(
          label,
          style: theme.textTheme.bodySmall?.copyWith(
            color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
          ),
        ),
        SizedBox(width: 8.w),
        Expanded(
          child: Text(
            value,
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurface,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.end,
          ),
        ),
      ],
    );
  }

  Color _getAccessTypeColor(FileAccessType accessType, ThemeData theme) {
    return accessType.color;
  }

  IconData _getAccessTypeIcon(FileAccessType accessType) {
    return accessType.icon;
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays == 0) {
      return 'Today';
    } else if (difference.inDays == 1) {
      return 'Yesterday';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} days ago';
    } else if (difference.inDays < 30) {
      final weeks = (difference.inDays / 7).floor();
      return '$weeks week${weeks > 1 ? 's' : ''} ago';
    } else if (difference.inDays < 365) {
      final months = (difference.inDays / 30).floor();
      return '$months month${months > 1 ? 's' : ''} ago';
    } else {
      final years = (difference.inDays / 365).floor();
      return '$years year${years > 1 ? 's' : ''} ago';
    }
  }
}
