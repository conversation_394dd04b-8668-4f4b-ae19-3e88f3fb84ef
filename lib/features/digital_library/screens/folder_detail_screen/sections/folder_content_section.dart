import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_symbols_icons/symbols.dart';

import '../../../controllers/digital_library_controller.dart';
import '../../../models/library_folder_model.dart';
import '../../../models/library_file_model.dart';
import '../../../enums/offline_access.dart';
import '../../../services/file_operations_service.dart';
import '../../../widgets/folder_tile.dart';
import '../../../widgets/file_list_tile.dart';

/// Content section for folder detail screen showing subfolders and files
class FolderContentSection extends ConsumerWidget {
  final List<LibraryFolderModel> subfolders;
  final List<LibraryFileModel> files;
  final ValueChanged<LibraryFolderModel> onSubfolderTap;
  final ValueChanged<LibraryFileModel> onFileTap;
  final bool isEmpty;

  const FolderContentSection({
    super.key,
    required this.subfolders,
    required this.files,
    required this.onSubfolderTap,
    required this.onFileTap,
    this.isEmpty = false,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    if (isEmpty) {
      return _buildEmptyState(context);
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Subfolders section
        if (subfolders.isNotEmpty) ...[
          _buildSectionHeader(context, 'Subfolders', subfolders.length),
          SizedBox(height: 12.h),
          ...subfolders.map(
            (subfolder) => Padding(
              padding: EdgeInsets.only(bottom: 8.h),
              child: FolderTile(
                folder: subfolder,
                onTap: () => onSubfolderTap(subfolder),
                onLongPress: () => _showSubfolderOptions(context, subfolder),
              ),
            ),
          ),
          SizedBox(height: 24.h),
        ],

        // Files section
        if (files.isNotEmpty) ...[
          _buildSectionHeader(context, 'Files', files.length),
          SizedBox(height: 12.h),
          ...files.map(
            (file) => Padding(
              padding: EdgeInsets.only(bottom: 8.h),
              child: FileListTile(
                file: file,
                onTap: () => onFileTap(file),
                onLongPress: () => _showFileOptions(context, file),
                onDownload: () => _downloadFile(context, ref, file),
                onOfflineToggle: () => _toggleOfflineAccess(context, ref, file),
              ),
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildSectionHeader(BuildContext context, String title, int count) {
    final theme = Theme.of(context);

    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      child: Row(
        children: [
          Text(
            title,
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w700,
              color: theme.colorScheme.onSurface,
            ),
          ),
          SizedBox(width: 8.w),
          Container(
            padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 2.h),
            decoration: BoxDecoration(
              color: theme.colorScheme.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12.r),
            ),
            child: Text(
              count.toString(),
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.primary,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(32.w),
      child: Column(
        children: [
          Icon(
            Symbols.folder_open,
            size: 64.sp,
            color: theme.colorScheme.onSurface.withValues(alpha: 0.4),
          ),
          SizedBox(height: 24.h),
          Text(
            'This folder is empty',
            style: theme.textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.w600,
              color: theme.colorScheme.onSurface,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            'Upload files or create subfolders to organize your content',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 24.h),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              ElevatedButton.icon(
                onPressed: () {
                  Navigator.pushNamed(context, '/digital-library/upload');
                },
                icon: Icon(Symbols.upload),
                label: Text('Upload Files'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: theme.colorScheme.primary,
                  foregroundColor: theme.colorScheme.onPrimary,
                ),
              ),
              SizedBox(width: 16.w),
              OutlinedButton.icon(
                onPressed: () {
                  // TODO: Show create subfolder dialog
                },
                icon: Icon(Symbols.create_new_folder),
                label: Text('New Folder'),
              ),
            ],
          ),
        ],
      ),
    );
  }

  void _showSubfolderOptions(
    BuildContext context,
    LibraryFolderModel subfolder,
  ) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => SubfolderOptionsBottomSheet(subfolder: subfolder),
    );
  }

  void _showFileOptions(BuildContext context, LibraryFileModel file) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => FileOptionsBottomSheet(file: file),
    );
  }

  void _downloadFile(
    BuildContext context,
    WidgetRef ref,
    LibraryFileModel file,
  ) async {
    try {
      final userId = ref.read(currentUserIdProvider);
      if (userId == 'anonymous') {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Please sign in to download files')),
        );
        return;
      }

      // Show loading indicator
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Starting download of ${file.displayTitle}...')),
      );

      // Download file using FileOperationsService
      final fileOperationsService = FileOperationsService();
      await fileOperationsService.downloadFile(
        file: file,
        userId: userId,
        onProgress: (progress) {
          // Could show progress in a dialog or notification
        },
      );

      // Show success message
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Successfully downloaded ${file.displayTitle}'),
            backgroundColor: Theme.of(context).colorScheme.primary,
          ),
        );
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to download ${file.displayTitle}: $e'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }

  void _toggleOfflineAccess(
    BuildContext context,
    WidgetRef ref,
    LibraryFileModel file,
  ) async {
    try {
      final userId = ref.read(currentUserIdProvider);
      if (userId == 'anonymous') {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Please sign in to manage offline access'),
          ),
        );
        return;
      }

      final isCurrentlyOffline = file.offlineAccess.canAccessOffline;

      if (isCurrentlyOffline) {
        // Remove from offline access
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Removing ${file.displayTitle} from offline access...',
            ),
          ),
        );
        // TODO: Implement actual offline removal logic
      } else {
        // Add to offline access by downloading
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Making ${file.displayTitle} available offline...'),
          ),
        );

        final fileOperationsService = FileOperationsService();
        await fileOperationsService.downloadFile(
          file: file,
          userId: userId,
          forOfflineAccess: true,
        );
      }

      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              isCurrentlyOffline
                  ? 'Removed ${file.displayTitle} from offline access'
                  : 'Made ${file.displayTitle} available offline',
            ),
            backgroundColor: Theme.of(context).colorScheme.primary,
          ),
        );
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to update offline access: $e'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }
}

/// Bottom sheet for subfolder options
class SubfolderOptionsBottomSheet extends StatelessWidget {
  final LibraryFolderModel subfolder;

  const SubfolderOptionsBottomSheet({super.key, required this.subfolder});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20.r)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Handle
          Container(
            width: 40.w,
            height: 4.h,
            margin: EdgeInsets.only(top: 12.h),
            decoration: BoxDecoration(
              color: theme.dividerColor,
              borderRadius: BorderRadius.circular(2.r),
            ),
          ),

          // Options
          ListTile(
            leading: Icon(Symbols.folder_open),
            title: Text('Open Folder'),
            onTap: () {
              Navigator.pop(context);
              Navigator.pushNamed(
                context,
                '/digital-library/folder',
                arguments: subfolder.id,
              );
            },
          ),
          ListTile(
            leading: Icon(Symbols.info),
            title: Text('Folder Details'),
            onTap: () {
              Navigator.pop(context);
              // TODO: Show folder details
            },
          ),
          ListTile(
            leading: Icon(Symbols.edit),
            title: Text('Rename'),
            onTap: () {
              Navigator.pop(context);
              // TODO: Rename folder
            },
          ),
          ListTile(
            leading: Icon(Symbols.share),
            title: Text('Share'),
            onTap: () {
              Navigator.pop(context);
              // TODO: Share folder
            },
          ),
          ListTile(
            leading: Icon(Symbols.delete, color: Colors.red),
            title: Text('Delete', style: TextStyle(color: Colors.red)),
            onTap: () {
              Navigator.pop(context);
              // TODO: Delete folder
            },
          ),

          SizedBox(height: MediaQuery.of(context).padding.bottom + 16.h),
        ],
      ),
    );
  }
}

/// Bottom sheet for file options
class FileOptionsBottomSheet extends StatelessWidget {
  final LibraryFileModel file;

  const FileOptionsBottomSheet({super.key, required this.file});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20.r)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Handle
          Container(
            width: 40.w,
            height: 4.h,
            margin: EdgeInsets.only(top: 12.h),
            decoration: BoxDecoration(
              color: theme.dividerColor,
              borderRadius: BorderRadius.circular(2.r),
            ),
          ),

          // Options
          ListTile(
            leading: Icon(Symbols.visibility),
            title: Text('View'),
            onTap: () {
              Navigator.pop(context);
              Navigator.pushNamed(
                context,
                '/digital-library/file',
                arguments: file.id,
              );
            },
          ),
          if (file.canDownload)
            ListTile(
              leading: Icon(Symbols.download),
              title: Text('Download'),
              onTap: () {
                Navigator.pop(context);
                // TODO: Download file
              },
            ),
          ListTile(
            leading: Icon(Symbols.share),
            title: Text('Share'),
            onTap: () {
              Navigator.pop(context);
              // TODO: Share file
            },
          ),
          ListTile(
            leading: Icon(Symbols.edit),
            title: Text('Rename'),
            onTap: () {
              Navigator.pop(context);
              // TODO: Rename file
            },
          ),
          ListTile(
            leading: Icon(Symbols.delete, color: Colors.red),
            title: Text('Delete', style: TextStyle(color: Colors.red)),
            onTap: () {
              Navigator.pop(context);
              // TODO: Delete file
            },
          ),

          SizedBox(height: MediaQuery.of(context).padding.bottom + 16.h),
        ],
      ),
    );
  }
}
