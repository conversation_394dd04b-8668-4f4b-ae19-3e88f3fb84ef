import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logger/logger.dart';

import '../../../core/providers/auth_providers.dart';
import '../models/library_file_model.dart';
import '../models/library_folder_model.dart';
import '../repositories/digital_library_repository.dart';
import '../enums/file_access_type.dart';
import '../enums/file_usage_type.dart';
import '../enums/folder_type.dart';
import '../enums/library_file_type.dart';

final Logger _logger = Logger();

/// Provider for DigitalLibraryRepository instance
final digitalLibraryRepositoryProvider = Provider<DigitalLibraryRepository>((
  ref,
) {
  return DigitalLibraryRepository();
});

/// Provider for current user ID from authentication
final currentUserIdProvider = Provider<String>((ref) {
  // For testing purposes, use the test user ID
  // TODO: Remove this when authentication is fully implemented
  const testUserId = 'XRTanMcAUWSMq3mrRvve2Y9IMP12';

  final authState = ref.watch(authStateProvider);
  return authState.when(
    data: (state) => state.user?.id ?? testUserId,
    loading: () => testUserId,
    error: (_, __) => testUserId,
  );
});

// ========== FILE PROVIDERS ==========

/// Provider to fetch all files for the current user
final userFilesProvider = FutureProvider<List<LibraryFileModel>>((ref) async {
  final repository = ref.read(digitalLibraryRepositoryProvider);
  final userId = ref.read(currentUserIdProvider);

  try {
    _logger.i('Fetching files for user: $userId');
    final files = await repository.getFilesForUser(userId);
    _logger.i('Successfully fetched ${files.length} files for user $userId');
    return files;
  } catch (e) {
    _logger.e('Error fetching files for user: $e');
    rethrow;
  }
});

/// Provider to fetch files in a specific folder
final folderFilesProvider =
    FutureProvider.family<List<LibraryFileModel>, String>((
      ref,
      folderId,
    ) async {
      final repository = ref.read(digitalLibraryRepositoryProvider);
      final userId = ref.read(currentUserIdProvider);

      try {
        _logger.i('Fetching files in folder: $folderId for user: $userId');
        final files = await repository.getFilesInFolder(folderId, userId);
        _logger.i(
          'Successfully fetched ${files.length} files in folder $folderId',
        );
        return files;
      } catch (e) {
        _logger.e('Error fetching files in folder: $e');
        rethrow;
      }
    });

/// Provider to fetch a specific file by ID
final fileByIdProvider = FutureProvider.family<LibraryFileModel?, String>((
  ref,
  fileId,
) async {
  final repository = ref.read(digitalLibraryRepositoryProvider);

  try {
    _logger.i('Fetching file by ID: $fileId');
    final file = await repository.getFileById(fileId);
    if (file != null) {
      _logger.i('Successfully fetched file: ${file.fileName}');
    } else {
      _logger.w('File not found: $fileId');
    }
    return file;
  } catch (e) {
    _logger.e('Error fetching file by ID: $e');
    rethrow;
  }
});

/// Provider to fetch files filtered by type
final filesByTypeProvider =
    FutureProvider.family<List<LibraryFileModel>, LibraryFileType>((
      ref,
      fileType,
    ) async {
      final repository = ref.read(digitalLibraryRepositoryProvider);
      final userId = ref.read(currentUserIdProvider);

      try {
        _logger.i(
          'Fetching files of type: ${fileType.label} for user: $userId',
        );
        final files = await repository.getFilesForUser(
          userId,
          fileType: fileType,
        );
        _logger.i(
          'Successfully fetched ${files.length} files of type ${fileType.label}',
        );
        return files;
      } catch (e) {
        _logger.e('Error fetching files by type: $e');
        rethrow;
      }
    });

/// Provider to fetch files filtered by class
final filesByClassProvider =
    FutureProvider.family<List<LibraryFileModel>, String>((ref, classId) async {
      final repository = ref.read(digitalLibraryRepositoryProvider);
      final userId = ref.read(currentUserIdProvider);

      try {
        _logger.i('Fetching files for class: $classId for user: $userId');
        final files = await repository.getFilesForUser(
          userId,
          classId: classId,
        );
        _logger.i(
          'Successfully fetched ${files.length} files for class $classId',
        );
        return files;
      } catch (e) {
        _logger.e('Error fetching files by class: $e');
        rethrow;
      }
    });

/// Provider to fetch files filtered by subject
final filesBySubjectProvider =
    FutureProvider.family<List<LibraryFileModel>, String>((ref, subject) async {
      final repository = ref.read(digitalLibraryRepositoryProvider);
      final userId = ref.read(currentUserIdProvider);

      try {
        _logger.i('Fetching files for subject: $subject for user: $userId');
        final files = await repository.getFilesForUser(
          userId,
          subject: subject,
        );
        _logger.i(
          'Successfully fetched ${files.length} files for subject $subject',
        );
        return files;
      } catch (e) {
        _logger.e('Error fetching files by subject: $e');
        rethrow;
      }
    });

/// Provider to fetch files filtered by access type
final filesByAccessTypeProvider =
    FutureProvider.family<List<LibraryFileModel>, FileAccessType>((
      ref,
      accessType,
    ) async {
      final repository = ref.read(digitalLibraryRepositoryProvider);
      final userId = ref.read(currentUserIdProvider);

      try {
        _logger.i(
          'Fetching files with access type: ${accessType.label} for user: $userId',
        );
        final files = await repository.getFilesForUser(
          userId,
          accessType: accessType,
        );
        _logger.i(
          'Successfully fetched ${files.length} files with access type ${accessType.label}',
        );
        return files;
      } catch (e) {
        _logger.e('Error fetching files by access type: $e');
        rethrow;
      }
    });

/// Provider to fetch recent files (limited to 20)
final recentFilesProvider = FutureProvider<List<LibraryFileModel>>((ref) async {
  final repository = ref.read(digitalLibraryRepositoryProvider);
  final userId = ref.read(currentUserIdProvider);

  try {
    _logger.i('Fetching recent files for user: $userId');
    final files = await repository.getFilesForUser(userId, limit: 20);
    _logger.i('Successfully fetched ${files.length} recent files');
    return files;
  } catch (e) {
    _logger.e('Error fetching recent files: $e');
    rethrow;
  }
});

// ========== FOLDER PROVIDERS ==========

/// Provider to fetch all folders for the current user
final userFoldersProvider = FutureProvider<List<LibraryFolderModel>>((
  ref,
) async {
  final repository = ref.read(digitalLibraryRepositoryProvider);
  final userId = ref.read(currentUserIdProvider);

  try {
    _logger.i('Fetching folders for user: $userId');
    final folders = await repository.getFoldersForUser(userId);
    _logger.i(
      'Successfully fetched ${folders.length} folders for user $userId',
    );
    return folders;
  } catch (e) {
    _logger.e('Error fetching folders for user: $e');
    rethrow;
  }
});

/// Provider to fetch root level folders only
final rootFoldersProvider = FutureProvider<List<LibraryFolderModel>>((
  ref,
) async {
  final repository = ref.read(digitalLibraryRepositoryProvider);
  final userId = ref.read(currentUserIdProvider);

  try {
    _logger.i('Fetching root folders for user: $userId');
    final folders = await repository.getFoldersForUser(
      userId,
      parentFolderId: null,
    );
    _logger.i('Successfully fetched ${folders.length} root folders');
    return folders;
  } catch (e) {
    _logger.e('Error fetching root folders: $e');
    rethrow;
  }
});

/// Provider to fetch subfolders of a specific folder
final subFoldersProvider =
    FutureProvider.family<List<LibraryFolderModel>, String>((
      ref,
      parentFolderId,
    ) async {
      final repository = ref.read(digitalLibraryRepositoryProvider);
      final userId = ref.read(currentUserIdProvider);

      try {
        _logger.i(
          'Fetching subfolders of folder: $parentFolderId for user: $userId',
        );
        final folders = await repository.getSubfolders(parentFolderId, userId);
        _logger.i('Successfully fetched ${folders.length} subfolders');
        return folders;
      } catch (e) {
        _logger.e('Error fetching subfolders: $e');
        rethrow;
      }
    });

/// Provider to fetch a specific folder by ID
final folderByIdProvider = FutureProvider.family<LibraryFolderModel?, String>((
  ref,
  folderId,
) async {
  final repository = ref.read(digitalLibraryRepositoryProvider);

  try {
    _logger.i('Fetching folder by ID: $folderId');
    final folder = await repository.getFolderById(folderId);
    if (folder != null) {
      _logger.i('Successfully fetched folder: ${folder.name}');
    } else {
      _logger.w('Folder not found: $folderId');
    }
    return folder;
  } catch (e) {
    _logger.e('Error fetching folder by ID: $e');
    rethrow;
  }
});

/// Provider to fetch folders filtered by type
final foldersByTypeProvider =
    FutureProvider.family<List<LibraryFolderModel>, FolderType>((
      ref,
      folderType,
    ) async {
      final repository = ref.read(digitalLibraryRepositoryProvider);
      final userId = ref.read(currentUserIdProvider);

      try {
        _logger.i(
          'Fetching folders of type: ${folderType.label} for user: $userId',
        );
        final folders = await repository.getFoldersForUser(
          userId,
          folderType: folderType,
        );
        _logger.i(
          'Successfully fetched ${folders.length} folders of type ${folderType.label}',
        );
        return folders;
      } catch (e) {
        _logger.e('Error fetching folders by type: $e');
        rethrow;
      }
    });

/// Provider to fetch folders filtered by class
final foldersByClassProvider =
    FutureProvider.family<List<LibraryFolderModel>, String>((
      ref,
      classId,
    ) async {
      final repository = ref.read(digitalLibraryRepositoryProvider);
      final userId = ref.read(currentUserIdProvider);

      try {
        _logger.i('Fetching folders for class: $classId for user: $userId');
        final folders = await repository.getFoldersForUser(
          userId,
          classId: classId,
        );
        _logger.i(
          'Successfully fetched ${folders.length} folders for class $classId',
        );
        return folders;
      } catch (e) {
        _logger.e('Error fetching folders by class: $e');
        rethrow;
      }
    });

/// Provider to fetch folders filtered by subject
final foldersBySubjectProvider =
    FutureProvider.family<List<LibraryFolderModel>, String>((
      ref,
      subject,
    ) async {
      final repository = ref.read(digitalLibraryRepositoryProvider);
      final userId = ref.read(currentUserIdProvider);

      try {
        _logger.i('Fetching folders for subject: $subject for user: $userId');
        final folders = await repository.getFoldersForUser(
          userId,
          subject: subject,
        );
        _logger.i(
          'Successfully fetched ${folders.length} folders for subject $subject',
        );
        return folders;
      } catch (e) {
        _logger.e('Error fetching folders by subject: $e');
        rethrow;
      }
    });

/// Provider to fetch featured folders (limited to 8)
final featuredFoldersProvider = FutureProvider<List<LibraryFolderModel>>((
  ref,
) async {
  final repository = ref.read(digitalLibraryRepositoryProvider);
  final userId = ref.read(currentUserIdProvider);

  try {
    _logger.i('Fetching featured folders for user: $userId');
    final folders = await repository.getFoldersForUser(userId, limit: 8);
    _logger.i('Successfully fetched ${folders.length} featured folders');
    return folders;
  } catch (e) {
    _logger.e('Error fetching featured folders: $e');
    rethrow;
  }
});

// ========== MUTATION PROVIDERS ==========

/// Provider for file operations (create, update, delete)
final fileOperationsProvider = Provider<FileOperations>((ref) {
  final repository = ref.read(digitalLibraryRepositoryProvider);
  return FileOperations(repository, ref);
});

/// Provider for folder operations (create, update, delete)
final folderOperationsProvider = Provider<FolderOperations>((ref) {
  final repository = ref.read(digitalLibraryRepositoryProvider);
  return FolderOperations(repository, ref);
});

/// Class to handle file operations with proper state invalidation
class FileOperations {
  final DigitalLibraryRepository _repository;
  final Ref _ref;

  FileOperations(this._repository, this._ref);

  /// Create a new file
  Future<LibraryFileModel> createFile(LibraryFileModel file) async {
    try {
      final createdFile = await _repository.createFile(file);

      // Invalidate relevant providers
      _ref.invalidate(userFilesProvider);
      _ref.invalidate(recentFilesProvider);
      if (file.folderId != null) {
        _ref.invalidate(folderFilesProvider(file.folderId!));
      }

      return createdFile;
    } catch (e) {
      _logger.e('Error creating file: $e');
      rethrow;
    }
  }

  /// Update an existing file
  Future<LibraryFileModel> updateFile(LibraryFileModel file) async {
    try {
      final updatedFile = await _repository.updateFile(file);

      // Invalidate relevant providers
      _ref.invalidate(userFilesProvider);
      _ref.invalidate(fileByIdProvider(file.id));
      _ref.invalidate(recentFilesProvider);
      if (file.folderId != null) {
        _ref.invalidate(folderFilesProvider(file.folderId!));
      }

      return updatedFile;
    } catch (e) {
      _logger.e('Error updating file: $e');
      rethrow;
    }
  }

  /// Delete a file
  Future<void> deleteFile(String fileId, {String? folderId}) async {
    try {
      await _repository.deleteFile(fileId);

      // Invalidate relevant providers
      _ref.invalidate(userFilesProvider);
      _ref.invalidate(fileByIdProvider(fileId));
      _ref.invalidate(recentFilesProvider);
      if (folderId != null) {
        _ref.invalidate(folderFilesProvider(folderId));
      }
    } catch (e) {
      _logger.e('Error deleting file: $e');
      rethrow;
    }
  }

  /// Toggle file favorite status
  Future<void> toggleFileFavorite(String fileId, bool isFavorite) async {
    try {
      await _repository.toggleFileFavorite(fileId, isFavorite);

      // Invalidate relevant providers
      _ref.invalidate(userFilesProvider);
      _ref.invalidate(fileByIdProvider(fileId));
      _ref.invalidate(recentFilesProvider);
    } catch (e) {
      _logger.e('Error toggling file favorite: $e');
      rethrow;
    }
  }

  /// Update file access count
  Future<void> updateFileAccessCount(
    String fileId, {
    bool incrementView = false,
    bool incrementDownload = false,
  }) async {
    try {
      await _repository.updateFileAccessCount(
        fileId,
        incrementView: incrementView,
        incrementDownload: incrementDownload,
      );

      // Invalidate file provider
      _ref.invalidate(fileByIdProvider(fileId));
    } catch (e) {
      _logger.e('Error updating file access count: $e');
      rethrow;
    }
  }
}

/// Class to handle folder operations with proper state invalidation
class FolderOperations {
  final DigitalLibraryRepository _repository;
  final Ref _ref;

  FolderOperations(this._repository, this._ref);

  /// Create a new folder
  Future<LibraryFolderModel> createFolder(LibraryFolderModel folder) async {
    try {
      final createdFolder = await _repository.createFolder(folder);

      // Invalidate relevant providers
      _ref.invalidate(userFoldersProvider);
      _ref.invalidate(rootFoldersProvider);
      _ref.invalidate(featuredFoldersProvider);
      if (folder.parentFolderId != null) {
        _ref.invalidate(subFoldersProvider(folder.parentFolderId!));
      }

      return createdFolder;
    } catch (e) {
      _logger.e('Error creating folder: $e');
      rethrow;
    }
  }

  /// Update an existing folder
  Future<LibraryFolderModel> updateFolder(LibraryFolderModel folder) async {
    try {
      final updatedFolder = await _repository.updateFolder(folder);

      // Invalidate relevant providers
      _ref.invalidate(userFoldersProvider);
      _ref.invalidate(folderByIdProvider(folder.id));
      _ref.invalidate(featuredFoldersProvider);
      if (folder.parentFolderId != null) {
        _ref.invalidate(subFoldersProvider(folder.parentFolderId!));
      }

      return updatedFolder;
    } catch (e) {
      _logger.e('Error updating folder: $e');
      rethrow;
    }
  }

  /// Delete a folder
  Future<void> deleteFolder(
    String folderId, {
    bool deleteContents = false,
  }) async {
    try {
      await _repository.deleteFolder(folderId, deleteContents: deleteContents);

      // Invalidate relevant providers
      _ref.invalidate(userFoldersProvider);
      _ref.invalidate(folderByIdProvider(folderId));
      _ref.invalidate(rootFoldersProvider);
      _ref.invalidate(featuredFoldersProvider);
    } catch (e) {
      _logger.e('Error deleting folder: $e');
      rethrow;
    }
  }

  /// Toggle folder favorite status
  Future<void> toggleFolderFavorite(String folderId, bool isFavorite) async {
    try {
      await _repository.toggleFolderFavorite(folderId, isFavorite);

      // Invalidate relevant providers
      _ref.invalidate(userFoldersProvider);
      _ref.invalidate(folderByIdProvider(folderId));
      _ref.invalidate(featuredFoldersProvider);
    } catch (e) {
      _logger.e('Error toggling folder favorite: $e');
      rethrow;
    }
  }

  /// Update folder counts
  Future<void> updateFolderCounts(String folderId) async {
    try {
      await _repository.updateFolderCounts(folderId);

      // Invalidate folder provider
      _ref.invalidate(folderByIdProvider(folderId));
    } catch (e) {
      _logger.e('Error updating folder counts: $e');
      rethrow;
    }
  }
}

// ========== USAGE TYPE FILTERING PROVIDERS ==========

/// Provider to fetch files by usage type for the current user
final filesByUsageTypeProvider =
    FutureProvider.family<List<LibraryFileModel>, FileUsageType>((
      ref,
      usageType,
    ) async {
      final repository = ref.read(digitalLibraryRepositoryProvider);
      final userId = ref.read(currentUserIdProvider);

      try {
        _logger.i(
          'Fetching files with usage type: ${usageType.label} for user: $userId',
        );
        final files = await repository.getFilesForUser(
          userId,
          usageType: usageType,
        );
        _logger.i(
          'Successfully fetched ${files.length} files with usage type: ${usageType.label}',
        );
        return files;
      } catch (e) {
        _logger.e('Error fetching files by usage type: $e');
        rethrow;
      }
    });

/// Provider to fetch folders by usage type for the current user
final foldersByUsageTypeProvider =
    FutureProvider.family<List<LibraryFolderModel>, FileUsageType>((
      ref,
      usageType,
    ) async {
      final repository = ref.read(digitalLibraryRepositoryProvider);
      final userId = ref.read(currentUserIdProvider);

      try {
        _logger.i(
          'Fetching folders with usage type: ${usageType.label} for user: $userId',
        );
        final folders = await repository.getFoldersForUser(
          userId,
          usageType: usageType,
        );
        _logger.i(
          'Successfully fetched ${folders.length} folders with usage type: ${usageType.label}',
        );
        return folders;
      } catch (e) {
        _logger.e('Error fetching folders by usage type: $e');
        rethrow;
      }
    });

/// Provider to get personal files (convenience provider)
final personalFilesProvider = FutureProvider<List<LibraryFileModel>>((
  ref,
) async {
  return ref
      .watch(filesByUsageTypeProvider(FileUsageType.personal))
      .when(
        data: (files) => files,
        loading: () => <LibraryFileModel>[],
        error: (_, __) => <LibraryFileModel>[],
      );
});

/// Provider to get shared files (convenience provider)
final sharedFilesProvider = FutureProvider<List<LibraryFileModel>>((ref) async {
  return ref
      .watch(filesByUsageTypeProvider(FileUsageType.shared))
      .when(
        data: (files) => files,
        loading: () => <LibraryFileModel>[],
        error: (_, __) => <LibraryFileModel>[],
      );
});

/// Provider to get class resource files (convenience provider)
final classResourceFilesProvider = FutureProvider<List<LibraryFileModel>>((
  ref,
) async {
  return ref
      .watch(filesByUsageTypeProvider(FileUsageType.classResource))
      .when(
        data: (files) => files,
        loading: () => <LibraryFileModel>[],
        error: (_, __) => <LibraryFileModel>[],
      );
});
