import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logger/logger.dart';

import '../models/upload_session_model.dart';
import '../repositories/upload_session_repository.dart';
import '../enums/upload_status.dart';
import 'digital_library_controller.dart';

final Logger _logger = Logger();

/// Provider for UploadSessionRepository instance
final uploadSessionRepositoryProvider = Provider<UploadSessionRepository>((ref) {
  return UploadSessionRepository();
});

/// Provider to fetch all upload sessions for the current user
final userUploadSessionsProvider = FutureProvider<List<UploadSessionModel>>((ref) async {
  final repository = ref.read(uploadSessionRepositoryProvider);
  final userId = ref.read(currentUserIdProvider);

  try {
    _logger.i('Fetching upload sessions for user: $userId');
    final sessions = await repository.getUploadSessionsForUser(userId);
    _logger.i('Successfully fetched ${sessions.length} upload sessions for user $userId');
    return sessions;
  } catch (e) {
    _logger.e('Error fetching upload sessions for user: $e');
    rethrow;
  }
});

/// Provider to fetch active upload sessions for the current user
final activeUploadSessionsProvider = FutureProvider<List<UploadSessionModel>>((ref) async {
  final repository = ref.read(uploadSessionRepositoryProvider);
  final userId = ref.read(currentUserIdProvider);

  try {
    _logger.i('Fetching active upload sessions for user: $userId');
    final sessions = await repository.getActiveUploadSessions(userId);
    _logger.i('Successfully fetched ${sessions.length} active upload sessions for user $userId');
    return sessions;
  } catch (e) {
    _logger.e('Error fetching active upload sessions: $e');
    rethrow;
  }
});

/// Provider to fetch a specific upload session by ID
final uploadSessionByIdProvider = FutureProvider.family<UploadSessionModel?, String>((
  ref,
  sessionId,
) async {
  final repository = ref.read(uploadSessionRepositoryProvider);

  try {
    _logger.i('Fetching upload session by ID: $sessionId');
    final session = await repository.getUploadSession(sessionId);
    if (session != null) {
      _logger.i('Successfully fetched upload session: $sessionId');
    } else {
      _logger.w('Upload session not found: $sessionId');
    }
    return session;
  } catch (e) {
    _logger.e('Error fetching upload session by ID: $e');
    rethrow;
  }
});

/// Provider for upload operations
final uploadOperationsProvider = Provider<UploadOperations>((ref) {
  final repository = ref.read(uploadSessionRepositoryProvider);
  return UploadOperations(repository, ref);
});

/// Class to handle upload operations with proper state invalidation
class UploadOperations {
  final UploadSessionRepository _repository;
  final Ref _ref;

  UploadOperations(this._repository, this._ref);

  /// Create a new upload session
  Future<UploadSessionModel> createUploadSession(UploadSessionModel session) async {
    try {
      _logger.i('Creating upload session: ${session.id}');
      final createdSession = await _repository.createUploadSession(session);
      
      // Invalidate relevant providers
      _ref.invalidate(userUploadSessionsProvider);
      _ref.invalidate(activeUploadSessionsProvider);
      
      return createdSession;
    } catch (e) {
      _logger.e('Error creating upload session: $e');
      rethrow;
    }
  }

  /// Update upload progress
  Future<UploadSessionModel> updateUploadProgress(
    String sessionId,
    double progress,
    UploadStatus status, {
    String? errorMessage,
    String? cloudUrl,
  }) async {
    try {
      _logger.i('Updating upload progress for session: $sessionId to $progress');
      final updatedSession = await _repository.updateUploadProgress(
        sessionId,
        progress,
        status,
        errorMessage: errorMessage,
        cloudUrl: cloudUrl,
      );
      
      // Invalidate relevant providers
      _ref.invalidate(uploadSessionByIdProvider(sessionId));
      _ref.invalidate(userUploadSessionsProvider);
      _ref.invalidate(activeUploadSessionsProvider);
      
      return updatedSession;
    } catch (e) {
      _logger.e('Error updating upload progress: $e');
      rethrow;
    }
  }

  /// Complete upload session
  Future<UploadSessionModel> completeUploadSession(
    String sessionId,
    String cloudUrl,
    String fileId,
  ) async {
    try {
      _logger.i('Completing upload session: $sessionId');
      final completedSession = await _repository.completeUploadSession(
        sessionId,
        cloudUrl,
        fileId,
      );
      
      // Invalidate relevant providers
      _ref.invalidate(uploadSessionByIdProvider(sessionId));
      _ref.invalidate(userUploadSessionsProvider);
      _ref.invalidate(activeUploadSessionsProvider);
      
      return completedSession;
    } catch (e) {
      _logger.e('Error completing upload session: $e');
      rethrow;
    }
  }

  /// Fail upload session
  Future<UploadSessionModel> failUploadSession(
    String sessionId,
    String errorMessage,
  ) async {
    try {
      _logger.i('Marking upload session as failed: $sessionId');
      final failedSession = await _repository.failUploadSession(
        sessionId,
        errorMessage,
      );
      
      // Invalidate relevant providers
      _ref.invalidate(uploadSessionByIdProvider(sessionId));
      _ref.invalidate(userUploadSessionsProvider);
      _ref.invalidate(activeUploadSessionsProvider);
      
      return failedSession;
    } catch (e) {
      _logger.e('Error marking upload session as failed: $e');
      rethrow;
    }
  }

  /// Retry failed upload session
  Future<UploadSessionModel> retryUploadSession(String sessionId) async {
    try {
      _logger.i('Retrying upload session: $sessionId');
      final retriedSession = await _repository.retryUploadSession(sessionId);
      
      // Invalidate relevant providers
      _ref.invalidate(uploadSessionByIdProvider(sessionId));
      _ref.invalidate(userUploadSessionsProvider);
      _ref.invalidate(activeUploadSessionsProvider);
      
      return retriedSession;
    } catch (e) {
      _logger.e('Error retrying upload session: $e');
      rethrow;
    }
  }

  /// Delete upload session
  Future<void> deleteUploadSession(String sessionId) async {
    try {
      _logger.i('Deleting upload session: $sessionId');
      await _repository.deleteUploadSession(sessionId);
      
      // Invalidate relevant providers
      _ref.invalidate(uploadSessionByIdProvider(sessionId));
      _ref.invalidate(userUploadSessionsProvider);
      _ref.invalidate(activeUploadSessionsProvider);
    } catch (e) {
      _logger.e('Error deleting upload session: $e');
      rethrow;
    }
  }

  /// Clean up old upload sessions
  Future<void> cleanupOldSessions({int daysOld = 7}) async {
    try {
      _logger.i('Cleaning up upload sessions older than $daysOld days');
      await _repository.cleanupOldSessions(daysOld: daysOld);
      
      // Invalidate relevant providers
      _ref.invalidate(userUploadSessionsProvider);
      _ref.invalidate(activeUploadSessionsProvider);
    } catch (e) {
      _logger.e('Error cleaning up old sessions: $e');
      rethrow;
    }
  }
}

/// State notifier for managing upload queue
class UploadQueueNotifier extends StateNotifier<List<UploadSessionModel>> {
  UploadQueueNotifier() : super([]);

  void addToQueue(UploadSessionModel session) {
    _logger.i('Adding upload session to queue: ${session.id}');
    state = [...state, session];
  }

  void removeFromQueue(String sessionId) {
    _logger.i('Removing upload session from queue: $sessionId');
    state = state.where((session) => session.id != sessionId).toList();
  }

  void updateSessionInQueue(UploadSessionModel updatedSession) {
    _logger.i('Updating upload session in queue: ${updatedSession.id}');
    state = state.map((session) {
      return session.id == updatedSession.id ? updatedSession : session;
    }).toList();
  }

  void clearQueue() {
    _logger.i('Clearing upload queue');
    state = [];
  }

  void clearCompletedSessions() {
    _logger.i('Clearing completed sessions from queue');
    state = state.where((session) => 
      session.status != UploadStatus.completed &&
      session.status != UploadStatus.failed
    ).toList();
  }

  List<UploadSessionModel> get activeSessions {
    return state.where((session) => 
      session.status == UploadStatus.pending ||
      session.status == UploadStatus.uploading ||
      session.status == UploadStatus.processing
    ).toList();
  }

  List<UploadSessionModel> get completedSessions {
    return state.where((session) => session.status == UploadStatus.completed).toList();
  }

  List<UploadSessionModel> get failedSessions {
    return state.where((session) => session.status == UploadStatus.failed).toList();
  }

  bool get hasActiveSessions {
    return activeSessions.isNotEmpty;
  }

  bool get hasFailedSessions {
    return failedSessions.isNotEmpty;
  }

  double get overallProgress {
    if (state.isEmpty) return 0.0;
    
    final totalProgress = state.fold<double>(
      0.0,
      (sum, session) => sum + session.progress,
    );
    
    return totalProgress / state.length;
  }
}

/// Provider for upload queue state
final uploadQueueProvider = StateNotifierProvider<UploadQueueNotifier, List<UploadSessionModel>>((ref) {
  return UploadQueueNotifier();
});

/// Provider for active upload sessions count
final activeUploadsCountProvider = Provider<int>((ref) {
  final queue = ref.watch(uploadQueueProvider);
  return queue.where((session) => 
    session.status == UploadStatus.pending ||
    session.status == UploadStatus.uploading ||
    session.status == UploadStatus.processing
  ).length;
});

/// Provider for overall upload progress
final overallUploadProgressProvider = Provider<double>((ref) {
  final queueNotifier = ref.read(uploadQueueProvider.notifier);
  return queueNotifier.overallProgress;
});

/// Provider for failed uploads count
final failedUploadsCountProvider = Provider<int>((ref) {
  final queue = ref.watch(uploadQueueProvider);
  return queue.where((session) => session.status == UploadStatus.failed).length;
});

/// Provider to check if there are any active uploads
final hasActiveUploadsProvider = Provider<bool>((ref) {
  final activeCount = ref.watch(activeUploadsCountProvider);
  return activeCount > 0;
});

/// Provider to check if there are any failed uploads
final hasFailedUploadsProvider = Provider<bool>((ref) {
  final failedCount = ref.watch(failedUploadsCountProvider);
  return failedCount > 0;
});
