import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logger/logger.dart';

import '../models/library_file_model.dart';
import '../models/library_folder_model.dart';
import '../enums/file_access_type.dart';
import '../enums/file_usage_type.dart';
import '../enums/folder_type.dart';
import '../enums/library_file_type.dart';
import 'digital_library_controller.dart';

final Logger _logger = Logger();

/// Enum for sort options
enum SortOption { name, dateCreated, dateModified, size, type }

extension SortOptionExtension on SortOption {
  String get label {
    switch (this) {
      case SortOption.name:
        return 'Name';
      case SortOption.dateCreated:
        return 'Date Created';
      case SortOption.dateModified:
        return 'Date Modified';
      case SortOption.size:
        return 'Size';
      case SortOption.type:
        return 'Type';
    }
  }
}

/// Search filter state model
class SearchFilterState {
  final String query;
  final FileAccessType? accessType;
  final FileUsageType? usageType;
  final LibraryFileType? fileType;
  final FolderType? folderType;
  final SortOption sortOption;
  final bool sortAscending;
  final List<String> tags;
  final String? classId;
  final String? subject;

  const SearchFilterState({
    this.query = '',
    this.accessType,
    this.usageType,
    this.fileType,
    this.folderType,
    this.sortOption = SortOption.name,
    this.sortAscending = true,
    this.tags = const [],
    this.classId,
    this.subject,
  });

  SearchFilterState copyWith({
    String? query,
    FileAccessType? accessType,
    FileUsageType? usageType,
    LibraryFileType? fileType,
    FolderType? folderType,
    SortOption? sortOption,
    bool? sortAscending,
    List<String>? tags,
    String? classId,
    String? subject,
  }) {
    return SearchFilterState(
      query: query ?? this.query,
      accessType: accessType ?? this.accessType,
      usageType: usageType ?? this.usageType,
      fileType: fileType ?? this.fileType,
      folderType: folderType ?? this.folderType,
      sortOption: sortOption ?? this.sortOption,
      sortAscending: sortAscending ?? this.sortAscending,
      tags: tags ?? this.tags,
      classId: classId ?? this.classId,
      subject: subject ?? this.subject,
    );
  }

  bool get hasActiveFilters {
    return query.isNotEmpty ||
        accessType != null ||
        usageType != null ||
        fileType != null ||
        folderType != null ||
        tags.isNotEmpty ||
        classId != null ||
        subject != null;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is SearchFilterState &&
        other.query == query &&
        other.accessType == accessType &&
        other.fileType == fileType &&
        other.folderType == folderType &&
        other.sortOption == sortOption &&
        other.sortAscending == sortAscending &&
        other.tags.length == tags.length &&
        other.tags.every((tag) => tags.contains(tag)) &&
        other.classId == classId &&
        other.subject == subject;
  }

  @override
  int get hashCode {
    return Object.hash(
      query,
      accessType,
      fileType,
      folderType,
      sortOption,
      sortAscending,
      tags,
      classId,
      subject,
    );
  }
}

/// State notifier for search filters
class SearchFilterNotifier extends StateNotifier<SearchFilterState> {
  SearchFilterNotifier() : super(const SearchFilterState());

  void updateQuery(String query) {
    _logger.i('Updating search query to: "$query"');
    state = state.copyWith(query: query);
  }

  void setAccessType(FileAccessType? accessType) {
    _logger.i('Setting access type filter to: ${accessType?.label}');
    state = state.copyWith(accessType: accessType);
  }

  void setFileType(LibraryFileType? fileType) {
    _logger.i('Setting file type filter to: ${fileType?.label}');
    state = state.copyWith(fileType: fileType);
  }

  void setFolderType(FolderType? folderType) {
    _logger.i('Setting folder type filter to: ${folderType?.label}');
    state = state.copyWith(folderType: folderType);
  }

  void setClassId(String? classId) {
    _logger.i('Setting class filter to: $classId');
    state = state.copyWith(classId: classId);
  }

  void setSubject(String? subject) {
    _logger.i('Setting subject filter to: $subject');
    state = state.copyWith(subject: subject);
  }

  void setSortOption(SortOption sortOption, {bool? ascending}) {
    _logger.i(
      'Setting sort option to: ${sortOption.label} (ascending: ${ascending ?? state.sortAscending})',
    );
    state = state.copyWith(
      sortOption: sortOption,
      sortAscending: ascending ?? state.sortAscending,
    );
  }

  void toggleSortDirection() {
    _logger.i('Toggling sort direction to: ${!state.sortAscending}');
    state = state.copyWith(sortAscending: !state.sortAscending);
  }

  void addTag(String tag) {
    if (!state.tags.contains(tag)) {
      _logger.i('Adding tag filter: $tag');
      state = state.copyWith(tags: [...state.tags, tag]);
    }
  }

  void removeTag(String tag) {
    _logger.i('Removing tag filter: $tag');
    state = state.copyWith(tags: state.tags.where((t) => t != tag).toList());
  }

  void clearTags() {
    _logger.i('Clearing all tag filters');
    state = state.copyWith(tags: []);
  }

  void clearAllFilters() {
    _logger.i('Clearing all search filters');
    state = const SearchFilterState();
  }

  void clearFiltersExceptQuery() {
    _logger.i('Clearing all filters except search query');
    state = SearchFilterState(
      query: state.query,
      sortOption: state.sortOption,
      sortAscending: state.sortAscending,
    );
  }
}

/// Provider for search filter state
final searchFilterProvider =
    StateNotifierProvider<SearchFilterNotifier, SearchFilterState>((ref) {
      return SearchFilterNotifier();
    });

/// Provider for search query only (for quick access)
final searchQueryProvider = Provider<String>((ref) {
  return ref.watch(searchFilterProvider).query;
});

/// Provider to search files with current filters
final searchFilesProvider = FutureProvider<List<LibraryFileModel>>((ref) async {
  final repository = ref.read(digitalLibraryRepositoryProvider);
  final userId = ref.read(currentUserIdProvider);
  final filterState = ref.watch(searchFilterProvider);

  try {
    _logger.i('Searching files with filters: ${filterState.query}');

    List<LibraryFileModel> files;

    if (filterState.query.isNotEmpty) {
      // Use search method if there's a query
      files = await repository.searchFiles(
        userId,
        filterState.query,
        accessType: filterState.accessType,
        fileType: filterState.fileType,
        classId: filterState.classId,
        subject: filterState.subject,
      );
    } else {
      // Get all files with filters
      files = await repository.getFilesForUser(
        userId,
        accessType: filterState.accessType,
        fileType: filterState.fileType,
        classId: filterState.classId,
        subject: filterState.subject,
        tags: filterState.tags.isNotEmpty ? filterState.tags : null,
      );
    }

    // Apply tag filtering if needed and not already applied in search
    if (filterState.tags.isNotEmpty && filterState.query.isEmpty) {
      files = files
          .where(
            (file) => filterState.tags.any((tag) => file.tags.contains(tag)),
          )
          .toList();
    }

    // Apply sorting
    files = _sortFiles(
      files,
      filterState.sortOption,
      filterState.sortAscending,
    );

    _logger.i('Found ${files.length} files matching search criteria');
    return files;
  } catch (e) {
    _logger.e('Error searching files: $e');
    rethrow;
  }
});

/// Provider to search folders with current filters
final searchFoldersProvider = FutureProvider<List<LibraryFolderModel>>((
  ref,
) async {
  final repository = ref.read(digitalLibraryRepositoryProvider);
  final userId = ref.read(currentUserIdProvider);
  final filterState = ref.watch(searchFilterProvider);

  try {
    _logger.i('Searching folders with filters: ${filterState.query}');

    List<LibraryFolderModel> folders;

    if (filterState.query.isNotEmpty) {
      // Use search method if there's a query
      folders = await repository.searchFolders(
        userId,
        filterState.query,
        folderType: filterState.folderType,
        accessType: filterState.accessType,
        classId: filterState.classId,
        subject: filterState.subject,
      );
    } else {
      // Get all folders with filters
      folders = await repository.getFoldersForUser(
        userId,
        folderType: filterState.folderType,
        accessType: filterState.accessType,
        classId: filterState.classId,
        subject: filterState.subject,
      );
    }

    // Apply tag filtering if needed
    if (filterState.tags.isNotEmpty) {
      folders = folders
          .where(
            (folder) =>
                filterState.tags.any((tag) => folder.tags.contains(tag)),
          )
          .toList();
    }

    // Apply sorting
    folders = _sortFolders(
      folders,
      filterState.sortOption,
      filterState.sortAscending,
    );

    _logger.i('Found ${folders.length} folders matching search criteria');
    return folders;
  } catch (e) {
    _logger.e('Error searching folders: $e');
    rethrow;
  }
});

/// Provider for combined search results (files and folders)
final combinedSearchProvider = FutureProvider<Map<String, List<dynamic>>>((
  ref,
) async {
  final files = await ref.watch(searchFilesProvider.future);
  final folders = await ref.watch(searchFoldersProvider.future);

  return {'files': files, 'folders': folders};
});

/// Helper function to sort files
List<LibraryFileModel> _sortFiles(
  List<LibraryFileModel> files,
  SortOption sortOption,
  bool ascending,
) {
  files.sort((a, b) {
    int comparison;

    switch (sortOption) {
      case SortOption.name:
        comparison = a.displayTitle.toLowerCase().compareTo(
          b.displayTitle.toLowerCase(),
        );
        break;
      case SortOption.dateCreated:
        comparison = a.createdAt.compareTo(b.createdAt);
        break;
      case SortOption.dateModified:
        final aModified = a.modifiedAt ?? a.createdAt;
        final bModified = b.modifiedAt ?? b.createdAt;
        comparison = aModified.compareTo(bModified);
        break;
      case SortOption.size:
        final aSize = a.fileSizeBytes ?? 0;
        final bSize = b.fileSizeBytes ?? 0;
        comparison = aSize.compareTo(bSize);
        break;
      case SortOption.type:
        comparison = a.fileType.label.compareTo(b.fileType.label);
        break;
    }

    return ascending ? comparison : -comparison;
  });

  return files;
}

/// Helper function to sort folders
List<LibraryFolderModel> _sortFolders(
  List<LibraryFolderModel> folders,
  SortOption sortOption,
  bool ascending,
) {
  folders.sort((a, b) {
    int comparison;

    switch (sortOption) {
      case SortOption.name:
        comparison = a.name.toLowerCase().compareTo(b.name.toLowerCase());
        break;
      case SortOption.dateCreated:
        comparison = a.createdAt.compareTo(b.createdAt);
        break;
      case SortOption.dateModified:
        final aModified = a.modifiedAt ?? a.createdAt;
        final bModified = b.modifiedAt ?? b.createdAt;
        comparison = aModified.compareTo(bModified);
        break;
      case SortOption.size:
        final aSize = a.totalSizeBytes ?? 0;
        final bSize = b.totalSizeBytes ?? 0;
        comparison = aSize.compareTo(bSize);
        break;
      case SortOption.type:
        comparison = a.folderType.label.compareTo(b.folderType.label);
        break;
    }

    return ascending ? comparison : -comparison;
  });

  return folders;
}
