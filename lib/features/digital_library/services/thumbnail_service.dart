import 'dart:io';
import 'dart:typed_data';
import 'package:logger/logger.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;

import '../models/library_file_model.dart';
import '../enums/library_file_type.dart';

/// Service for generating and managing file thumbnails
class ThumbnailService {
  static final ThumbnailService _instance = ThumbnailService._internal();
  factory ThumbnailService() => _instance;
  ThumbnailService._internal();

  final Logger _logger = Logger();

  /// Generate thumbnail for a file
  Future<String?> generateThumbnail({
    required LibraryFileModel file,
    required String localFilePath,
    int maxWidth = 200,
    int maxHeight = 200,
  }) async {
    try {
      _logger.i('Generating thumbnail for file: ${file.fileName}');

      if (!_shouldGenerateThumbnail(file.fileType)) {
        _logger.i(
          'Thumbnail not supported for file type: ${file.fileType.label}',
        );
        return null;
      }

      final thumbnailPath = await _getThumbnailPath(file);

      switch (file.fileType) {
        case LibraryFileType.image:
          return await _generateImageThumbnail(
            localFilePath,
            thumbnailPath,
            maxWidth,
            maxHeight,
          );
        case LibraryFileType.video:
          return await _generateVideoThumbnail(localFilePath, thumbnailPath);
        case LibraryFileType.pdf:
          return await _generatePdfThumbnail(localFilePath, thumbnailPath);
        default:
          return null;
      }
    } catch (e, stackTrace) {
      _logger.e(
        'Error generating thumbnail: $e',
        error: e,
        stackTrace: stackTrace,
      );
      return null;
    }
  }

  /// Get thumbnail path for a file
  Future<String?> getThumbnailPath(LibraryFileModel file) async {
    try {
      final thumbnailPath = await _getThumbnailPath(file);
      final thumbnailFile = File(thumbnailPath);

      if (await thumbnailFile.exists()) {
        return thumbnailPath;
      }

      return null;
    } catch (e) {
      _logger.e('Error getting thumbnail path: $e');
      return null;
    }
  }

  /// Check if thumbnail exists for a file
  Future<bool> hasThumbnail(LibraryFileModel file) async {
    try {
      final thumbnailPath = await getThumbnailPath(file);
      return thumbnailPath != null;
    } catch (e) {
      _logger.e('Error checking thumbnail existence: $e');
      return false;
    }
  }

  /// Delete thumbnail for a file
  Future<void> deleteThumbnail(LibraryFileModel file) async {
    try {
      _logger.i('Deleting thumbnail for file: ${file.fileName}');

      final thumbnailPath = await _getThumbnailPath(file);
      final thumbnailFile = File(thumbnailPath);

      if (await thumbnailFile.exists()) {
        await thumbnailFile.delete();
        _logger.i('Successfully deleted thumbnail: $thumbnailPath');
      }
    } catch (e, stackTrace) {
      _logger.e(
        'Error deleting thumbnail: $e',
        error: e,
        stackTrace: stackTrace,
      );
    }
  }

  /// Clean up old thumbnails
  Future<void> cleanupOldThumbnails({int daysOld = 30}) async {
    try {
      _logger.i('Cleaning up thumbnails older than $daysOld days');

      final thumbnailsDir = await _getThumbnailsDirectory();
      if (!await thumbnailsDir.exists()) {
        return;
      }

      final cutoffDate = DateTime.now().subtract(Duration(days: daysOld));
      final files = thumbnailsDir.listSync(recursive: true);
      int deletedCount = 0;

      for (final file in files) {
        if (file is File) {
          final stat = await file.stat();
          if (stat.modified.isBefore(cutoffDate)) {
            await file.delete();
            deletedCount++;
          }
        }
      }

      _logger.i('Cleaned up $deletedCount old thumbnails');
    } catch (e, stackTrace) {
      _logger.e(
        'Error cleaning up old thumbnails: $e',
        error: e,
        stackTrace: stackTrace,
      );
    }
  }

  /// Get total size of thumbnails directory
  Future<int> getThumbnailsSize() async {
    try {
      final thumbnailsDir = await _getThumbnailsDirectory();
      if (!await thumbnailsDir.exists()) {
        return 0;
      }

      int totalSize = 0;
      final files = thumbnailsDir.listSync(recursive: true);

      for (final file in files) {
        if (file is File) {
          totalSize += await file.length();
        }
      }

      return totalSize;
    } catch (e) {
      _logger.e('Error calculating thumbnails size: $e');
      return 0;
    }
  }

  /// Clear all thumbnails
  Future<void> clearAllThumbnails() async {
    try {
      _logger.i('Clearing all thumbnails');

      final thumbnailsDir = await _getThumbnailsDirectory();
      if (await thumbnailsDir.exists()) {
        await thumbnailsDir.delete(recursive: true);
        _logger.i('Successfully cleared all thumbnails');
      }
    } catch (e, stackTrace) {
      _logger.e(
        'Error clearing all thumbnails: $e',
        error: e,
        stackTrace: stackTrace,
      );
    }
  }

  /// Check if thumbnail should be generated for file type
  bool _shouldGenerateThumbnail(LibraryFileType fileType) {
    switch (fileType) {
      case LibraryFileType.image:
      case LibraryFileType.video:
      case LibraryFileType.pdf:
        return true;
      default:
        return false;
    }
  }

  /// Get thumbnail path for a file
  Future<String> _getThumbnailPath(LibraryFileModel file) async {
    final thumbnailsDir = await _getThumbnailsDirectory();
    await thumbnailsDir.create(recursive: true);

    final thumbnailFileName = '${file.id}_thumbnail.jpg';
    return path.join(thumbnailsDir.path, thumbnailFileName);
  }

  /// Get thumbnails directory
  Future<Directory> _getThumbnailsDirectory() async {
    final appDir = await getApplicationDocumentsDirectory();
    return Directory(path.join(appDir.path, 'thumbnails'));
  }

  /// Generate thumbnail for image files
  Future<String?> _generateImageThumbnail(
    String imagePath,
    String thumbnailPath,
    int maxWidth,
    int maxHeight,
  ) async {
    try {
      _logger.i('Generating image thumbnail: $imagePath');

      // For now, we'll use a placeholder implementation
      // In a real app, you would use packages like:
      // - image: for image processing
      // - flutter_image_compress: for image compression

      // Copy the original image as thumbnail for now
      // This should be replaced with actual thumbnail generation
      final originalFile = File(imagePath);

      if (await originalFile.exists()) {
        await originalFile.copy(thumbnailPath);
        _logger.i('Generated image thumbnail: $thumbnailPath');
        return thumbnailPath;
      }

      return null;
    } catch (e) {
      _logger.e('Error generating image thumbnail: $e');
      return null;
    }
  }

  /// Generate thumbnail for video files
  Future<String?> _generateVideoThumbnail(
    String videoPath,
    String thumbnailPath,
  ) async {
    try {
      _logger.i('Generating video thumbnail: $videoPath');

      // For now, we'll use a placeholder implementation
      // In a real app, you would use packages like:
      // - video_thumbnail: for video thumbnail generation
      // - ffmpeg_kit_flutter: for advanced video processing

      // Create a placeholder thumbnail file
      final thumbnailFile = File(thumbnailPath);
      await thumbnailFile.writeAsBytes(
        Uint8List(0),
      ); // Empty file as placeholder

      _logger.i('Generated video thumbnail placeholder: $thumbnailPath');
      return thumbnailPath;
    } catch (e) {
      _logger.e('Error generating video thumbnail: $e');
      return null;
    }
  }

  /// Generate thumbnail for PDF files
  Future<String?> _generatePdfThumbnail(
    String pdfPath,
    String thumbnailPath,
  ) async {
    try {
      _logger.i('Generating PDF thumbnail: $pdfPath');

      // For now, we'll use a placeholder implementation
      // In a real app, you would use packages like:
      // - pdf_render: for PDF thumbnail generation
      // - syncfusion_flutter_pdf: for PDF processing

      // Create a placeholder thumbnail file
      final thumbnailFile = File(thumbnailPath);
      await thumbnailFile.writeAsBytes(
        Uint8List(0),
      ); // Empty file as placeholder

      _logger.i('Generated PDF thumbnail placeholder: $thumbnailPath');
      return thumbnailPath;
    } catch (e) {
      _logger.e('Error generating PDF thumbnail: $e');
      return null;
    }
  }

  /// Get default thumbnail for file type
  String getDefaultThumbnailAsset(LibraryFileType fileType) {
    switch (fileType) {
      case LibraryFileType.pdf:
        return 'assets/icons/file_types/pdf.png';
      case LibraryFileType.document:
        return 'assets/icons/file_types/document.png';
      case LibraryFileType.spreadsheet:
        return 'assets/icons/file_types/spreadsheet.png';
      case LibraryFileType.presentation:
        return 'assets/icons/file_types/presentation.png';
      case LibraryFileType.image:
        return 'assets/icons/file_types/image.png';
      case LibraryFileType.video:
        return 'assets/icons/file_types/video.png';
      case LibraryFileType.audio:
        return 'assets/icons/file_types/audio.png';
      case LibraryFileType.archive:
        return 'assets/icons/file_types/archive.png';
      case LibraryFileType.note:
        return 'assets/icons/file_types/text.png';
      case LibraryFileType.code:
        return 'assets/icons/file_types/code.png';
      case LibraryFileType.other:
      default:
        return 'assets/icons/file_types/file.png';
    }
  }

  /// Check if file type supports thumbnail generation
  bool supportsThumbnailGeneration(LibraryFileType fileType) {
    return _shouldGenerateThumbnail(fileType);
  }
}
