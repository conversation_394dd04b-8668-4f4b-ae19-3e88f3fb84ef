import 'dart:io';
import 'dart:typed_data';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:logger/logger.dart';
import 'package:path/path.dart' as path;

import '../models/library_file_model.dart';
import '../models/upload_session_model.dart';
import '../enums/upload_status.dart';
import '../enums/library_file_type.dart';
import '../repositories/upload_session_repository.dart';
import '../repositories/digital_library_repository.dart';

/// Service for handling file uploads to Firebase Storage
class FileUploadService {
  static final FileUploadService _instance = FileUploadService._internal();
  factory FileUploadService() => _instance;
  FileUploadService._internal();

  final FirebaseStorage _storage = FirebaseStorage.instance;
  final Logger _logger = Logger();
  final UploadSessionRepository _uploadSessionRepository =
      UploadSessionRepository();
  final DigitalLibraryRepository _digitalLibraryRepository =
      DigitalLibraryRepository();

  /// Upload a file to Firebase Storage with progress tracking
  Future<String> uploadFile({
    required File file,
    required String fileName,
    required String userId,
    required String sessionId,
    String? folderId,
    Function(double)? onProgress,
  }) async {
    try {
      _logger.i('Starting file upload: $fileName for user: $userId');

      // Create storage reference
      final storageRef = _storage.ref().child(
        _buildStoragePath(userId, fileName, folderId),
      );

      // Create upload task
      final uploadTask = storageRef.putFile(file);

      // Listen to upload progress
      uploadTask.snapshotEvents.listen(
        (TaskSnapshot snapshot) async {
          final progress = snapshot.bytesTransferred / snapshot.totalBytes;
          _logger.i(
            'Upload progress for $fileName: ${(progress * 100).toStringAsFixed(1)}%',
          );

          // Update progress callback
          onProgress?.call(progress);

          // Update upload session progress
          await _uploadSessionRepository.updateUploadProgress(
            sessionId,
            progress,
            UploadStatus.uploading,
          );
        },
        onError: (error) async {
          _logger.e('Upload error for $fileName: $error');
          await _uploadSessionRepository.failUploadSession(
            sessionId,
            'Upload failed: $error',
          );
        },
      );

      // Wait for upload completion
      final snapshot = await uploadTask;
      final downloadUrl = await snapshot.ref.getDownloadURL();

      _logger.i('Successfully uploaded file: $fileName');
      return downloadUrl;
    } catch (e, stackTrace) {
      _logger.e('Error uploading file: $e', error: e, stackTrace: stackTrace);

      // Mark upload session as failed
      await _uploadSessionRepository.failUploadSession(
        sessionId,
        'Upload failed: $e',
      );

      rethrow;
    }
  }

  /// Upload file from bytes (for web or memory-based uploads)
  Future<String> uploadFileFromBytes({
    required Uint8List bytes,
    required String fileName,
    required String userId,
    required String sessionId,
    String? folderId,
    String? mimeType,
    Function(double)? onProgress,
  }) async {
    try {
      _logger.i('Starting file upload from bytes: $fileName for user: $userId');

      // Create storage reference
      final storageRef = _storage.ref().child(
        _buildStoragePath(userId, fileName, folderId),
      );

      // Create metadata
      final metadata = SettableMetadata(
        contentType: mimeType,
        customMetadata: {
          'uploadedBy': userId,
          'sessionId': sessionId,
          'originalFileName': fileName,
        },
      );

      // Create upload task
      final uploadTask = storageRef.putData(bytes, metadata);

      // Listen to upload progress
      uploadTask.snapshotEvents.listen(
        (TaskSnapshot snapshot) async {
          final progress = snapshot.bytesTransferred / snapshot.totalBytes;
          _logger.i(
            'Upload progress for $fileName: ${(progress * 100).toStringAsFixed(1)}%',
          );

          // Update progress callback
          onProgress?.call(progress);

          // Update upload session progress
          await _uploadSessionRepository.updateUploadProgress(
            sessionId,
            progress,
            UploadStatus.uploading,
          );
        },
        onError: (error) async {
          _logger.e('Upload error for $fileName: $error');
          await _uploadSessionRepository.failUploadSession(
            sessionId,
            'Upload failed: $error',
          );
        },
      );

      // Wait for upload completion
      final snapshot = await uploadTask;
      final downloadUrl = await snapshot.ref.getDownloadURL();

      _logger.i('Successfully uploaded file from bytes: $fileName');
      return downloadUrl;
    } catch (e, stackTrace) {
      _logger.e(
        'Error uploading file from bytes: $e',
        error: e,
        stackTrace: stackTrace,
      );

      // Mark upload session as failed
      await _uploadSessionRepository.failUploadSession(
        sessionId,
        'Upload failed: $e',
      );

      rethrow;
    }
  }

  /// Complete the upload process by creating the file record
  Future<LibraryFileModel> completeUpload({
    required UploadSessionModel session,
    required String downloadUrl,
    required LibraryFileModel fileModel,
  }) async {
    try {
      _logger.i('Completing upload for session: ${session.id}');

      // Update file model with cloud URL and completed status
      final completedFile = fileModel.copyWith(
        cloudUrl: downloadUrl,
        uploadStatus: UploadStatus.completed,
        uploadProgress: 1.0,
        modifiedAt: DateTime.now(),
      );

      // Create file record in database
      final createdFile = await _digitalLibraryRepository.createFile(
        completedFile,
      );

      // Complete upload session
      await _uploadSessionRepository.completeUploadSession(
        session.id,
        downloadUrl,
        createdFile.id,
      );

      _logger.i(
        'Successfully completed upload for file: ${createdFile.fileName}',
      );
      return createdFile;
    } catch (e, stackTrace) {
      _logger.e(
        'Error completing upload: $e',
        error: e,
        stackTrace: stackTrace,
      );

      // Mark upload session as failed
      await _uploadSessionRepository.failUploadSession(
        session.id,
        'Failed to complete upload: $e',
      );

      rethrow;
    }
  }

  /// Cancel an ongoing upload
  Future<void> cancelUpload(String sessionId) async {
    try {
      _logger.i('Cancelling upload session: $sessionId');

      // Mark upload session as failed
      await _uploadSessionRepository.failUploadSession(
        sessionId,
        'Upload cancelled by user',
      );

      _logger.i('Successfully cancelled upload session: $sessionId');
    } catch (e, stackTrace) {
      _logger.e(
        'Error cancelling upload: $e',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// Delete a file from Firebase Storage
  Future<void> deleteFileFromStorage(String downloadUrl) async {
    try {
      _logger.i('Deleting file from storage: $downloadUrl');

      final ref = _storage.refFromURL(downloadUrl);
      await ref.delete();

      _logger.i('Successfully deleted file from storage');
    } catch (e, stackTrace) {
      _logger.e(
        'Error deleting file from storage: $e',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// Get file metadata from Firebase Storage
  Future<FullMetadata> getFileMetadata(String downloadUrl) async {
    try {
      _logger.i('Getting file metadata from storage: $downloadUrl');

      final ref = _storage.refFromURL(downloadUrl);
      final metadata = await ref.getMetadata();

      _logger.i('Successfully retrieved file metadata');
      return metadata;
    } catch (e, stackTrace) {
      _logger.e(
        'Error getting file metadata: $e',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// Generate a unique file name to avoid conflicts
  String generateUniqueFileName(String originalFileName) {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final extension = path.extension(originalFileName);
    final nameWithoutExtension = path.basenameWithoutExtension(
      originalFileName,
    );

    return '${nameWithoutExtension}_$timestamp$extension';
  }

  /// Build storage path for file organization
  String _buildStoragePath(String userId, String fileName, String? folderId) {
    final sanitizedFileName = _sanitizeFileName(fileName);

    if (folderId != null) {
      return 'digital_library/$userId/folders/$folderId/$sanitizedFileName';
    } else {
      return 'digital_library/$userId/root/$sanitizedFileName';
    }
  }

  /// Sanitize file name for storage
  String _sanitizeFileName(String fileName) {
    // Remove or replace invalid characters
    return fileName
        .replaceAll(RegExp(r'[<>:"/\\|?*]'), '_')
        .replaceAll(RegExp(r'\s+'), '_')
        .toLowerCase();
  }

  /// Detect file type from file extension
  LibraryFileType detectFileType(String fileName) {
    final extension = path.extension(fileName).toLowerCase();

    for (final fileType in LibraryFileType.values) {
      if (fileType.extensions.contains(extension.substring(1))) {
        return fileType;
      }
    }

    return LibraryFileType.other;
  }

  /// Get MIME type from file extension
  String? getMimeType(String fileName) {
    final extension = path.extension(fileName).toLowerCase();

    const mimeTypes = {
      '.pdf': 'application/pdf',
      '.doc': 'application/msword',
      '.docx':
          'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      '.xls': 'application/vnd.ms-excel',
      '.xlsx':
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      '.ppt': 'application/vnd.ms-powerpoint',
      '.pptx':
          'application/vnd.openxmlformats-officedocument.presentationml.presentation',
      '.txt': 'text/plain',
      '.jpg': 'image/jpeg',
      '.jpeg': 'image/jpeg',
      '.png': 'image/png',
      '.gif': 'image/gif',
      '.mp4': 'video/mp4',
      '.avi': 'video/x-msvideo',
      '.mov': 'video/quicktime',
      '.mp3': 'audio/mpeg',
      '.wav': 'audio/wav',
      '.zip': 'application/zip',
      '.rar': 'application/x-rar-compressed',
    };

    return mimeTypes[extension];
  }

  /// Validate file before upload
  bool validateFile(File file, {int maxSizeBytes = 100 * 1024 * 1024}) {
    try {
      // Check if file exists
      if (!file.existsSync()) {
        _logger.w('File does not exist: ${file.path}');
        return false;
      }

      // Check file size
      final fileSize = file.lengthSync();
      if (fileSize > maxSizeBytes) {
        _logger.w('File too large: $fileSize bytes (max: $maxSizeBytes bytes)');
        return false;
      }

      // Check if file is readable
      try {
        file.readAsBytesSync();
      } catch (e) {
        _logger.w('File is not readable: ${file.path}');
        return false;
      }

      return true;
    } catch (e) {
      _logger.e('Error validating file: $e');
      return false;
    }
  }

  /// Validate file from bytes
  bool validateFileFromBytes(
    Uint8List bytes, {
    int maxSizeBytes = 100 * 1024 * 1024,
  }) {
    try {
      // Check file size
      if (bytes.length > maxSizeBytes) {
        _logger.w(
          'File too large: ${bytes.length} bytes (max: $maxSizeBytes bytes)',
        );
        return false;
      }

      // Check if bytes are not empty
      if (bytes.isEmpty) {
        _logger.w('File is empty');
        return false;
      }

      return true;
    } catch (e) {
      _logger.e('Error validating file from bytes: $e');
      return false;
    }
  }
}
