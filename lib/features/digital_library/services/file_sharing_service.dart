// ignore_for_file: deprecated_member_use

import 'package:flutter/material.dart';
import 'package:share_plus/share_plus.dart';
import 'package:logger/logger.dart';
import '../models/file_model.dart';
import '../enums/library_file_type.dart';

/// Service for handling file sharing operations
class FileSharingService {
  static final FileSharingService _instance = FileSharingService._internal();
  factory FileSharingService() => _instance;
  FileSharingService._internal();

  final Logger _logger = Logger();

  /// Share a file
  Future<ShareResult?> shareFile(
    FileModel fileModel, {
    String? customText,
    String? customSubject,
    Rect? sharePositionOrigin,
  }) async {
    try {
      _logger.i('Sharing file: ${fileModel.fileName}');

      if (fileModel.isLocal && fileModel.localPath != null) {
        return await _shareLocalFile(
          fileModel,
          customText: customText,
          customSubject: customSubject,
          sharePositionOrigin: sharePositionOrigin,
        );
      } else if (fileModel.url != null) {
        return await _shareRemoteFile(
          fileModel,
          customText: customText,
          customSubject: customSubject,
          sharePositionOrigin: sharePositionOrigin,
        );
      } else {
        throw Exception('No valid file path or URL to share');
      }
    } catch (e) {
      _logger.e('Error sharing file: $e');
      rethrow;
    }
  }

  /// Share multiple files
  Future<ShareResult?> shareFiles(
    List<FileModel> fileModels, {
    String? customText,
    String? customSubject,
    Rect? sharePositionOrigin,
  }) async {
    try {
      _logger.i('Sharing ${fileModels.length} files');

      final localFiles = <XFile>[];
      final urls = <String>[];

      for (final fileModel in fileModels) {
        if (fileModel.isLocal && fileModel.localPath != null) {
          localFiles.add(XFile(fileModel.localPath!));
        } else if (fileModel.url != null) {
          urls.add(fileModel.url!);
        }
      }

      if (localFiles.isNotEmpty && urls.isEmpty) {
        // Share only local files
        return await Share.shareXFiles(
          localFiles,
          text: customText ?? _generateShareText(fileModels),
          subject: customSubject ?? _generateShareSubject(fileModels),
          sharePositionOrigin: sharePositionOrigin,
        );
      } else if (urls.isNotEmpty && localFiles.isEmpty) {
        // Share only URLs
        return await Share.share(
          urls.join('\n'),
          subject: customSubject ?? _generateShareSubject(fileModels),
          sharePositionOrigin: sharePositionOrigin,
        );
      } else if (localFiles.isNotEmpty && urls.isNotEmpty) {
        // Mixed content - share files first, then URLs
        final fileResult = await Share.shareXFiles(
          localFiles,
          text: customText ?? _generateShareText(fileModels),
          subject: customSubject ?? _generateShareSubject(fileModels),
          sharePositionOrigin: sharePositionOrigin,
        );

        // Note: We can't share both files and URLs in a single share action
        // This is a limitation of the platform share APIs
        return fileResult;
      } else {
        throw Exception('No valid files to share');
      }
    } catch (e) {
      _logger.e('Error sharing files: $e');
      rethrow;
    }
  }

  /// Share a local file
  Future<ShareResult> _shareLocalFile(
    FileModel fileModel, {
    String? customText,
    String? customSubject,
    Rect? sharePositionOrigin,
  }) async {
    final xFile = XFile(fileModel.localPath!);

    return await Share.shareXFiles(
      [xFile],
      text: customText ?? _generateShareText([fileModel]),
      subject: customSubject ?? _generateShareSubject([fileModel]),
      sharePositionOrigin: sharePositionOrigin,
    );
  }

  /// Share a remote file (URL)
  Future<ShareResult> _shareRemoteFile(
    FileModel fileModel, {
    String? customText,
    String? customSubject,
    Rect? sharePositionOrigin,
  }) async {
    final shareText =
        customText ?? '${_generateShareText([fileModel])}\n\n${fileModel.url}';

    return await Share.share(
      shareText,
      subject: customSubject ?? _generateShareSubject([fileModel]),
      sharePositionOrigin: sharePositionOrigin,
    );
  }

  /// Generate share text for files
  String _generateShareText(List<FileModel> fileModels) {
    if (fileModels.length == 1) {
      return 'Check out this file: ${fileModels.first.fileName}';
    } else {
      return 'Check out these ${fileModels.length} files';
    }
  }

  /// Generate share subject for files
  String _generateShareSubject(List<FileModel> fileModels) {
    if (fileModels.length == 1) {
      return 'Sharing: ${fileModels.first.fileName}';
    } else {
      return 'Sharing ${fileModels.length} files';
    }
  }

  /// Check if a file can be shared
  bool canShareFile(FileModel fileModel) {
    return (fileModel.isLocal && fileModel.localPath != null) ||
        fileModel.url != null;
  }

  /// Check if multiple files can be shared together
  bool canShareFiles(List<FileModel> fileModels) {
    return fileModels.every((file) => canShareFile(file));
  }

  /// Get share options for a file type
  List<ShareOption> getShareOptions(FileType fileType) {
    final baseOptions = [
      ShareOption.general,
      ShareOption.email,
      ShareOption.messaging,
    ];

    switch (fileType) {
      case FileType.image:
        return [
          ...baseOptions,
          ShareOption.socialMedia,
          ShareOption.photoLibrary,
        ];
      case FileType.document:
      case FileType.pdf:
        return [
          ...baseOptions,
          ShareOption.cloudStorage,
          ShareOption.productivity,
        ];
      case FileType.video:
        return [...baseOptions, ShareOption.socialMedia, ShareOption.videoApps];
      case FileType.audio:
        return [...baseOptions, ShareOption.musicApps];
      default:
        return baseOptions;
    }
  }

  /// Share file with specific app (if supported by platform)
  Future<ShareResult?> shareWithApp(
    FileModel fileModel,
    String packageName, {
    String? customText,
    String? customSubject,
  }) async {
    try {
      _logger.i('Sharing file with app: $packageName');

      // Note: This is a simplified implementation
      // In a real app, you might want to use platform-specific APIs
      // to share with specific apps

      return await shareFile(
        fileModel,
        customText: customText,
        customSubject: customSubject,
      );
    } catch (e) {
      _logger.e('Error sharing file with app: $e');
      rethrow;
    }
  }

  /// Check if sharing is available on the platform
  Future<bool> isSharingAvailable() async {
    try {
      // This is a simple check - in a real implementation,
      // you might want to check platform capabilities
      return true;
    } catch (e) {
      _logger.e('Error checking sharing availability: $e');
      return false;
    }
  }
}

/// Share option types
enum ShareOption {
  general,
  email,
  messaging,
  socialMedia,
  cloudStorage,
  productivity,
  photoLibrary,
  videoApps,
  musicApps,
}

/// Extension for ShareOption
extension ShareOptionExtension on ShareOption {
  String get label {
    switch (this) {
      case ShareOption.general:
        return 'General';
      case ShareOption.email:
        return 'Email';
      case ShareOption.messaging:
        return 'Messaging';
      case ShareOption.socialMedia:
        return 'Social Media';
      case ShareOption.cloudStorage:
        return 'Cloud Storage';
      case ShareOption.productivity:
        return 'Productivity Apps';
      case ShareOption.photoLibrary:
        return 'Photo Library';
      case ShareOption.videoApps:
        return 'Video Apps';
      case ShareOption.musicApps:
        return 'Music Apps';
    }
  }

  IconData get icon {
    switch (this) {
      case ShareOption.general:
        return Icons.share;
      case ShareOption.email:
        return Icons.email;
      case ShareOption.messaging:
        return Icons.message;
      case ShareOption.socialMedia:
        return Icons.people;
      case ShareOption.cloudStorage:
        return Icons.cloud;
      case ShareOption.productivity:
        return Icons.work;
      case ShareOption.photoLibrary:
        return Icons.photo_library;
      case ShareOption.videoApps:
        return Icons.video_library;
      case ShareOption.musicApps:
        return Icons.music_note;
    }
  }
}
