import 'package:logger/logger.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../models/library_file_model.dart';
import '../models/library_folder_model.dart';
import '../utils/filter_utils.dart';
import '../enums/file_access_type.dart';
import '../enums/folder_type.dart';
import '../enums/library_file_type.dart';

/// Advanced search service for digital library
class SearchService {
  static final SearchService _instance = SearchService._internal();
  factory SearchService() => _instance;
  SearchService._internal();

  final Logger _logger = Logger();

  /// Perform advanced search on files and folders
  Future<SearchResults> performAdvancedSearch({
    required List<LibraryFileModel> allFiles,
    required List<LibraryFolderModel> allFolders,
    required SearchCriteria criteria,
  }) async {
    try {
      _logger.i('Performing advanced search with criteria: ${criteria.query}');

      // Filter files
      final filteredFiles = FilterUtils.filterFiles(
        allFiles,
        searchQuery: criteria.query,
        accessType: criteria.accessType,
        fileType: criteria.fileType,
        tags: criteria.tags,
        createdAfter: criteria.createdAfter,
        createdBefore: criteria.createdBefore,
        modifiedAfter: criteria.modifiedAfter,
        modifiedBefore: criteria.modifiedBefore,
        minSizeBytes: criteria.minSizeBytes,
        maxSizeBytes: criteria.maxSizeBytes,
        isFavorite: criteria.isFavorite,
        uploaderId: criteria.uploaderId,
        folderId: criteria.folderId,
        classId: criteria.classId,
        subject: criteria.subject,
      );

      // Filter folders
      final filteredFolders = FilterUtils.filterFolders(
        allFolders,
        searchQuery: criteria.query,
        folderType: criteria.folderType,
        accessType: criteria.accessType,
        tags: criteria.tags,
        createdAfter: criteria.createdAfter,
        createdBefore: criteria.createdBefore,
        modifiedAfter: criteria.modifiedAfter,
        modifiedBefore: criteria.modifiedBefore,
        isFavorite: criteria.isFavorite,
        creatorId: criteria.creatorId,
        parentFolderId: criteria.parentFolderId,
        classId: criteria.classId,
        subject: criteria.subject,
      );

      // Calculate search statistics
      final stats = SearchStatistics(
        totalFiles: filteredFiles.length,
        totalFolders: filteredFolders.length,
        totalSize: FilterUtils.calculateTotalFileSize(filteredFiles),
        fileTypeDistribution: FilterUtils.getFileTypeDistribution(
          filteredFiles,
        ),
        folderTypeDistribution: FilterUtils.getFolderTypeDistribution(
          filteredFolders,
        ),
        accessTypeDistribution: FilterUtils.getFileAccessTypeDistribution(
          filteredFiles,
        ),
      );

      final results = SearchResults(
        files: filteredFiles,
        folders: filteredFolders,
        statistics: stats,
        searchCriteria: criteria,
      );

      _logger.i('Search completed: ${results.totalResults} results found');
      return results;
    } catch (e, stackTrace) {
      _logger.e(
        'Error performing advanced search: $e',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// Perform quick search (text-based only)
  Future<SearchResults> performQuickSearch({
    required List<LibraryFileModel> allFiles,
    required List<LibraryFolderModel> allFolders,
    required String query,
  }) async {
    try {
      _logger.i('Performing quick search: "$query"');

      final criteria = SearchCriteria(query: query);

      return await performAdvancedSearch(
        allFiles: allFiles,
        allFolders: allFolders,
        criteria: criteria,
      );
    } catch (e, stackTrace) {
      _logger.e(
        'Error performing quick search: $e',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// Get search suggestions based on query
  List<String> getSearchSuggestions({
    required List<LibraryFileModel> allFiles,
    required List<LibraryFolderModel> allFolders,
    required String query,
    int maxSuggestions = 10,
  }) {
    try {
      if (query.isEmpty) return [];

      final suggestions = <String>{};
      final queryLower = query.toLowerCase();

      // Add file name suggestions
      for (final file in allFiles) {
        if (file.fileName.toLowerCase().contains(queryLower)) {
          suggestions.add(file.fileName);
        }
        if (file.title?.toLowerCase().contains(queryLower) ?? false) {
          suggestions.add(file.title!);
        }
      }

      // Add folder name suggestions
      for (final folder in allFolders) {
        if (folder.name.toLowerCase().contains(queryLower)) {
          suggestions.add(folder.name);
        }
      }

      // Add tag suggestions
      final allTags = [
        ...FilterUtils.getUniqueTagsFromFiles(allFiles),
        ...FilterUtils.getUniqueTagsFromFolders(allFolders),
      ];

      for (final tag in allTags) {
        if (tag.toLowerCase().contains(queryLower)) {
          suggestions.add(tag);
        }
      }

      return suggestions.take(maxSuggestions).toList();
    } catch (e) {
      _logger.e('Error getting search suggestions: $e');
      return [];
    }
  }

  /// Get popular search terms
  List<String> getPopularSearchTerms({
    required List<LibraryFileModel> allFiles,
    required List<LibraryFolderModel> allFolders,
    int maxTerms = 10,
  }) {
    try {
      final termFrequency = <String, int>{};

      // Count file type labels
      for (final file in allFiles) {
        final term = file.fileType.label.toLowerCase();
        termFrequency[term] = (termFrequency[term] ?? 0) + 1;
      }

      // Count folder type labels
      for (final folder in allFolders) {
        final term = folder.folderType.label.toLowerCase();
        termFrequency[term] = (termFrequency[term] ?? 0) + 1;
      }

      // Count tags
      final allTags = [
        ...FilterUtils.getUniqueTagsFromFiles(allFiles),
        ...FilterUtils.getUniqueTagsFromFolders(allFolders),
      ];

      for (final tag in allTags) {
        final term = tag.toLowerCase();
        termFrequency[term] = (termFrequency[term] ?? 0) + 1;
      }

      // Sort by frequency and return top terms
      final sortedTerms = termFrequency.entries.toList()
        ..sort((a, b) => b.value.compareTo(a.value));

      return sortedTerms.take(maxTerms).map((entry) => entry.key).toList();
    } catch (e) {
      _logger.e('Error getting popular search terms: $e');
      return [];
    }
  }

  /// Get recent search terms from SharedPreferences
  Future<List<String>> getRecentSearchTerms({int maxTerms = 10}) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final recentTerms = prefs.getStringList('recent_search_terms') ?? [];
      return recentTerms.take(maxTerms).toList();
    } catch (e) {
      _logger.e('Error getting recent search terms: $e');
      return [];
    }
  }

  /// Save a search term to recent searches
  Future<void> saveSearchTerm(String term) async {
    try {
      if (term.trim().isEmpty) return;

      final prefs = await SharedPreferences.getInstance();
      final recentTerms = prefs.getStringList('recent_search_terms') ?? [];

      // Remove if already exists to avoid duplicates
      recentTerms.remove(term);

      // Add to beginning of list
      recentTerms.insert(0, term);

      // Keep only the most recent 20 terms
      if (recentTerms.length > 20) {
        recentTerms.removeRange(20, recentTerms.length);
      }

      await prefs.setStringList('recent_search_terms', recentTerms);
    } catch (e) {
      _logger.e('Error saving search term: $e');
    }
  }

  /// Clear all recent search terms
  Future<void> clearRecentSearchTerms() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('recent_search_terms');
    } catch (e) {
      _logger.e('Error clearing recent search terms: $e');
    }
  }
}

/// Search criteria model
class SearchCriteria {
  final String query;
  final FileAccessType? accessType;
  final LibraryFileType? fileType;
  final FolderType? folderType;
  final List<String>? tags;
  final DateTime? createdAfter;
  final DateTime? createdBefore;
  final DateTime? modifiedAfter;
  final DateTime? modifiedBefore;
  final int? minSizeBytes;
  final int? maxSizeBytes;
  final bool? isFavorite;
  final String? uploaderId;
  final String? creatorId;
  final String? folderId;
  final String? parentFolderId;
  final String? classId;
  final String? subject;

  const SearchCriteria({
    this.query = '',
    this.accessType,
    this.fileType,
    this.folderType,
    this.tags,
    this.createdAfter,
    this.createdBefore,
    this.modifiedAfter,
    this.modifiedBefore,
    this.minSizeBytes,
    this.maxSizeBytes,
    this.isFavorite,
    this.uploaderId,
    this.creatorId,
    this.folderId,
    this.parentFolderId,
    this.classId,
    this.subject,
  });

  bool get hasFilters {
    return query.isNotEmpty ||
        accessType != null ||
        fileType != null ||
        folderType != null ||
        (tags?.isNotEmpty ?? false) ||
        createdAfter != null ||
        createdBefore != null ||
        modifiedAfter != null ||
        modifiedBefore != null ||
        minSizeBytes != null ||
        maxSizeBytes != null ||
        isFavorite != null ||
        uploaderId != null ||
        creatorId != null ||
        folderId != null ||
        parentFolderId != null ||
        classId != null ||
        subject != null;
  }
}

/// Search results model
class SearchResults {
  final List<LibraryFileModel> files;
  final List<LibraryFolderModel> folders;
  final SearchStatistics statistics;
  final SearchCriteria searchCriteria;

  const SearchResults({
    required this.files,
    required this.folders,
    required this.statistics,
    required this.searchCriteria,
  });

  int get totalResults => files.length + folders.length;
  bool get hasResults => totalResults > 0;
  bool get isEmpty => totalResults == 0;
}

/// Search statistics model
class SearchStatistics {
  final int totalFiles;
  final int totalFolders;
  final int totalSize;
  final Map<LibraryFileType, int> fileTypeDistribution;
  final Map<FolderType, int> folderTypeDistribution;
  final Map<FileAccessType, int> accessTypeDistribution;

  const SearchStatistics({
    required this.totalFiles,
    required this.totalFolders,
    required this.totalSize,
    required this.fileTypeDistribution,
    required this.folderTypeDistribution,
    required this.accessTypeDistribution,
  });

  int get totalItems => totalFiles + totalFolders;

  String get totalSizeFormatted {
    if (totalSize < 1024) return '$totalSize B';
    if (totalSize < 1024 * 1024) {
      return '${(totalSize / 1024).toStringAsFixed(1)} KB';
    }
    if (totalSize < 1024 * 1024 * 1024) {
      return '${(totalSize / (1024 * 1024)).toStringAsFixed(1)} MB';
    }
    return '${(totalSize / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }
}
