import 'dart:io';
import 'dart:typed_data';
import 'package:dio/dio.dart';
import 'package:logger/logger.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;
import 'package:shared_preferences/shared_preferences.dart';

import '../models/library_file_model.dart';
import '../enums/offline_access.dart';
import '../repositories/digital_library_repository.dart';

/// Service for handling file operations like download, view, and offline access
class FileOperationsService {
  static final FileOperationsService _instance =
      FileOperationsService._internal();
  factory FileOperationsService() => _instance;
  FileOperationsService._internal();

  final Dio _dio = Dio();
  final Logger _logger = Logger();
  final DigitalLibraryRepository _repository = DigitalLibraryRepository();

  /// Download a file to local storage
  Future<String> downloadFile({
    required LibraryFileModel file,
    required String userId,
    Function(double)? onProgress,
    bool forOfflineAccess = false,
  }) async {
    try {
      _logger.i('Downloading file: ${file.fileName} for user: $userId');

      if (file.cloudUrl == null) {
        throw Exception('File has no cloud URL');
      }

      // Get local file path
      final localPath = await _getLocalFilePath(file, userId, forOfflineAccess);
      final localFile = File(localPath);

      // Create directory if it doesn't exist
      await localFile.parent.create(recursive: true);

      // Download file with progress tracking
      await _dio.download(
        file.cloudUrl!,
        localPath,
        onReceiveProgress: (received, total) {
          if (total != -1) {
            final progress = received / total;
            _logger.i(
              'Download progress for ${file.fileName}: ${(progress * 100).toStringAsFixed(1)}%',
            );
            onProgress?.call(progress);
          }
        },
      );

      // Update file access count
      await _repository.updateFileAccessCount(file.id, incrementDownload: true);

      // If downloading for offline access, update the file record
      if (forOfflineAccess) {
        await _repository.updateFile(
          file.copyWith(
            localPath: localPath,
            offlineAccess: OfflineAccess.available,
          ),
        );
      }

      _logger.i(
        'Successfully downloaded file: ${file.fileName} to: $localPath',
      );
      return localPath;
    } catch (e, stackTrace) {
      _logger.e('Error downloading file: $e', error: e, stackTrace: stackTrace);
      rethrow;
    }
  }

  /// Get file for viewing (download if not available locally)
  Future<String> getFileForViewing({
    required LibraryFileModel file,
    required String userId,
  }) async {
    try {
      _logger.i('Getting file for viewing: ${file.fileName}');

      // Check if file is available locally
      if (file.localPath != null && await File(file.localPath!).exists()) {
        _logger.i('File available locally: ${file.localPath}');

        // Update view count
        await _repository.updateFileAccessCount(file.id, incrementView: true);

        return file.localPath!;
      }

      // Download file for viewing
      final localPath = await downloadFile(
        file: file,
        userId: userId,
        forOfflineAccess: false,
      );

      // Update view count
      await _repository.updateFileAccessCount(file.id, incrementView: true);

      return localPath;
    } catch (e, stackTrace) {
      _logger.e(
        'Error getting file for viewing: $e',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// Enable offline access for a file
  Future<LibraryFileModel> enableOfflineAccess({
    required LibraryFileModel file,
    required String userId,
    Function(double)? onProgress,
  }) async {
    try {
      _logger.i('Enabling offline access for file: ${file.fileName}');

      if (file.offlineAccess == OfflineAccess.available) {
        _logger.i('File already available offline');
        return file;
      }

      // Download file for offline access
      final localPath = await downloadFile(
        file: file,
        userId: userId,
        onProgress: onProgress,
        forOfflineAccess: true,
      );

      // Update file record
      final updatedFile = file.copyWith(
        localPath: localPath,
        offlineAccess: OfflineAccess.available,
      );

      final result = await _repository.updateFile(updatedFile);

      // Store offline file info in SharedPreferences
      await _storeOfflineFileInfo(result);

      _logger.i(
        'Successfully enabled offline access for file: ${file.fileName}',
      );
      return result;
    } catch (e, stackTrace) {
      _logger.e(
        'Error enabling offline access: $e',
        error: e,
        stackTrace: stackTrace,
      );

      // Update file with error status
      await _repository.updateFile(
        file.copyWith(offlineAccess: OfflineAccess.failed),
      );

      rethrow;
    }
  }

  /// Disable offline access for a file
  Future<LibraryFileModel> disableOfflineAccess({
    required LibraryFileModel file,
  }) async {
    try {
      _logger.i('Disabling offline access for file: ${file.fileName}');

      // Delete local file if it exists
      if (file.localPath != null) {
        final localFile = File(file.localPath!);
        if (await localFile.exists()) {
          await localFile.delete();
          _logger.i('Deleted local file: ${file.localPath}');
        }
      }

      // Update file record
      final updatedFile = file.copyWith(
        localPath: null,
        offlineAccess: OfflineAccess.none,
      );

      final result = await _repository.updateFile(updatedFile);

      // Remove offline file info from SharedPreferences
      await _removeOfflineFileInfo(file.id);

      _logger.i(
        'Successfully disabled offline access for file: ${file.fileName}',
      );
      return result;
    } catch (e, stackTrace) {
      _logger.e(
        'Error disabling offline access: $e',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// Check if file is available offline
  Future<bool> isFileAvailableOffline(LibraryFileModel file) async {
    try {
      if (file.offlineAccess != OfflineAccess.available ||
          file.localPath == null) {
        return false;
      }

      final localFile = File(file.localPath!);
      return await localFile.exists();
    } catch (e) {
      _logger.e('Error checking offline availability: $e');
      return false;
    }
  }

  /// Get all offline files for a user
  Future<List<String>> getOfflineFiles(String userId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final offlineFilesKey = 'offline_files_$userId';
      return prefs.getStringList(offlineFilesKey) ?? [];
    } catch (e) {
      _logger.e('Error getting offline files: $e');
      return [];
    }
  }

  /// Clean up offline files (remove files that no longer exist)
  Future<void> cleanupOfflineFiles(String userId) async {
    try {
      _logger.i('Cleaning up offline files for user: $userId');

      final offlineFileIds = await getOfflineFiles(userId);
      final filesToRemove = <String>[];

      for (final fileId in offlineFileIds) {
        final file = await _repository.getFileById(fileId);
        if (file == null || file.localPath == null) {
          filesToRemove.add(fileId);
          continue;
        }

        final localFile = File(file.localPath!);
        if (!await localFile.exists()) {
          filesToRemove.add(fileId);

          // Update file record to remove offline access
          await _repository.updateFile(
            file.copyWith(localPath: null, offlineAccess: OfflineAccess.none),
          );
        }
      }

      // Remove invalid files from offline list
      for (final fileId in filesToRemove) {
        await _removeOfflineFileInfo(fileId);
      }

      _logger.i('Cleaned up ${filesToRemove.length} offline files');
    } catch (e, stackTrace) {
      _logger.e(
        'Error cleaning up offline files: $e',
        error: e,
        stackTrace: stackTrace,
      );
    }
  }

  /// Get total size of offline files for a user
  Future<int> getOfflineFilesSize(String userId) async {
    try {
      final offlineFileIds = await getOfflineFiles(userId);
      int totalSize = 0;

      for (final fileId in offlineFileIds) {
        final file = await _repository.getFileById(fileId);
        if (file?.localPath != null) {
          final localFile = File(file!.localPath!);
          if (await localFile.exists()) {
            totalSize += await localFile.length();
          }
        }
      }

      return totalSize;
    } catch (e) {
      _logger.e('Error calculating offline files size: $e');
      return 0;
    }
  }

  /// Clear all offline files for a user
  Future<void> clearAllOfflineFiles(String userId) async {
    try {
      _logger.i('Clearing all offline files for user: $userId');

      final offlineFileIds = await getOfflineFiles(userId);

      for (final fileId in offlineFileIds) {
        final file = await _repository.getFileById(fileId);
        if (file != null) {
          await disableOfflineAccess(file: file);
        }
      }

      // Clear the offline files list
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('offline_files_$userId');

      _logger.i('Successfully cleared all offline files for user: $userId');
    } catch (e, stackTrace) {
      _logger.e(
        'Error clearing offline files: $e',
        error: e,
        stackTrace: stackTrace,
      );
    }
  }

  /// Get local file path for a file
  Future<String> _getLocalFilePath(
    LibraryFileModel file,
    String userId,
    bool forOfflineAccess,
  ) async {
    final appDir = await getApplicationDocumentsDirectory();
    final baseDir = forOfflineAccess ? 'offline_files' : 'temp_files';

    final folderPath = file.folderId != null
        ? '$baseDir/$userId/folders/${file.folderId}'
        : '$baseDir/$userId/root';

    return path.join(appDir.path, folderPath, file.fileName);
  }

  /// Store offline file info in SharedPreferences
  Future<void> _storeOfflineFileInfo(LibraryFileModel file) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final offlineFilesKey = 'offline_files_${file.uploaderId}';
      final offlineFiles = prefs.getStringList(offlineFilesKey) ?? [];

      if (!offlineFiles.contains(file.id)) {
        offlineFiles.add(file.id);
        await prefs.setStringList(offlineFilesKey, offlineFiles);
      }
    } catch (e) {
      _logger.e('Error storing offline file info: $e');
    }
  }

  /// Remove offline file info from SharedPreferences
  Future<void> _removeOfflineFileInfo(String fileId) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // We need to check all users' offline files since we don't know the user ID
      final keys = prefs.getKeys().where(
        (key) => key.startsWith('offline_files_'),
      );

      for (final key in keys) {
        final offlineFiles = prefs.getStringList(key) ?? [];
        if (offlineFiles.contains(fileId)) {
          offlineFiles.remove(fileId);
          await prefs.setStringList(key, offlineFiles);
          break;
        }
      }
    } catch (e) {
      _logger.e('Error removing offline file info: $e');
    }
  }

  /// Get file bytes for sharing or processing
  Future<Uint8List> getFileBytes(LibraryFileModel file, String userId) async {
    try {
      _logger.i('Getting file bytes for: ${file.fileName}');

      // Try to get from local file first
      if (file.localPath != null && await File(file.localPath!).exists()) {
        return await File(file.localPath!).readAsBytes();
      }

      // Download from cloud if not available locally
      if (file.cloudUrl != null) {
        final response = await _dio.get<List<int>>(
          file.cloudUrl!,
          options: Options(responseType: ResponseType.bytes),
        );
        return Uint8List.fromList(response.data!);
      }

      throw Exception('File not available locally or in cloud');
    } catch (e, stackTrace) {
      _logger.e(
        'Error getting file bytes: $e',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// Check if file can be previewed (based on file type)
  bool canPreviewFile(LibraryFileModel file) {
    const previewableTypes = [
      'pdf',
      'jpg',
      'jpeg',
      'png',
      'gif',
      'webp',
      'txt',
      'md',
      'mp4',
      'webm',
      'ogg',
      'mp3',
      'wav',
      'ogg',
    ];

    return previewableTypes.contains(file.fileExtension.toLowerCase());
  }

  /// Check if file can be edited (based on file type)
  bool canEditFile(LibraryFileModel file) {
    const editableTypes = ['txt', 'md', 'json', 'xml', 'csv'];

    return editableTypes.contains(file.fileExtension.toLowerCase());
  }
}
