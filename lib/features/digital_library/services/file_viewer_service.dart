import 'dart:io';
import 'package:flutter/material.dart';
import 'package:logger/logger.dart';
import 'package:url_launcher/url_launcher.dart';
import '../models/file_model.dart';
import '../enums/library_file_type.dart';
import '../enums/viewer_type.dart';
import '../screens/file_viewer_screen/file_viewer_screen.dart';
import 'file_download_service.dart';

/// Service for handling file viewing operations
class FileViewerService {
  static final FileViewerService _instance = FileViewerService._internal();
  factory FileViewerService() => _instance;
  FileViewerService._internal();

  final Logger _logger = Logger();
  final FileDownloadService _downloadService = FileDownloadService();

  /// Open a file for viewing
  Future<void> openFile(
    BuildContext context,
    FileModel fileModel, {
    List<FileModel>? fileList,
    int? currentIndex,
  }) async {
    try {
      _logger.i('Opening file: ${fileModel.fileName} (${fileModel.fileType})');

      // Check if file can be viewed natively
      if (fileModel.fileType.canViewNatively) {
        await _openInNativeViewer(context, fileModel, fileList, currentIndex);
      } else {
        await _openInPlaceholderViewer(
          context,
          fileModel,
          fileList,
          currentIndex,
        );
      }
    } catch (e) {
      _logger.e('Error opening file: $e');
      if (context.mounted) {
        _showErrorDialog(context, 'Failed to open file: $e');
      }
    }
  }

  /// Open file in native viewer (for images and PDFs)
  Future<void> _openInNativeViewer(
    BuildContext context,
    FileModel fileModel,
    List<FileModel>? fileList,
    int? currentIndex,
  ) async {
    // If file is remote and not downloaded, download it first
    if (!fileModel.isLocal && !fileModel.isAvailableForViewing) {
      final shouldDownload = await _showDownloadDialog(context, fileModel);
      if (!shouldDownload) return;

      // Show loading dialog while downloading
      if (context.mounted) {
        _showDownloadingDialog(context, fileModel);
      }
    }

    // Navigate to file viewer screen
    if (context.mounted) {
      await Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => FileViewerScreen(
            fileModel: fileModel,
            fileList: fileList,
            currentIndex: currentIndex,
          ),
        ),
      );
    }
  }

  /// Open file in placeholder viewer (for unsupported types)
  Future<void> _openInPlaceholderViewer(
    BuildContext context,
    FileModel fileModel,
    List<FileModel>? fileList,
    int? currentIndex,
  ) async {
    // Navigate to file viewer screen with placeholder viewer
    await Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => FileViewerScreen(
          fileModel: fileModel,
          fileList: fileList,
          currentIndex: currentIndex,
        ),
      ),
    );
  }

  /// Open file externally using system default app
  Future<void> openFileExternally(FileModel fileModel) async {
    try {
      String? pathToOpen;

      if (fileModel.isLocal && fileModel.localPath != null) {
        pathToOpen = fileModel.localPath!;
      } else if (fileModel.url != null) {
        pathToOpen = fileModel.url!;
      }

      if (pathToOpen != null) {
        final uri = fileModel.isLocal
            ? Uri.file(pathToOpen)
            : Uri.parse(pathToOpen);

        if (await canLaunchUrl(uri)) {
          await launchUrl(uri, mode: LaunchMode.externalApplication);
          _logger.i('Opened file externally: $pathToOpen');
        } else {
          throw Exception('Cannot open file externally');
        }
      } else {
        throw Exception('No valid path or URL to open');
      }
    } catch (e) {
      _logger.e('Error opening file externally: $e');
      rethrow;
    }
  }

  /// Get the appropriate viewer type for a file
  ViewerType getViewerType(FileModel fileModel) {
    return fileModel.viewerType;
  }

  /// Check if a file can be viewed in the app
  bool canViewInApp(FileModel fileModel) {
    return fileModel.fileType.canViewNatively;
  }

  /// Check if a file needs to be downloaded before viewing
  bool needsDownload(FileModel fileModel) {
    return !fileModel.isLocal && !fileModel.isAvailableForViewing;
  }

  /// Download a file for viewing
  Future<FileModel?> downloadForViewing(FileModel fileModel) async {
    if (fileModel.isLocal || fileModel.url == null) {
      return fileModel;
    }

    try {
      _logger.i('Downloading file for viewing: ${fileModel.fileName}');
      return await _downloadService.downloadFile(fileModel);
    } catch (e) {
      _logger.e('Error downloading file for viewing: $e');
      return null;
    }
  }

  /// Show download confirmation dialog
  Future<bool> _showDownloadDialog(
    BuildContext context,
    FileModel fileModel,
  ) async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Download File'),
        content: Text(
          'This file needs to be downloaded before it can be viewed.\n\n'
          'File: ${fileModel.fileName}\n'
          'Size: ${fileModel.fileSizeString}',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('Download'),
          ),
        ],
      ),
    );

    return result ?? false;
  }

  /// Show downloading progress dialog
  void _showDownloadingDialog(BuildContext context, FileModel fileModel) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text('Downloading'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const CircularProgressIndicator(),
            const SizedBox(height: 16),
            Text('Downloading ${fileModel.fileName}...'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              _downloadService.cancelDownload(fileModel.id);
              Navigator.of(context).pop();
            },
            child: const Text('Cancel'),
          ),
        ],
      ),
    );

    // Listen for download completion
    _downloadService.progressStream
        .where((progress) => progress.fileId == fileModel.id)
        .listen((progress) {
          if (progress.isComplete || progress.hasFailed) {
            // Use a post-frame callback to ensure context is still valid
            WidgetsBinding.instance.addPostFrameCallback((_) {
              if (context.mounted) {
                Navigator.of(context).pop(); // Close downloading dialog

                if (progress.hasFailed) {
                  _showErrorDialog(
                    context,
                    progress.errorMessage ?? 'Download failed',
                  );
                }
              }
            });
          }
        });
  }

  /// Show error dialog
  void _showErrorDialog(BuildContext context, String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Error'),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  /// Create a FileModel from a URL string
  FileModel createFileModelFromUrl(String url, {String? customFileName}) {
    return FileModel.fromUrl(url, customFileName: customFileName);
  }

  /// Create a FileModel from a local file path
  FileModel createFileModelFromPath(String filePath) {
    final file = File(filePath);
    return FileModel.fromLocalFile(file);
  }

  /// Get file icon based on file type
  IconData getFileIcon(FileType fileType) {
    return fileType.icon;
  }

  /// Get file color based on file type
  Color getFileColor(FileType fileType) {
    // Use the LibraryFileType color system
    final libraryFileType = fileType.toLibraryFileType();
    final colorHex = libraryFileType.colorHex;
    return Color(int.parse(colorHex.substring(1), radix: 16) + 0xFF000000);
  }
}
