import 'dart:io';
import 'dart:convert';
import 'package:path_provider/path_provider.dart';
import 'package:logger/logger.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Service for managing file caching
class FileCacheService {
  static final FileCacheService _instance = FileCacheService._internal();
  factory FileCacheService() => _instance;
  FileCacheService._internal();

  final Logger _logger = Logger();
  static const String _cacheMetadataKey = 'file_cache_metadata';
  static const String _cacheDirectoryName = 'file_cache';

  Directory? _cacheDirectory;
  Map<String, CacheEntry> _cacheMetadata = {};

  /// Initialize the cache service
  Future<void> initialize() async {
    try {
      _cacheDirectory = await _getCacheDirectory();
      await _loadCacheMetadata();
      await _cleanupExpiredFiles();
      _logger.i('FileCacheService initialized');
    } catch (e) {
      _logger.e('Failed to initialize FileCacheService: $e');
    }
  }

  /// Check if a file is cached
  bool isFileCached(String url) {
    final cacheKey = _generateCacheKey(url);
    final entry = _cacheMetadata[cacheKey];

    if (entry == null) return false;

    // Check if file still exists
    final file = File(entry.localPath);
    if (!file.existsSync()) {
      _removeCacheEntry(cacheKey);
      return false;
    }

    // Check if file is expired
    if (_isExpired(entry)) {
      _removeCacheEntry(cacheKey);
      return false;
    }

    return true;
  }

  /// Get cached file path
  String? getCachedFilePath(String url) {
    if (!isFileCached(url)) return null;

    final cacheKey = _generateCacheKey(url);
    final entry = _cacheMetadata[cacheKey];
    return entry?.localPath;
  }

  /// Cache a file by URL and local path
  Future<String?> cacheFile(String url, String localPath) async {
    if (_cacheDirectory == null) return null;

    try {
      final cacheKey = _generateCacheKey(url);
      final fileName = _generateCacheFileName(localPath.split('/').last);
      final cachedFilePath = '${_cacheDirectory!.path}/$fileName';

      // Copy file to cache
      final sourceFile = File(localPath);
      if (await sourceFile.exists()) {
        await sourceFile.copy(cachedFilePath);
      } else {
        return null;
      }

      // Get file size
      final fileSize = await sourceFile.length();

      // Create cache entry
      final entry = CacheEntry(
        url: url,
        localPath: cachedFilePath,
        fileName: fileName,
        fileSize: fileSize,
        cachedAt: DateTime.now(),
        lastAccessed: DateTime.now(),
        expiresAt: DateTime.now().add(
          const Duration(days: 30),
        ), // 30 days default
      );

      _cacheMetadata[cacheKey] = entry;
      await _saveCacheMetadata();

      _logger.i('File cached: $fileName');
      return cachedFilePath;
    } catch (e) {
      _logger.e('Failed to cache file: $e');
      return null;
    }
  }

  /// Update last accessed time for a cached file
  Future<void> updateLastAccessed(String url) async {
    final cacheKey = _generateCacheKey(url);
    final entry = _cacheMetadata[cacheKey];

    if (entry != null) {
      _cacheMetadata[cacheKey] = entry.copyWith(lastAccessed: DateTime.now());
      await _saveCacheMetadata();
    }
  }

  /// Get cache statistics
  CacheStats getCacheStats() {
    int totalFiles = _cacheMetadata.length;
    int totalSize = 0;
    int expiredFiles = 0;

    for (final entry in _cacheMetadata.values) {
      totalSize += entry.fileSize;
      if (_isExpired(entry)) {
        expiredFiles++;
      }
    }

    return CacheStats(
      totalFiles: totalFiles,
      totalSizeBytes: totalSize,
      expiredFiles: expiredFiles,
      cacheDirectory: _cacheDirectory?.path,
    );
  }

  /// Clear all cached files
  Future<void> clearCache() async {
    try {
      if (_cacheDirectory != null && await _cacheDirectory!.exists()) {
        await _cacheDirectory!.delete(recursive: true);
        await _cacheDirectory!.create();
      }

      _cacheMetadata.clear();
      await _saveCacheMetadata();

      _logger.i('Cache cleared');
    } catch (e) {
      _logger.e('Failed to clear cache: $e');
    }
  }

  /// Remove expired files from cache
  Future<void> _cleanupExpiredFiles() async {
    final expiredKeys = <String>[];

    for (final entry in _cacheMetadata.entries) {
      if (_isExpired(entry.value)) {
        expiredKeys.add(entry.key);

        // Delete the actual file
        try {
          final file = File(entry.value.localPath);
          if (await file.exists()) {
            await file.delete();
          }
        } catch (e) {
          _logger.w('Failed to delete expired file: $e');
        }
      }
    }

    // Remove expired entries from metadata
    for (final key in expiredKeys) {
      _cacheMetadata.remove(key);
    }

    if (expiredKeys.isNotEmpty) {
      await _saveCacheMetadata();
      _logger.i('Cleaned up ${expiredKeys.length} expired files');
    }
  }

  /// Get cache directory
  Future<Directory> _getCacheDirectory() async {
    final appDir = await getApplicationDocumentsDirectory();
    final cacheDir = Directory('${appDir.path}/$_cacheDirectoryName');

    if (!await cacheDir.exists()) {
      await cacheDir.create(recursive: true);
    }

    return cacheDir;
  }

  /// Generate cache key from URL
  String _generateCacheKey(String url) {
    return url.hashCode.toString();
  }

  /// Generate unique cache filename
  String _generateCacheFileName(String originalName) {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    return '${timestamp}_$originalName';
  }

  /// Check if cache entry is expired
  bool _isExpired(CacheEntry entry) {
    return DateTime.now().isAfter(entry.expiresAt);
  }

  /// Remove cache entry
  void _removeCacheEntry(String cacheKey) {
    final entry = _cacheMetadata[cacheKey];
    if (entry != null) {
      // Delete the actual file
      try {
        final file = File(entry.localPath);
        if (file.existsSync()) {
          file.deleteSync();
        }
      } catch (e) {
        _logger.w('Failed to delete cache file: $e');
      }

      _cacheMetadata.remove(cacheKey);
      _saveCacheMetadata();
    }
  }

  /// Load cache metadata from shared preferences
  Future<void> _loadCacheMetadata() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final metadataJson = prefs.getString(_cacheMetadataKey);

      if (metadataJson != null) {
        final Map<String, dynamic> decoded = jsonDecode(metadataJson);
        _cacheMetadata = decoded.map(
          (key, value) => MapEntry(key, CacheEntry.fromJson(value)),
        );
      }
    } catch (e) {
      _logger.e('Failed to load cache metadata: $e');
      _cacheMetadata = {};
    }
  }

  /// Save cache metadata to shared preferences
  Future<void> _saveCacheMetadata() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final metadataJson = jsonEncode(
        _cacheMetadata.map((key, value) => MapEntry(key, value.toJson())),
      );
      await prefs.setString(_cacheMetadataKey, metadataJson);
    } catch (e) {
      _logger.e('Failed to save cache metadata: $e');
    }
  }
}

/// Cache entry model
class CacheEntry {
  final String url;
  final String localPath;
  final String fileName;
  final int fileSize;
  final DateTime cachedAt;
  final DateTime lastAccessed;
  final DateTime expiresAt;

  const CacheEntry({
    required this.url,
    required this.localPath,
    required this.fileName,
    required this.fileSize,
    required this.cachedAt,
    required this.lastAccessed,
    required this.expiresAt,
  });

  CacheEntry copyWith({
    String? url,
    String? localPath,
    String? fileName,
    int? fileSize,
    DateTime? cachedAt,
    DateTime? lastAccessed,
    DateTime? expiresAt,
  }) {
    return CacheEntry(
      url: url ?? this.url,
      localPath: localPath ?? this.localPath,
      fileName: fileName ?? this.fileName,
      fileSize: fileSize ?? this.fileSize,
      cachedAt: cachedAt ?? this.cachedAt,
      lastAccessed: lastAccessed ?? this.lastAccessed,
      expiresAt: expiresAt ?? this.expiresAt,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'url': url,
      'localPath': localPath,
      'fileName': fileName,
      'fileSize': fileSize,
      'cachedAt': cachedAt.toIso8601String(),
      'lastAccessed': lastAccessed.toIso8601String(),
      'expiresAt': expiresAt.toIso8601String(),
    };
  }

  factory CacheEntry.fromJson(Map<String, dynamic> json) {
    return CacheEntry(
      url: json['url'],
      localPath: json['localPath'],
      fileName: json['fileName'],
      fileSize: json['fileSize'],
      cachedAt: DateTime.parse(json['cachedAt']),
      lastAccessed: DateTime.parse(json['lastAccessed']),
      expiresAt: DateTime.parse(json['expiresAt']),
    );
  }
}

/// Cache statistics model
class CacheStats {
  final int totalFiles;
  final int totalSizeBytes;
  final int expiredFiles;
  final String? cacheDirectory;

  const CacheStats({
    required this.totalFiles,
    required this.totalSizeBytes,
    required this.expiredFiles,
    this.cacheDirectory,
  });

  String get formattedSize {
    if (totalSizeBytes < 1024) {
      return '$totalSizeBytes B';
    } else if (totalSizeBytes < 1024 * 1024) {
      return '${(totalSizeBytes / 1024).toStringAsFixed(1)} KB';
    } else if (totalSizeBytes < 1024 * 1024 * 1024) {
      return '${(totalSizeBytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    }
    return '${(totalSizeBytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }
}
