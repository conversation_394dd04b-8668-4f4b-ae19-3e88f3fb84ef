import 'dart:io';
import 'dart:async';
import 'package:dio/dio.dart';
import 'package:path_provider/path_provider.dart';

import 'package:logger/logger.dart';
import '../models/file_model.dart';
import '../models/download_progress_model.dart';
import '../enums/download_state.dart';
import 'file_cache_service.dart';

/// Service for downloading files from URLs with progress tracking
class FileDownloadService {
  static final FileDownloadService _instance = FileDownloadService._internal();
  factory FileDownloadService() => _instance;
  FileDownloadService._internal();

  final Dio _dio = Dio();
  final Logger _logger = Logger();
  final FileCacheService _cacheService = FileCacheService();

  /// Map to track active downloads
  final Map<String, CancelToken> _activeDownloads = {};

  /// Stream controller for download progress updates
  final StreamController<DownloadProgressModel> _progressController =
      StreamController<DownloadProgressModel>.broadcast();

  /// Stream of download progress updates
  Stream<DownloadProgressModel> get progressStream =>
      _progressController.stream;

  /// Initialize the download service
  void initialize() {
    _dio.options.connectTimeout = const Duration(seconds: 30);
    _dio.options.receiveTimeout = const Duration(seconds: 30);
    _dio.options.sendTimeout = const Duration(seconds: 30);

    _logger.i('FileDownloadService initialized');
  }

  /// Download a file from URL
  Future<FileModel?> downloadFile(FileModel fileModel) async {
    if (fileModel.url == null) {
      _logger.e('Cannot download file: URL is null');
      return null;
    }

    // Check if file is already cached
    if (_cacheService.isFileCached(fileModel.url!)) {
      final cachedPath = _cacheService.getCachedFilePath(fileModel.url!);
      if (cachedPath != null) {
        _logger.i('File found in cache: ${fileModel.fileName}');
        await _cacheService.updateLastAccessed(fileModel.url!);

        return fileModel.copyWith(
          localPath: cachedPath,
          downloadState: DownloadState.downloaded,
          downloadProgress: 1.0,
        );
      }
    }

    if (_activeDownloads.containsKey(fileModel.id)) {
      _logger.w('Download already in progress for file: ${fileModel.id}');
      return null;
    }

    try {
      // Create cancel token for this download
      final cancelToken = CancelToken();
      _activeDownloads[fileModel.id] = cancelToken;

      // Emit started progress
      _emitProgress(DownloadProgressModel.started(fileModel.id));

      // Get download directory
      final downloadDir = await _getDownloadDirectory();
      if (downloadDir == null) {
        throw Exception('Could not access download directory');
      }

      // Generate unique filename to avoid conflicts
      final fileName = _generateUniqueFileName(downloadDir, fileModel.fileName);
      final filePath = '${downloadDir.path}/$fileName';

      _logger.i('Starting download: ${fileModel.url} -> $filePath');

      // Download the file
      final response = await _dio.download(
        fileModel.url!,
        filePath,
        cancelToken: cancelToken,
        onReceiveProgress: (received, total) {
          if (total > 0) {
            final progress = received / total;
            final speed = _calculateDownloadSpeed(received, total);
            final estimatedTime = _calculateEstimatedTime(
              received,
              total,
              speed,
            );

            _emitProgress(DownloadProgressModel.inProgress(
              fileId: fileModel.id,
              progress: progress,
              bytesDownloaded: received,
              totalBytes: total,
              speedBytesPerSecond: speed,
              estimatedTimeRemainingSeconds: estimatedTime,
            ));
          }
        },
      );

      if (response.statusCode == 200) {
        // Cache the downloaded file
        await _cacheService.cacheFile(fileModel.url!, filePath);

        // Emit completion progress
        final fileSize = await File(filePath).length();
        _emitProgress(DownloadProgressModel.completed(fileModel.id, fileSize));

        _logger.i('Download completed: ${fileModel.fileName}');

        return fileModel.copyWith(
          localPath: filePath,
          downloadState: DownloadState.downloaded,
          downloadProgress: 1.0,
          fileSizeBytes: fileSize,
        );
      } else {
        throw Exception('Download failed with status: ${response.statusCode}');
      }
    } catch (e) {
      _logger.e('Download failed for ${fileModel.fileName}: $e');

      // Emit failure progress
      _emitProgress(DownloadProgressModel.failed(fileModel.id, e.toString()));

      return fileModel.copyWith(
        downloadState: DownloadState.failed,
        downloadError: e.toString(),
      );
    } finally {
      _activeDownloads.remove(fileModel.id);
    }
  }

  /// Cancel a download
  void cancelDownload(String fileId) {
    final cancelToken = _activeDownloads[fileId];
    if (cancelToken != null) {
      cancelToken.cancel('Download cancelled by user');
      _activeDownloads.remove(fileId);
      _emitProgress(DownloadProgressModel.cancelled(fileId));
      _logger.i('Download cancelled for file: $fileId');
    }
  }

  /// Pause a download (not implemented in this version)
  void pauseDownload(String fileId) {
    // TODO: Implement pause functionality
    _logger.w('Pause download not implemented for file: $fileId');
  }

  /// Resume a download (not implemented in this version)
  void resumeDownload(String fileId) {
    // TODO: Implement resume functionality
    _logger.w('Resume download not implemented for file: $fileId');
  }

  /// Get list of active downloads
  List<String> getActiveDownloads() {
    return _activeDownloads.keys.toList();
  }

  /// Check if a file is currently being downloaded
  bool isDownloading(String fileId) {
    return _activeDownloads.containsKey(fileId);
  }

  /// Clear all downloads
  void clearAllDownloads() {
    for (final cancelToken in _activeDownloads.values) {
      cancelToken.cancel('All downloads cleared');
    }
    _activeDownloads.clear();
    _logger.i('All downloads cleared');
  }

  /// Get download directory
  Future<Directory?> _getDownloadDirectory() async {
    try {
      final appDir = await getApplicationDocumentsDirectory();
      final downloadDir = Directory('${appDir.path}/downloads');
      
      if (!await downloadDir.exists()) {
        await downloadDir.create(recursive: true);
      }
      
      return downloadDir;
    } catch (e) {
      _logger.e('Error getting download directory: $e');
      return null;
    }
  }

  /// Generate unique filename to avoid conflicts
  String _generateUniqueFileName(Directory directory, String originalName) {
    final file = File('${directory.path}/$originalName');
    if (!file.existsSync()) {
      return originalName;
    }

    final nameWithoutExtension = originalName.split('.').first;
    final extension = originalName.split('.').last;
    
    int counter = 1;
    while (true) {
      final newName = '${nameWithoutExtension}_$counter.$extension';
      final newFile = File('${directory.path}/$newName');
      if (!newFile.existsSync()) {
        return newName;
      }
      counter++;
    }
  }

  /// Calculate download speed
  double _calculateDownloadSpeed(int received, int total) {
    // Simple speed calculation - in a real implementation,
    // you'd want to track time and calculate actual speed
    return received / 1024; // KB/s approximation
  }

  /// Calculate estimated time remaining
  int _calculateEstimatedTime(int received, int total, double speed) {
    if (speed <= 0) return 0;
    final remaining = total - received;
    return (remaining / speed).round();
  }

  /// Emit progress update
  void _emitProgress(DownloadProgressModel progress) {
    if (!_progressController.isClosed) {
      _progressController.add(progress);
    }
  }

  /// Dispose the service
  void dispose() {
    clearAllDownloads();
    _progressController.close();
    _logger.i('FileDownloadService disposed');
  }
}
