import 'dart:io';
import 'package:flutter/material.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:logger/logger.dart';

/// Service for handling file-related permissions
class FilePermissionsService {
  static final FilePermissionsService _instance =
      FilePermissionsService._internal();
  factory FilePermissionsService() => _instance;
  FilePermissionsService._internal();

  final Logger _logger = Logger();

  /// Request storage permission for file downloads
  Future<bool> requestStoragePermission() async {
    try {
      if (Platform.isAndroid) {
        // For Android 13+ (API 33+), we need different permissions
        final androidInfo = await _getAndroidVersion();

        if (androidInfo >= 33) {
          // Android 13+ uses scoped storage, no special permission needed for app-specific directories
          return true;
        } else {
          // Android 12 and below
          final status = await Permission.storage.request();
          return status.isGranted;
        }
      } else if (Platform.isIOS) {
        // iOS doesn't require explicit storage permission for app documents
        return true;
      }

      return false;
    } catch (e) {
      _logger.e('Error requesting storage permission: $e');
      return false;
    }
  }

  /// Request photo library permission
  Future<bool> requestPhotoPermission() async {
    try {
      final status = await Permission.photos.request();
      return status.isGranted;
    } catch (e) {
      _logger.e('Error requesting photo permission: $e');
      return false;
    }
  }

  /// Request camera permission
  Future<bool> requestCameraPermission() async {
    try {
      final status = await Permission.camera.request();
      return status.isGranted;
    } catch (e) {
      _logger.e('Error requesting camera permission: $e');
      return false;
    }
  }

  /// Check if storage permission is granted
  Future<bool> hasStoragePermission() async {
    try {
      if (Platform.isAndroid) {
        final androidInfo = await _getAndroidVersion();

        if (androidInfo >= 33) {
          return true; // No explicit permission needed for scoped storage
        } else {
          final status = await Permission.storage.status;
          return status.isGranted;
        }
      } else if (Platform.isIOS) {
        return true; // iOS doesn't require explicit storage permission
      }

      return false;
    } catch (e) {
      _logger.e('Error checking storage permission: $e');
      return false;
    }
  }

  /// Check if photo permission is granted
  Future<bool> hasPhotoPermission() async {
    try {
      final status = await Permission.photos.status;
      return status.isGranted;
    } catch (e) {
      _logger.e('Error checking photo permission: $e');
      return false;
    }
  }

  /// Check if camera permission is granted
  Future<bool> hasCameraPermission() async {
    try {
      final status = await Permission.camera.status;
      return status.isGranted;
    } catch (e) {
      _logger.e('Error checking camera permission: $e');
      return false;
    }
  }

  /// Show permission denied dialog
  Future<bool> showPermissionDeniedDialog(
    BuildContext context,
    String permissionName,
    String reason,
  ) async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('$permissionName Permission Required'),
        content: Text(reason),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('Open Settings'),
          ),
        ],
      ),
    );

    if (result == true) {
      await openAppSettings();
    }

    return result ?? false;
  }

  /// Show permission rationale dialog
  Future<bool> showPermissionRationaleDialog(
    BuildContext context,
    String permissionName,
    String reason,
  ) async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('$permissionName Permission'),
        content: Text(reason),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('Grant Permission'),
          ),
        ],
      ),
    );

    return result ?? false;
  }

  /// Request all necessary permissions for file operations
  Future<PermissionStatus> requestFileOperationPermissions() async {
    try {
      final storageGranted = await requestStoragePermission();

      if (!storageGranted) {
        return PermissionStatus.denied;
      }

      return PermissionStatus.granted;
    } catch (e) {
      _logger.e('Error requesting file operation permissions: $e');
      return PermissionStatus.denied;
    }
  }

  /// Check if all necessary permissions are granted
  Future<bool> hasAllFilePermissions() async {
    try {
      final storageGranted = await hasStoragePermission();
      return storageGranted;
    } catch (e) {
      _logger.e('Error checking file permissions: $e');
      return false;
    }
  }

  /// Handle permission request with user-friendly dialogs
  Future<bool> handlePermissionRequest(
    BuildContext context,
    Permission permission,
    String permissionName,
    String reason,
  ) async {
    try {
      final status = await permission.status;

      if (status.isGranted) {
        return true;
      }

      if (status.isDenied) {
        // Check if context is still valid before showing dialog
        if (!context.mounted) return false;

        // Show rationale and request permission
        final shouldRequest = await showPermissionRationaleDialog(
          context,
          permissionName,
          reason,
        );

        if (shouldRequest) {
          final newStatus = await permission.request();
          return newStatus.isGranted;
        }
      }

      if (status.isPermanentlyDenied) {
        // Check if context is still valid before showing dialog
        if (!context.mounted) return false;

        // Show dialog to open settings
        await showPermissionDeniedDialog(
          context,
          permissionName,
          'Permission was permanently denied. Please enable it in settings.',
        );
      }

      return false;
    } catch (e) {
      _logger.e('Error handling permission request: $e');
      return false;
    }
  }

  /// Get Android version (simplified)
  Future<int> _getAndroidVersion() async {
    try {
      // This is a simplified implementation
      // In a real app, you might want to use device_info_plus package
      return 30; // Default to API 30 for compatibility
    } catch (e) {
      _logger.e('Error getting Android version: $e');
      return 30;
    }
  }

  /// Check if permission is permanently denied
  Future<bool> isPermissionPermanentlyDenied(Permission permission) async {
    try {
      final status = await permission.status;
      return status.isPermanentlyDenied;
    } catch (e) {
      _logger.e('Error checking if permission is permanently denied: $e');
      return false;
    }
  }

  /// Get permission status message
  String getPermissionStatusMessage(PermissionStatus status) {
    switch (status) {
      case PermissionStatus.granted:
        return 'Permission granted';
      case PermissionStatus.denied:
        return 'Permission denied';
      case PermissionStatus.restricted:
        return 'Permission restricted';
      case PermissionStatus.limited:
        return 'Permission limited';
      case PermissionStatus.permanentlyDenied:
        return 'Permission permanently denied';
      case PermissionStatus.provisional:
        return 'Permission provisional';
    }
  }
}
