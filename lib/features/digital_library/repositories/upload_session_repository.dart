import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:logger/logger.dart';

import '../../../core/constants/firebase_collections.dart';
import '../models/upload_session_model.dart';
import '../enums/upload_status.dart';

/// Repository for managing upload sessions with Firebase Firestore
/// Handles upload progress tracking and session management
class UploadSessionRepository {
  static final UploadSessionRepository _instance =
      UploadSessionRepository._internal();
  factory UploadSessionRepository() => _instance;
  UploadSessionRepository._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final Logger _logger = Logger();

  // Collection name
  static const String _uploadSessionsCollection =
      FirebaseCollections.libraryUploadSessions;

  /// Create a new upload session
  Future<UploadSessionModel> createUploadSession(
    UploadSessionModel session,
  ) async {
    try {
      _logger.i('Creating upload session: ${session.id}');

      final docRef = _firestore
          .collection(_uploadSessionsCollection)
          .doc(session.id);
      await docRef.set(session.toJson());

      _logger.i('Successfully created upload session: ${session.id}');
      return session;
    } catch (e, stackTrace) {
      _logger.e(
        'Error creating upload session: $e',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// Get upload session by ID
  Future<UploadSessionModel?> getUploadSession(String sessionId) async {
    try {
      _logger.i('Fetching upload session: $sessionId');

      final doc = await _firestore
          .collection(_uploadSessionsCollection)
          .doc(sessionId)
          .get();

      if (!doc.exists) {
        _logger.w('Upload session not found: $sessionId');
        return null;
      }

      final session = UploadSessionModel.fromJson({
        'id': doc.id,
        ...doc.data() as Map<String, dynamic>,
      });

      _logger.i('Successfully fetched upload session: $sessionId');
      return session;
    } catch (e, stackTrace) {
      _logger.e(
        'Error fetching upload session: $e',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// Get all upload sessions for a user
  Future<List<UploadSessionModel>> getUploadSessionsForUser(
    String userId,
  ) async {
    try {
      _logger.i('Fetching upload sessions for user: $userId');

      final querySnapshot = await _firestore
          .collection(_uploadSessionsCollection)
          .where('userId', isEqualTo: userId)
          .orderBy('createdAt', descending: true)
          .get();

      final sessions = querySnapshot.docs
          .map(
            (doc) => UploadSessionModel.fromJson({'id': doc.id, ...doc.data()}),
          )
          .toList();

      _logger.i(
        'Successfully fetched ${sessions.length} upload sessions for user $userId',
      );
      return sessions;
    } catch (e, stackTrace) {
      _logger.e(
        'Error fetching upload sessions for user: $e',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// Get active upload sessions for a user
  Future<List<UploadSessionModel>> getActiveUploadSessions(
    String userId,
  ) async {
    try {
      _logger.i('Fetching active upload sessions for user: $userId');

      final querySnapshot = await _firestore
          .collection(_uploadSessionsCollection)
          .where('userId', isEqualTo: userId)
          .where(
            'status',
            whereIn: [
              UploadStatus.pending.value,
              UploadStatus.uploading.value,
              UploadStatus.processing.value,
            ],
          )
          .orderBy('createdAt', descending: true)
          .get();

      final sessions = querySnapshot.docs
          .map(
            (doc) => UploadSessionModel.fromJson({'id': doc.id, ...doc.data()}),
          )
          .toList();

      _logger.i(
        'Successfully fetched ${sessions.length} active upload sessions for user $userId',
      );
      return sessions;
    } catch (e, stackTrace) {
      _logger.e(
        'Error fetching active upload sessions: $e',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// Update upload session progress
  Future<UploadSessionModel> updateUploadProgress(
    String sessionId,
    double progress,
    UploadStatus status, {
    String? errorMessage,
    String? cloudUrl,
  }) async {
    try {
      _logger.i(
        'Updating upload progress for session: $sessionId to $progress',
      );

      final updateData = {
        'progress': progress,
        'status': status.value,
        'updatedAt': DateTime.now().toIso8601String(),
      };

      if (errorMessage != null) {
        updateData['errorMessage'] = errorMessage;
      }

      if (cloudUrl != null) {
        updateData['cloudUrl'] = cloudUrl;
      }

      await _firestore
          .collection(_uploadSessionsCollection)
          .doc(sessionId)
          .update(updateData);

      // Fetch and return updated session
      final updatedSession = await getUploadSession(sessionId);
      if (updatedSession == null) {
        throw Exception('Upload session not found after update: $sessionId');
      }

      _logger.i('Successfully updated upload progress for session: $sessionId');
      return updatedSession;
    } catch (e, stackTrace) {
      _logger.e(
        'Error updating upload progress: $e',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// Mark upload session as completed
  Future<UploadSessionModel> completeUploadSession(
    String sessionId,
    String cloudUrl,
    String fileId,
  ) async {
    try {
      _logger.i('Completing upload session: $sessionId');

      final updateData = {
        'status': UploadStatus.completed.value,
        'progress': 1.0,
        'cloudUrl': cloudUrl,
        'fileId': fileId,
        'completedAt': DateTime.now().toIso8601String(),
        'updatedAt': DateTime.now().toIso8601String(),
      };

      await _firestore
          .collection(_uploadSessionsCollection)
          .doc(sessionId)
          .update(updateData);

      // Fetch and return updated session
      final updatedSession = await getUploadSession(sessionId);
      if (updatedSession == null) {
        throw Exception(
          'Upload session not found after completion: $sessionId',
        );
      }

      _logger.i('Successfully completed upload session: $sessionId');
      return updatedSession;
    } catch (e, stackTrace) {
      _logger.e(
        'Error completing upload session: $e',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// Mark upload session as failed
  Future<UploadSessionModel> failUploadSession(
    String sessionId,
    String errorMessage,
  ) async {
    try {
      _logger.i('Marking upload session as failed: $sessionId');

      final updateData = {
        'status': UploadStatus.failed.value,
        'errorMessage': errorMessage,
        'updatedAt': DateTime.now().toIso8601String(),
      };

      await _firestore
          .collection(_uploadSessionsCollection)
          .doc(sessionId)
          .update(updateData);

      // Fetch and return updated session
      final updatedSession = await getUploadSession(sessionId);
      if (updatedSession == null) {
        throw Exception('Upload session not found after failure: $sessionId');
      }

      _logger.i('Successfully marked upload session as failed: $sessionId');
      return updatedSession;
    } catch (e, stackTrace) {
      _logger.e(
        'Error marking upload session as failed: $e',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// Delete upload session
  Future<void> deleteUploadSession(String sessionId) async {
    try {
      _logger.i('Deleting upload session: $sessionId');

      await _firestore
          .collection(_uploadSessionsCollection)
          .doc(sessionId)
          .delete();

      _logger.i('Successfully deleted upload session: $sessionId');
    } catch (e, stackTrace) {
      _logger.e(
        'Error deleting upload session: $e',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// Clean up old completed/failed upload sessions
  Future<void> cleanupOldSessions({int daysOld = 7}) async {
    try {
      _logger.i('Cleaning up upload sessions older than $daysOld days');

      final cutoffDate = DateTime.now().subtract(Duration(days: daysOld));

      final querySnapshot = await _firestore
          .collection(_uploadSessionsCollection)
          .where(
            'status',
            whereIn: [UploadStatus.completed.value, UploadStatus.failed.value],
          )
          .where('updatedAt', isLessThan: cutoffDate.toIso8601String())
          .get();

      final batch = _firestore.batch();
      for (final doc in querySnapshot.docs) {
        batch.delete(doc.reference);
      }

      await batch.commit();

      _logger.i(
        'Successfully cleaned up ${querySnapshot.docs.length} old upload sessions',
      );
    } catch (e, stackTrace) {
      _logger.e(
        'Error cleaning up old sessions: $e',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// Retry failed upload session
  Future<UploadSessionModel> retryUploadSession(String sessionId) async {
    try {
      _logger.i('Retrying upload session: $sessionId');

      final updateData = {
        'status': UploadStatus.pending.value,
        'progress': 0.0,
        'errorMessage': null,
        'retryCount': FieldValue.increment(1),
        'updatedAt': DateTime.now().toIso8601String(),
      };

      await _firestore
          .collection(_uploadSessionsCollection)
          .doc(sessionId)
          .update(updateData);

      // Fetch and return updated session
      final updatedSession = await getUploadSession(sessionId);
      if (updatedSession == null) {
        throw Exception('Upload session not found after retry: $sessionId');
      }

      _logger.i('Successfully retried upload session: $sessionId');
      return updatedSession;
    } catch (e, stackTrace) {
      _logger.e(
        'Error retrying upload session: $e',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }
}
