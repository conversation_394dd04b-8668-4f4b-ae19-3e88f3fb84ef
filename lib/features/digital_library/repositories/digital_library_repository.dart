import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:logger/logger.dart';

import '../../../core/constants/firebase_collections.dart';
import '../models/library_file_model.dart';
import '../models/library_folder_model.dart';
import '../enums/file_access_type.dart';
import '../enums/file_usage_type.dart';
import '../enums/folder_type.dart';
import '../enums/library_file_type.dart';
import '../enums/upload_status.dart';

/// Repository for managing digital library data with Firebase Firestore
/// Handles files, folders, and related operations
class DigitalLibraryRepository {
  static final DigitalLibraryRepository _instance =
      DigitalLibraryRepository._internal();
  factory DigitalLibraryRepository() => _instance;
  DigitalLibraryRepository._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final Logger _logger = Logger();

  // Collection names
  static const String _filesCollection = FirebaseCollections.libraryFiles;
  static const String _foldersCollection = FirebaseCollections.libraryFolders;

  // ========== FILE OPERATIONS ==========

  /// Get all files for a user with optional filtering
  /// TEMPORARY: Simplified version to avoid index requirements
  Future<List<LibraryFileModel>> getFilesForUser(
    String userId, {
    String? folderId,
    FileAccessType? accessType,
    FileUsageType? usageType,
    LibraryFileType? fileType,
    List<String>? tags,
    String? classId,
    String? subject,
    int? limit,
  }) async {
    try {
      _logger.i(
        'Fetching files for user: $userId with filters - usageType: $usageType, accessType: $accessType',
      );

      final List<LibraryFileModel> allFiles = [];

      // TEMPORARY: Use simple queries and filter client-side to avoid index requirements

      // Query 1: Files uploaded by user (simple query, no compound index needed)
      final uploadedQuery = _firestore
          .collection(_filesCollection)
          .where('uploaderId', isEqualTo: userId);

      final uploadedSnapshot = await uploadedQuery.get();
      allFiles.addAll(
        uploadedSnapshot.docs.map(
          (doc) => LibraryFileModel.fromJson({'id': doc.id, ...doc.data()}),
        ),
      );

      // Query 2: Public downloadable files (simple query)
      final publicDownloadableQuery = _firestore
          .collection(_filesCollection)
          .where(
            'accessType',
            isEqualTo: FileAccessType.publicDownloadable.value,
          );

      final publicDownloadableSnapshot = await publicDownloadableQuery.get();
      allFiles.addAll(
        publicDownloadableSnapshot.docs.map(
          (doc) => LibraryFileModel.fromJson({'id': doc.id, ...doc.data()}),
        ),
      );

      // Query 3: Public view-only files (simple query)
      final publicViewQuery = _firestore
          .collection(_filesCollection)
          .where('accessType', isEqualTo: FileAccessType.publicViewOnly.value);

      final publicViewSnapshot = await publicViewQuery.get();
      allFiles.addAll(
        publicViewSnapshot.docs.map(
          (doc) => LibraryFileModel.fromJson({'id': doc.id, ...doc.data()}),
        ),
      );

      // Query 4: Shared files where user has access
      final sharedQuery = _firestore
          .collection(_filesCollection)
          .where('sharedUserIds', arrayContains: userId);

      final sharedSnapshot = await sharedQuery.get();
      allFiles.addAll(
        sharedSnapshot.docs.map(
          (doc) => LibraryFileModel.fromJson({'id': doc.id, ...doc.data()}),
        ),
      );

      // Remove duplicates and sort by creation date
      final uniqueFiles = <String, LibraryFileModel>{};
      for (final file in allFiles) {
        uniqueFiles[file.id] = file;
      }

      var sortedFiles = uniqueFiles.values.toList()
        ..sort((a, b) => b.createdAt.compareTo(a.createdAt));

      // Apply client-side filtering (since we're using simple queries to avoid indexes)
      if (usageType != null) {
        sortedFiles = sortedFiles
            .where((file) => file.usageType == usageType)
            .toList();
      }

      if (accessType != null) {
        sortedFiles = sortedFiles
            .where((file) => file.accessType == accessType)
            .toList();
      }

      if (fileType != null) {
        sortedFiles = sortedFiles
            .where((file) => file.fileType == fileType)
            .toList();
      }

      if (folderId != null) {
        sortedFiles = sortedFiles
            .where((file) => file.folderId == folderId)
            .toList();
      }

      if (classId != null) {
        sortedFiles = sortedFiles
            .where((file) => file.classId == classId)
            .toList();
      }

      if (subject != null) {
        sortedFiles = sortedFiles
            .where((file) => file.subject == subject)
            .toList();
      }

      // Apply tag filtering (client-side since Firestore doesn't support array intersection)
      if (tags != null && tags.isNotEmpty) {
        sortedFiles = sortedFiles
            .where((file) => tags.any((tag) => file.tags.contains(tag)))
            .toList();
      }

      // Apply limit if specified
      final resultFiles = limit != null && sortedFiles.length > limit
          ? sortedFiles.take(limit).toList()
          : sortedFiles;

      _logger.i(
        'Successfully fetched ${resultFiles.length} files for user $userId',
      );
      return resultFiles;
    } catch (e, stackTrace) {
      _logger.e(
        'Error fetching files for user: $e',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// Get files in a specific folder
  Future<List<LibraryFileModel>> getFilesInFolder(
    String folderId,
    String userId,
  ) async {
    try {
      _logger.i('Fetching files in folder: $folderId for user: $userId');

      return await getFilesForUser(userId, folderId: folderId);
    } catch (e, stackTrace) {
      _logger.e(
        'Error fetching files in folder: $e',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// Get a specific file by ID
  Future<LibraryFileModel?> getFileById(String fileId) async {
    try {
      _logger.i('Fetching file by ID: $fileId');

      final doc = await _firestore
          .collection(_filesCollection)
          .doc(fileId)
          .get();

      if (!doc.exists) {
        _logger.w('File not found: $fileId');
        return null;
      }

      final file = LibraryFileModel.fromJson({
        'id': doc.id,
        ...doc.data() as Map<String, dynamic>,
      });

      _logger.i('Successfully fetched file: ${file.fileName}');
      return file;
    } catch (e, stackTrace) {
      _logger.e(
        'Error fetching file by ID: $e',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// Create a new file
  Future<LibraryFileModel> createFile(LibraryFileModel file) async {
    try {
      _logger.i('Creating new file: ${file.fileName}');

      final docRef = _firestore.collection(_filesCollection).doc(file.id);
      await docRef.set(file.toJson());

      _logger.i('Successfully created file: ${file.fileName}');
      return file;
    } catch (e, stackTrace) {
      _logger.e('Error creating file: $e', error: e, stackTrace: stackTrace);
      rethrow;
    }
  }

  /// Update an existing file
  Future<LibraryFileModel> updateFile(LibraryFileModel file) async {
    try {
      _logger.i('Updating file: ${file.fileName}');

      final updatedFile = file.copyWith(modifiedAt: DateTime.now());

      await _firestore
          .collection(_filesCollection)
          .doc(file.id)
          .update(updatedFile.toJson());

      _logger.i('Successfully updated file: ${file.fileName}');
      return updatedFile;
    } catch (e, stackTrace) {
      _logger.e('Error updating file: $e', error: e, stackTrace: stackTrace);
      rethrow;
    }
  }

  /// Delete a file
  Future<void> deleteFile(String fileId) async {
    try {
      _logger.i('Deleting file: $fileId');

      await _firestore.collection(_filesCollection).doc(fileId).delete();

      _logger.i('Successfully deleted file: $fileId');
    } catch (e, stackTrace) {
      _logger.e('Error deleting file: $e', error: e, stackTrace: stackTrace);
      rethrow;
    }
  }

  /// Update file upload progress
  Future<void> updateFileUploadProgress(
    String fileId,
    double progress,
    UploadStatus status, {
    String? errorMessage,
  }) async {
    try {
      _logger.i('Updating upload progress for file: $fileId to $progress');

      final updateData = {
        'uploadProgress': progress,
        'uploadStatus': status.value,
        'modifiedAt': DateTime.now().toIso8601String(),
      };

      if (errorMessage != null) {
        updateData['errorMessage'] = errorMessage;
      }

      await _firestore
          .collection(_filesCollection)
          .doc(fileId)
          .update(updateData);

      _logger.i('Successfully updated upload progress for file: $fileId');
    } catch (e, stackTrace) {
      _logger.e(
        'Error updating upload progress: $e',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// Update file access count (view/download)
  Future<void> updateFileAccessCount(
    String fileId, {
    bool incrementView = false,
    bool incrementDownload = false,
  }) async {
    try {
      _logger.i('Updating access count for file: $fileId');

      final updateData = <String, dynamic>{
        'lastAccessedAt': DateTime.now().toIso8601String(),
      };

      if (incrementView) {
        updateData['viewCount'] = FieldValue.increment(1);
      }

      if (incrementDownload) {
        updateData['downloadCount'] = FieldValue.increment(1);
      }

      await _firestore
          .collection(_filesCollection)
          .doc(fileId)
          .update(updateData);

      _logger.i('Successfully updated access count for file: $fileId');
    } catch (e, stackTrace) {
      _logger.e(
        'Error updating access count: $e',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// Toggle file favorite status
  Future<void> toggleFileFavorite(String fileId, bool isFavorite) async {
    try {
      _logger.i('Toggling favorite status for file: $fileId to $isFavorite');

      await _firestore.collection(_filesCollection).doc(fileId).update({
        'isFavorite': isFavorite,
        'modifiedAt': DateTime.now().toIso8601String(),
      });

      _logger.i('Successfully toggled favorite status for file: $fileId');
    } catch (e, stackTrace) {
      _logger.e(
        'Error toggling favorite status: $e',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// Search files by query
  Future<List<LibraryFileModel>> searchFiles(
    String userId,
    String query, {
    FileAccessType? accessType,
    LibraryFileType? fileType,
    String? classId,
    String? subject,
    int? limit,
  }) async {
    try {
      _logger.i('Searching files for user: $userId with query: "$query"');

      // Get all accessible files first
      final allFiles = await getFilesForUser(
        userId,
        accessType: accessType,
        fileType: fileType,
        classId: classId,
        subject: subject,
        limit: limit,
      );

      // Filter by search query (client-side)
      final searchQuery = query.toLowerCase();
      final filteredFiles = allFiles.where((file) {
        return file.fileName.toLowerCase().contains(searchQuery) ||
            (file.title?.toLowerCase().contains(searchQuery) ?? false) ||
            (file.description?.toLowerCase().contains(searchQuery) ?? false) ||
            file.tags.any((tag) => tag.toLowerCase().contains(searchQuery));
      }).toList();

      _logger.i('Found ${filteredFiles.length} files matching query: "$query"');
      return filteredFiles;
    } catch (e, stackTrace) {
      _logger.e('Error searching files: $e', error: e, stackTrace: stackTrace);
      rethrow;
    }
  }

  // ========== FOLDER OPERATIONS ==========

  /// Get all folders for a user with optional filtering
  /// TEMPORARY: Simplified version to avoid index requirements
  Future<List<LibraryFolderModel>> getFoldersForUser(
    String userId, {
    String? parentFolderId,
    FolderType? folderType,
    FileAccessType? accessType,
    FileUsageType? usageType,
    String? classId,
    String? subject,
    int? limit,
  }) async {
    try {
      _logger.i(
        'Fetching folders for user: $userId with filters - usageType: $usageType, parentFolderId: $parentFolderId',
      );

      final List<LibraryFolderModel> allFolders = [];

      // TEMPORARY: Use simple queries and filter client-side to avoid index requirements

      // Query 1: Folders created by user (simple query)
      final createdQuery = _firestore
          .collection(_foldersCollection)
          .where('creatorId', isEqualTo: userId);

      final createdSnapshot = await createdQuery.get();
      allFolders.addAll(
        createdSnapshot.docs.map(
          (doc) => LibraryFolderModel.fromJson({'id': doc.id, ...doc.data()}),
        ),
      );

      // Query 2: Public downloadable folders (simple query)
      final publicDownloadableQuery = _firestore
          .collection(_foldersCollection)
          .where(
            'accessType',
            isEqualTo: FileAccessType.publicDownloadable.value,
          );

      final publicDownloadableSnapshot = await publicDownloadableQuery.get();
      allFolders.addAll(
        publicDownloadableSnapshot.docs.map(
          (doc) => LibraryFolderModel.fromJson({'id': doc.id, ...doc.data()}),
        ),
      );

      // Query 3: Public view-only folders (simple query)
      final publicViewQuery = _firestore
          .collection(_foldersCollection)
          .where('accessType', isEqualTo: FileAccessType.publicViewOnly.value);

      final publicViewSnapshot = await publicViewQuery.get();
      allFolders.addAll(
        publicViewSnapshot.docs.map(
          (doc) => LibraryFolderModel.fromJson({'id': doc.id, ...doc.data()}),
        ),
      );

      // Query 4: Shared folders where user has access
      final sharedQuery = _firestore
          .collection(_foldersCollection)
          .where('sharedUserIds', arrayContains: userId);

      final sharedSnapshot = await sharedQuery.get();
      allFolders.addAll(
        sharedSnapshot.docs.map(
          (doc) => LibraryFolderModel.fromJson({'id': doc.id, ...doc.data()}),
        ),
      );

      // Remove duplicates and sort by creation date
      final uniqueFolders = <String, LibraryFolderModel>{};
      for (final folder in allFolders) {
        uniqueFolders[folder.id] = folder;
      }

      var sortedFolders = uniqueFolders.values.toList()
        ..sort((a, b) => b.createdAt.compareTo(a.createdAt));

      // Apply client-side filtering (since we're using simple queries to avoid indexes)
      if (parentFolderId != null) {
        sortedFolders = sortedFolders
            .where((folder) => folder.parentFolderId == parentFolderId)
            .toList();
      } else {
        // If parentFolderId is null, we want root folders (parentFolderId == null)
        sortedFolders = sortedFolders
            .where((folder) => folder.parentFolderId == null)
            .toList();
      }

      if (folderType != null) {
        sortedFolders = sortedFolders
            .where((folder) => folder.folderType == folderType)
            .toList();
      }

      if (accessType != null) {
        sortedFolders = sortedFolders
            .where((folder) => folder.accessType == accessType)
            .toList();
      }

      if (usageType != null) {
        sortedFolders = sortedFolders
            .where((folder) => folder.usageType == usageType)
            .toList();
      }

      if (classId != null) {
        sortedFolders = sortedFolders
            .where((folder) => folder.classId == classId)
            .toList();
      }

      if (subject != null) {
        sortedFolders = sortedFolders
            .where((folder) => folder.subject == subject)
            .toList();
      }

      // Apply limit if specified
      final resultFolders = limit != null && sortedFolders.length > limit
          ? sortedFolders.take(limit).toList()
          : sortedFolders;

      _logger.i(
        'Successfully fetched ${resultFolders.length} folders for user $userId',
      );
      return resultFolders;
    } catch (e, stackTrace) {
      _logger.e(
        'Error fetching folders for user: $e',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  // Helper methods removed - using client-side filtering instead to avoid index requirements

  /// Get subfolders of a specific folder
  Future<List<LibraryFolderModel>> getSubfolders(
    String parentFolderId,
    String userId,
  ) async {
    try {
      _logger.i(
        'Fetching subfolders of folder: $parentFolderId for user: $userId',
      );

      return await getFoldersForUser(userId, parentFolderId: parentFolderId);
    } catch (e, stackTrace) {
      _logger.e(
        'Error fetching subfolders: $e',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// Get a specific folder by ID
  Future<LibraryFolderModel?> getFolderById(String folderId) async {
    try {
      _logger.i('Fetching folder by ID: $folderId');

      final doc = await _firestore
          .collection(_foldersCollection)
          .doc(folderId)
          .get();

      if (!doc.exists) {
        _logger.w('Folder not found: $folderId');
        return null;
      }

      final folder = LibraryFolderModel.fromJson({
        'id': doc.id,
        ...doc.data() as Map<String, dynamic>,
      });

      _logger.i('Successfully fetched folder: ${folder.name}');
      return folder;
    } catch (e, stackTrace) {
      _logger.e(
        'Error fetching folder by ID: $e',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// Create a new folder
  Future<LibraryFolderModel> createFolder(LibraryFolderModel folder) async {
    try {
      _logger.i('Creating new folder: ${folder.name}');

      final docRef = _firestore.collection(_foldersCollection).doc(folder.id);
      await docRef.set(folder.toJson());

      _logger.i('Successfully created folder: ${folder.name}');
      return folder;
    } catch (e, stackTrace) {
      _logger.e('Error creating folder: $e', error: e, stackTrace: stackTrace);
      rethrow;
    }
  }

  /// Update an existing folder
  Future<LibraryFolderModel> updateFolder(LibraryFolderModel folder) async {
    try {
      _logger.i('Updating folder: ${folder.name}');

      final updatedFolder = folder.copyWith(modifiedAt: DateTime.now());

      await _firestore
          .collection(_foldersCollection)
          .doc(folder.id)
          .update(updatedFolder.toJson());

      _logger.i('Successfully updated folder: ${folder.name}');
      return updatedFolder;
    } catch (e, stackTrace) {
      _logger.e('Error updating folder: $e', error: e, stackTrace: stackTrace);
      rethrow;
    }
  }

  /// Delete a folder and optionally its contents
  Future<void> deleteFolder(
    String folderId, {
    bool deleteContents = false,
  }) async {
    try {
      _logger.i('Deleting folder: $folderId (deleteContents: $deleteContents)');

      if (deleteContents) {
        // Delete all files in the folder
        final files = await getFilesInFolder(
          folderId,
          'system',
        ); // Use system user for deletion
        for (final file in files) {
          await deleteFile(file.id);
        }

        // Delete all subfolders recursively
        final subfolders = await getSubfolders(folderId, 'system');
        for (final subfolder in subfolders) {
          await deleteFolder(subfolder.id, deleteContents: true);
        }
      }

      await _firestore.collection(_foldersCollection).doc(folderId).delete();

      _logger.i('Successfully deleted folder: $folderId');
    } catch (e, stackTrace) {
      _logger.e('Error deleting folder: $e', error: e, stackTrace: stackTrace);
      rethrow;
    }
  }

  /// Update folder counts (file and subfolder counts)
  Future<void> updateFolderCounts(String folderId) async {
    try {
      _logger.i('Updating counts for folder: $folderId');

      // Count files in folder
      final filesQuery = await _firestore
          .collection(_filesCollection)
          .where('folderId', isEqualTo: folderId)
          .count()
          .get();

      // Count subfolders
      final subfoldersQuery = await _firestore
          .collection(_foldersCollection)
          .where('parentFolderId', isEqualTo: folderId)
          .count()
          .get();

      await _firestore.collection(_foldersCollection).doc(folderId).update({
        'fileCount': filesQuery.count,
        'subfolderCount': subfoldersQuery.count,
        'modifiedAt': DateTime.now().toIso8601String(),
      });

      _logger.i('Successfully updated counts for folder: $folderId');
    } catch (e, stackTrace) {
      _logger.e(
        'Error updating folder counts: $e',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// Toggle folder favorite status
  Future<void> toggleFolderFavorite(String folderId, bool isFavorite) async {
    try {
      _logger.i(
        'Toggling favorite status for folder: $folderId to $isFavorite',
      );

      await _firestore.collection(_foldersCollection).doc(folderId).update({
        'isFavorite': isFavorite,
        'modifiedAt': DateTime.now().toIso8601String(),
      });

      _logger.i('Successfully toggled favorite status for folder: $folderId');
    } catch (e, stackTrace) {
      _logger.e(
        'Error toggling favorite status: $e',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// Search folders by query
  Future<List<LibraryFolderModel>> searchFolders(
    String userId,
    String query, {
    FolderType? folderType,
    FileAccessType? accessType,
    String? classId,
    String? subject,
    int? limit,
  }) async {
    try {
      _logger.i('Searching folders for user: $userId with query: "$query"');

      // Get all accessible folders first
      final allFolders = await getFoldersForUser(
        userId,
        folderType: folderType,
        accessType: accessType,
        classId: classId,
        subject: subject,
        limit: limit,
      );

      // Filter by search query (client-side)
      final searchQuery = query.toLowerCase();
      final filteredFolders = allFolders.where((folder) {
        return folder.name.toLowerCase().contains(searchQuery) ||
            (folder.description?.toLowerCase().contains(searchQuery) ??
                false) ||
            folder.tags.any((tag) => tag.toLowerCase().contains(searchQuery));
      }).toList();

      _logger.i(
        'Found ${filteredFolders.length} folders matching query: "$query"',
      );
      return filteredFolders;
    } catch (e, stackTrace) {
      _logger.e(
        'Error searching folders: $e',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }
}
