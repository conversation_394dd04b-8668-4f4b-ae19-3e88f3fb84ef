/// Model representing settings and configuration for a chat
class ChatSettings {
  /// Whether the chat allows file attachments
  final bool allowAttachments;

  /// Whether the chat allows voice messages
  final bool allowVoiceMessages;

  /// Whether the chat allows adding new participants
  final bool allowAddingParticipants;

  /// Whether the chat allows participants to leave
  final bool allowParticipantsToLeave;

  /// Whether message reactions are enabled
  final bool enableReactions;

  /// Whether message editing is allowed
  final bool allowMessageEditing;

  /// Whether message deletion is allowed
  final bool allowMessageDeletion;

  /// Whether read receipts are enabled
  final bool enableReadReceipts;

  /// Maximum number of participants allowed (null = unlimited)
  final int? maxParticipants;

  /// Message retention period in days (null = forever)
  final int? messageRetentionDays;

  /// Whether only admins can send messages
  final bool adminOnlyMessages;

  /// Whether the chat is archived
  final bool isArchived;

  /// Whether the chat is pinned for all participants
  final bool isPinned;

  /// Custom theme/color for the chat
  final String? customTheme;

  /// Chat description or purpose
  final String? description;

  /// Additional custom settings
  final Map<String, dynamic>? customSettings;

  const ChatSettings({
    this.allowAttachments = true,
    this.allowVoiceMessages = true,
    this.allowAddingParticipants = true,
    this.allowParticipantsToLeave = true,
    this.enableReactions = true,
    this.allowMessageEditing = true,
    this.allowMessageDeletion = false,
    this.enableReadReceipts = true,
    this.maxParticipants,
    this.messageRetentionDays,
    this.adminOnlyMessages = false,
    this.isArchived = false,
    this.isPinned = false,
    this.customTheme,
    this.description,
    this.customSettings,
  });

  /// Create a ChatSettings from JSON
  factory ChatSettings.fromJson(Map<String, dynamic> json) {
    return ChatSettings(
      allowAttachments: json['allowAttachments'] as bool? ?? true,
      allowVoiceMessages: json['allowVoiceMessages'] as bool? ?? true,
      allowAddingParticipants: json['allowAddingParticipants'] as bool? ?? true,
      allowParticipantsToLeave: json['allowParticipantsToLeave'] as bool? ?? true,
      enableReactions: json['enableReactions'] as bool? ?? true,
      allowMessageEditing: json['allowMessageEditing'] as bool? ?? true,
      allowMessageDeletion: json['allowMessageDeletion'] as bool? ?? false,
      enableReadReceipts: json['enableReadReceipts'] as bool? ?? true,
      maxParticipants: json['maxParticipants'] as int?,
      messageRetentionDays: json['messageRetentionDays'] as int?,
      adminOnlyMessages: json['adminOnlyMessages'] as bool? ?? false,
      isArchived: json['isArchived'] as bool? ?? false,
      isPinned: json['isPinned'] as bool? ?? false,
      customTheme: json['customTheme'] as String?,
      description: json['description'] as String?,
      customSettings: json['customSettings'] as Map<String, dynamic>?,
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'allowAttachments': allowAttachments,
      'allowVoiceMessages': allowVoiceMessages,
      'allowAddingParticipants': allowAddingParticipants,
      'allowParticipantsToLeave': allowParticipantsToLeave,
      'enableReactions': enableReactions,
      'allowMessageEditing': allowMessageEditing,
      'allowMessageDeletion': allowMessageDeletion,
      'enableReadReceipts': enableReadReceipts,
      'maxParticipants': maxParticipants,
      'messageRetentionDays': messageRetentionDays,
      'adminOnlyMessages': adminOnlyMessages,
      'isArchived': isArchived,
      'isPinned': isPinned,
      'customTheme': customTheme,
      'description': description,
      'customSettings': customSettings,
    };
  }

  /// Create a copy of this ChatSettings with updated fields
  ChatSettings copyWith({
    bool? allowAttachments,
    bool? allowVoiceMessages,
    bool? allowAddingParticipants,
    bool? allowParticipantsToLeave,
    bool? enableReactions,
    bool? allowMessageEditing,
    bool? allowMessageDeletion,
    bool? enableReadReceipts,
    int? maxParticipants,
    int? messageRetentionDays,
    bool? adminOnlyMessages,
    bool? isArchived,
    bool? isPinned,
    String? customTheme,
    String? description,
    Map<String, dynamic>? customSettings,
  }) {
    return ChatSettings(
      allowAttachments: allowAttachments ?? this.allowAttachments,
      allowVoiceMessages: allowVoiceMessages ?? this.allowVoiceMessages,
      allowAddingParticipants: allowAddingParticipants ?? this.allowAddingParticipants,
      allowParticipantsToLeave: allowParticipantsToLeave ?? this.allowParticipantsToLeave,
      enableReactions: enableReactions ?? this.enableReactions,
      allowMessageEditing: allowMessageEditing ?? this.allowMessageEditing,
      allowMessageDeletion: allowMessageDeletion ?? this.allowMessageDeletion,
      enableReadReceipts: enableReadReceipts ?? this.enableReadReceipts,
      maxParticipants: maxParticipants ?? this.maxParticipants,
      messageRetentionDays: messageRetentionDays ?? this.messageRetentionDays,
      adminOnlyMessages: adminOnlyMessages ?? this.adminOnlyMessages,
      isArchived: isArchived ?? this.isArchived,
      isPinned: isPinned ?? this.isPinned,
      customTheme: customTheme ?? this.customTheme,
      description: description ?? this.description,
      customSettings: customSettings ?? this.customSettings,
    );
  }

  /// Get default settings for a specific chat type
  static ChatSettings getDefaultForChatType(String chatType) {
    switch (chatType.toLowerCase()) {
      case 'one_to_one':
        return const ChatSettings(
          maxParticipants: 2,
          allowAddingParticipants: false,
        );
      case 'classroom_discussion':
        return const ChatSettings(
          allowAddingParticipants: false,
          adminOnlyMessages: false,
          allowMessageDeletion: true,
        );
      case 'study_group':
        return const ChatSettings(
          maxParticipants: 10,
          allowAddingParticipants: true,
        );
      case 'project_team':
        return const ChatSettings(
          maxParticipants: 8,
          allowAddingParticipants: true,
          allowMessageDeletion: true,
        );
      case 'parent_group':
        return const ChatSettings(
          allowVoiceMessages: false,
          adminOnlyMessages: false,
        );
      case 'teacher_circle':
        return const ChatSettings(
          allowAddingParticipants: true,
          allowMessageDeletion: true,
        );
      default:
        return const ChatSettings();
    }
  }

  /// Check if a specific feature is enabled
  bool isFeatureEnabled(String feature) {
    switch (feature.toLowerCase()) {
      case 'attachments':
        return allowAttachments;
      case 'voice_messages':
        return allowVoiceMessages;
      case 'adding_participants':
        return allowAddingParticipants;
      case 'leaving':
        return allowParticipantsToLeave;
      case 'reactions':
        return enableReactions;
      case 'editing':
        return allowMessageEditing;
      case 'deletion':
        return allowMessageDeletion;
      case 'read_receipts':
        return enableReadReceipts;
      default:
        return false;
    }
  }

  /// Check if the chat has reached its participant limit
  bool hasReachedParticipantLimit(int currentParticipants) {
    if (maxParticipants == null) return false;
    return currentParticipants >= maxParticipants!;
  }

  /// Check if messages should be deleted based on retention policy
  bool shouldDeleteMessage(DateTime messageDate) {
    if (messageRetentionDays == null) return false;
    final retentionPeriod = Duration(days: messageRetentionDays!);
    final cutoffDate = DateTime.now().subtract(retentionPeriod);
    return messageDate.isBefore(cutoffDate);
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ChatSettings &&
        other.allowAttachments == allowAttachments &&
        other.allowVoiceMessages == allowVoiceMessages &&
        other.allowAddingParticipants == allowAddingParticipants &&
        other.allowParticipantsToLeave == allowParticipantsToLeave &&
        other.enableReactions == enableReactions &&
        other.allowMessageEditing == allowMessageEditing &&
        other.allowMessageDeletion == allowMessageDeletion &&
        other.enableReadReceipts == enableReadReceipts &&
        other.maxParticipants == maxParticipants &&
        other.messageRetentionDays == messageRetentionDays &&
        other.adminOnlyMessages == adminOnlyMessages &&
        other.isArchived == isArchived &&
        other.isPinned == isPinned &&
        other.customTheme == customTheme &&
        other.description == description;
  }

  @override
  int get hashCode {
    return Object.hash(
      allowAttachments,
      allowVoiceMessages,
      allowAddingParticipants,
      allowParticipantsToLeave,
      enableReactions,
      allowMessageEditing,
      allowMessageDeletion,
      enableReadReceipts,
      maxParticipants,
      messageRetentionDays,
      adminOnlyMessages,
      isArchived,
      isPinned,
      customTheme,
      description,
    );
  }

  @override
  String toString() {
    return 'ChatSettings(allowAttachments: $allowAttachments, maxParticipants: $maxParticipants, isArchived: $isArchived)';
  }
}
