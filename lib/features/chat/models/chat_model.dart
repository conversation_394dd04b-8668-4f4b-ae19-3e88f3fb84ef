import '../enums/chat_type.dart';
import '../enums/chat_role.dart';
import 'chat_participant.dart';
import 'chat_settings.dart';
import 'message_model.dart';

/// Model representing a chat conversation
class ChatModel {
  /// Unique identifier for the chat
  final String id;

  /// Type of chat (one-to-one, group, classroom discussion, etc.)
  final ChatType type;

  /// Title/name of the chat
  final String title;

  /// List of participant user IDs
  final List<String> participantIds;

  /// Map of participants with their details and roles
  final Map<String, ChatParticipant> participants;

  /// ID of the classroom this chat belongs to (if applicable)
  final String? classroomId;

  /// ID of the last message in this chat
  final String? lastMessageId;

  /// The last message object (for display purposes)
  final MessageModel? lastMessage;

  /// When the last message was sent
  final DateTime? lastMessageAt;

  /// Chat settings and configuration
  final ChatSettings settings;

  /// When the chat was created
  final DateTime createdAt;

  /// When the chat was last updated
  final DateTime updatedAt;

  /// Whether the chat is currently active
  final bool isActive;

  /// Chat avatar/image URL
  final String? avatarUrl;

  /// Number of unread messages for the current user
  final int unreadCount;

  /// Additional metadata for the chat
  final Map<String, dynamic>? metadata;

  const ChatModel({
    required this.id,
    required this.type,
    required this.title,
    required this.participantIds,
    required this.participants,
    this.classroomId,
    this.lastMessageId,
    this.lastMessage,
    this.lastMessageAt,
    required this.settings,
    required this.createdAt,
    required this.updatedAt,
    this.isActive = true,
    this.avatarUrl,
    this.unreadCount = 0,
    this.metadata,
  });

  /// Create a ChatModel from JSON
  factory ChatModel.fromJson(Map<String, dynamic> json) {
    // Parse participants
    final participantsMap = json['participants'] as Map<String, dynamic>? ?? {};
    final participants = <String, ChatParticipant>{};
    participantsMap.forEach((userId, participantData) {
      participants[userId] = ChatParticipant.fromJson(
        participantData as Map<String, dynamic>,
      );
    });

    // Parse participant IDs
    final participantIds =
        (json['participantIds'] as List<dynamic>?)
            ?.map((id) => id as String)
            .toList() ??
        [];

    // Parse last message if present
    MessageModel? lastMessage;
    if (json['lastMessage'] != null) {
      lastMessage = MessageModel.fromJson(
        json['lastMessage'] as Map<String, dynamic>,
      );
    }

    // Parse settings
    final settings = json['settings'] != null
        ? ChatSettings.fromJson(json['settings'] as Map<String, dynamic>)
        : ChatSettings.getDefaultForChatType(
            json['type'] as String? ?? 'one_to_one',
          );

    return ChatModel(
      id: json['id'] as String,
      type: ChatTypeExtension.fromString(
        json['type'] as String? ?? 'one_to_one',
      ),
      title: json['title'] as String,
      participantIds: participantIds,
      participants: participants,
      classroomId: json['classroomId'] as String?,
      lastMessageId: json['lastMessageId'] as String?,
      lastMessage: lastMessage,
      lastMessageAt: json['lastMessageAt'] != null
          ? DateTime.parse(json['lastMessageAt'] as String)
          : null,
      settings: settings,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      isActive: json['isActive'] as bool? ?? true,
      avatarUrl: json['avatarUrl'] as String?,
      unreadCount: json['unreadCount'] as int? ?? 0,
      metadata: json['metadata'] as Map<String, dynamic>?,
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    // Convert participants to JSON
    final participantsJson = <String, dynamic>{};
    participants.forEach((userId, participant) {
      participantsJson[userId] = participant.toJson();
    });

    return {
      'id': id,
      'type': type.value,
      'title': title,
      'participantIds': participantIds,
      'participants': participantsJson,
      'classroomId': classroomId,
      'lastMessageId': lastMessageId,
      'lastMessage': lastMessage?.toJson(),
      'lastMessageAt': lastMessageAt?.toIso8601String(),
      'settings': settings.toJson(),
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'isActive': isActive,
      'avatarUrl': avatarUrl,
      'unreadCount': unreadCount,
      'metadata': metadata,
    };
  }

  /// Create a copy of this ChatModel with updated fields
  ChatModel copyWith({
    String? id,
    ChatType? type,
    String? title,
    List<String>? participantIds,
    Map<String, ChatParticipant>? participants,
    String? classroomId,
    String? lastMessageId,
    MessageModel? lastMessage,
    DateTime? lastMessageAt,
    ChatSettings? settings,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isActive,
    String? avatarUrl,
    int? unreadCount,
    Map<String, dynamic>? metadata,
  }) {
    return ChatModel(
      id: id ?? this.id,
      type: type ?? this.type,
      title: title ?? this.title,
      participantIds: participantIds ?? this.participantIds,
      participants: participants ?? this.participants,
      classroomId: classroomId ?? this.classroomId,
      lastMessageId: lastMessageId ?? this.lastMessageId,
      lastMessage: lastMessage ?? this.lastMessage,
      lastMessageAt: lastMessageAt ?? this.lastMessageAt,
      settings: settings ?? this.settings,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isActive: isActive ?? this.isActive,
      avatarUrl: avatarUrl ?? this.avatarUrl,
      unreadCount: unreadCount ?? this.unreadCount,
      metadata: metadata ?? this.metadata,
    );
  }

  /// Get the number of participants in this chat
  int get participantCount => participantIds.length;

  /// Check if this is a group chat (more than 2 participants)
  bool get isGroupChat => type.isGroupType;

  /// Check if this chat requires classroom context
  bool get requiresClassroom => type.requiresClassroom;

  /// Get the other participant in a one-to-one chat
  ChatParticipant? getOtherParticipant(String currentUserId) {
    if (type != ChatType.oneToOne) return null;

    final otherUserId = participantIds.firstWhere(
      (id) => id != currentUserId,
      orElse: () => '',
    );

    return otherUserId.isNotEmpty ? participants[otherUserId] : null;
  }

  /// Get a participant by user ID
  ChatParticipant? getParticipant(String userId) {
    return participants[userId];
  }

  /// Check if a user is a participant in this chat
  bool hasParticipant(String userId) {
    return participantIds.contains(userId);
  }

  /// Check if the chat has reached its participant limit
  bool get hasReachedParticipantLimit {
    return settings.hasReachedParticipantLimit(participantCount);
  }

  /// Get list of admin participants
  List<ChatParticipant> get adminParticipants {
    return participants.values
        .where((participant) => participant.role.hierarchyLevel >= 3)
        .toList();
  }

  /// Get list of moderator and admin participants
  List<ChatParticipant> get moderatorAndAdminParticipants {
    return participants.values
        .where((participant) => participant.role.hierarchyLevel >= 2)
        .toList();
  }

  /// Check if a user can perform a specific action in this chat
  bool canUserPerformAction(String userId, String action) {
    final participant = getParticipant(userId);
    if (participant == null) return false;

    return participant.canPerformAction(action);
  }

  /// Get display title for the chat
  String getDisplayTitle(String currentUserId) {
    if (type == ChatType.oneToOne) {
      final otherParticipant = getOtherParticipant(currentUserId);
      return otherParticipant?.userName ?? 'Unknown User';
    }

    return title;
  }

  /// Get display subtitle (last message preview or participant count)
  String? getDisplaySubtitle() {
    if (lastMessage != null) {
      final content = lastMessage!.displayContent;
      final senderName = lastMessage!.senderName;

      if (type == ChatType.oneToOne) {
        return content;
      } else {
        return '$senderName: $content';
      }
    }

    if (isGroupChat) {
      return '$participantCount participants';
    }

    return null;
  }

  /// Get formatted last message time
  String? get formattedLastMessageTime {
    if (lastMessageAt == null) return null;

    final now = DateTime.now();
    final difference = now.difference(lastMessageAt!);

    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes}m';
    } else if (difference.inDays < 1) {
      return '${difference.inHours}h';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}d';
    } else {
      return '${lastMessageAt!.day}/${lastMessageAt!.month}';
    }
  }

  /// Check if the chat has unread messages
  bool get hasUnreadMessages => unreadCount > 0;

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ChatModel &&
        other.id == id &&
        other.type == type &&
        other.title == title &&
        other.classroomId == classroomId &&
        other.lastMessageId == lastMessageId &&
        other.lastMessageAt == lastMessageAt &&
        other.createdAt == createdAt &&
        other.isActive == isActive;
  }

  @override
  int get hashCode {
    return Object.hash(
      id,
      type,
      title,
      classroomId,
      lastMessageId,
      lastMessageAt,
      createdAt,
      isActive,
    );
  }

  @override
  String toString() {
    return 'ChatModel(id: $id, type: ${type.displayName}, title: $title, participants: $participantCount)';
  }
}
