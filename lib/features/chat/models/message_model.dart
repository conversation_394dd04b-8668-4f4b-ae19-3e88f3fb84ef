import '../enums/message_type.dart';
import '../enums/message_status.dart';
import 'message_attachment.dart';
import 'message_reaction.dart';

/// Model representing a message in a chat
class MessageModel {
  /// Unique identifier for the message
  final String id;

  /// ID of the chat this message belongs to
  final String chatId;

  /// ID of the user who sent this message
  final String senderId;

  /// Name of the sender (for display purposes)
  final String senderName;

  /// Profile image URL of the sender
  final String? senderProfileImageUrl;

  /// Type of message (text, image, file, etc.)
  final MessageType type;

  /// Content of the message (text content or description for attachments)
  final String content;

  /// List of file attachments (if any)
  final List<MessageAttachment> attachments;

  /// ID of the message this is replying to (if any)
  final String? replyToMessageId;

  /// The original message being replied to (for display)
  final MessageModel? replyToMessage;

  /// Map of reactions to this message (emoji -> list of reactions)
  final Map<String, List<MessageReaction>> reactions;

  /// Delivery and read status of the message
  final MessageStatus status;

  /// When the message was sent
  final DateTime sentAt;

  /// When the message was last edited (if applicable)
  final DateTime? editedAt;

  /// Whether the message has been deleted
  final bool isDeleted;

  /// Whether the message is pinned in the chat
  final bool isPinned;

  /// Additional metadata for the message
  final Map<String, dynamic>? metadata;

  const MessageModel({
    required this.id,
    required this.chatId,
    required this.senderId,
    required this.senderName,
    this.senderProfileImageUrl,
    required this.type,
    required this.content,
    this.attachments = const [],
    this.replyToMessageId,
    this.replyToMessage,
    this.reactions = const {},
    required this.status,
    required this.sentAt,
    this.editedAt,
    this.isDeleted = false,
    this.isPinned = false,
    this.metadata,
  });

  /// Create a MessageModel from JSON
  factory MessageModel.fromJson(Map<String, dynamic> json) {
    // Parse attachments
    final attachmentsList = json['attachments'] as List<dynamic>? ?? [];
    final attachments = attachmentsList
        .map(
          (attachment) =>
              MessageAttachment.fromJson(attachment as Map<String, dynamic>),
        )
        .toList();

    // Parse reactions
    final reactionsMap = json['reactions'] as Map<String, dynamic>? ?? {};
    final reactions = <String, List<MessageReaction>>{};
    reactionsMap.forEach((emoji, reactionsList) {
      final reactionList = (reactionsList as List<dynamic>)
          .map(
            (reaction) =>
                MessageReaction.fromJson(reaction as Map<String, dynamic>),
          )
          .toList();
      reactions[emoji] = reactionList;
    });

    // Parse reply message if present
    MessageModel? replyToMessage;
    if (json['replyToMessage'] != null) {
      replyToMessage = MessageModel.fromJson(
        json['replyToMessage'] as Map<String, dynamic>,
      );
    }

    return MessageModel(
      id: json['id'] as String,
      chatId: json['chatId'] as String,
      senderId: json['senderId'] as String,
      senderName: json['senderName'] as String,
      senderProfileImageUrl: json['senderProfileImageUrl'] as String?,
      type: MessageTypeExtension.fromString(json['type'] as String? ?? 'text'),
      content: json['content'] as String,
      attachments: attachments,
      replyToMessageId: json['replyToMessageId'] as String?,
      replyToMessage: replyToMessage,
      reactions: reactions,
      status: MessageStatusExtension.fromString(
        json['status'] as String? ?? 'sent',
      ),
      sentAt: DateTime.parse(json['sentAt'] as String),
      editedAt: json['editedAt'] != null
          ? DateTime.parse(json['editedAt'] as String)
          : null,
      isDeleted: json['isDeleted'] as bool? ?? false,
      isPinned: json['isPinned'] as bool? ?? false,
      metadata: json['metadata'] as Map<String, dynamic>?,
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    // Convert attachments to JSON
    final attachmentsJson = attachments
        .map((attachment) => attachment.toJson())
        .toList();

    // Convert reactions to JSON
    final reactionsJson = <String, dynamic>{};
    reactions.forEach((emoji, reactionList) {
      reactionsJson[emoji] = reactionList
          .map((reaction) => reaction.toJson())
          .toList();
    });

    return {
      'id': id,
      'chatId': chatId,
      'senderId': senderId,
      'senderName': senderName,
      'senderProfileImageUrl': senderProfileImageUrl,
      'type': type.value,
      'content': content,
      'attachments': attachmentsJson,
      'replyToMessageId': replyToMessageId,
      'replyToMessage': replyToMessage?.toJson(),
      'reactions': reactionsJson,
      'status': status.value,
      'sentAt': sentAt.toIso8601String(),
      'editedAt': editedAt?.toIso8601String(),
      'isDeleted': isDeleted,
      'isPinned': isPinned,
      'metadata': metadata,
    };
  }

  /// Create a copy of this MessageModel with updated fields
  MessageModel copyWith({
    String? id,
    String? chatId,
    String? senderId,
    String? senderName,
    String? senderProfileImageUrl,
    MessageType? type,
    String? content,
    List<MessageAttachment>? attachments,
    String? replyToMessageId,
    MessageModel? replyToMessage,
    Map<String, List<MessageReaction>>? reactions,
    MessageStatus? status,
    DateTime? sentAt,
    DateTime? editedAt,
    bool? isDeleted,
    bool? isPinned,
    Map<String, dynamic>? metadata,
  }) {
    return MessageModel(
      id: id ?? this.id,
      chatId: chatId ?? this.chatId,
      senderId: senderId ?? this.senderId,
      senderName: senderName ?? this.senderName,
      senderProfileImageUrl:
          senderProfileImageUrl ?? this.senderProfileImageUrl,
      type: type ?? this.type,
      content: content ?? this.content,
      attachments: attachments ?? this.attachments,
      replyToMessageId: replyToMessageId ?? this.replyToMessageId,
      replyToMessage: replyToMessage ?? this.replyToMessage,
      reactions: reactions ?? this.reactions,
      status: status ?? this.status,
      sentAt: sentAt ?? this.sentAt,
      editedAt: editedAt ?? this.editedAt,
      isDeleted: isDeleted ?? this.isDeleted,
      isPinned: isPinned ?? this.isPinned,
      metadata: metadata ?? this.metadata,
    );
  }

  /// Get total count of reactions
  int get totalReactionCount {
    return reactions.values.fold(
      0,
      (sum, reactionList) => sum + reactionList.length,
    );
  }

  /// Get list of all reaction groups for display
  List<ReactionGroup> get reactionGroups {
    return reactions.entries
        .map((entry) => ReactionGroup(emoji: entry.key, reactions: entry.value))
        .toList();
  }

  /// Check if a specific user has reacted to this message
  bool hasUserReacted(String userId) {
    return reactions.values.any(
      (reactionList) =>
          reactionList.any((reaction) => reaction.userId == userId),
    );
  }

  /// Get the reaction by a specific user (if any)
  MessageReaction? getUserReaction(String userId) {
    for (final reactionList in reactions.values) {
      for (final reaction in reactionList) {
        if (reaction.userId == userId) {
          return reaction;
        }
      }
    }
    return null;
  }

  /// Check if the message has any attachments
  bool get hasAttachments => attachments.isNotEmpty;

  /// Check if the message is a reply to another message
  bool get isReply => replyToMessageId != null;

  /// Check if the message has been edited
  bool get isEdited => editedAt != null;

  /// Get formatted time since the message was sent
  String get formattedSentTime {
    final now = DateTime.now();
    final difference = now.difference(sentAt);

    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inDays < 1) {
      return '${difference.inHours}h ago';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}d ago';
    } else {
      return '${sentAt.day}/${sentAt.month}/${sentAt.year}';
    }
  }

  /// Get display content (handles deleted messages, system messages, etc.)
  String get displayContent {
    if (isDeleted) {
      return 'This message was deleted';
    }

    if (type == MessageType.system) {
      return content;
    }

    if (hasAttachments && content.isEmpty) {
      if (attachments.length == 1) {
        return attachments.first.fileName;
      } else {
        return '${attachments.length} files';
      }
    }

    return content;
  }

  /// Check if this message can be edited by a specific user
  bool canBeEditedBy(String userId) {
    return senderId == userId &&
        type.canEdit &&
        !isDeleted &&
        DateTime.now().difference(sentAt).inMinutes <
            15; // 15 minute edit window
  }

  /// Check if this message can be deleted by a specific user
  bool canBeDeletedBy(String userId) {
    return senderId == userId && !isDeleted;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is MessageModel &&
        other.id == id &&
        other.chatId == chatId &&
        other.senderId == senderId &&
        other.senderName == senderName &&
        other.type == type &&
        other.content == content &&
        other.status == status &&
        other.sentAt == sentAt &&
        other.isDeleted == isDeleted &&
        other.isPinned == isPinned;
  }

  @override
  int get hashCode {
    return Object.hash(
      id,
      chatId,
      senderId,
      senderName,
      type,
      content,
      status,
      sentAt,
      isDeleted,
      isPinned,
    );
  }

  @override
  String toString() {
    return 'MessageModel(id: $id, senderId: $senderId, type: ${type.displayName}, content: ${content.length > 50 ? '${content.substring(0, 50)}...' : content})';
  }
}
