import '../enums/chat_role.dart';

/// Model representing a participant in a chat
class ChatParticipant {
  /// ID of the user who is participating
  final String userId;

  /// Display name of the participant
  final String userName;

  /// Profile image URL of the participant
  final String? profileImageUrl;

  /// Role of the participant in this chat
  final ChatRole role;

  /// When the participant joined the chat
  final DateTime joinedAt;

  /// When the participant last read messages (for read receipts)
  final DateTime? lastReadAt;

  /// Whether the participant has muted this chat
  final bool isMuted;

  /// Whether the participant can send messages in this chat
  final bool canSendMessages;

  /// Whether the participant can add other participants
  final bool canAddParticipants;

  /// Whether the participant is currently active in the chat
  final bool isActive;

  /// Additional metadata for the participant
  final Map<String, dynamic>? metadata;

  const ChatParticipant({
    required this.userId,
    required this.userName,
    this.profileImageUrl,
    required this.role,
    required this.joinedAt,
    this.lastReadAt,
    this.isMuted = false,
    this.canSendMessages = true,
    this.canAddParticipants = false,
    this.isActive = true,
    this.metadata,
  });

  /// Create a ChatParticipant from JSON
  factory ChatParticipant.fromJson(Map<String, dynamic> json) {
    return ChatParticipant(
      userId: json['userId'] as String,
      userName: json['userName'] as String,
      profileImageUrl: json['profileImageUrl'] as String?,
      role: ChatRoleExtension.fromString(json['role'] as String? ?? 'participant'),
      joinedAt: DateTime.parse(json['joinedAt'] as String),
      lastReadAt: json['lastReadAt'] != null 
          ? DateTime.parse(json['lastReadAt'] as String)
          : null,
      isMuted: json['isMuted'] as bool? ?? false,
      canSendMessages: json['canSendMessages'] as bool? ?? true,
      canAddParticipants: json['canAddParticipants'] as bool? ?? false,
      isActive: json['isActive'] as bool? ?? true,
      metadata: json['metadata'] as Map<String, dynamic>?,
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'userName': userName,
      'profileImageUrl': profileImageUrl,
      'role': role.value,
      'joinedAt': joinedAt.toIso8601String(),
      'lastReadAt': lastReadAt?.toIso8601String(),
      'isMuted': isMuted,
      'canSendMessages': canSendMessages,
      'canAddParticipants': canAddParticipants,
      'isActive': isActive,
      'metadata': metadata,
    };
  }

  /// Create a copy of this ChatParticipant with updated fields
  ChatParticipant copyWith({
    String? userId,
    String? userName,
    String? profileImageUrl,
    ChatRole? role,
    DateTime? joinedAt,
    DateTime? lastReadAt,
    bool? isMuted,
    bool? canSendMessages,
    bool? canAddParticipants,
    bool? isActive,
    Map<String, dynamic>? metadata,
  }) {
    return ChatParticipant(
      userId: userId ?? this.userId,
      userName: userName ?? this.userName,
      profileImageUrl: profileImageUrl ?? this.profileImageUrl,
      role: role ?? this.role,
      joinedAt: joinedAt ?? this.joinedAt,
      lastReadAt: lastReadAt ?? this.lastReadAt,
      isMuted: isMuted ?? this.isMuted,
      canSendMessages: canSendMessages ?? this.canSendMessages,
      canAddParticipants: canAddParticipants ?? this.canAddParticipants,
      isActive: isActive ?? this.isActive,
      metadata: metadata ?? this.metadata,
    );
  }

  /// Check if this participant can perform a specific action based on their role
  bool canPerformAction(String action) {
    switch (action.toLowerCase()) {
      case 'send_messages':
        return canSendMessages && role.canSendMessages;
      case 'delete_messages':
        return role.canDeleteMessages;
      case 'add_participants':
        return canAddParticipants && role.canAddParticipants;
      case 'remove_participants':
        return role.canRemoveParticipants;
      case 'change_settings':
        return role.canChangeSettings;
      case 'assign_roles':
        return role.canAssignRoles;
      case 'pin_messages':
        return role.canPinMessages;
      case 'mute_participants':
        return role.canMuteParticipants;
      default:
        return false;
    }
  }

  /// Check if this participant has higher permissions than another participant
  bool hasHigherPermissionsThan(ChatParticipant other) {
    return role.hasHigherPermissionsThan(other.role);
  }

  /// Get display name with role indicator if needed
  String get displayNameWithRole {
    if (role == ChatRole.participant) {
      return userName;
    }
    return '$userName (${role.displayName})';
  }

  /// Check if the participant has read all messages up to a certain time
  bool hasReadMessagesUpTo(DateTime messageTime) {
    if (lastReadAt == null) return false;
    return lastReadAt!.isAfter(messageTime) || lastReadAt!.isAtSameMomentAs(messageTime);
  }

  /// Get the time since the participant last read messages
  Duration? get timeSinceLastRead {
    if (lastReadAt == null) return null;
    return DateTime.now().difference(lastReadAt!);
  }

  /// Check if the participant is considered recently active (read within last hour)
  bool get isRecentlyActive {
    final timeSince = timeSinceLastRead;
    if (timeSince == null) return false;
    return timeSince.inHours < 1;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ChatParticipant &&
        other.userId == userId &&
        other.userName == userName &&
        other.profileImageUrl == profileImageUrl &&
        other.role == role &&
        other.joinedAt == joinedAt &&
        other.lastReadAt == lastReadAt &&
        other.isMuted == isMuted &&
        other.canSendMessages == canSendMessages &&
        other.canAddParticipants == canAddParticipants &&
        other.isActive == isActive;
  }

  @override
  int get hashCode {
    return Object.hash(
      userId,
      userName,
      profileImageUrl,
      role,
      joinedAt,
      lastReadAt,
      isMuted,
      canSendMessages,
      canAddParticipants,
      isActive,
    );
  }

  @override
  String toString() {
    return 'ChatParticipant(userId: $userId, userName: $userName, role: ${role.displayName})';
  }
}
