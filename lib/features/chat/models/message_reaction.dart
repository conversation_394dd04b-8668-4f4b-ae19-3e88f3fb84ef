/// Model representing an emoji reaction to a chat message
class MessageReaction {
  /// Unique identifier for the reaction
  final String id;

  /// ID of the message this reaction belongs to
  final String messageId;

  /// ID of the user who added this reaction
  final String userId;

  /// Name of the user who added this reaction (for display)
  final String userName;

  /// Emoji unicode or name for the reaction
  final String emoji;

  /// When the reaction was added
  final DateTime createdAt;

  const MessageReaction({
    required this.id,
    required this.messageId,
    required this.userId,
    required this.userName,
    required this.emoji,
    required this.createdAt,
  });

  /// Create a MessageReaction from JSON
  factory MessageReaction.fromJson(Map<String, dynamic> json) {
    return MessageReaction(
      id: json['id'] as String,
      messageId: json['messageId'] as String,
      userId: json['userId'] as String,
      userName: json['userName'] as String,
      emoji: json['emoji'] as String,
      createdAt: DateTime.parse(json['createdAt'] as String),
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'messageId': messageId,
      'userId': userId,
      'userName': userName,
      'emoji': emoji,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  /// Create a copy of this MessageReaction with updated fields
  MessageReaction copyWith({
    String? id,
    String? messageId,
    String? userId,
    String? userName,
    String? emoji,
    DateTime? createdAt,
  }) {
    return MessageReaction(
      id: id ?? this.id,
      messageId: messageId ?? this.messageId,
      userId: userId ?? this.userId,
      userName: userName ?? this.userName,
      emoji: emoji ?? this.emoji,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is MessageReaction &&
        other.id == id &&
        other.messageId == messageId &&
        other.userId == userId &&
        other.userName == userName &&
        other.emoji == emoji &&
        other.createdAt == createdAt;
  }

  @override
  int get hashCode {
    return Object.hash(
      id,
      messageId,
      userId,
      userName,
      emoji,
      createdAt,
    );
  }

  @override
  String toString() {
    return 'MessageReaction(id: $id, userId: $userId, emoji: $emoji, messageId: $messageId)';
  }
}

/// Helper class to group reactions by emoji
class ReactionGroup {
  /// The emoji for this group
  final String emoji;

  /// List of reactions with this emoji
  final List<MessageReaction> reactions;

  /// Count of reactions in this group
  int get count => reactions.length;

  /// List of user names who reacted with this emoji
  List<String> get userNames => reactions.map((r) => r.userName).toList();

  /// List of user IDs who reacted with this emoji
  List<String> get userIds => reactions.map((r) => r.userId).toList();

  const ReactionGroup({
    required this.emoji,
    required this.reactions,
  });

  /// Check if a specific user has reacted with this emoji
  bool hasUserReacted(String userId) {
    return reactions.any((reaction) => reaction.userId == userId);
  }

  /// Get the reaction by a specific user (if exists)
  MessageReaction? getReactionByUser(String userId) {
    try {
      return reactions.firstWhere((reaction) => reaction.userId == userId);
    } catch (e) {
      return null;
    }
  }

  /// Create a formatted string of user names for display
  String getFormattedUserNames({int maxNames = 3}) {
    if (reactions.isEmpty) return '';
    
    if (reactions.length <= maxNames) {
      return userNames.join(', ');
    } else {
      final displayNames = userNames.take(maxNames).toList();
      final remaining = reactions.length - maxNames;
      return '${displayNames.join(', ')} and $remaining more';
    }
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ReactionGroup &&
        other.emoji == emoji &&
        _listEquals(other.reactions, reactions);
  }

  @override
  int get hashCode {
    return Object.hash(emoji, Object.hashAll(reactions));
  }

  bool _listEquals<T>(List<T>? a, List<T>? b) {
    if (a == null) return b == null;
    if (b == null || a.length != b.length) return false;
    for (int index = 0; index < a.length; index += 1) {
      if (a[index] != b[index]) return false;
    }
    return true;
  }

  @override
  String toString() {
    return 'ReactionGroup(emoji: $emoji, count: $count)';
  }
}
