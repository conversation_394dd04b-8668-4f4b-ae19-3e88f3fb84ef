/// Model representing a file attachment in a chat message
class MessageAttachment {
  /// Unique identifier for the attachment
  final String id;

  /// Original filename of the attachment
  final String fileName;

  /// File size in bytes
  final int fileSize;

  /// MIME type of the file
  final String mimeType;

  /// URL to access the file (Firebase Storage URL)
  final String fileUrl;

  /// Thumbnail URL for images/videos (optional)
  final String? thumbnailUrl;

  /// Width of image/video (for display purposes)
  final int? width;

  /// Height of image/video (for display purposes)
  final int? height;

  /// Duration in seconds (for audio/video files)
  final int? duration;

  /// When the attachment was uploaded
  final DateTime uploadedAt;

  /// Whether the file is from Digital Library
  final bool isFromLibrary;

  /// Digital Library file ID if applicable
  final String? libraryFileId;

  /// Additional metadata for the attachment
  final Map<String, dynamic>? metadata;

  const MessageAttachment({
    required this.id,
    required this.fileName,
    required this.fileSize,
    required this.mimeType,
    required this.fileUrl,
    this.thumbnailUrl,
    this.width,
    this.height,
    this.duration,
    required this.uploadedAt,
    this.isFromLibrary = false,
    this.libraryFileId,
    this.metadata,
  });

  /// Create a MessageAttachment from JSON
  factory MessageAttachment.fromJson(Map<String, dynamic> json) {
    return MessageAttachment(
      id: json['id'] as String,
      fileName: json['fileName'] as String,
      fileSize: json['fileSize'] as int,
      mimeType: json['mimeType'] as String,
      fileUrl: json['fileUrl'] as String,
      thumbnailUrl: json['thumbnailUrl'] as String?,
      width: json['width'] as int?,
      height: json['height'] as int?,
      duration: json['duration'] as int?,
      uploadedAt: DateTime.parse(json['uploadedAt'] as String),
      isFromLibrary: json['isFromLibrary'] as bool? ?? false,
      libraryFileId: json['libraryFileId'] as String?,
      metadata: json['metadata'] as Map<String, dynamic>?,
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'fileName': fileName,
      'fileSize': fileSize,
      'mimeType': mimeType,
      'fileUrl': fileUrl,
      'thumbnailUrl': thumbnailUrl,
      'width': width,
      'height': height,
      'duration': duration,
      'uploadedAt': uploadedAt.toIso8601String(),
      'isFromLibrary': isFromLibrary,
      'libraryFileId': libraryFileId,
      'metadata': metadata,
    };
  }

  /// Create a copy of this MessageAttachment with updated fields
  MessageAttachment copyWith({
    String? id,
    String? fileName,
    int? fileSize,
    String? mimeType,
    String? fileUrl,
    String? thumbnailUrl,
    int? width,
    int? height,
    int? duration,
    DateTime? uploadedAt,
    bool? isFromLibrary,
    String? libraryFileId,
    Map<String, dynamic>? metadata,
  }) {
    return MessageAttachment(
      id: id ?? this.id,
      fileName: fileName ?? this.fileName,
      fileSize: fileSize ?? this.fileSize,
      mimeType: mimeType ?? this.mimeType,
      fileUrl: fileUrl ?? this.fileUrl,
      thumbnailUrl: thumbnailUrl ?? this.thumbnailUrl,
      width: width ?? this.width,
      height: height ?? this.height,
      duration: duration ?? this.duration,
      uploadedAt: uploadedAt ?? this.uploadedAt,
      isFromLibrary: isFromLibrary ?? this.isFromLibrary,
      libraryFileId: libraryFileId ?? this.libraryFileId,
      metadata: metadata ?? this.metadata,
    );
  }

  /// Get file extension from filename
  String get fileExtension {
    final parts = fileName.split('.');
    return parts.length > 1 ? parts.last.toLowerCase() : '';
  }

  /// Check if this is an image file
  bool get isImage {
    final imageTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    return imageTypes.contains(mimeType.toLowerCase());
  }

  /// Check if this is a video file
  bool get isVideo {
    return mimeType.toLowerCase().startsWith('video/');
  }

  /// Check if this is an audio file
  bool get isAudio {
    return mimeType.toLowerCase().startsWith('audio/');
  }

  /// Check if this is a document file
  bool get isDocument {
    final docTypes = [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.ms-powerpoint',
      'application/vnd.openxmlformats-officedocument.presentationml.presentation',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'text/plain',
    ];
    return docTypes.contains(mimeType.toLowerCase());
  }

  /// Get human-readable file size
  String get formattedFileSize {
    if (fileSize < 1024) {
      return '$fileSize B';
    } else if (fileSize < 1024 * 1024) {
      return '${(fileSize / 1024).toStringAsFixed(1)} KB';
    } else if (fileSize < 1024 * 1024 * 1024) {
      return '${(fileSize / (1024 * 1024)).toStringAsFixed(1)} MB';
    } else {
      return '${(fileSize / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
    }
  }

  /// Get formatted duration for audio/video files
  String? get formattedDuration {
    if (duration == null) return null;
    
    final minutes = duration! ~/ 60;
    final seconds = duration! % 60;
    return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is MessageAttachment &&
        other.id == id &&
        other.fileName == fileName &&
        other.fileSize == fileSize &&
        other.mimeType == mimeType &&
        other.fileUrl == fileUrl &&
        other.thumbnailUrl == thumbnailUrl &&
        other.width == width &&
        other.height == height &&
        other.duration == duration &&
        other.uploadedAt == uploadedAt &&
        other.isFromLibrary == isFromLibrary &&
        other.libraryFileId == libraryFileId;
  }

  @override
  int get hashCode {
    return Object.hash(
      id,
      fileName,
      fileSize,
      mimeType,
      fileUrl,
      thumbnailUrl,
      width,
      height,
      duration,
      uploadedAt,
      isFromLibrary,
      libraryFileId,
    );
  }

  @override
  String toString() {
    return 'MessageAttachment(id: $id, fileName: $fileName, fileSize: $fileSize, mimeType: $mimeType)';
  }
}
