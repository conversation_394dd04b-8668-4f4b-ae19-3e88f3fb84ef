import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';

import '../../../../../core/routes/app_routes.dart';
import '../../../models/chat_model.dart';
import '../../../widgets/chat_tile.dart';
import '../../../../debug/mock_data/generators/chat_mock_generator.dart';
import 'empty_state_section.dart';

/// Main chat list section displaying conversations
class ChatListSection extends ConsumerWidget {
  /// Selected category filter
  final String selectedCategory;

  /// Search query filter
  final String searchQuery;

  const ChatListSection({
    super.key,
    required this.selectedCategory,
    required this.searchQuery,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Using generated mock data for UI testing
    final chats = mockChatsList;

    // Filter chats based on category and search
    final filteredChats = _filterChats(chats, selectedCategory, searchQuery);

    if (filteredChats.isEmpty) {
      return EmptyStateSection(
        selectedCategory: selectedCategory,
        searchQuery: searchQuery,
      );
    }

    return ListView.separated(
      padding: EdgeInsets.symmetric(vertical: 8.h),
      itemCount: filteredChats.length,
      separatorBuilder: (context, index) => SizedBox(height: 8.h),
      itemBuilder: (context, index) {
        final chat = filteredChats[index];
        return ChatTile(chat: chat, onTap: () => _onChatTap(context, chat));
      },
    );
  }

  void _onChatTap(BuildContext context, ChatModel chat) {
    context.pushNamed(RouteNames.chatDetail, pathParameters: {'id': chat.id});
  }

  List<ChatModel> _filterChats(
    List<ChatModel> chats,
    String category,
    String searchQuery,
  ) {
    var filtered = chats;

    // Filter by category
    if (category != 'All') {
      filtered = filtered.where((chat) {
        switch (category) {
          case 'Direct':
            return chat.type.name.contains('oneToOne');
          case 'Groups':
            return chat.type.name.contains('group') ||
                chat.type.name.contains('Group');
          case 'Classroom':
            return chat.type.name.contains('classroom') ||
                chat.type.name.contains('Classroom');
          case 'Study Groups':
            return chat.type.name.contains('study') ||
                chat.type.name.contains('Study');
          default:
            return true;
        }
      }).toList();
    }

    // Filter by search query
    if (searchQuery.isNotEmpty) {
      final query = searchQuery.toLowerCase();
      filtered = filtered.where((chat) {
        return chat.title.toLowerCase().contains(query);
      }).toList();
    }

    return filtered;
  }
}
