import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_symbols_icons/symbols.dart';

/// Empty state section when no chats are found
class EmptyStateSection extends StatelessWidget {
  /// Selected category filter
  final String selectedCategory;

  /// Search query filter
  final String searchQuery;

  const EmptyStateSection({
    super.key,
    required this.selectedCategory,
    required this.searchQuery,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    final isSearching = searchQuery.isNotEmpty;
    final isFiltered = selectedCategory != 'All';

    return Center(
      child: Padding(
        padding: EdgeInsets.all(32.w),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Icon
            Container(
              width: 80.w,
              height: 80.w,
              decoration: BoxDecoration(
                color: colorScheme.surfaceContainerHighest,
                shape: BoxShape.circle,
              ),
              child: Icon(
                isSearching ? Symbols.search_off : Symbols.chat,
                size: 40.sp,
                color: colorScheme.onSurface.withValues(alpha: 0.6),
              ),
            ),

            SizedBox(height: 24.h),

            // Title
            Text(
              _getTitle(isSearching, isFiltered),
              style: theme.textTheme.titleMedium?.copyWith(
                color: colorScheme.onSurface,
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
            ),

            SizedBox(height: 8.h),

            // Description
            Text(
              _getDescription(isSearching, isFiltered),
              style: theme.textTheme.bodyMedium?.copyWith(
                color: colorScheme.onSurface.withValues(alpha: 0.7),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  String _getTitle(bool isSearching, bool isFiltered) {
    if (isSearching) {
      return 'No conversations found';
    } else if (isFiltered) {
      return 'No $selectedCategory conversations';
    } else {
      return 'No conversations yet';
    }
  }

  String _getDescription(bool isSearching, bool isFiltered) {
    if (isSearching) {
      return 'Try adjusting your search terms or check the spelling.';
    } else if (isFiltered) {
      return 'You don\'t have any conversations in this category yet.';
    } else {
      return 'Start a conversation by tapping the + button below.';
    }
  }
}
