import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_symbols_icons/symbols.dart';
import 'package:go_router/go_router.dart';

/// Section for dangerous chat actions like leaving or deleting
class DangerZoneSection extends StatelessWidget {
  /// The chat ID
  final String chatId;

  const DangerZoneSection({
    super.key,
    required this.chatId,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: colorScheme.errorContainer.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(
          color: colorScheme.error.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section title
          Row(
            children: [
              Icon(
                Symbols.warning,
                size: 20.sp,
                color: colorScheme.error,
              ),
              SizedBox(width: 8.w),
              Text(
                'Danger Zone',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: colorScheme.error,
                ),
              ),
            ],
          ),
          SizedBox(height: 16.h),

          // Clear chat history
          _buildDangerAction(
            context,
            title: 'Clear Chat History',
            subtitle: 'Delete all messages in this chat',
            icon: Symbols.delete_sweep,
            onTap: () => _showClearHistoryDialog(context),
          ),

          SizedBox(height: 12.h),

          // Archive chat
          _buildDangerAction(
            context,
            title: 'Archive Chat',
            subtitle: 'Hide this chat from your chat list',
            icon: Symbols.archive,
            onTap: () => _showArchiveChatDialog(context),
          ),

          SizedBox(height: 12.h),

          // Leave chat
          _buildDangerAction(
            context,
            title: 'Leave Chat',
            subtitle: 'Remove yourself from this chat',
            icon: Symbols.exit_to_app,
            onTap: () => _showLeaveChatDialog(context),
          ),

          SizedBox(height: 12.h),

          // Delete chat (only for admins)
          _buildDangerAction(
            context,
            title: 'Delete Chat',
            subtitle: 'Permanently delete this chat for everyone',
            icon: Symbols.delete_forever,
            onTap: () => _showDeleteChatDialog(context),
            isDestructive: true,
          ),
        ],
      ),
    );
  }

  Widget _buildDangerAction(
    BuildContext context, {
    required String title,
    required String subtitle,
    required IconData icon,
    required VoidCallback onTap,
    bool isDestructive = false,
  }) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8.r),
      child: Container(
        padding: EdgeInsets.all(12.w),
        decoration: BoxDecoration(
          color: isDestructive 
              ? colorScheme.error.withValues(alpha: 0.1)
              : Colors.transparent,
          borderRadius: BorderRadius.circular(8.r),
          border: isDestructive 
              ? Border.all(
                  color: colorScheme.error.withValues(alpha: 0.3),
                  width: 1,
                )
              : null,
        ),
        child: Row(
          children: [
            Icon(
              icon,
              size: 20.sp,
              color: isDestructive 
                  ? colorScheme.error
                  : colorScheme.error.withValues(alpha: 0.8),
            ),
            SizedBox(width: 12.w),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: theme.textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w500,
                      color: isDestructive 
                          ? colorScheme.error
                          : colorScheme.error.withValues(alpha: 0.8),
                    ),
                  ),
                  SizedBox(height: 2.h),
                  Text(
                    subtitle,
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: colorScheme.onSurface.withValues(alpha: 0.7),
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Symbols.chevron_right,
              size: 16.sp,
              color: colorScheme.onSurface.withValues(alpha: 0.4),
            ),
          ],
        ),
      ),
    );
  }

  void _showClearHistoryDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Clear Chat History'),
        content: const Text(
          'Are you sure you want to delete all messages in this chat? This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _clearChatHistory(context);
            },
            style: TextButton.styleFrom(
              foregroundColor: Theme.of(context).colorScheme.error,
            ),
            child: const Text('Clear'),
          ),
        ],
      ),
    );
  }

  void _showArchiveChatDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Archive Chat'),
        content: const Text(
          'This chat will be hidden from your chat list. You can find it in archived chats.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _archiveChat(context);
            },
            child: const Text('Archive'),
          ),
        ],
      ),
    );
  }

  void _showLeaveChatDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Leave Chat'),
        content: const Text(
          'Are you sure you want to leave this chat? You won\'t be able to see new messages.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _leaveChat(context);
            },
            style: TextButton.styleFrom(
              foregroundColor: Theme.of(context).colorScheme.error,
            ),
            child: const Text('Leave'),
          ),
        ],
      ),
    );
  }

  void _showDeleteChatDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Chat'),
        content: const Text(
          'Are you sure you want to permanently delete this chat? This will delete the chat for all participants and cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _deleteChat(context);
            },
            style: TextButton.styleFrom(
              foregroundColor: Theme.of(context).colorScheme.error,
            ),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  void _clearChatHistory(BuildContext context) {
    // TODO: Implement clear chat history
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Chat history cleared'),
      ),
    );
  }

  void _archiveChat(BuildContext context) {
    // TODO: Implement archive chat
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Chat archived'),
      ),
    );
    context.pop(); // Go back to chat list
  }

  void _leaveChat(BuildContext context) {
    // TODO: Implement leave chat
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Left chat'),
      ),
    );
    context.pop(); // Go back to chat list
  }

  void _deleteChat(BuildContext context) {
    // TODO: Implement delete chat
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Chat deleted'),
      ),
    );
    context.pop(); // Go back to chat list
  }
}
