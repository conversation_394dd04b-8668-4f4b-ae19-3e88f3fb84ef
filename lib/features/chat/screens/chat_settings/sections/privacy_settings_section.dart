import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_symbols_icons/symbols.dart';

/// Section for managing chat privacy and security settings
class PrivacySettingsSection extends StatefulWidget {
  /// The chat ID
  final String chatId;

  const PrivacySettingsSection({
    super.key,
    required this.chatId,
  });

  @override
  State<PrivacySettingsSection> createState() => _PrivacySettingsSectionState();
}

class _PrivacySettingsSectionState extends State<PrivacySettingsSection> {
  // TODO: Load actual settings from provider
  bool _allowFileSharing = true;
  bool _allowVoiceMessages = true;
  bool _readReceipts = true;
  bool _typingIndicators = true;
  bool _lastSeenStatus = true;
  String _messageRetention = 'Forever';
  bool _autoDeleteMessages = false;
  int _autoDeleteDays = 30;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: colorScheme.surface,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(
          color: colorScheme.outline.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section title
          Row(
            children: [
              Icon(
                Symbols.privacy_tip,
                size: 20.sp,
                color: colorScheme.primary,
              ),
              SizedBox(width: 8.w),
              Text(
                'Privacy & Security',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: colorScheme.onSurface,
                ),
              ),
            ],
          ),
          SizedBox(height: 16.h),

          // File sharing toggle
          _buildSwitchTile(
            context,
            title: 'Allow File Sharing',
            subtitle: 'Members can share files and documents',
            value: _allowFileSharing,
            onChanged: (value) {
              setState(() {
                _allowFileSharing = value;
              });
            },
            icon: Symbols.attach_file,
          ),

          SizedBox(height: 16.h),

          // Voice messages toggle
          _buildSwitchTile(
            context,
            title: 'Allow Voice Messages',
            subtitle: 'Members can send voice recordings',
            value: _allowVoiceMessages,
            onChanged: (value) {
              setState(() {
                _allowVoiceMessages = value;
              });
            },
            icon: Symbols.mic,
          ),

          SizedBox(height: 16.h),

          // Read receipts toggle
          _buildSwitchTile(
            context,
            title: 'Read Receipts',
            subtitle: 'Show when messages are read',
            value: _readReceipts,
            onChanged: (value) {
              setState(() {
                _readReceipts = value;
              });
            },
            icon: Symbols.done_all,
          ),

          SizedBox(height: 16.h),

          // Typing indicators toggle
          _buildSwitchTile(
            context,
            title: 'Typing Indicators',
            subtitle: 'Show when someone is typing',
            value: _typingIndicators,
            onChanged: (value) {
              setState(() {
                _typingIndicators = value;
              });
            },
            icon: Symbols.edit,
          ),

          SizedBox(height: 16.h),

          // Last seen status toggle
          _buildSwitchTile(
            context,
            title: 'Last Seen Status',
            subtitle: 'Show when you were last online',
            value: _lastSeenStatus,
            onChanged: (value) {
              setState(() {
                _lastSeenStatus = value;
              });
            },
            icon: Symbols.schedule,
          ),

          SizedBox(height: 16.h),

          // Message retention selector
          _buildListTile(
            context,
            title: 'Message Retention',
            subtitle: _messageRetention,
            icon: Symbols.history,
            onTap: () => _showMessageRetentionSelector(context),
          ),

          SizedBox(height: 16.h),

          // Auto delete messages toggle
          _buildSwitchTile(
            context,
            title: 'Auto-Delete Messages',
            subtitle: 'Automatically delete old messages',
            value: _autoDeleteMessages,
            onChanged: (value) {
              setState(() {
                _autoDeleteMessages = value;
              });
            },
            icon: Symbols.auto_delete,
          ),

          if (_autoDeleteMessages) ...[
            SizedBox(height: 16.h),
            _buildListTile(
              context,
              title: 'Auto-Delete After',
              subtitle: '$_autoDeleteDays days',
              icon: Symbols.timer,
              onTap: () => _showAutoDeleteDaysSelector(context),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildSwitchTile(
    BuildContext context, {
    required String title,
    required String subtitle,
    required bool value,
    required ValueChanged<bool> onChanged,
    required IconData icon,
  }) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Row(
      children: [
        Icon(
          icon,
          size: 20.sp,
          color: colorScheme.onSurface.withValues(alpha: 0.7),
        ),
        SizedBox(width: 12.w),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: theme.textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w500,
                  color: colorScheme.onSurface,
                ),
              ),
              SizedBox(height: 2.h),
              Text(
                subtitle,
                style: theme.textTheme.bodySmall?.copyWith(
                  color: colorScheme.onSurface.withValues(alpha: 0.7),
                ),
              ),
            ],
          ),
        ),
        Switch(
          value: value,
          onChanged: onChanged,
          activeColor: colorScheme.primary,
        ),
      ],
    );
  }

  Widget _buildListTile(
    BuildContext context, {
    required String title,
    required String subtitle,
    required IconData icon,
    required VoidCallback onTap,
  }) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8.r),
      child: Padding(
        padding: EdgeInsets.symmetric(vertical: 4.h),
        child: Row(
          children: [
            Icon(
              icon,
              size: 20.sp,
              color: colorScheme.onSurface.withValues(alpha: 0.7),
            ),
            SizedBox(width: 12.w),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: theme.textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w500,
                      color: colorScheme.onSurface,
                    ),
                  ),
                  SizedBox(height: 2.h),
                  Text(
                    subtitle,
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: colorScheme.primary,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Symbols.chevron_right,
              size: 16.sp,
              color: colorScheme.onSurface.withValues(alpha: 0.4),
            ),
          ],
        ),
      ),
    );
  }

  void _showMessageRetentionSelector(BuildContext context) {
    final options = ['Forever', '1 Year', '6 Months', '3 Months', '1 Month'];

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Message Retention'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: options.map((option) {
            return RadioListTile<String>(
              title: Text(option),
              value: option,
              groupValue: _messageRetention,
              onChanged: (value) {
                setState(() {
                  _messageRetention = value!;
                });
                Navigator.of(context).pop();
              },
            );
          }).toList(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  void _showAutoDeleteDaysSelector(BuildContext context) {
    final options = [7, 14, 30, 60, 90];

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Auto-Delete After'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: options.map((days) {
            return RadioListTile<int>(
              title: Text('$days days'),
              value: days,
              groupValue: _autoDeleteDays,
              onChanged: (value) {
                setState(() {
                  _autoDeleteDays = value!;
                });
                Navigator.of(context).pop();
              },
            );
          }).toList(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }
}
