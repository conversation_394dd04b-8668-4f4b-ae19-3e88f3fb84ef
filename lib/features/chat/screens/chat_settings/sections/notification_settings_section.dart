import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_symbols_icons/symbols.dart';

/// Section for managing chat notification settings
class NotificationSettingsSection extends StatefulWidget {
  /// The chat ID
  final String chatId;

  const NotificationSettingsSection({
    super.key,
    required this.chatId,
  });

  @override
  State<NotificationSettingsSection> createState() => _NotificationSettingsSectionState();
}

class _NotificationSettingsSectionState extends State<NotificationSettingsSection> {
  // TODO: Load actual settings from provider
  bool _muteNotifications = false;
  bool _showPreviews = true;
  bool _soundEnabled = true;
  bool _vibrationEnabled = true;
  String _notificationTone = 'Default';
  bool _mentionNotifications = true;
  bool _reactionNotifications = false;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: colorScheme.surface,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(
          color: colorScheme.outline.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section title
          Row(
            children: [
              Icon(
                Symbols.notifications,
                size: 20.sp,
                color: colorScheme.primary,
              ),
              SizedBox(width: 8.w),
              Text(
                'Notifications',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: colorScheme.onSurface,
                ),
              ),
            ],
          ),
          SizedBox(height: 16.h),

          // Mute notifications toggle
          _buildSwitchTile(
            context,
            title: 'Mute Notifications',
            subtitle: 'Turn off all notifications for this chat',
            value: _muteNotifications,
            onChanged: (value) {
              setState(() {
                _muteNotifications = value;
              });
            },
            icon: _muteNotifications ? Symbols.notifications_off : Symbols.notifications,
          ),

          if (!_muteNotifications) ...[
            SizedBox(height: 16.h),

            // Show previews toggle
            _buildSwitchTile(
              context,
              title: 'Show Previews',
              subtitle: 'Display message content in notifications',
              value: _showPreviews,
              onChanged: (value) {
                setState(() {
                  _showPreviews = value;
                });
              },
              icon: Symbols.preview,
            ),

            SizedBox(height: 16.h),

            // Sound toggle
            _buildSwitchTile(
              context,
              title: 'Sound',
              subtitle: 'Play notification sound',
              value: _soundEnabled,
              onChanged: (value) {
                setState(() {
                  _soundEnabled = value;
                });
              },
              icon: _soundEnabled ? Symbols.volume_up : Symbols.volume_off,
            ),

            SizedBox(height: 16.h),

            // Vibration toggle
            _buildSwitchTile(
              context,
              title: 'Vibration',
              subtitle: 'Vibrate on new messages',
              value: _vibrationEnabled,
              onChanged: (value) {
                setState(() {
                  _vibrationEnabled = value;
                });
              },
              icon: Symbols.vibration,
            ),

            SizedBox(height: 16.h),

            // Notification tone selector
            _buildListTile(
              context,
              title: 'Notification Tone',
              subtitle: _notificationTone,
              icon: Symbols.music_note,
              onTap: () => _showNotificationToneSelector(context),
            ),

            SizedBox(height: 16.h),

            // Mention notifications toggle
            _buildSwitchTile(
              context,
              title: 'Mention Notifications',
              subtitle: 'Get notified when you\'re mentioned',
              value: _mentionNotifications,
              onChanged: (value) {
                setState(() {
                  _mentionNotifications = value;
                });
              },
              icon: Symbols.alternate_email,
            ),

            SizedBox(height: 16.h),

            // Reaction notifications toggle
            _buildSwitchTile(
              context,
              title: 'Reaction Notifications',
              subtitle: 'Get notified when someone reacts to your messages',
              value: _reactionNotifications,
              onChanged: (value) {
                setState(() {
                  _reactionNotifications = value;
                });
              },
              icon: Symbols.sentiment_satisfied,
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildSwitchTile(
    BuildContext context, {
    required String title,
    required String subtitle,
    required bool value,
    required ValueChanged<bool> onChanged,
    required IconData icon,
  }) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Row(
      children: [
        Icon(
          icon,
          size: 20.sp,
          color: colorScheme.onSurface.withValues(alpha: 0.7),
        ),
        SizedBox(width: 12.w),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: theme.textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w500,
                  color: colorScheme.onSurface,
                ),
              ),
              SizedBox(height: 2.h),
              Text(
                subtitle,
                style: theme.textTheme.bodySmall?.copyWith(
                  color: colorScheme.onSurface.withValues(alpha: 0.7),
                ),
              ),
            ],
          ),
        ),
        Switch(
          value: value,
          onChanged: onChanged,
          activeColor: colorScheme.primary,
        ),
      ],
    );
  }

  Widget _buildListTile(
    BuildContext context, {
    required String title,
    required String subtitle,
    required IconData icon,
    required VoidCallback onTap,
  }) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8.r),
      child: Padding(
        padding: EdgeInsets.symmetric(vertical: 4.h),
        child: Row(
          children: [
            Icon(
              icon,
              size: 20.sp,
              color: colorScheme.onSurface.withValues(alpha: 0.7),
            ),
            SizedBox(width: 12.w),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: theme.textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w500,
                      color: colorScheme.onSurface,
                    ),
                  ),
                  SizedBox(height: 2.h),
                  Text(
                    subtitle,
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: colorScheme.primary,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Symbols.chevron_right,
              size: 16.sp,
              color: colorScheme.onSurface.withValues(alpha: 0.4),
            ),
          ],
        ),
      ),
    );
  }

  void _showNotificationToneSelector(BuildContext context) {
    final tones = ['Default', 'Bell', 'Chime', 'Ding', 'Pop', 'Whistle'];

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Select Notification Tone'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: tones.map((tone) {
            return RadioListTile<String>(
              title: Text(tone),
              value: tone,
              groupValue: _notificationTone,
              onChanged: (value) {
                setState(() {
                  _notificationTone = value!;
                });
                Navigator.of(context).pop();
              },
            );
          }).toList(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }
}
