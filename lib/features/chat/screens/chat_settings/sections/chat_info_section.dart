import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_symbols_icons/symbols.dart';

import '../../../models/chat_model.dart';
import '../../../models/chat_settings.dart';
import '../../../enums/chat_type.dart';
import '../../../../debug/mock_data/generators/chat_mock_generator.dart';
import '../../../widgets/participant_avatar.dart';

/// Section displaying chat information and basic details
class ChatInfoSection extends StatelessWidget {
  /// The chat ID to display info for
  final String chatId;

  const ChatInfoSection({super.key, required this.chatId});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    // Using generated mock data for UI testing
    final chat = mockChatsList.firstWhere(
      (c) => c.id == chatId,
      orElse: () => mockChatsList.first,
    );

    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: colorScheme.surface,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(
          color: colorScheme.outline.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section title
          Text(
            'Chat Information',
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
              color: colorScheme.onSurface,
            ),
          ),
          SizedBox(height: 16.h),

          // Chat avatar and basic info
          Row(
            children: [
              // Chat avatar
              Container(
                width: 64.w,
                height: 64.w,
                decoration: BoxDecoration(
                  color: colorScheme.primaryContainer,
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  _getChatTypeIcon(chat.type),
                  size: 32.sp,
                  color: colorScheme.onPrimaryContainer,
                ),
              ),

              SizedBox(width: 16.w),

              // Chat details
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Chat title
                    Text(
                      chat.title,
                      style: theme.textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: colorScheme.onSurface,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),

                    SizedBox(height: 4.h),

                    // Chat type and participant count
                    Row(
                      children: [
                        Container(
                          padding: EdgeInsets.symmetric(
                            horizontal: 8.w,
                            vertical: 2.h,
                          ),
                          decoration: BoxDecoration(
                            color: colorScheme.secondaryContainer,
                            borderRadius: BorderRadius.circular(8.r),
                          ),
                          child: Text(
                            _getChatTypeLabel(chat.type),
                            style: theme.textTheme.labelSmall?.copyWith(
                              color: colorScheme.onSecondaryContainer,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                        SizedBox(width: 8.w),
                        Text(
                          '${chat.participantIds.length} participants',
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: colorScheme.onSurface.withValues(alpha: 0.7),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),

              // Edit button
              IconButton(
                onPressed: () => _onEditChatInfo(context),
                icon: Icon(
                  Symbols.edit,
                  color: colorScheme.primary,
                  size: 20.sp,
                ),
                style: IconButton.styleFrom(
                  backgroundColor: colorScheme.primaryContainer.withValues(
                    alpha: 0.3,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8.r),
                  ),
                ),
              ),
            ],
          ),

          SizedBox(height: 16.h),

          // Chat statistics
          Row(
            children: [
              Expanded(
                child: _buildStatItem(
                  context,
                  'Created',
                  _formatDate(chat.createdAt),
                  Symbols.calendar_add_on,
                ),
              ),
              SizedBox(width: 16.w),
              Expanded(
                child: _buildStatItem(
                  context,
                  'Last Activity',
                  chat.lastMessageAt != null
                      ? _formatDate(chat.lastMessageAt!)
                      : 'No messages',
                  Symbols.schedule,
                ),
              ),
            ],
          ),

          SizedBox(height: 16.h),

          // Recent participants preview
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Recent Participants',
                style: theme.textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: colorScheme.onSurface,
                ),
              ),
              SizedBox(height: 8.h),
              SizedBox(
                height: 40.h,
                child: ListView.separated(
                  scrollDirection: Axis.horizontal,
                  itemCount: chat.participantIds.take(5).length,
                  separatorBuilder: (context, index) => SizedBox(width: 8.w),
                  itemBuilder: (context, index) {
                    final participantId = chat.participantIds[index];
                    return ParticipantAvatar(
                      participantId: participantId,
                      participantName: 'User $participantId',
                      size: 40.w,
                    );
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(
    BuildContext context,
    String label,
    String value,
    IconData icon,
  ) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Container(
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: colorScheme.surfaceContainerHighest,
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, size: 16.sp, color: colorScheme.primary),
              SizedBox(width: 4.w),
              Text(
                label,
                style: theme.textTheme.labelSmall?.copyWith(
                  color: colorScheme.onSurface.withValues(alpha: 0.7),
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
          SizedBox(height: 4.h),
          Text(
            value,
            style: theme.textTheme.bodySmall?.copyWith(
              color: colorScheme.onSurface,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  void _onEditChatInfo(BuildContext context) {
    // TODO: Show edit chat info dialog
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Edit Chat Info'),
        content: const Text('Edit chat name, description, and other details.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              // TODO: Implement chat info editing
            },
            child: const Text('Save'),
          ),
        ],
      ),
    );
  }

  IconData _getChatTypeIcon(ChatType type) {
    switch (type) {
      case ChatType.oneToOne:
        return Symbols.person;
      case ChatType.groupChat:
        return Symbols.group;
      case ChatType.classroomDiscussion:
        return Symbols.school;
      case ChatType.studyGroup:
        return Symbols.groups;
      case ChatType.projectTeam:
        return Symbols.work_history;
      case ChatType.parentGroup:
        return Symbols.family_restroom;
      case ChatType.teacherCircle:
        return Symbols.supervisor_account;
    }
  }

  String _getChatTypeLabel(ChatType type) {
    switch (type) {
      case ChatType.oneToOne:
        return 'Direct Message';
      case ChatType.groupChat:
        return 'Group Chat';
      case ChatType.classroomDiscussion:
        return 'Classroom Discussion';
      case ChatType.studyGroup:
        return 'Study Group';
      case ChatType.projectTeam:
        return 'Project Team';
      case ChatType.parentGroup:
        return 'Parent Group';
      case ChatType.teacherCircle:
        return 'Teacher Circle';
    }
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays > 30) {
      return '${(difference.inDays / 30).floor()}mo ago';
    } else if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else {
      return 'Just now';
    }
  }
}
