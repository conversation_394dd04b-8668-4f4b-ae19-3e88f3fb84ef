import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:material_symbols_icons/symbols.dart';
import 'package:go_router/go_router.dart';

import '../../enums/chat_type.dart';
import 'sections/chat_type_selection_section.dart';
import 'sections/participant_selection_section.dart';
import 'sections/chat_details_section.dart';

/// Screen for creating a new chat
class CreateChatScreen extends ConsumerStatefulWidget {
  const CreateChatScreen({
    super.key,
    this.initialChatType,
    this.initialStep,
    this.selectionMode,
  });

  final ChatType? initialChatType;
  final String? initialStep;
  final String?
  selectionMode; // 'single' for direct message, 'multiple' for group

  @override
  ConsumerState<CreateChatScreen> createState() => _CreateChatScreenState();
}

class _CreateChatScreenState extends ConsumerState<CreateChatScreen> {
  final PageController _pageController = PageController();
  int _currentStep = 0;

  ChatType? _selectedChatType;
  List<String> _selectedParticipants = [];

  @override
  void initState() {
    super.initState();
    _initializeFromParameters();
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  void _initializeFromParameters() {
    if (widget.initialChatType != null) {
      _selectedChatType = widget.initialChatType;
    }

    if (widget.initialStep == 'participants' && _selectedChatType != null) {
      // Start from participant selection step
      _currentStep = 1;
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _pageController.animateToPage(
          1,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
      });
    }
  }

  void _onChatTypeSelected(ChatType chatType) {
    setState(() {
      _selectedChatType = chatType;
    });
    _nextStep();
  }

  void _onParticipantsSelected(List<String> participants) {
    setState(() {
      _selectedParticipants = participants;
    });

    // Handle different flows based on selection mode
    if (widget.selectionMode == 'single' &&
        _selectedChatType == ChatType.oneToOne) {
      // For direct messages, create chat immediately after selecting participant
      _createDirectMessage();
    } else {
      // For group chats, proceed to next step (settings)
      _nextStep();
    }
  }

  void _onChatDetailsCompleted(String title, String description) {
    // TODO: Use title and description when implementing actual chat creation
    _onCreateChat();
  }

  void _nextStep() {
    if (_currentStep < 2) {
      setState(() {
        _currentStep++;
      });
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _previousStep() {
    if (_currentStep > 0) {
      setState(() {
        _currentStep--;
      });
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _createDirectMessage() {
    // TODO: Implement direct message creation logic
    // For now, just navigate back to chat list
    context.pop();
  }

  void _onCreateChat() {
    // TODO: Implement group chat creation logic
    context.pop();
  }

  List<Widget> _buildPageViewChildren() {
    // If we have initial parameters (coming from modal), skip chat type selection
    if (widget.initialChatType != null &&
        widget.initialStep == 'participants') {
      return [
        // Step 1: Participant selection (skipping chat type selection)
        ParticipantSelectionSection(
          chatType: _selectedChatType!,
          onParticipantsSelected: _onParticipantsSelected,
          onBack: () =>
              context.pop(), // Go back to chat list instead of previous step
          selectionMode: widget.selectionMode,
        ),

        // Step 2: Chat details (only for group chats)
        if (widget.selectionMode == 'multiple')
          ChatDetailsSection(
            chatType: _selectedChatType!,
            participants: _selectedParticipants,
            onDetailsCompleted: _onChatDetailsCompleted,
            onBack: _previousStep,
          ),
      ];
    }

    // Default full flow (when accessed directly)
    return [
      // Step 1: Chat type selection
      ChatTypeSelectionSection(onChatTypeSelected: _onChatTypeSelected),

      // Step 2: Participant selection
      ParticipantSelectionSection(
        chatType: _selectedChatType!,
        onParticipantsSelected: _onParticipantsSelected,
        onBack: _previousStep,
        selectionMode: widget.selectionMode,
      ),

      // Step 3: Chat details
      ChatDetailsSection(
        chatType: _selectedChatType!,
        participants: _selectedParticipants,
        onDetailsCompleted: _onChatDetailsCompleted,
        onBack: _previousStep,
      ),
    ];
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Create Chat',
          style: theme.textTheme.titleLarge?.copyWith(
            color: colorScheme.onSurface,
            fontWeight: FontWeight.w600,
          ),
        ),
        backgroundColor: colorScheme.surface,
        elevation: 0,
        leading: IconButton(
          onPressed: () => context.pop(),
          icon: Icon(Symbols.arrow_back_ios, color: colorScheme.onSurface),
        ),
      ),
      body: SafeArea(
        child: Column(
          children: [
            // Page view
            Expanded(
              child: PageView(
                controller: _pageController,
                physics: const NeverScrollableScrollPhysics(),
                children: _buildPageViewChildren(),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
