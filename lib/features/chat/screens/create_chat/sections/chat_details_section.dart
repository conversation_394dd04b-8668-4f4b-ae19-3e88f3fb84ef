import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_symbols_icons/symbols.dart';

import '../../../../../core/widgets/buttons/custom_button.dart';
import '../../../enums/chat_type.dart';
import '../../../widgets/participant_avatar.dart';

/// Section for entering chat details when creating a new chat
class ChatDetailsSection extends StatefulWidget {
  /// The selected chat type
  final ChatType chatType;

  /// Selected participants
  final List<String> participants;

  /// Callback when details are completed
  final Function(String title, String description) onDetailsCompleted;

  /// Callback to go back to previous step
  final VoidCallback onBack;

  const ChatDetailsSection({
    super.key,
    required this.chatType,
    required this.participants,
    required this.onDetailsCompleted,
    required this.onBack,
  });

  @override
  State<ChatDetailsSection> createState() => _ChatDetailsSectionState();
}

class _ChatDetailsSectionState extends State<ChatDetailsSection> {
  final TextEditingController _titleController = TextEditingController();
  final TextEditingController _descriptionController = TextEditingController();
  final _formKey = GlobalKey<FormState>();

  @override
  void initState() {
    super.initState();
    // Set default title based on chat type
    _titleController.text = _getDefaultTitle();
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return SingleChildScrollView(
      padding: EdgeInsets.all(24.w),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Title and back button
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Chat Details',
                        style: theme.textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.w600,
                          color: colorScheme.onSurface,
                        ),
                      ),
                      Text(
                        'Add a name and description for your chat',
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: colorScheme.onSurface.withValues(alpha: 0.7),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),

            SizedBox(height: 24.h),

            // Chat preview
            Container(
              padding: EdgeInsets.all(16.w),
              decoration: BoxDecoration(
                color: colorScheme.surfaceContainerHighest,
                borderRadius: BorderRadius.circular(12.r),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Preview',
                    style: theme.textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: colorScheme.onSurface,
                    ),
                  ),
                  SizedBox(height: 12.h),
                  Row(
                    children: [
                      // Chat icon
                      Container(
                        width: 48.w,
                        height: 48.w,
                        decoration: BoxDecoration(
                          color: colorScheme.primaryContainer,
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          _getChatTypeIcon(),
                          size: 24.sp,
                          color: colorScheme.onPrimaryContainer,
                        ),
                      ),
                      SizedBox(width: 12.w),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              _titleController.text.isNotEmpty
                                  ? _titleController.text
                                  : 'Chat Name',
                              style: theme.textTheme.titleMedium?.copyWith(
                                fontWeight: FontWeight.w600,
                                color: colorScheme.onSurface,
                              ),
                            ),
                            Text(
                              '${widget.participants.length + 1} participants',
                              style: theme.textTheme.bodySmall?.copyWith(
                                color: colorScheme.onSurface.withValues(
                                  alpha: 0.7,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 12.h),
                  // Participants preview
                  SizedBox(
                    height: 32.h,
                    child: ListView.separated(
                      scrollDirection: Axis.horizontal,
                      itemCount: widget.participants.length,
                      separatorBuilder: (context, index) =>
                          SizedBox(width: 8.w),
                      itemBuilder: (context, index) {
                        final participantId = widget.participants[index];
                        return ParticipantAvatar(
                          participantId: participantId,
                          participantName: 'User $participantId',
                          size: 32.w,
                        );
                      },
                    ),
                  ),
                ],
              ),
            ),

            SizedBox(height: 24.h),

            // Chat name field
            TextFormField(
              controller: _titleController,
              decoration: InputDecoration(
                labelText: 'Chat Name',
                hintText: 'Enter a name for your chat',
                prefixIcon: Icon(Symbols.edit),
                filled: true,
                fillColor: colorScheme.surfaceContainerHighest,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12.r),
                  borderSide: BorderSide.none,
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12.r),
                  borderSide: BorderSide(color: colorScheme.primary, width: 2),
                ),
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Please enter a chat name';
                }
                if (value.trim().length < 2) {
                  return 'Chat name must be at least 2 characters';
                }
                return null;
              },
              onChanged: (value) {
                setState(() {}); // Rebuild to update preview
              },
            ),

            SizedBox(height: 16.h),

            // Description field
            TextFormField(
              controller: _descriptionController,
              maxLines: 3,
              decoration: InputDecoration(
                labelText: 'Description (Optional)',
                hintText: 'Add a description for your chat',
                prefixIcon: Icon(Symbols.description),
                filled: true,
                fillColor: colorScheme.surfaceContainerHighest,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12.r),
                  borderSide: BorderSide.none,
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12.r),
                  borderSide: BorderSide(color: colorScheme.primary, width: 2),
                ),
              ),
            ),

            SizedBox(height: 24.h),

            // Chat type info
            Container(
              padding: EdgeInsets.all(12.w),
              decoration: BoxDecoration(
                color: colorScheme.primaryContainer.withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(8.r),
              ),
              child: Row(
                children: [
                  Icon(Symbols.info, size: 16.sp, color: colorScheme.primary),
                  SizedBox(width: 8.w),
                  Expanded(
                    child: Text(
                      _getChatTypeInfo(),
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: colorScheme.onSurface.withValues(alpha: 0.8),
                      ),
                    ),
                  ),
                ],
              ),
            ),

            SizedBox(height: 32.h),

            // Create button
            SizedBox(
              width: double.infinity,
              child: CustomButton.primary(
                text: 'Create Chat',
                onPressed: _onCreateChat,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _onCreateChat() {
    if (_formKey.currentState?.validate() ?? false) {
      widget.onDetailsCompleted(
        _titleController.text.trim(),
        _descriptionController.text.trim(),
      );
    }
  }

  String _getDefaultTitle() {
    switch (widget.chatType) {
      case ChatType.oneToOne:
        return ''; // Will be set to participant name
      case ChatType.groupChat:
        return 'Group Chat';
      case ChatType.studyGroup:
        return 'Study Group';
      case ChatType.projectTeam:
        return 'Project Team';
      case ChatType.classroomDiscussion:
        return 'Classroom Discussion';
      case ChatType.parentGroup:
        return 'Parent Group';
      case ChatType.teacherCircle:
        return 'Teacher Circle';
    }
  }

  IconData _getChatTypeIcon() {
    switch (widget.chatType) {
      case ChatType.oneToOne:
        return Symbols.person;
      case ChatType.groupChat:
        return Symbols.group;
      case ChatType.studyGroup:
        return Symbols.groups;
      case ChatType.projectTeam:
        return Symbols.work_history;
      case ChatType.classroomDiscussion:
        return Symbols.school;
      case ChatType.parentGroup:
        return Symbols.family_restroom;
      case ChatType.teacherCircle:
        return Symbols.supervisor_account;
    }
  }

  String _getChatTypeInfo() {
    switch (widget.chatType) {
      case ChatType.oneToOne:
        return 'Private conversation between two people';
      case ChatType.groupChat:
        return 'Group chat with file sharing and reactions enabled';
      case ChatType.studyGroup:
        return 'Study-focused chat with limited participants (max 10)';
      case ChatType.projectTeam:
        return 'Project collaboration with file sharing and editing';
      case ChatType.classroomDiscussion:
        return 'Class-wide discussion with moderation features';
      case ChatType.parentGroup:
        return 'Parent communication with restricted features';
      case ChatType.teacherCircle:
        return 'Professional teacher discussion with full features';
    }
  }
}
