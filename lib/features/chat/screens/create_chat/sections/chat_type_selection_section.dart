import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_symbols_icons/symbols.dart';

import '../../../enums/chat_type.dart';

/// Section for selecting chat type when creating a new chat
class ChatTypeSelectionSection extends StatelessWidget {
  /// Callback when chat type is selected
  final Function(ChatType) onChatTypeSelected;

  const ChatTypeSelectionSection({
    super.key,
    required this.onChatTypeSelected,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Padding(
      padding: EdgeInsets.all(24.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title
          Text(
            'Choose Chat Type',
            style: theme.textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.w600,
              color: colorScheme.onSurface,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            'Select the type of chat you want to create',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: colorScheme.onSurface.withValues(alpha: 0.7),
            ),
          ),
          SizedBox(height: 32.h),

          // Chat type options
          Expanded(
            child: ListView(
              children: [
                _buildChatTypeOption(
                  context,
                  type: ChatType.oneToOne,
                  title: 'Direct Message',
                  description: 'Private conversation between two people',
                  icon: Symbols.person,
                ),
                SizedBox(height: 16.h),
                _buildChatTypeOption(
                  context,
                  type: ChatType.groupChat,
                  title: 'Group Chat',
                  description: 'Chat with multiple participants',
                  icon: Symbols.group,
                ),
                SizedBox(height: 16.h),
                _buildChatTypeOption(
                  context,
                  type: ChatType.studyGroup,
                  title: 'Study Group',
                  description: 'Focused group for studying together',
                  icon: Symbols.groups,
                ),
                SizedBox(height: 16.h),
                _buildChatTypeOption(
                  context,
                  type: ChatType.projectTeam,
                  title: 'Project Team',
                  description: 'Collaborate on projects and assignments',
                  icon: Symbols.work_history,
                ),
                SizedBox(height: 16.h),
                _buildChatTypeOption(
                  context,
                  type: ChatType.classroomDiscussion,
                  title: 'Classroom Discussion',
                  description: 'Class-wide discussions and announcements',
                  icon: Symbols.school,
                ),
                SizedBox(height: 16.h),
                _buildChatTypeOption(
                  context,
                  type: ChatType.parentGroup,
                  title: 'Parent Group',
                  description: 'Communication between parents',
                  icon: Symbols.family_restroom,
                ),
                SizedBox(height: 16.h),
                _buildChatTypeOption(
                  context,
                  type: ChatType.teacherCircle,
                  title: 'Teacher Circle',
                  description: 'Professional discussions among educators',
                  icon: Symbols.supervisor_account,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildChatTypeOption(
    BuildContext context, {
    required ChatType type,
    required String title,
    required String description,
    required IconData icon,
  }) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return InkWell(
      onTap: () => onChatTypeSelected(type),
      borderRadius: BorderRadius.circular(16.r),
      child: Container(
        padding: EdgeInsets.all(20.w),
        decoration: BoxDecoration(
          color: colorScheme.surface,
          borderRadius: BorderRadius.circular(16.r),
          border: Border.all(
            color: colorScheme.outline.withValues(alpha: 0.2),
            width: 1,
          ),
        ),
        child: Row(
          children: [
            // Icon
            Container(
              width: 56.w,
              height: 56.w,
              decoration: BoxDecoration(
                color: colorScheme.primaryContainer,
                borderRadius: BorderRadius.circular(12.r),
              ),
              child: Icon(
                icon,
                size: 28.sp,
                color: colorScheme.onPrimaryContainer,
              ),
            ),

            SizedBox(width: 16.w),

            // Content
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: colorScheme.onSurface,
                    ),
                  ),
                  SizedBox(height: 4.h),
                  Text(
                    description,
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: colorScheme.onSurface.withValues(alpha: 0.7),
                    ),
                  ),
                ],
              ),
            ),

            // Arrow
            Icon(
              Symbols.chevron_right,
              size: 20.sp,
              color: colorScheme.onSurface.withValues(alpha: 0.4),
            ),
          ],
        ),
      ),
    );
  }
}
