import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../../core/widgets/buttons/custom_button.dart';

/// Actions section for the create chat screen
class CreateChatActionsSection extends StatelessWidget {
  /// Callback when create chat button is pressed
  final VoidCallback onCreateChat;

  /// Callback when back button is pressed
  final VoidCallback onBack;

  const CreateChatActionsSection({
    super.key,
    required this.onCreateChat,
    required this.onBack,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: colorScheme.surface,
        border: Border(
          top: BorderSide(
            color: colorScheme.outline.withValues(alpha: 0.2),
            width: 1,
          ),
        ),
      ),
      child: SafeArea(
        top: false,
        child: Row(
          children: [
            Expanded(
              child: CustomButton.secondary(
                text: 'Back',
                onPressed: onBack,
              ),
            ),
            SizedBox(width: 12.w),
            Expanded(
              flex: 2,
              child: CustomButton.primary(
                text: 'Create Chat',
                onPressed: onCreateChat,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
