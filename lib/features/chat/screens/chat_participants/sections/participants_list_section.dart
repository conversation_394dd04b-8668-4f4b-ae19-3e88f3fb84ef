import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../models/chat_participant.dart';
import '../../../widgets/participant_tile.dart';
import '../../../../debug/mock_data/generators/chat_mock_generator.dart';

/// Section displaying the list of chat participants
class ParticipantsListSection extends ConsumerWidget {
  /// The chat ID to display participants for
  final String chatId;

  const ParticipantsListSection({super.key, required this.chatId});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Using generated mock data for UI testing
    final participants = mockChatParticipantsList;

    if (participants.isEmpty) {
      return _buildEmptyState(context);
    }

    return ListView.separated(
      padding: EdgeInsets.all(16.w),
      itemCount: participants.length,
      separatorBuilder: (context, index) => SizedBox(height: 12.h),
      itemBuilder: (context, index) {
        final participant = participants[index];
        return ParticipantTile(
          participant: participant,
          participantName: _getParticipantName(participant.userId),
          participantInfo: _getParticipantInfo(participant.userId),
          isOnline: _isParticipantOnline(participant.userId),
          showRole: true,
          showActions: _canManageParticipant(participant),
          onTap: () => _onParticipantTap(context, participant),
          onActionSelected: (action) =>
              _onActionSelected(context, participant, action),
        );
      },
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Center(
      child: Padding(
        padding: EdgeInsets.all(32.w),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.people_outline,
              size: 64.sp,
              color: colorScheme.onSurface.withValues(alpha: 0.6),
            ),
            SizedBox(height: 16.h),
            Text(
              'No participants',
              style: theme.textTheme.titleMedium?.copyWith(
                color: colorScheme.onSurface,
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: 8.h),
            Text(
              'Add participants to start the conversation.',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: colorScheme.onSurface.withValues(alpha: 0.7),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  void _onParticipantTap(BuildContext context, ChatParticipant participant) {
    // TODO: Navigate to participant profile or show details
  }

  void _onActionSelected(
    BuildContext context,
    ChatParticipant participant,
    String action,
  ) {
    switch (action) {
      case 'view_profile':
        // TODO: Navigate to participant profile
        break;
      case 'make_admin':
        // TODO: Promote participant to admin
        break;
      case 'make_moderator':
        // TODO: Promote participant to moderator
        break;
      case 'remove':
        _showRemoveParticipantDialog(context, participant);
        break;
    }
  }

  void _showRemoveParticipantDialog(
    BuildContext context,
    ChatParticipant participant,
  ) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Remove Participant'),
        content: Text(
          'Are you sure you want to remove ${_getParticipantName(participant.userId)} from this chat?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              // TODO: Implement participant removal
            },
            style: TextButton.styleFrom(
              foregroundColor: Theme.of(context).colorScheme.error,
            ),
            child: const Text('Remove'),
          ),
        ],
      ),
    );
  }

  bool _canManageParticipant(ChatParticipant participant) {
    // TODO: Check current user's permissions
    return true; // Placeholder
  }

  String _getParticipantName(String userId) {
    // TODO: Get actual participant name from user data
    return 'User $userId';
  }

  String _getParticipantInfo(String userId) {
    // TODO: Get actual participant info (email, etc.)
    return 'user$<EMAIL>';
  }

  bool _isParticipantOnline(String userId) {
    // TODO: Get actual online status
    return userId.hashCode % 2 == 0; // Mock random online status
  }
}
