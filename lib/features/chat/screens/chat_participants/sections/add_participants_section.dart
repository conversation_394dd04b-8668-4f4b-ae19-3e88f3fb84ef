import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_symbols_icons/symbols.dart';

import '../../../../../core/widgets/buttons/custom_button.dart';
import '../../../widgets/participant_avatar.dart';

/// Section for adding new participants to a chat
class AddParticipantsSection extends ConsumerStatefulWidget {
  /// The chat ID to add participants to
  final String chatId;

  /// Callback when cancel is pressed
  final VoidCallback onCancel;

  const AddParticipantsSection({
    super.key,
    required this.chatId,
    required this.onCancel,
  });

  @override
  ConsumerState<AddParticipantsSection> createState() => _AddParticipantsSectionState();
}

class _AddParticipantsSectionState extends ConsumerState<AddParticipantsSection> {
  final TextEditingController _searchController = TextEditingController();
  final Set<String> _selectedUserIds = {};
  String _searchQuery = '';

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    // TODO: Replace with actual users from provider
    final availableUsers = _getAvailableUsers();
    final filteredUsers = _filterUsers(availableUsers, _searchQuery);

    return Column(
      children: [
        // Search bar
        Container(
          padding: EdgeInsets.all(16.w),
          child: TextField(
            controller: _searchController,
            onChanged: (value) {
              setState(() {
                _searchQuery = value;
              });
            },
            decoration: InputDecoration(
              hintText: 'Search users...',
              hintStyle: theme.textTheme.bodyMedium?.copyWith(
                color: colorScheme.onSurface.withValues(alpha: 0.6),
              ),
              prefixIcon: Icon(
                Symbols.search,
                color: colorScheme.onSurface.withValues(alpha: 0.6),
                size: 20.sp,
              ),
              suffixIcon: _searchQuery.isNotEmpty
                  ? IconButton(
                      onPressed: () {
                        _searchController.clear();
                        setState(() {
                          _searchQuery = '';
                        });
                      },
                      icon: Icon(
                        Symbols.close,
                        color: colorScheme.onSurface.withValues(alpha: 0.6),
                        size: 20.sp,
                      ),
                    )
                  : null,
              filled: true,
              fillColor: colorScheme.surfaceContainerHighest,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12.r),
                borderSide: BorderSide.none,
              ),
              contentPadding: EdgeInsets.symmetric(
                horizontal: 16.w,
                vertical: 12.h,
              ),
            ),
          ),
        ),

        // Selected users
        if (_selectedUserIds.isNotEmpty) ...[
          Container(
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Selected (${_selectedUserIds.length})',
                  style: theme.textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: colorScheme.onSurface,
                  ),
                ),
                SizedBox(height: 8.h),
                SizedBox(
                  height: 60.h,
                  child: ListView.separated(
                    scrollDirection: Axis.horizontal,
                    itemCount: _selectedUserIds.length,
                    separatorBuilder: (context, index) => SizedBox(width: 8.w),
                    itemBuilder: (context, index) {
                      final userId = _selectedUserIds.elementAt(index);
                      return _buildSelectedUserChip(userId);
                    },
                  ),
                ),
                SizedBox(height: 16.h),
              ],
            ),
          ),
        ],

        // Available users list
        Expanded(
          child: filteredUsers.isEmpty
              ? _buildEmptyState(context)
              : ListView.separated(
                  padding: EdgeInsets.symmetric(horizontal: 16.w),
                  itemCount: filteredUsers.length,
                  separatorBuilder: (context, index) => SizedBox(height: 8.h),
                  itemBuilder: (context, index) {
                    final user = filteredUsers[index];
                    final isSelected = _selectedUserIds.contains(user['id']);
                    return _buildUserTile(user, isSelected);
                  },
                ),
        ),

        // Action buttons
        Container(
          padding: EdgeInsets.all(16.w),
          child: Row(
            children: [
              Expanded(
                child: CustomButton.secondary(
                  text: 'Cancel',
                  onPressed: widget.onCancel,
                ),
              ),
              SizedBox(width: 12.w),
              Expanded(
                child: CustomButton.primary(
                  text: 'Add (${_selectedUserIds.length})',
                  onPressed: _selectedUserIds.isNotEmpty ? _onAddParticipants : null,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildSelectedUserChip(String userId) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final userName = _getUserName(userId);

    return Container(
      padding: EdgeInsets.all(4.w),
      decoration: BoxDecoration(
        color: colorScheme.primaryContainer,
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Column(
        children: [
          Stack(
            children: [
              ParticipantAvatar(
                participantId: userId,
                participantName: userName,
                size: 32.w,
              ),
              Positioned(
                right: -4.w,
                top: -4.h,
                child: GestureDetector(
                  onTap: () {
                    setState(() {
                      _selectedUserIds.remove(userId);
                    });
                  },
                  child: Container(
                    width: 16.w,
                    height: 16.w,
                    decoration: BoxDecoration(
                      color: colorScheme.error,
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      Symbols.close,
                      size: 10.sp,
                      color: colorScheme.onError,
                    ),
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: 4.h),
          Text(
            userName.split(' ').first,
            style: theme.textTheme.labelSmall?.copyWith(
              color: colorScheme.onPrimaryContainer,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  Widget _buildUserTile(Map<String, dynamic> user, bool isSelected) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return InkWell(
      onTap: () {
        setState(() {
          if (isSelected) {
            _selectedUserIds.remove(user['id']);
          } else {
            _selectedUserIds.add(user['id']);
          }
        });
      },
      borderRadius: BorderRadius.circular(12.r),
      child: Container(
        padding: EdgeInsets.all(12.w),
        decoration: BoxDecoration(
          color: isSelected 
              ? colorScheme.primaryContainer.withValues(alpha: 0.3)
              : colorScheme.surface,
          borderRadius: BorderRadius.circular(12.r),
          border: Border.all(
            color: isSelected 
                ? colorScheme.primary
                : colorScheme.outline.withValues(alpha: 0.2),
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Row(
          children: [
            ParticipantAvatar(
              participantId: user['id'],
              participantName: user['name'],
              size: 40.w,
            ),
            SizedBox(width: 12.w),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    user['name'],
                    style: theme.textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: colorScheme.onSurface,
                    ),
                  ),
                  if (user['email'] != null) ...[
                    SizedBox(height: 2.h),
                    Text(
                      user['email'],
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: colorScheme.onSurface.withValues(alpha: 0.7),
                      ),
                    ),
                  ],
                ],
              ),
            ),
            if (isSelected)
              Icon(
                Symbols.check_circle,
                color: colorScheme.primary,
                size: 24.sp,
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Center(
      child: Padding(
        padding: EdgeInsets.all(32.w),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Symbols.person_search,
              size: 64.sp,
              color: colorScheme.onSurface.withValues(alpha: 0.6),
            ),
            SizedBox(height: 16.h),
            Text(
              _searchQuery.isNotEmpty ? 'No users found' : 'No available users',
              style: theme.textTheme.titleMedium?.copyWith(
                color: colorScheme.onSurface,
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: 8.h),
            Text(
              _searchQuery.isNotEmpty 
                  ? 'Try adjusting your search terms.'
                  : 'All users are already in this chat.',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: colorScheme.onSurface.withValues(alpha: 0.7),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  void _onAddParticipants() {
    // TODO: Implement adding participants to chat
    widget.onCancel(); // Close the add participants view
  }

  List<Map<String, dynamic>> _filterUsers(List<Map<String, dynamic>> users, String query) {
    if (query.isEmpty) return users;
    
    return users.where((user) {
      final name = user['name'].toString().toLowerCase();
      final email = user['email']?.toString().toLowerCase() ?? '';
      final searchQuery = query.toLowerCase();
      
      return name.contains(searchQuery) || email.contains(searchQuery);
    }).toList();
  }

  String _getUserName(String userId) {
    // TODO: Get actual user name from user data
    return 'User $userId';
  }

  // TODO: Replace with actual data from provider
  List<Map<String, dynamic>> _getAvailableUsers() {
    return [
      // Mock users will be added when we implement mock data integration
    ];
  }
}
