import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_symbols_icons/symbols.dart';

/// Section for participant management actions
class ParticipantActionsSection extends StatelessWidget {
  /// The chat ID
  final String chatId;

  const ParticipantActionsSection({
    super.key,
    required this.chatId,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: colorScheme.surface,
        border: Border(
          top: BorderSide(
            color: colorScheme.outline.withValues(alpha: 0.2),
            width: 1,
          ),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Chat Actions',
            style: theme.textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.w600,
              color: colorScheme.onSurface,
            ),
          ),
          SizedBox(height: 12.h),
          
          // Action buttons
          Row(
            children: [
              Expanded(
                child: _ActionButton(
                  icon: Symbols.link,
                  label: 'Invite Link',
                  onTap: () => _onGenerateInviteLink(context),
                ),
              ),
              SizedBox(width: 12.w),
              Expanded(
                child: _ActionButton(
                  icon: Symbols.qr_code,
                  label: 'QR Code',
                  onTap: () => _onShowQRCode(context),
                ),
              ),
            ],
          ),
          
          SizedBox(height: 12.h),
          
          Row(
            children: [
              Expanded(
                child: _ActionButton(
                  icon: Symbols.group_add,
                  label: 'Bulk Add',
                  onTap: () => _onBulkAdd(context),
                ),
              ),
              SizedBox(width: 12.w),
              Expanded(
                child: _ActionButton(
                  icon: Symbols.download,
                  label: 'Export List',
                  onTap: () => _onExportParticipants(context),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  void _onGenerateInviteLink(BuildContext context) {
    // TODO: Generate and share invite link
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Invite link copied to clipboard'),
      ),
    );
  }

  void _onShowQRCode(BuildContext context) {
    // TODO: Show QR code for chat invitation
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Chat QR Code'),
        content: Container(
          width: 200.w,
          height: 200.w,
          decoration: BoxDecoration(
            color: Colors.grey[200],
            borderRadius: BorderRadius.circular(8.r),
          ),
          child: const Center(
            child: Text('QR Code\n(Coming Soon)'),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _onBulkAdd(BuildContext context) {
    // TODO: Show bulk add participants dialog
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Bulk Add Participants'),
        content: const Text('Add multiple participants at once using email addresses or usernames.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              // TODO: Implement bulk add functionality
            },
            child: const Text('Add'),
          ),
        ],
      ),
    );
  }

  void _onExportParticipants(BuildContext context) {
    // TODO: Export participants list
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Participants list exported'),
      ),
    );
  }
}

/// Individual action button widget
class _ActionButton extends StatelessWidget {
  final IconData icon;
  final String label;
  final VoidCallback onTap;

  const _ActionButton({
    required this.icon,
    required this.label,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12.r),
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 16.h, horizontal: 12.w),
        decoration: BoxDecoration(
          color: colorScheme.surfaceContainerHighest,
          borderRadius: BorderRadius.circular(12.r),
          border: Border.all(
            color: colorScheme.outline.withValues(alpha: 0.2),
            width: 1,
          ),
        ),
        child: Column(
          children: [
            Icon(
              icon,
              size: 24.sp,
              color: colorScheme.primary,
            ),
            SizedBox(height: 8.h),
            Text(
              label,
              style: theme.textTheme.labelMedium?.copyWith(
                color: colorScheme.onSurface,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
