import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_symbols_icons/symbols.dart';

import '../../../models/chat_model.dart';
import '../../../models/chat_settings.dart';
import '../../../enums/chat_type.dart';
import '../../../../debug/mock_data/generators/chat_mock_generator.dart';

/// Header section for chat detail screen showing chat info
class ChatHeaderSection extends StatelessWidget {
  /// The chat ID to display info for
  final String chatId;

  /// Callback when header is tapped
  final VoidCallback? onTap;

  const ChatHeaderSection({super.key, required this.chatId, this.onTap});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    // Using generated mock data for UI testing
    final chat = mockChatsList.firstWhere(
      (c) => c.id == chatId,
      orElse: () => mockChatsList.first,
    );

    return GestureDetector(
      onTap: onTap,
      child: Row(
        children: [
          // Chat avatar
          Container(
            width: 40.w,
            height: 40.w,
            decoration: BoxDecoration(
              color: colorScheme.primaryContainer,
              shape: BoxShape.circle,
            ),
            child: Icon(
              _getChatTypeIcon(chat.type),
              size: 20.sp,
              color: colorScheme.onPrimaryContainer,
            ),
          ),

          SizedBox(width: 12.w),

          // Chat info
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Chat title
                Text(
                  chat.title,
                  style: theme.textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: colorScheme.onSurface,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),

                SizedBox(height: 2.h),

                // Participant count and status
                Row(
                  children: [
                    Text(
                      '${chat.participantIds.length} participants',
                      style: theme.textTheme.labelSmall?.copyWith(
                        color: colorScheme.onSurface.withValues(alpha: 0.7),
                      ),
                    ),

                    // Online status indicator (placeholder)
                    if (chat.type == ChatType.oneToOne) ...[
                      SizedBox(width: 8.w),
                      Container(
                        width: 6.w,
                        height: 6.w,
                        decoration: BoxDecoration(
                          color: Colors.green,
                          shape: BoxShape.circle,
                        ),
                      ),
                      SizedBox(width: 4.w),
                      Text(
                        'Online',
                        style: theme.textTheme.labelSmall?.copyWith(
                          color: Colors.green,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ],
                ),
              ],
            ),
          ),

          // Chevron indicator
          if (onTap != null)
            Icon(
              Symbols.chevron_right,
              size: 16.sp,
              color: colorScheme.onSurface.withValues(alpha: 0.4),
            ),
        ],
      ),
    );
  }

  IconData _getChatTypeIcon(ChatType type) {
    switch (type) {
      case ChatType.oneToOne:
        return Symbols.person;
      case ChatType.groupChat:
        return Symbols.group;
      case ChatType.classroomDiscussion:
        return Symbols.school;
      case ChatType.studyGroup:
        return Symbols.groups;
      case ChatType.projectTeam:
        return Symbols.work_history;
      case ChatType.parentGroup:
        return Symbols.family_restroom;
      case ChatType.teacherCircle:
        return Symbols.supervisor_account;
    }
  }
}
