import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../models/message_model.dart';
import '../../../widgets/message_bubble.dart';
import '../../../../debug/mock_data/generators/chat_mock_generator.dart';
import 'empty_messages_section.dart';

/// Section displaying the list of messages in a chat
class MessagesListSection extends ConsumerWidget {
  /// The chat ID to display messages for
  final String chatId;

  /// Scroll controller for the messages list
  final ScrollController scrollController;

  const MessagesListSection({
    super.key,
    required this.chatId,
    required this.scrollController,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Using generated mock data for UI testing - filter by chatId
    final messages = mockMessagesList.where((m) => m.chatId == chatId).toList();

    if (messages.isEmpty) {
      return const EmptyMessagesSection();
    }

    return ListView.separated(
      controller: scrollController,
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
      itemCount: messages.length,
      separatorBuilder: (context, index) => SizedBox(height: 8.h),
      itemBuilder: (context, index) {
        final message = messages[index];
        final previousMessage = index > 0 ? messages[index - 1] : null;
        final nextMessage = index < messages.length - 1
            ? messages[index + 1]
            : null;

        // Check if we need to show date separator
        final showDateSeparator = _shouldShowDateSeparator(
          message,
          previousMessage,
        );

        return Column(
          children: [
            // Date separator
            if (showDateSeparator) ...[
              _buildDateSeparator(context, message.sentAt),
              SizedBox(height: 16.h),
            ],

            // Message bubble
            MessageBubble(
              message: message,
              showAvatar: _shouldShowAvatar(
                message,
                previousMessage,
                nextMessage,
              ),
              showTimestamp: _shouldShowTimestamp(message, nextMessage),
            ),
          ],
        );
      },
    );
  }

  Widget _buildDateSeparator(BuildContext context, DateTime date) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
      decoration: BoxDecoration(
        color: colorScheme.surfaceContainerHighest,
        borderRadius: BorderRadius.circular(16.r),
      ),
      child: Text(
        _formatDateSeparator(date),
        style: theme.textTheme.labelSmall?.copyWith(
          color: colorScheme.onSurface.withValues(alpha: 0.7),
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  bool _shouldShowDateSeparator(
    MessageModel message,
    MessageModel? previousMessage,
  ) {
    if (previousMessage == null) return true;

    final messageDate = DateTime(
      message.sentAt.year,
      message.sentAt.month,
      message.sentAt.day,
    );
    final previousDate = DateTime(
      previousMessage.sentAt.year,
      previousMessage.sentAt.month,
      previousMessage.sentAt.day,
    );

    return !messageDate.isAtSameMomentAs(previousDate);
  }

  bool _shouldShowAvatar(
    MessageModel message,
    MessageModel? previousMessage,
    MessageModel? nextMessage,
  ) {
    // Show avatar if it's the last message from this sender
    return nextMessage == null || nextMessage.senderId != message.senderId;
  }

  bool _shouldShowTimestamp(MessageModel message, MessageModel? nextMessage) {
    if (nextMessage == null) return true;

    // Show timestamp if next message is from different sender
    if (nextMessage.senderId != message.senderId) return true;

    // Show timestamp if there's a significant time gap (5+ minutes)
    final timeDifference = nextMessage.sentAt.difference(message.sentAt);
    return timeDifference.inMinutes >= 5;
  }

  String _formatDateSeparator(DateTime date) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(const Duration(days: 1));
    final messageDate = DateTime(date.year, date.month, date.day);

    if (messageDate.isAtSameMomentAs(today)) {
      return 'Today';
    } else if (messageDate.isAtSameMomentAs(yesterday)) {
      return 'Yesterday';
    } else {
      // Format as "Mon, Jan 15"
      const months = [
        'Jan',
        'Feb',
        'Mar',
        'Apr',
        'May',
        'Jun',
        'Jul',
        'Aug',
        'Sep',
        'Oct',
        'Nov',
        'Dec',
      ];
      const weekdays = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];

      final weekday = weekdays[date.weekday - 1];
      final month = months[date.month - 1];

      return '$weekday, $month ${date.day}';
    }
  }
}
