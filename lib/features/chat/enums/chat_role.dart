/// Enum representing different roles a user can have in a chat
enum ChatRole {
  /// Regular participant with basic messaging permissions
  participant,

  /// Moderator with enhanced permissions for content management
  moderator,

  /// Administrator with full control over the chat
  admin,

  /// Observer with read-only access (cannot send messages)
  observer,
}

/// Extension to provide additional functionality for ChatRole enum
extension ChatRoleExtension on ChatRole {
  /// Get a user-friendly display name for the chat role
  String get displayName {
    switch (this) {
      case ChatRole.participant:
        return 'Participant';
      case ChatRole.moderator:
        return 'Moderator';
      case ChatRole.admin:
        return 'Admin';
      case ChatRole.observer:
        return 'Observer';
    }
  }

  /// Get the string value for the chat role (for JSON serialization)
  String get value {
    switch (this) {
      case ChatRole.participant:
        return 'participant';
      case ChatRole.moderator:
        return 'moderator';
      case ChatRole.admin:
        return 'admin';
      case ChatRole.observer:
        return 'observer';
    }
  }

  /// Get a description for the chat role
  String get description {
    switch (this) {
      case ChatRole.participant:
        return 'Can send messages and participate in conversations';
      case ChatRole.moderator:
        return 'Can moderate content and manage participants';
      case ChatRole.admin:
        return 'Full control over chat settings and participants';
      case ChatRole.observer:
        return 'Can view messages but cannot participate';
    }
  }

  /// Check if this role can send messages
  bool get canSendMessages {
    switch (this) {
      case ChatRole.participant:
      case ChatRole.moderator:
      case ChatRole.admin:
        return true;
      case ChatRole.observer:
        return false;
    }
  }

  /// Check if this role can delete messages
  bool get canDeleteMessages {
    switch (this) {
      case ChatRole.moderator:
      case ChatRole.admin:
        return true;
      case ChatRole.participant:
      case ChatRole.observer:
        return false;
    }
  }

  /// Check if this role can add participants
  bool get canAddParticipants {
    switch (this) {
      case ChatRole.admin:
        return true;
      case ChatRole.participant:
      case ChatRole.moderator:
      case ChatRole.observer:
        return false;
    }
  }

  /// Check if this role can remove participants
  bool get canRemoveParticipants {
    switch (this) {
      case ChatRole.admin:
        return true;
      case ChatRole.moderator:
        return false; // Moderators can only mute/warn, not remove
      case ChatRole.participant:
      case ChatRole.observer:
        return false;
    }
  }

  /// Check if this role can change chat settings
  bool get canChangeSettings {
    switch (this) {
      case ChatRole.admin:
        return true;
      case ChatRole.participant:
      case ChatRole.moderator:
      case ChatRole.observer:
        return false;
    }
  }

  /// Check if this role can assign roles to other participants
  bool get canAssignRoles {
    switch (this) {
      case ChatRole.admin:
        return true;
      case ChatRole.participant:
      case ChatRole.moderator:
      case ChatRole.observer:
        return false;
    }
  }

  /// Check if this role can pin/unpin messages
  bool get canPinMessages {
    switch (this) {
      case ChatRole.moderator:
      case ChatRole.admin:
        return true;
      case ChatRole.participant:
      case ChatRole.observer:
        return false;
    }
  }

  /// Check if this role can mute participants
  bool get canMuteParticipants {
    switch (this) {
      case ChatRole.moderator:
      case ChatRole.admin:
        return true;
      case ChatRole.participant:
      case ChatRole.observer:
        return false;
    }
  }

  /// Get the hierarchy level of this role (higher number = more permissions)
  int get hierarchyLevel {
    switch (this) {
      case ChatRole.observer:
        return 0;
      case ChatRole.participant:
        return 1;
      case ChatRole.moderator:
        return 2;
      case ChatRole.admin:
        return 3;
    }
  }

  /// Check if this role has higher permissions than another role
  bool hasHigherPermissionsThan(ChatRole other) {
    return hierarchyLevel > other.hierarchyLevel;
  }

  /// Check if this role has equal or higher permissions than another role
  bool hasEqualOrHigherPermissionsThan(ChatRole other) {
    return hierarchyLevel >= other.hierarchyLevel;
  }

  /// Create ChatRole from string value
  static ChatRole fromString(String value) {
    switch (value.toLowerCase()) {
      case 'participant':
        return ChatRole.participant;
      case 'moderator':
        return ChatRole.moderator;
      case 'admin':
        return ChatRole.admin;
      case 'observer':
        return ChatRole.observer;
      default:
        return ChatRole.participant; // Default fallback
    }
  }
}
