/// Enum representing different types of messages in chats
enum MessageType {
  /// Regular text message
  text,

  /// Image attachment message
  image,

  /// File attachment message
  file,

  /// Voice recording message
  voice,

  /// Link/URL message with preview
  link,

  /// System-generated message (user joined, left, etc.)
  system,
}

/// Extension to provide additional functionality for MessageType enum
extension MessageTypeExtension on MessageType {
  /// Get a user-friendly display name for the message type
  String get displayName {
    switch (this) {
      case MessageType.text:
        return 'Text';
      case MessageType.image:
        return 'Image';
      case MessageType.file:
        return 'File';
      case MessageType.voice:
        return 'Voice';
      case MessageType.link:
        return 'Link';
      case MessageType.system:
        return 'System';
    }
  }

  /// Get the string value for the message type (for JSON serialization)
  String get value {
    switch (this) {
      case MessageType.text:
        return 'text';
      case MessageType.image:
        return 'image';
      case MessageType.file:
        return 'file';
      case MessageType.voice:
        return 'voice';
      case MessageType.link:
        return 'link';
      case MessageType.system:
        return 'system';
    }
  }

  /// Get icon name for the message type
  String get iconName {
    switch (this) {
      case MessageType.text:
        return 'chat_bubble';
      case MessageType.image:
        return 'image';
      case MessageType.file:
        return 'attach_file';
      case MessageType.voice:
        return 'mic';
      case MessageType.link:
        return 'link';
      case MessageType.system:
        return 'info';
    }
  }

  /// Check if this message type supports attachments
  bool get hasAttachment {
    switch (this) {
      case MessageType.image:
      case MessageType.file:
      case MessageType.voice:
        return true;
      case MessageType.text:
      case MessageType.link:
      case MessageType.system:
        return false;
    }
  }

  /// Check if this message type is user-generated (not system)
  bool get isUserGenerated {
    switch (this) {
      case MessageType.text:
      case MessageType.image:
      case MessageType.file:
      case MessageType.voice:
      case MessageType.link:
        return true;
      case MessageType.system:
        return false;
    }
  }

  /// Check if this message type supports editing
  bool get canEdit {
    switch (this) {
      case MessageType.text:
      case MessageType.link:
        return true;
      case MessageType.image:
      case MessageType.file:
      case MessageType.voice:
      case MessageType.system:
        return false;
    }
  }

  /// Check if this message type supports reactions
  bool get canReact {
    switch (this) {
      case MessageType.text:
      case MessageType.image:
      case MessageType.file:
      case MessageType.voice:
      case MessageType.link:
        return true;
      case MessageType.system:
        return false;
    }
  }

  /// Get file extension filter for this message type (for file picker)
  List<String>? get allowedExtensions {
    switch (this) {
      case MessageType.image:
        return ['jpg', 'jpeg', 'png', 'gif', 'webp'];
      case MessageType.file:
        return ['pdf', 'doc', 'docx', 'txt', 'ppt', 'pptx', 'xls', 'xlsx'];
      case MessageType.voice:
        return ['mp3', 'wav', 'm4a', 'aac'];
      case MessageType.text:
      case MessageType.link:
      case MessageType.system:
        return null;
    }
  }

  /// Get maximum file size in bytes for this message type
  int? get maxFileSize {
    switch (this) {
      case MessageType.image:
        return 10 * 1024 * 1024; // 10 MB
      case MessageType.file:
        return 50 * 1024 * 1024; // 50 MB
      case MessageType.voice:
        return 25 * 1024 * 1024; // 25 MB
      case MessageType.text:
      case MessageType.link:
      case MessageType.system:
        return null;
    }
  }

  /// Create MessageType from string value
  static MessageType fromString(String value) {
    switch (value.toLowerCase()) {
      case 'text':
        return MessageType.text;
      case 'image':
        return MessageType.image;
      case 'file':
        return MessageType.file;
      case 'voice':
        return MessageType.voice;
      case 'link':
        return MessageType.link;
      case 'system':
        return MessageType.system;
      default:
        return MessageType.text; // Default fallback
    }
  }
}
