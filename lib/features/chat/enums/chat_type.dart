/// Enum representing different types of chats in the system
enum ChatType {
  /// Direct one-to-one conversation between two users
  oneToOne,

  /// Group chat with multiple participants
  groupChat,

  /// Discussion thread within a classroom context
  classroomDiscussion,

  /// Student-created study group for collaborative learning
  studyGroup,

  /// Task-focused group for project collaboration
  projectTeam,

  /// Parent communication group for class-wide coordination
  parentGroup,

  /// Professional development group for teachers
  teacherCircle,
}

/// Extension to provide additional functionality for ChatType enum
extension ChatTypeExtension on ChatType {
  /// Get a user-friendly display name for the chat type
  String get displayName {
    switch (this) {
      case ChatType.oneToOne:
        return 'Direct Message';
      case ChatType.groupChat:
        return 'Group Chat';
      case ChatType.classroomDiscussion:
        return 'Classroom Discussion';
      case ChatType.studyGroup:
        return 'Study Group';
      case ChatType.projectTeam:
        return 'Project Team';
      case ChatType.parentGroup:
        return 'Parent Group';
      case ChatType.teacherCircle:
        return 'Teacher Circle';
    }
  }

  /// Get the string value for the chat type (for JSON serialization)
  String get value {
    switch (this) {
      case ChatType.oneToOne:
        return 'one_to_one';
      case ChatType.groupChat:
        return 'group_chat';
      case ChatType.classroomDiscussion:
        return 'classroom_discussion';
      case ChatType.studyGroup:
        return 'study_group';
      case ChatType.projectTeam:
        return 'project_team';
      case ChatType.parentGroup:
        return 'parent_group';
      case ChatType.teacherCircle:
        return 'teacher_circle';
    }
  }

  /// Get a description for the chat type
  String get description {
    switch (this) {
      case ChatType.oneToOne:
        return 'Private conversation between two users';
      case ChatType.groupChat:
        return 'Multi-participant group conversation';
      case ChatType.classroomDiscussion:
        return 'Academic discussion within classroom context';
      case ChatType.studyGroup:
        return 'Collaborative learning group for students';
      case ChatType.projectTeam:
        return 'Project-focused collaboration group';
      case ChatType.parentGroup:
        return 'Parent communication and coordination group';
      case ChatType.teacherCircle:
        return 'Professional development group for educators';
    }
  }

  /// Check if this chat type supports multiple participants
  bool get isGroupType {
    switch (this) {
      case ChatType.oneToOne:
        return false;
      case ChatType.groupChat:
      case ChatType.classroomDiscussion:
      case ChatType.studyGroup:
      case ChatType.projectTeam:
      case ChatType.parentGroup:
      case ChatType.teacherCircle:
        return true;
    }
  }

  /// Check if this chat type requires classroom context
  bool get requiresClassroom {
    switch (this) {
      case ChatType.classroomDiscussion:
        return true;
      case ChatType.oneToOne:
      case ChatType.groupChat:
      case ChatType.studyGroup:
      case ChatType.projectTeam:
      case ChatType.parentGroup:
      case ChatType.teacherCircle:
        return false;
    }
  }

  /// Get maximum number of participants for this chat type (null = unlimited)
  int? get maxParticipants {
    switch (this) {
      case ChatType.oneToOne:
        return 2;
      case ChatType.studyGroup:
        return 10; // Reasonable limit for study groups
      case ChatType.projectTeam:
        return 8; // Typical project team size
      case ChatType.groupChat:
      case ChatType.classroomDiscussion:
      case ChatType.parentGroup:
      case ChatType.teacherCircle:
        return null; // No specific limit
    }
  }

  /// Create ChatType from string value
  static ChatType fromString(String value) {
    switch (value.toLowerCase()) {
      case 'one_to_one':
        return ChatType.oneToOne;
      case 'group_chat':
        return ChatType.groupChat;
      case 'classroom_discussion':
        return ChatType.classroomDiscussion;
      case 'study_group':
        return ChatType.studyGroup;
      case 'project_team':
        return ChatType.projectTeam;
      case 'parent_group':
        return ChatType.parentGroup;
      case 'teacher_circle':
        return ChatType.teacherCircle;
      default:
        return ChatType.oneToOne; // Default fallback
    }
  }
}
