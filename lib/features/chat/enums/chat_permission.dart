/// Enum representing different chat permissions for users
enum ChatPermission {
  /// User can initiate chats with anyone
  canChatWithAnyone,

  /// User can only chat with teachers and parents
  canChatWithTeachersAndParents,

  /// User can only chat with students in same class
  canChatWithClassmates,

  /// User can only chat with specific approved users
  canChatWithApprovedOnly,

  /// User cannot initiate new chats (can only respond)
  canOnlyRespond,

  /// User is completely restricted from chatting
  noChatAccess,
}

/// Extension to provide additional functionality for ChatPermission enum
extension ChatPermissionExtension on ChatPermission {
  /// Get a user-friendly display name for the chat permission
  String get displayName {
    switch (this) {
      case ChatPermission.canChatWithAnyone:
        return 'Chat with Anyone';
      case ChatPermission.canChatWithTeachersAndParents:
        return 'Teachers & Parents Only';
      case ChatPermission.canChatWithClassmates:
        return 'Classmates Only';
      case ChatPermission.canChatWithApprovedOnly:
        return 'Approved Users Only';
      case ChatPermission.canOnlyRespond:
        return 'Respond Only';
      case ChatPermission.noChatAccess:
        return 'No Chat Access';
    }
  }

  /// Get the string value for the chat permission (for JSON serialization)
  String get value {
    switch (this) {
      case ChatPermission.canChatWithAnyone:
        return 'can_chat_with_anyone';
      case ChatPermission.canChatWithTeachersAndParents:
        return 'can_chat_with_teachers_and_parents';
      case ChatPermission.canChatWithClassmates:
        return 'can_chat_with_classmates';
      case ChatPermission.canChatWithApprovedOnly:
        return 'can_chat_with_approved_only';
      case ChatPermission.canOnlyRespond:
        return 'can_only_respond';
      case ChatPermission.noChatAccess:
        return 'no_chat_access';
    }
  }

  /// Get a description for the chat permission
  String get description {
    switch (this) {
      case ChatPermission.canChatWithAnyone:
        return 'Can start conversations with any user in the system';
      case ChatPermission.canChatWithTeachersAndParents:
        return 'Can only start conversations with teachers and parents';
      case ChatPermission.canChatWithClassmates:
        return 'Can only start conversations with students in the same class';
      case ChatPermission.canChatWithApprovedOnly:
        return 'Can only start conversations with pre-approved users';
      case ChatPermission.canOnlyRespond:
        return 'Cannot start new conversations, can only respond to existing ones';
      case ChatPermission.noChatAccess:
        return 'Cannot access chat features at all';
    }
  }

  /// Check if this permission allows initiating new chats
  bool get canInitiateChats {
    switch (this) {
      case ChatPermission.canChatWithAnyone:
      case ChatPermission.canChatWithTeachersAndParents:
      case ChatPermission.canChatWithClassmates:
      case ChatPermission.canChatWithApprovedOnly:
        return true;
      case ChatPermission.canOnlyRespond:
      case ChatPermission.noChatAccess:
        return false;
    }
  }

  /// Check if this permission allows responding to existing chats
  bool get canRespondToChats {
    switch (this) {
      case ChatPermission.canChatWithAnyone:
      case ChatPermission.canChatWithTeachersAndParents:
      case ChatPermission.canChatWithClassmates:
      case ChatPermission.canChatWithApprovedOnly:
      case ChatPermission.canOnlyRespond:
        return true;
      case ChatPermission.noChatAccess:
        return false;
    }
  }

  /// Check if this permission allows creating group chats
  bool get canCreateGroups {
    switch (this) {
      case ChatPermission.canChatWithAnyone:
      case ChatPermission.canChatWithClassmates:
        return true;
      case ChatPermission.canChatWithTeachersAndParents:
      case ChatPermission.canChatWithApprovedOnly:
      case ChatPermission.canOnlyRespond:
      case ChatPermission.noChatAccess:
        return false;
    }
  }

  /// Check if this permission allows joining group chats
  bool get canJoinGroups {
    switch (this) {
      case ChatPermission.canChatWithAnyone:
      case ChatPermission.canChatWithTeachersAndParents:
      case ChatPermission.canChatWithClassmates:
      case ChatPermission.canChatWithApprovedOnly:
        return true;
      case ChatPermission.canOnlyRespond:
      case ChatPermission.noChatAccess:
        return false;
    }
  }

  /// Check if this permission has any chat access
  bool get hasAnyAccess {
    switch (this) {
      case ChatPermission.noChatAccess:
        return false;
      case ChatPermission.canChatWithAnyone:
      case ChatPermission.canChatWithTeachersAndParents:
      case ChatPermission.canChatWithClassmates:
      case ChatPermission.canChatWithApprovedOnly:
      case ChatPermission.canOnlyRespond:
        return true;
    }
  }

  /// Get the restriction level (higher number = more restrictive)
  int get restrictionLevel {
    switch (this) {
      case ChatPermission.canChatWithAnyone:
        return 0;
      case ChatPermission.canChatWithClassmates:
        return 1;
      case ChatPermission.canChatWithTeachersAndParents:
        return 2;
      case ChatPermission.canChatWithApprovedOnly:
        return 3;
      case ChatPermission.canOnlyRespond:
        return 4;
      case ChatPermission.noChatAccess:
        return 5;
    }
  }

  /// Check if this permission is more restrictive than another
  bool isMoreRestrictiveThan(ChatPermission other) {
    return restrictionLevel > other.restrictionLevel;
  }

  /// Get the default permission for a user type
  static ChatPermission getDefaultForUserType(String userType) {
    switch (userType.toLowerCase()) {
      case 'student':
        return ChatPermission.canChatWithClassmates;
      case 'teacher':
        return ChatPermission.canChatWithAnyone;
      case 'parent':
        return ChatPermission.canChatWithTeachersAndParents;
      case 'admin':
        return ChatPermission.canChatWithAnyone;
      default:
        return ChatPermission.canOnlyRespond;
    }
  }

  /// Create ChatPermission from string value
  static ChatPermission fromString(String value) {
    switch (value.toLowerCase()) {
      case 'can_chat_with_anyone':
        return ChatPermission.canChatWithAnyone;
      case 'can_chat_with_teachers_and_parents':
        return ChatPermission.canChatWithTeachersAndParents;
      case 'can_chat_with_classmates':
        return ChatPermission.canChatWithClassmates;
      case 'can_chat_with_approved_only':
        return ChatPermission.canChatWithApprovedOnly;
      case 'can_only_respond':
        return ChatPermission.canOnlyRespond;
      case 'no_chat_access':
        return ChatPermission.noChatAccess;
      default:
        return ChatPermission.canOnlyRespond; // Safe default
    }
  }
}
