/// Enum representing the delivery and read status of messages
enum MessageStatus {
  /// Message is being sent (local state)
  sending,

  /// Message has been sent to the server
  sent,

  /// Message has been delivered to recipient(s)
  delivered,

  /// Message has been read by recipient(s)
  read,

  /// Message failed to send
  failed,
}

/// Extension to provide additional functionality for MessageStatus enum
extension MessageStatusExtension on MessageStatus {
  /// Get a user-friendly display name for the message status
  String get displayName {
    switch (this) {
      case MessageStatus.sending:
        return 'Sending';
      case MessageStatus.sent:
        return 'Sent';
      case MessageStatus.delivered:
        return 'Delivered';
      case MessageStatus.read:
        return 'Read';
      case MessageStatus.failed:
        return 'Failed';
    }
  }

  /// Get the string value for the message status (for JSON serialization)
  String get value {
    switch (this) {
      case MessageStatus.sending:
        return 'sending';
      case MessageStatus.sent:
        return 'sent';
      case MessageStatus.delivered:
        return 'delivered';
      case MessageStatus.read:
        return 'read';
      case MessageStatus.failed:
        return 'failed';
    }
  }

  /// Get icon name for the message status
  String get iconName {
    switch (this) {
      case MessageStatus.sending:
        return 'schedule';
      case MessageStatus.sent:
        return 'check';
      case MessageStatus.delivered:
        return 'done_all';
      case MessageStatus.read:
        return 'done_all';
      case MessageStatus.failed:
        return 'error';
    }
  }

  /// Get a description for the message status
  String get description {
    switch (this) {
      case MessageStatus.sending:
        return 'Message is being sent';
      case MessageStatus.sent:
        return 'Message has been sent';
      case MessageStatus.delivered:
        return 'Message has been delivered';
      case MessageStatus.read:
        return 'Message has been read';
      case MessageStatus.failed:
        return 'Message failed to send';
    }
  }

  /// Check if the message is in a final state (no more status updates expected)
  bool get isFinal {
    switch (this) {
      case MessageStatus.read:
      case MessageStatus.failed:
        return true;
      case MessageStatus.sending:
      case MessageStatus.sent:
      case MessageStatus.delivered:
        return false;
    }
  }

  /// Check if the message is in an error state
  bool get isError {
    switch (this) {
      case MessageStatus.failed:
        return true;
      case MessageStatus.sending:
      case MessageStatus.sent:
      case MessageStatus.delivered:
      case MessageStatus.read:
        return false;
    }
  }

  /// Check if the message is in a pending state (not yet delivered)
  bool get isPending {
    switch (this) {
      case MessageStatus.sending:
      case MessageStatus.sent:
        return true;
      case MessageStatus.delivered:
      case MessageStatus.read:
      case MessageStatus.failed:
        return false;
    }
  }

  /// Check if the message has been successfully delivered
  bool get isDelivered {
    switch (this) {
      case MessageStatus.delivered:
      case MessageStatus.read:
        return true;
      case MessageStatus.sending:
      case MessageStatus.sent:
      case MessageStatus.failed:
        return false;
    }
  }

  /// Check if the message has been read
  bool get isRead {
    switch (this) {
      case MessageStatus.read:
        return true;
      case MessageStatus.sending:
      case MessageStatus.sent:
      case MessageStatus.delivered:
      case MessageStatus.failed:
        return false;
    }
  }

  /// Get the next logical status in the message lifecycle
  MessageStatus? get nextStatus {
    switch (this) {
      case MessageStatus.sending:
        return MessageStatus.sent;
      case MessageStatus.sent:
        return MessageStatus.delivered;
      case MessageStatus.delivered:
        return MessageStatus.read;
      case MessageStatus.read:
      case MessageStatus.failed:
        return null; // Final states
    }
  }

  /// Get the hierarchy level of this status (higher number = more advanced)
  int get hierarchyLevel {
    switch (this) {
      case MessageStatus.failed:
        return -1; // Error state
      case MessageStatus.sending:
        return 0;
      case MessageStatus.sent:
        return 1;
      case MessageStatus.delivered:
        return 2;
      case MessageStatus.read:
        return 3;
    }
  }

  /// Check if this status is more advanced than another status
  bool isMoreAdvancedThan(MessageStatus other) {
    return hierarchyLevel > other.hierarchyLevel;
  }

  /// Create MessageStatus from string value
  static MessageStatus fromString(String value) {
    switch (value.toLowerCase()) {
      case 'sending':
        return MessageStatus.sending;
      case 'sent':
        return MessageStatus.sent;
      case 'delivered':
        return MessageStatus.delivered;
      case 'read':
        return MessageStatus.read;
      case 'failed':
        return MessageStatus.failed;
      default:
        return MessageStatus.sent; // Default fallback
    }
  }
}
