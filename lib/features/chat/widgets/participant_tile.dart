import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_symbols_icons/symbols.dart';

import '../models/chat_participant.dart';
import '../enums/chat_role.dart';
import 'participant_avatar.dart';

/// Tile widget for displaying participant information
class ParticipantTile extends StatelessWidget {
  /// The participant data to display
  final ChatParticipant participant;

  /// Participant name for display
  final String participantName;

  /// Participant email or additional info
  final String? participantInfo;

  /// Whether the participant is online
  final bool isOnline;

  /// Whether to show role badge
  final bool showRole;

  /// Whether to show actions menu
  final bool showActions;

  /// Callback when tile is tapped
  final VoidCallback? onTap;

  /// Callback when actions menu is tapped
  final Function(String action)? onActionSelected;

  const ParticipantTile({
    super.key,
    required this.participant,
    required this.participantName,
    this.participantInfo,
    this.isOnline = false,
    this.showRole = true,
    this.showActions = false,
    this.onTap,
    this.onActionSelected,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12.r),
      child: Container(
        padding: EdgeInsets.all(16.w),
        decoration: BoxDecoration(
          color: colorScheme.surface,
          borderRadius: BorderRadius.circular(12.r),
          border: Border.all(
            color: colorScheme.outline.withValues(alpha: 0.2),
            width: 1,
          ),
        ),
        child: Row(
          children: [
            // Participant avatar
            ParticipantAvatar(
              participantId: participant.userId,
              participantName: participantName,
              size: 48.w,
              showOnlineStatus: true,
              isOnline: isOnline,
            ),

            SizedBox(width: 12.w),

            // Participant info
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Name and role row
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          participantName,
                          style: theme.textTheme.titleSmall?.copyWith(
                            fontWeight: FontWeight.w600,
                            color: colorScheme.onSurface,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      if (showRole)
                        _buildRoleBadge(theme, colorScheme),
                    ],
                  ),

                  if (participantInfo != null) ...[
                    SizedBox(height: 4.h),
                    Text(
                      participantInfo!,
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: colorScheme.onSurface.withValues(alpha: 0.7),
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],

                  SizedBox(height: 4.h),

                  // Status and join date
                  Row(
                    children: [
                      // Online status
                      Container(
                        width: 6.w,
                        height: 6.w,
                        decoration: BoxDecoration(
                          color: isOnline ? Colors.green : Colors.grey,
                          shape: BoxShape.circle,
                        ),
                      ),
                      SizedBox(width: 6.w),
                      Text(
                        isOnline ? 'Online' : 'Offline',
                        style: theme.textTheme.labelSmall?.copyWith(
                          color: isOnline 
                              ? Colors.green 
                              : colorScheme.onSurface.withValues(alpha: 0.6),
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      SizedBox(width: 8.w),
                      Text(
                        '• Joined ${_formatJoinDate(participant.joinedAt)}',
                        style: theme.textTheme.labelSmall?.copyWith(
                          color: colorScheme.onSurface.withValues(alpha: 0.6),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),

            // Actions menu
            if (showActions)
              PopupMenuButton<String>(
                onSelected: onActionSelected,
                icon: Icon(
                  Symbols.more_vert,
                  color: colorScheme.onSurface.withValues(alpha: 0.6),
                  size: 20.sp,
                ),
                itemBuilder: (context) => [
                  PopupMenuItem(
                    value: 'view_profile',
                    child: Row(
                      children: [
                        Icon(Symbols.person, size: 16.sp),
                        SizedBox(width: 8.w),
                        const Text('View Profile'),
                      ],
                    ),
                  ),
                  if (participant.role != ChatRole.admin) ...[
                    PopupMenuItem(
                      value: 'make_admin',
                      child: Row(
                        children: [
                          Icon(Symbols.admin_panel_settings, size: 16.sp),
                          SizedBox(width: 8.w),
                          const Text('Make Admin'),
                        ],
                      ),
                    ),
                  ],
                  if (participant.role != ChatRole.moderator) ...[
                    PopupMenuItem(
                      value: 'make_moderator',
                      child: Row(
                        children: [
                          Icon(Symbols.shield, size: 16.sp),
                          SizedBox(width: 8.w),
                          const Text('Make Moderator'),
                        ],
                      ),
                    ),
                  ],
                  PopupMenuItem(
                    value: 'remove',
                    child: Row(
                      children: [
                        Icon(Symbols.person_remove, size: 16.sp, color: Colors.red),
                        SizedBox(width: 8.w),
                        Text('Remove', style: TextStyle(color: Colors.red)),
                      ],
                    ),
                  ),
                ],
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildRoleBadge(ThemeData theme, ColorScheme colorScheme) {
    Color badgeColor;
    String roleText;

    switch (participant.role) {
      case ChatRole.admin:
        badgeColor = colorScheme.error;
        roleText = 'Admin';
        break;
      case ChatRole.moderator:
        badgeColor = colorScheme.primary;
        roleText = 'Mod';
        break;
      case ChatRole.participant:
        return const SizedBox.shrink(); // Don't show badge for regular participants
      case ChatRole.observer:
        badgeColor = colorScheme.outline;
        roleText = 'Observer';
        break;
    }

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 2.h),
      decoration: BoxDecoration(
        color: badgeColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(
          color: badgeColor.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Text(
        roleText,
        style: theme.textTheme.labelSmall?.copyWith(
          color: badgeColor,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  String _formatJoinDate(DateTime joinDate) {
    final now = DateTime.now();
    final difference = now.difference(joinDate);

    if (difference.inDays > 30) {
      return '${(difference.inDays / 30).floor()}mo ago';
    } else if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else {
      return 'Just now';
    }
  }
}
