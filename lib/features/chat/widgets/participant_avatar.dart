import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_symbols_icons/symbols.dart';

/// Avatar widget for displaying chat participants
class ParticipantAvatar extends StatelessWidget {
  /// Participant ID
  final String participantId;

  /// Participant name for fallback display
  final String? participantName;

  /// Avatar image URL
  final String? imageUrl;

  /// Size of the avatar
  final double? size;

  /// Whether to show online status indicator
  final bool showOnlineStatus;

  /// Whether the participant is online
  final bool isOnline;

  /// Callback when avatar is tapped
  final VoidCallback? onTap;

  const ParticipantAvatar({
    super.key,
    required this.participantId,
    this.participantName,
    this.imageUrl,
    this.size,
    this.showOnlineStatus = false,
    this.isOnline = false,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final avatarSize = size ?? 40.w;

    return GestureDetector(
      onTap: onTap,
      child: Stack(
        children: [
          // Avatar container
          Container(
            width: avatarSize,
            height: avatarSize,
            decoration: BoxDecoration(
              color: colorScheme.primaryContainer,
              shape: BoxShape.circle,
              border: Border.all(
                color: colorScheme.outline.withValues(alpha: 0.2),
                width: 1,
              ),
            ),
            child: _buildAvatarContent(theme, colorScheme, avatarSize),
          ),

          // Online status indicator
          if (showOnlineStatus)
            Positioned(
              right: 0,
              bottom: 0,
              child: Container(
                width: avatarSize * 0.25,
                height: avatarSize * 0.25,
                decoration: BoxDecoration(
                  color: isOnline ? Colors.green : Colors.grey,
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: colorScheme.surface,
                    width: 2,
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildAvatarContent(ThemeData theme, ColorScheme colorScheme, double avatarSize) {
    if (imageUrl != null && imageUrl!.isNotEmpty) {
      // Show network image
      return ClipOval(
        child: Image.network(
          imageUrl!,
          width: avatarSize,
          height: avatarSize,
          fit: BoxFit.cover,
          errorBuilder: (context, error, stackTrace) {
            return _buildFallbackAvatar(theme, colorScheme, avatarSize);
          },
        ),
      );
    } else {
      return _buildFallbackAvatar(theme, colorScheme, avatarSize);
    }
  }

  Widget _buildFallbackAvatar(ThemeData theme, ColorScheme colorScheme, double avatarSize) {
    if (participantName != null && participantName!.isNotEmpty) {
      // Show initials
      final initials = _getInitials(participantName!);
      return Center(
        child: Text(
          initials,
          style: theme.textTheme.titleMedium?.copyWith(
            color: colorScheme.onPrimaryContainer,
            fontWeight: FontWeight.w600,
            fontSize: avatarSize * 0.4,
          ),
        ),
      );
    } else {
      // Show default icon
      return Icon(
        Symbols.person,
        size: avatarSize * 0.6,
        color: colorScheme.onPrimaryContainer,
      );
    }
  }

  String _getInitials(String name) {
    final words = name.trim().split(' ');
    if (words.length >= 2) {
      return '${words[0][0]}${words[1][0]}'.toUpperCase();
    } else if (words.isNotEmpty) {
      return words[0][0].toUpperCase();
    } else {
      return '?';
    }
  }
}
