import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_symbols_icons/symbols.dart';

/// Status indicator widget for chats
class ChatStatusIndicator extends StatelessWidget {
  /// Number of unread messages
  final int unreadCount;

  /// Whether the chat is muted
  final bool isMuted;

  /// Whether the chat is pinned
  final bool isPinned;

  /// Whether the chat is archived
  final bool isArchived;

  /// Size of the indicator
  final double? size;

  const ChatStatusIndicator({
    super.key,
    this.unreadCount = 0,
    this.isMuted = false,
    this.isPinned = false,
    this.isArchived = false,
    this.size,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final indicatorSize = size ?? 20.w;

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Pinned indicator
        if (isPinned) ...[
          Icon(
            Symbols.push_pin,
            size: indicatorSize,
            color: colorScheme.primary,
          ),
          SizedBox(width: 4.w),
        ],

        // Muted indicator
        if (isMuted) ...[
          Icon(
            Symbols.notifications_off,
            size: indicatorSize,
            color: colorScheme.onSurface.withValues(alpha: 0.6),
          ),
          SizedBox(width: 4.w),
        ],

        // Archived indicator
        if (isArchived) ...[
          Icon(
            Symbols.archive,
            size: indicatorSize,
            color: colorScheme.onSurface.withValues(alpha: 0.6),
          ),
          SizedBox(width: 4.w),
        ],

        // Unread count badge
        if (unreadCount > 0 && !isMuted)
          _buildUnreadBadge(theme, colorScheme),
      ],
    );
  }

  Widget _buildUnreadBadge(ThemeData theme, ColorScheme colorScheme) {
    final displayCount = unreadCount > 99 ? '99+' : unreadCount.toString();

    return Container(
      constraints: BoxConstraints(
        minWidth: 20.w,
        minHeight: 20.w,
      ),
      padding: EdgeInsets.symmetric(
        horizontal: 6.w,
        vertical: 2.h,
      ),
      decoration: BoxDecoration(
        color: colorScheme.error,
        borderRadius: BorderRadius.circular(10.r),
      ),
      child: Text(
        displayCount,
        style: theme.textTheme.labelSmall?.copyWith(
          color: colorScheme.onError,
          fontWeight: FontWeight.w600,
          fontSize: 10.sp,
        ),
        textAlign: TextAlign.center,
      ),
    );
  }
}
