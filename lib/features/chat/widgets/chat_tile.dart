import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_symbols_icons/symbols.dart';

import '../models/chat_model.dart';
import '../enums/chat_type.dart';
import '../enums/message_type.dart';
import 'chat_status_indicator.dart';

/// Tile widget for displaying chat information in lists
class ChatTile extends StatelessWidget {
  /// The chat data to display
  final ChatModel chat;

  /// Callback when tile is tapped
  final VoidCallback onTap;

  /// Whether to show in compact mode
  final bool isCompact;

  const ChatTile({
    super.key,
    required this.chat,
    required this.onTap,
    this.isCompact = false,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12.r),
      child: Container(
        padding: EdgeInsets.all(16.w),
        decoration: BoxDecoration(
          // color: colorScheme.surface,
          // borderRadius: BorderRadius.circular(12.r),
          // border: Border.all(
          //   color: colorScheme.outline.withValues(alpha: 0.2),
          //   width: 1,
          // ),
          border: Border(
            bottom: BorderSide(
              color: colorScheme.outline.withValues(alpha: 0.2),
              width: 1,
            ),
          ),
        ),
        child: Row(
          children: [
            // Chat avatar/icon
            _buildChatAvatar(colorScheme),

            SizedBox(width: 12.w),

            // Chat info
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Title and timestamp row
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          chat.title,
                          style: theme.textTheme.titleSmall?.copyWith(
                            fontWeight: FontWeight.w600,
                            color: colorScheme.onSurface,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),

                  if (!isCompact) ...[
                    SizedBox(height: 4.h),

                    // Last message preview
                    Text(
                      _getLastMessagePreview(),
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: colorScheme.onSurface.withValues(alpha: 0.7),
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),

                    SizedBox(height: 4.h),

                    // Chat type and participant count
                    Row(
                      children: [
                        _buildChatTypeChip(theme, colorScheme),
                        SizedBox(width: 8.w),
                        Text(
                          '${chat.participantIds.length} participants',
                          style: theme.textTheme.labelSmall?.copyWith(
                            color: colorScheme.onSurface.withValues(alpha: 0.6),
                          ),
                        ),
                      ],
                    ),
                  ],
                ],
              ),
            ),

            Column(
              children: [
                if (chat.lastMessageAt != null) ...[
                  SizedBox(width: 8.w),
                  Text(
                    _formatTimestamp(chat.lastMessageAt!),
                    style: theme.textTheme.labelSmall?.copyWith(
                      color: colorScheme.onSurface.withValues(alpha: 0.6),
                    ),
                  ),
                ],
                SizedBox(height: 8.h),
                // Status indicators (unread, muted, etc.)
                ChatStatusIndicator(
                  unreadCount: chat.unreadCount,
                  isMuted: false, // TODO: Add muted status to user preferences
                  isPinned: chat.settings.isPinned,
                  isArchived: chat.settings.isArchived,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildChatAvatar(ColorScheme colorScheme) {
    return Container(
      width: 48.w,
      height: 48.w,
      decoration: BoxDecoration(
        color: colorScheme.primaryContainer,
        shape: BoxShape.circle,
      ),
      child: Icon(
        _getChatTypeIcon(),
        size: 24.sp,
        color: colorScheme.onPrimaryContainer,
      ),
    );
  }

  Widget _buildChatTypeChip(ThemeData theme, ColorScheme colorScheme) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 2.h),
      decoration: BoxDecoration(
        color: colorScheme.secondaryContainer,
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Text(
        _getChatTypeLabel(),
        style: theme.textTheme.labelSmall?.copyWith(
          color: colorScheme.onSecondaryContainer,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  IconData _getChatTypeIcon() {
    switch (chat.type) {
      case ChatType.oneToOne:
        return Symbols.person;
      case ChatType.groupChat:
        return Symbols.group;
      case ChatType.classroomDiscussion:
        return Symbols.school;
      case ChatType.studyGroup:
        return Symbols.groups;
      case ChatType.projectTeam:
        return Symbols.work_history;
      case ChatType.parentGroup:
        return Symbols.family_restroom;
      case ChatType.teacherCircle:
        return Symbols.supervisor_account;
    }
  }

  String _getChatTypeLabel() {
    switch (chat.type) {
      case ChatType.oneToOne:
        return 'Direct';
      case ChatType.groupChat:
        return 'Group';
      case ChatType.classroomDiscussion:
        return 'Classroom';
      case ChatType.studyGroup:
        return 'Study';
      case ChatType.projectTeam:
        return 'Project';
      case ChatType.parentGroup:
        return 'Parents';
      case ChatType.teacherCircle:
        return 'Teachers';
    }
  }

  String _getLastMessagePreview() {
    if (chat.lastMessage != null) {
      final message = chat.lastMessage!;
      switch (message.type) {
        case MessageType.text:
          return message.content;
        case MessageType.image:
          return '📷 Image';
        case MessageType.file:
          return '📎 File';
        case MessageType.voice:
          return '🎤 Voice message';
        case MessageType.link:
          return '� Link';
        case MessageType.system:
          return message.content;
      }
    }
    return 'No messages yet';
  }

  String _formatTimestamp(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inDays > 0) {
      return '${difference.inDays}d';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m';
    } else {
      return 'now';
    }
  }
}
