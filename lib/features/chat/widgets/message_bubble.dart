import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_symbols_icons/symbols.dart';

import '../models/message_model.dart';
import '../enums/message_type.dart';
import '../enums/message_status.dart';

/// Widget for displaying individual message bubbles
class MessageBubble extends StatelessWidget {
  /// The message to display
  final MessageModel message;

  /// Whether to show the sender avatar
  final bool showAvatar;

  /// Whether to show the timestamp
  final bool showTimestamp;

  /// The current user ID for determining message alignment
  final String? currentUserId;

  const MessageBubble({
    super.key,
    required this.message,
    this.showAvatar = true,
    this.showTimestamp = true,
    this.currentUserId,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    
    // TODO: Get current user ID from auth provider
    final isOwnMessage = currentUserId != null && message.senderId == currentUserId;

    return Padding(
      padding: EdgeInsets.symmetric(vertical: 2.h),
      child: Row(
        mainAxisAlignment: isOwnMessage 
            ? MainAxisAlignment.end 
            : MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          // Avatar for other users' messages
          if (!isOwnMessage && showAvatar) ...[
            _buildAvatar(colorScheme),
            SizedBox(width: 8.w),
          ] else if (!isOwnMessage && !showAvatar) ...[
            SizedBox(width: 40.w), // Space for avatar alignment
          ],

          // Message bubble
          Flexible(
            child: Container(
              constraints: BoxConstraints(
                maxWidth: MediaQuery.of(context).size.width * 0.75,
              ),
              child: Column(
                crossAxisAlignment: isOwnMessage 
                    ? CrossAxisAlignment.end 
                    : CrossAxisAlignment.start,
                children: [
                  // Message content
                  Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: 16.w,
                      vertical: 12.h,
                    ),
                    decoration: BoxDecoration(
                      color: isOwnMessage 
                          ? colorScheme.primary 
                          : colorScheme.surfaceContainerHighest,
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(16.r),
                        topRight: Radius.circular(16.r),
                        bottomLeft: Radius.circular(isOwnMessage ? 16.r : 4.r),
                        bottomRight: Radius.circular(isOwnMessage ? 4.r : 16.r),
                      ),
                    ),
                    child: _buildMessageContent(theme, colorScheme, isOwnMessage),
                  ),

                  // Timestamp and status
                  if (showTimestamp) ...[
                    SizedBox(height: 4.h),
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          _formatTimestamp(message.sentAt),
                          style: theme.textTheme.labelSmall?.copyWith(
                            color: colorScheme.onSurface.withValues(alpha: 0.6),
                          ),
                        ),
                        
                        // Message status for own messages
                        if (isOwnMessage) ...[
                          SizedBox(width: 4.w),
                          _buildStatusIcon(colorScheme),
                        ],
                      ],
                    ),
                  ],
                ],
              ),
            ),
          ),

          // Avatar space for own messages
          if (isOwnMessage) ...[
            SizedBox(width: 8.w),
            SizedBox(width: 32.w), // Space for alignment
          ],
        ],
      ),
    );
  }

  Widget _buildAvatar(ColorScheme colorScheme) {
    return Container(
      width: 32.w,
      height: 32.w,
      decoration: BoxDecoration(
        color: colorScheme.primaryContainer,
        shape: BoxShape.circle,
      ),
      child: Icon(
        Symbols.person,
        size: 16.sp,
        color: colorScheme.onPrimaryContainer,
      ),
    );
  }

  Widget _buildMessageContent(ThemeData theme, ColorScheme colorScheme, bool isOwnMessage) {
    switch (message.type) {
      case MessageType.text:
        return Text(
          message.content,
          style: theme.textTheme.bodyMedium?.copyWith(
            color: isOwnMessage 
                ? colorScheme.onPrimary 
                : colorScheme.onSurface,
          ),
        );
      
      case MessageType.image:
        return _buildImageMessage(theme, colorScheme, isOwnMessage);
      
      case MessageType.file:
        return _buildFileMessage(theme, colorScheme, isOwnMessage);
      
      case MessageType.voice:
        return _buildVoiceMessage(theme, colorScheme, isOwnMessage);
      
      case MessageType.link:
        return _buildLinkMessage(theme, colorScheme, isOwnMessage);
      
      case MessageType.system:
        return _buildSystemMessage(theme, colorScheme);
    }
  }

  Widget _buildImageMessage(ThemeData theme, ColorScheme colorScheme, bool isOwnMessage) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          width: 200.w,
          height: 150.h,
          decoration: BoxDecoration(
            color: colorScheme.surfaceContainerHighest,
            borderRadius: BorderRadius.circular(8.r),
          ),
          child: Icon(
            Symbols.image,
            size: 40.sp,
            color: colorScheme.onSurface.withValues(alpha: 0.6),
          ),
        ),
        if (message.content.isNotEmpty) ...[
          SizedBox(height: 8.h),
          Text(
            message.content,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: isOwnMessage 
                  ? colorScheme.onPrimary 
                  : colorScheme.onSurface,
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildFileMessage(ThemeData theme, ColorScheme colorScheme, bool isOwnMessage) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(
          Symbols.attach_file,
          size: 20.sp,
          color: isOwnMessage 
              ? colorScheme.onPrimary 
              : colorScheme.onSurface,
        ),
        SizedBox(width: 8.w),
        Flexible(
          child: Text(
            message.content.isNotEmpty ? message.content : 'File attachment',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: isOwnMessage 
                  ? colorScheme.onPrimary 
                  : colorScheme.onSurface,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildVoiceMessage(ThemeData theme, ColorScheme colorScheme, bool isOwnMessage) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(
          Symbols.mic,
          size: 20.sp,
          color: isOwnMessage 
              ? colorScheme.onPrimary 
              : colorScheme.onSurface,
        ),
        SizedBox(width: 8.w),
        Text(
          'Voice message',
          style: theme.textTheme.bodyMedium?.copyWith(
            color: isOwnMessage 
                ? colorScheme.onPrimary 
                : colorScheme.onSurface,
          ),
        ),
      ],
    );
  }

  Widget _buildLinkMessage(ThemeData theme, ColorScheme colorScheme, bool isOwnMessage) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          message.content,
          style: theme.textTheme.bodyMedium?.copyWith(
            color: isOwnMessage 
                ? colorScheme.onPrimary 
                : colorScheme.primary,
            decoration: TextDecoration.underline,
          ),
        ),
      ],
    );
  }

  Widget _buildSystemMessage(ThemeData theme, ColorScheme colorScheme) {
    return Text(
      message.content,
      style: theme.textTheme.bodySmall?.copyWith(
        color: colorScheme.onSurface.withValues(alpha: 0.7),
        fontStyle: FontStyle.italic,
      ),
      textAlign: TextAlign.center,
    );
  }

  Widget _buildStatusIcon(ColorScheme colorScheme) {
    IconData icon;
    Color color;

    switch (message.status) {
      case MessageStatus.sending:
        icon = Symbols.schedule;
        color = colorScheme.onSurface.withValues(alpha: 0.6);
        break;
      case MessageStatus.sent:
        icon = Symbols.check;
        color = colorScheme.onSurface.withValues(alpha: 0.6);
        break;
      case MessageStatus.delivered:
        icon = Symbols.done_all;
        color = colorScheme.onSurface.withValues(alpha: 0.6);
        break;
      case MessageStatus.read:
        icon = Symbols.done_all;
        color = colorScheme.primary;
        break;
      case MessageStatus.failed:
        icon = Symbols.error;
        color = colorScheme.error;
        break;
    }

    return Icon(
      icon,
      size: 12.sp,
      color: color,
    );
  }

  String _formatTimestamp(DateTime timestamp) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final messageDate = DateTime(timestamp.year, timestamp.month, timestamp.day);

    if (messageDate.isAtSameMomentAs(today)) {
      // Today: show time only
      final hour = timestamp.hour.toString().padLeft(2, '0');
      final minute = timestamp.minute.toString().padLeft(2, '0');
      return '$hour:$minute';
    } else {
      // Other days: show date and time
      final day = timestamp.day.toString().padLeft(2, '0');
      final month = timestamp.month.toString().padLeft(2, '0');
      final hour = timestamp.hour.toString().padLeft(2, '0');
      final minute = timestamp.minute.toString().padLeft(2, '0');
      return '$day/$month $hour:$minute';
    }
  }
}
