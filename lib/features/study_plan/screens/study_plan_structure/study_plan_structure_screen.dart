import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_symbols_icons/symbols.dart';
import 'package:go_router/go_router.dart';

import '../../../../core/routes/app_routes.dart';

import '../../../../core/widgets/responsive/responsive_padding.dart';
import '../../../debug/mock_data/mock_data.dart';
import '../../models/study_plan_models.dart';
import '../study_plan_detail/sections/sections_list_section.dart';

/// Study plan structure screen showing hierarchical sections and tasks
class StudyPlanStructureScreen extends ConsumerStatefulWidget {
  final String studyPlanId;

  const StudyPlanStructureScreen({super.key, required this.studyPlanId});

  @override
  ConsumerState<StudyPlanStructureScreen> createState() =>
      _StudyPlanStructureScreenState();
}

class _StudyPlanStructureScreenState
    extends ConsumerState<StudyPlanStructureScreen> {
  // Mock data - will be replaced with actual providers
  StudyPlanModel? _mockStudyPlan;

  @override
  void initState() {
    super.initState();
    // Find the study plan by ID from mock data
    _mockStudyPlan = mockStudyPlansList.firstWhere(
      (plan) => plan.id == widget.studyPlanId,
      orElse: () =>
          mockStudyPlansList.first, // Fallback to first plan if ID not found
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: theme.colorScheme.surface,
      appBar: _buildAppBar(theme),
      body: _mockStudyPlan == null
          ? _buildLoadingState()
          : ResponsivePadding(
              mobile: EdgeInsets.all(16.w),
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Study plan info header
                    _buildPlanInfoHeader(theme),

                    SizedBox(height: 24.h),

                    // Hierarchical sections list
                    SectionsListSection(
                      studyPlan: _mockStudyPlan!,
                      onTaskTap: _onTaskTap,
                      onSectionTap: _onSectionTap,
                    ),

                    // Bottom padding
                    SizedBox(height: 32.h),
                  ],
                ),
              ),
            ),
    );
  }

  PreferredSizeWidget _buildAppBar(ThemeData theme) {
    return AppBar(
      title: Text(_mockStudyPlan?.title ?? 'Study Plan Structure'),
      elevation: 0,
      backgroundColor: theme.colorScheme.surface,
      actions: [
        IconButton(
          icon: const Icon(Symbols.edit),
          onPressed: () {
            context.pushNamed(
              RouteNames.studyPlanEdit,
              pathParameters: {'id': widget.studyPlanId},
            );
          },
        ),
        PopupMenuButton<String>(
          icon: const Icon(Symbols.more_vert),
          onSelected: _handleMenuAction,
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'add_section',
              child: Text('Add Section'),
            ),
            const PopupMenuItem(
              value: 'reorder',
              child: Text('Reorder Sections'),
            ),
            const PopupMenuItem(
              value: 'export',
              child: Text('Export Structure'),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildPlanInfoHeader(ThemeData theme) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(
          color: theme.colorScheme.outline.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Symbols.account_tree,
                color: theme.colorScheme.primary,
                size: 24.sp,
              ),
              SizedBox(width: 8.w),
              Text(
                'Plan Structure',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          SizedBox(height: 8.h),
          Text(
            'Manage sections, tasks, and learning objectives for this study plan.',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
          if (_mockStudyPlan != null) ...[
            SizedBox(height: 12.h),
            Row(
              children: [
                _buildInfoChip(
                  theme,
                  '${_mockStudyPlan!.sections.length} Sections',
                  Symbols.folder,
                ),
                SizedBox(width: 8.w),
                _buildInfoChip(
                  theme,
                  '${_mockStudyPlan!.sections.fold<int>(0, (sum, section) => sum + section.tasks.length)} Tasks',
                  Symbols.task_alt,
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildInfoChip(ThemeData theme, String label, IconData icon) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
      decoration: BoxDecoration(
        color: theme.colorScheme.primary.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 16.sp, color: theme.colorScheme.primary),
          SizedBox(width: 4.w),
          Text(
            label,
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.primary,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const CircularProgressIndicator(),
          SizedBox(height: 16.h),
          Text(
            'Loading study plan structure...',
            style: TextStyle(fontSize: 16.sp, color: Colors.grey[600]),
          ),
        ],
      ),
    );
  }

  // Event handlers
  void _onTaskTap(StudyPlanTask task) {
    // TODO: Navigate to task detail or show task actions
    debugPrint('Tapped task: ${task.title}');
  }

  void _onSectionTap(StudyPlanSection section) {
    // TODO: Navigate to section detail or show section actions
    debugPrint('Tapped section: ${section.title}');
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'add_section':
        // TODO: Add new section
        debugPrint('Add section to: ${widget.studyPlanId}');
        break;
      case 'reorder':
        // TODO: Reorder sections
        debugPrint('Reorder sections: ${widget.studyPlanId}');
        break;
      case 'export':
        // TODO: Export structure
        debugPrint('Export structure: ${widget.studyPlanId}');
        break;
    }
  }
}
