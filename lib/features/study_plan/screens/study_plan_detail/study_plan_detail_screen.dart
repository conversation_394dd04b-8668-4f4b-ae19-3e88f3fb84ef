import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_symbols_icons/symbols.dart';
import 'package:go_router/go_router.dart';

import '../../../../core/routes/app_routes.dart';
import '../../../../core/widgets/responsive/responsive_padding.dart';
import '../../controllers/study_plan_controller.dart';
import '../../controllers/study_plan_crud_controller.dart';
import '../../models/study_plan_models.dart';
import 'sections/study_plan_header_section.dart';
import 'sections/progress_visualization_section.dart';
import 'sections/todays_tasks_section.dart';
import 'sections/weekly_goals_section.dart';

/// Comprehensive study plan detail view with hierarchical sections and progress
class StudyPlanDetailScreen extends ConsumerStatefulWidget {
  final String studyPlanId;

  const StudyPlanDetailScreen({super.key, required this.studyPlanId});

  @override
  ConsumerState<StudyPlanDetailScreen> createState() =>
      _StudyPlanDetailScreenState();
}

class _StudyPlanDetailScreenState extends ConsumerState<StudyPlanDetailScreen> {
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final studyPlanAsync = ref.watch(studyPlanByIdProvider(widget.studyPlanId));

    return Scaffold(
      backgroundColor: theme.colorScheme.surface,
      appBar: _buildAppBar(theme, studyPlanAsync.value),
      body: studyPlanAsync.when(
        data: (studyPlan) {
          if (studyPlan == null) {
            return _buildNotFoundState();
          }
          return ResponsivePadding(
            mobile: EdgeInsets.all(16.w),
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Study plan header section
                  StudyPlanHeaderSection(studyPlan: studyPlan),

                  SizedBox(height: 32.h),

                  // Progress visualization section
                  ProgressVisualizationSection(studyPlan: studyPlan),

                  SizedBox(height: 32.h),

                  // Today's tasks section
                  TodaysTasksSection(
                    studyPlan: studyPlan,
                    onTaskTap: _onTaskTap,
                  ),

                  SizedBox(height: 24.h),

                  // Weekly goals section
                  WeeklyGoalsSection(studyPlan: studyPlan, onDayTap: _onDayTap),

                  SizedBox(height: 24.h),

                  // Structure navigation button
                  _buildStructureButton(),

                  // Bottom padding
                  SizedBox(height: 32.h),
                ],
              ),
            ),
          );
        },
        loading: () => _buildLoadingState(),
        error: (error, stackTrace) => _buildErrorState(error),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar(ThemeData theme, StudyPlanModel? studyPlan) {
    return AppBar(
      title: Text(studyPlan?.title ?? 'Study Plan Details'),
      elevation: 0,
      backgroundColor: theme.colorScheme.surface,
      actions: [
        IconButton(
          icon: const Icon(Symbols.edit),
          onPressed: () {
            context.pushNamed(
              RouteNames.studyPlanEdit,
              pathParameters: {'id': widget.studyPlanId},
            );
          },
        ),
        IconButton(
          icon: const Icon(Symbols.share),
          onPressed: () {
            // TODO: Implement share functionality
            debugPrint('Share study plan: ${widget.studyPlanId}');
          },
        ),
        PopupMenuButton<String>(
          icon: const Icon(Symbols.more_vert),
          onSelected: _handleMenuAction,
          itemBuilder: (context) => [
            const PopupMenuItem(value: 'duplicate', child: Text('Duplicate')),
            const PopupMenuItem(value: 'export', child: Text('Export')),
            const PopupMenuItem(
              value: 'progress',
              child: Text('View Progress'),
            ),
            const PopupMenuItem(value: 'archive', child: Text('Archive')),
          ],
        ),
      ],
    );
  }

  Widget _buildStructureButton() {
    final theme = Theme.of(context);

    return SizedBox(
      width: double.infinity,
      child: OutlinedButton.icon(
        onPressed: () {
          context.pushNamed(
            RouteNames.studyPlanStructure,
            pathParameters: {'id': widget.studyPlanId},
          );
        },
        icon: const Icon(Symbols.account_tree),
        label: const Text('View Plan Structure'),
        style: OutlinedButton.styleFrom(
          padding: EdgeInsets.symmetric(vertical: 16.h),
          side: BorderSide(color: theme.colorScheme.outline),
        ),
      ),
    );
  }

  Widget _buildLoadingState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const CircularProgressIndicator(),
          SizedBox(height: 16.h),
          Text(
            'Loading study plan...',
            style: TextStyle(fontSize: 16.sp, color: Colors.grey[600]),
          ),
          SizedBox(height: 8.h),
          Text(
            'Plan ID: ${widget.studyPlanId}',
            style: TextStyle(fontSize: 14.sp, color: Colors.grey[500]),
          ),
        ],
      ),
    );
  }

  Widget _buildNotFoundState() {
    final theme = Theme.of(context);
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Symbols.error, size: 48.sp, color: theme.colorScheme.error),
          SizedBox(height: 16.h),
          Text('Study Plan Not Found', style: theme.textTheme.titleMedium),
          SizedBox(height: 8.h),
          Text(
            'The requested study plan could not be found.',
            style: theme.textTheme.bodySmall,
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 16.h),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Go Back'),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(Object error) {
    final theme = Theme.of(context);
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Symbols.error, size: 48.sp, color: theme.colorScheme.error),
          SizedBox(height: 16.h),
          Text('Failed to Load Study Plan', style: theme.textTheme.titleMedium),
          SizedBox(height: 8.h),
          Text(
            error.toString(),
            style: theme.textTheme.bodySmall,
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 16.h),
          ElevatedButton(
            onPressed: () =>
                ref.invalidate(studyPlanByIdProvider(widget.studyPlanId)),
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  // Event handlers
  void _onTaskTap(StudyPlanTask task) {
    // TODO: Navigate to task detail or show task actions
    debugPrint('Tapped task: ${task.title}');
  }

  void _onDayTap(DateTime date) {
    // TODO: Navigate to day view or show day details
    debugPrint('Tapped day: ${date.toString()}');
  }

  void _handleMenuAction(String action) async {
    final crudHelper = ref.read(studyPlanCrudHelperProvider);

    switch (action) {
      case 'duplicate':
        final studyPlan = ref
            .read(studyPlanByIdProvider(widget.studyPlanId))
            .value;
        if (studyPlan != null) {
          final success = await crudHelper.duplicateStudyPlan(
            studyPlan.id,
            '${studyPlan.title} (Copy)',
          );
          if (success != null && mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Study plan duplicated: ${success.title}'),
              ),
            );
          } else if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('Failed to duplicate study plan')),
            );
          }
        }
        break;
      case 'export':
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Export functionality coming soon')),
        );
        break;
      case 'progress':
        context.pushNamed(
          RouteNames.studyPlanProgress,
          pathParameters: {'id': widget.studyPlanId},
        );
        break;
      case 'archive':
        final success = await crudHelper.archiveStudyPlan(widget.studyPlanId);
        if (success && mounted) {
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(const SnackBar(content: Text('Study plan archived')));
          Navigator.of(context).pop(); // Go back after archiving
        } else if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Failed to archive study plan')),
          );
        }
        break;
    }
  }
}
