import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_symbols_icons/symbols.dart';

import '../../../models/study_plan_models.dart';

/// Section displaying weekly goals with 7-day calendar view
class WeeklyGoalsSection extends ConsumerWidget {
  final StudyPlanModel studyPlan;
  final Function(DateTime date)? onDayTap;

  const WeeklyGoalsSection({super.key, required this.studyPlan, this.onDayTap});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final weekDays = _getWeekDays();
    final weeklyGoals = _getWeeklyGoals();

    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(
          color: theme.colorScheme.outline.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              Icon(
                Symbols.calendar_view_week,
                color: theme.colorScheme.primary,
                size: 24.sp,
              ),
              SizedBox(width: 8.w),
              Text(
                'This Week\'s Goals',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const Spacer(),
              Text(
                '${_getCompletedGoalsCount()} / ${weeklyGoals.length}',
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.primary,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          SizedBox(height: 16.h),

          // 7-day calendar
          _buildWeekCalendar(theme, weekDays),

          SizedBox(height: 16.h),

          // Weekly goals list
          if (weeklyGoals.isEmpty)
            _buildEmptyState(theme)
          else
            ...weeklyGoals.map((goal) => _buildGoalItem(theme, goal)),
        ],
      ),
    );
  }

  Widget _buildWeekCalendar(ThemeData theme, List<DateTime> weekDays) {
    final today = DateTime.now();

    return Container(
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(
          color: theme.colorScheme.outline.withValues(alpha: 0.2),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: weekDays.map((day) {
          final isToday = _isSameDay(day, today);
          final hasGoals = _hasGoalsOnDay(day);
          final dayName = _getDayName(day.weekday);

          return GestureDetector(
            onTap: onDayTap != null ? () => onDayTap!(day) : null,
            child: Container(
              width: 36.w,
              height: 48.h,
              decoration: BoxDecoration(
                color: isToday
                    ? theme.colorScheme.primary
                    : hasGoals
                    ? theme.colorScheme.primary.withValues(alpha: 0.1)
                    : Colors.transparent,
                borderRadius: BorderRadius.circular(8.r),
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    dayName,
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: isToday
                          ? theme.colorScheme.onPrimary
                          : theme.colorScheme.onSurfaceVariant,
                      fontSize: 10.sp,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  SizedBox(height: 2.h),
                  Text(
                    day.day.toString(),
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: isToday
                          ? theme.colorScheme.onPrimary
                          : theme.colorScheme.onSurface,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  if (hasGoals && !isToday) ...[
                    SizedBox(height: 2.h),
                    Container(
                      width: 4.w,
                      height: 4.h,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: theme.colorScheme.primary,
                      ),
                    ),
                  ],
                ],
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildEmptyState(ThemeData theme) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 24.h),
      child: Column(
        children: [
          Icon(
            Symbols.flag,
            size: 48.sp,
            color: theme.colorScheme.onSurfaceVariant.withValues(alpha: 0.5),
          ),
          SizedBox(height: 12.h),
          Text(
            'No goals set for this week',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            'Set weekly milestones to track progress',
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurfaceVariant.withValues(alpha: 0.7),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGoalItem(ThemeData theme, StudyPlanMilestone goal) {
    final isCompleted = goal.isCompleted;
    final isOverdue =
        goal.targetDate != null &&
        goal.targetDate!.isBefore(DateTime.now()) &&
        !isCompleted;

    return Container(
      margin: EdgeInsets.only(bottom: 12.h),
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(
          color: isOverdue
              ? theme.colorScheme.error.withValues(alpha: 0.3)
              : theme.colorScheme.outline.withValues(alpha: 0.2),
        ),
      ),
      child: Row(
        children: [
          // Status indicator
          Container(
            width: 20.w,
            height: 20.h,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: isCompleted
                  ? theme.colorScheme.primary
                  : Colors.transparent,
              border: Border.all(
                color: isCompleted
                    ? theme.colorScheme.primary
                    : theme.colorScheme.outline,
                width: 2,
              ),
            ),
            child: isCompleted
                ? Icon(
                    Symbols.check,
                    size: 12.sp,
                    color: theme.colorScheme.onPrimary,
                  )
                : null,
          ),
          SizedBox(width: 12.w),

          // Goal content
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  goal.title,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                    decoration: isCompleted ? TextDecoration.lineThrough : null,
                    color: isCompleted
                        ? theme.colorScheme.onSurfaceVariant
                        : null,
                  ),
                ),
                if (goal.description?.isNotEmpty == true) ...[
                  SizedBox(height: 4.h),
                  Text(
                    goal.description!,
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
                SizedBox(height: 8.h),
                if (goal.targetDate != null)
                  _buildTargetDateChip(theme, goal.targetDate!, isOverdue),
              ],
            ),
          ),

          // Goal icon
          Icon(
            Symbols.flag,
            size: 20.sp,
            color: isCompleted
                ? theme.colorScheme.primary
                : theme.colorScheme.onSurfaceVariant,
          ),
        ],
      ),
    );
  }

  Widget _buildTargetDateChip(
    ThemeData theme,
    DateTime targetDate,
    bool isOverdue,
  ) {
    final dayName = _getDayName(targetDate.weekday);
    final dateString = '$dayName ${targetDate.day}';

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 2.h),
      decoration: BoxDecoration(
        color: isOverdue
            ? theme.colorScheme.error.withValues(alpha: 0.1)
            : theme.colorScheme.primary.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(4.r),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Symbols.calendar_today,
            size: 10.sp,
            color: isOverdue
                ? theme.colorScheme.error
                : theme.colorScheme.primary,
          ),
          SizedBox(width: 2.w),
          Text(
            dateString,
            style: theme.textTheme.bodySmall?.copyWith(
              color: isOverdue
                  ? theme.colorScheme.error
                  : theme.colorScheme.primary,
              fontSize: 10.sp,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  List<DateTime> _getWeekDays() {
    final now = DateTime.now();
    final startOfWeek = now.subtract(Duration(days: now.weekday - 1));

    return List.generate(
      7,
      (index) => DateTime(
        startOfWeek.year,
        startOfWeek.month,
        startOfWeek.day + index,
      ),
    );
  }

  List<StudyPlanMilestone> _getWeeklyGoals() {
    final weekDays = _getWeekDays();
    final weekStart = weekDays.first;
    final weekEnd = weekDays.last.add(const Duration(days: 1));

    return studyPlan.sections
        .expand((section) => section.milestones)
        .where(
          (milestone) =>
              milestone.targetDate != null &&
              milestone.targetDate!.isAfter(weekStart) &&
              milestone.targetDate!.isBefore(weekEnd),
        )
        .toList()
      ..sort((a, b) => a.targetDate!.compareTo(b.targetDate!));
  }

  int _getCompletedGoalsCount() {
    return _getWeeklyGoals().where((goal) => goal.isCompleted).length;
  }

  bool _hasGoalsOnDay(DateTime day) {
    final dayStart = DateTime(day.year, day.month, day.day);
    final dayEnd = dayStart.add(const Duration(days: 1));

    return studyPlan.sections
        .expand((section) => section.milestones)
        .any(
          (milestone) =>
              milestone.targetDate != null &&
              milestone.targetDate!.isAfter(dayStart) &&
              milestone.targetDate!.isBefore(dayEnd),
        );
  }

  bool _isSameDay(DateTime a, DateTime b) {
    return a.year == b.year && a.month == b.month && a.day == b.day;
  }

  String _getDayName(int weekday) {
    const days = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
    return days[weekday - 1];
  }
}
