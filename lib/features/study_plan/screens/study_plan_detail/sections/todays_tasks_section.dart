import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_symbols_icons/symbols.dart';

import '../../../models/study_plan_models.dart';
import '../../../enums/study_plan_enums.dart';

/// Section displaying today's tasks from the study plan
class TodaysTasksSection extends ConsumerWidget {
  final StudyPlanModel studyPlan;
  final Function(StudyPlanTask task)? onTaskTap;

  const TodaysTasksSection({
    super.key,
    required this.studyPlan,
    this.onTaskTap,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final todaysTasks = _getTodaysTasks();

    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(
          color: theme.colorScheme.outline.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              Icon(
                Symbols.today,
                color: theme.colorScheme.primary,
                size: 24.sp,
              ),
              SizedBox(width: 8.w),
              Text(
                'Today\'s Tasks',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const Spacer(),
              if (todaysTasks.isNotEmpty)
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
                  decoration: BoxDecoration(
                    color: theme.colorScheme.primary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12.r),
                  ),
                  child: Text(
                    '${todaysTasks.length}',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.primary,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
            ],
          ),
          SizedBox(height: 16.h),

          // Tasks list
          if (todaysTasks.isEmpty)
            _buildEmptyState(theme)
          else
            ...todaysTasks.map((task) => _buildTaskItem(theme, task)),
        ],
      ),
    );
  }

  Widget _buildEmptyState(ThemeData theme) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 24.h),
      child: Column(
        children: [
          Icon(
            Symbols.task_alt,
            size: 48.sp,
            color: theme.colorScheme.onSurfaceVariant.withValues(alpha: 0.5),
          ),
          SizedBox(height: 12.h),
          Text(
            'No tasks scheduled for today',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            'Great job staying on track!',
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurfaceVariant.withValues(alpha: 0.7),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTaskItem(ThemeData theme, StudyPlanTask task) {
    final isCompleted = task.status == TaskStatus.completed;
    final isOverdue =
        task.dueDate != null &&
        task.dueDate!.isBefore(DateTime.now()) &&
        !isCompleted;

    return Container(
      margin: EdgeInsets.only(bottom: 12.h),
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(
          color: isOverdue
              ? theme.colorScheme.error.withValues(alpha: 0.3)
              : theme.colorScheme.outline.withValues(alpha: 0.2),
        ),
      ),
      child: InkWell(
        onTap: onTaskTap != null ? () => onTaskTap!(task) : null,
        borderRadius: BorderRadius.circular(8.r),
        child: Row(
          children: [
            // Status indicator
            Container(
              width: 20.w,
              height: 20.h,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: isCompleted
                    ? theme.colorScheme.primary
                    : Colors.transparent,
                border: Border.all(
                  color: isCompleted
                      ? theme.colorScheme.primary
                      : theme.colorScheme.outline,
                  width: 2,
                ),
              ),
              child: isCompleted
                  ? Icon(
                      Symbols.check,
                      size: 12.sp,
                      color: theme.colorScheme.onPrimary,
                    )
                  : null,
            ),
            SizedBox(width: 12.w),

            // Task content
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    task.title,
                    style: theme.textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w500,
                      decoration: isCompleted
                          ? TextDecoration.lineThrough
                          : null,
                      color: isCompleted
                          ? theme.colorScheme.onSurfaceVariant
                          : null,
                    ),
                  ),
                  if (task.description?.isNotEmpty == true) ...[
                    SizedBox(height: 4.h),
                    Text(
                      task.description!,
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.onSurfaceVariant,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                  SizedBox(height: 8.h),
                  Row(
                    children: [
                      // Priority indicator
                      _buildPriorityChip(theme, task.priority),
                      SizedBox(width: 8.w),
                      // Due time
                      if (task.dueDate != null)
                        _buildTimeChip(theme, task.dueDate!, isOverdue),
                    ],
                  ),
                ],
              ),
            ),

            // Progress indicator
            if (task.progressPercentage > 0)
              SizedBox(
                width: 40.w,
                height: 40.h,
                child: CircularProgressIndicator(
                  value: task.progressPercentage / 100,
                  strokeWidth: 3,
                  backgroundColor: theme.colorScheme.outline.withValues(
                    alpha: 0.2,
                  ),
                  valueColor: AlwaysStoppedAnimation<Color>(
                    theme.colorScheme.primary,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildPriorityChip(ThemeData theme, TaskPriority priority) {
    final color = switch (priority) {
      TaskPriority.low => Colors.green,
      TaskPriority.normal => Colors.blue,
      TaskPriority.high => Colors.orange,
      TaskPriority.urgent => Colors.red,
    };

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 2.h),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(4.r),
      ),
      child: Text(
        priority.displayName,
        style: theme.textTheme.bodySmall?.copyWith(
          color: color,
          fontSize: 10.sp,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Widget _buildTimeChip(ThemeData theme, DateTime dueDate, bool isOverdue) {
    final timeString =
        '${dueDate.hour.toString().padLeft(2, '0')}:${dueDate.minute.toString().padLeft(2, '0')}';

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 2.h),
      decoration: BoxDecoration(
        color: isOverdue
            ? theme.colorScheme.error.withValues(alpha: 0.1)
            : theme.colorScheme.primary.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(4.r),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Symbols.schedule,
            size: 10.sp,
            color: isOverdue
                ? theme.colorScheme.error
                : theme.colorScheme.primary,
          ),
          SizedBox(width: 2.w),
          Text(
            timeString,
            style: theme.textTheme.bodySmall?.copyWith(
              color: isOverdue
                  ? theme.colorScheme.error
                  : theme.colorScheme.primary,
              fontSize: 10.sp,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  List<StudyPlanTask> _getTodaysTasks() {
    final today = DateTime.now();
    final todayStart = DateTime(today.year, today.month, today.day);
    final todayEnd = todayStart.add(const Duration(days: 1));

    final allTasks = studyPlan.sections
        .expand((section) => section.tasks)
        .where((task) {
          if (task.dueDate == null) return false;
          return task.dueDate!.isAfter(todayStart) &&
              task.dueDate!.isBefore(todayEnd);
        })
        .toList();

    // Sort by due time, then by priority
    allTasks.sort((a, b) {
      final timeComparison = a.dueDate!.compareTo(b.dueDate!);
      if (timeComparison != 0) return timeComparison;
      return b.priority.index.compareTo(a.priority.index);
    });

    return allTasks;
  }
}
