import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_symbols_icons/symbols.dart';

import '../../../models/study_plan_models.dart';
import '../../../enums/study_plan_enums.dart';

/// Header section for study plan detail screen showing title, description, and key info
class StudyPlanHeaderSection extends ConsumerWidget {
  final StudyPlanModel studyPlan;

  const StudyPlanHeaderSection({super.key, required this.studyPlan});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Title and type
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
              child: Text(
                studyPlan.title,
                style: theme.textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.w700,
                ),
              ),
            ),
            SizedBox(width: 12.w),
            _buildTypeChip(theme),
          ],
        ),

        SizedBox(height: 16.h),

        // Description
        if (studyPlan.description != null) ...[
          Text(
            studyPlan.description!,
            style: theme.textTheme.bodyLarge?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.8),
              height: 1.5,
            ),
          ),
          SizedBox(height: 20.h),
        ],

        // Info container
        Container(
          width: double.infinity,
          padding: EdgeInsets.all(16.w),
          decoration: BoxDecoration(
            color: theme.colorScheme.surface,
            borderRadius: BorderRadius.circular(12.r),
            border: Border.all(
              color: theme.colorScheme.outline.withValues(alpha: 0.2),
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Status and priority row
              Row(
                children: [
                  _buildStatusChip(theme),
                  SizedBox(width: 12.w),
                  _buildPriorityChip(theme),
                  const Spacer(),
                  if (studyPlan.isMandatory) _buildMandatoryBadge(theme),
                ],
              ),

              SizedBox(height: 16.h),

              // Info rows
              _InfoRow(
                icon: Symbols.person,
                label: 'Created by',
                value:
                    '${studyPlan.creatorName ?? 'Unknown'} (${studyPlan.creatorRole.displayName})',
              ),

              SizedBox(height: 12.h),

              _InfoRow(
                icon: Symbols.schedule,
                label: 'Created',
                value: _formatDate(studyPlan.createdAt),
              ),

              if (studyPlan.startDate != null || studyPlan.endDate != null) ...[
                SizedBox(height: 12.h),
                _InfoRow(
                  icon: Symbols.event,
                  label: 'Duration',
                  value: _formatDateRange(),
                ),
              ],

              if (studyPlan.estimatedDuration != null) ...[
                SizedBox(height: 12.h),
                _InfoRow(
                  icon: Symbols.timer,
                  label: 'Estimated Duration',
                  value: _formatDuration(studyPlan.estimatedDuration!),
                ),
              ],

              SizedBox(height: 12.h),

              _InfoRow(
                icon: Symbols.people,
                label: 'Assigned to',
                value:
                    '${studyPlan.assignedUserIds.length} ${studyPlan.assignedUserIds.length == 1 ? 'person' : 'people'}',
              ),

              if (studyPlan.classId != null) ...[
                SizedBox(height: 12.h),
                _InfoRow(
                  icon: Symbols.school,
                  label: 'Class',
                  value:
                      'Class ID: ${studyPlan.classId}', // TODO: Get actual class name
                ),
              ],

              if (studyPlan.associationType != null) ...[
                SizedBox(height: 12.h),
                _InfoRow(
                  icon: studyPlan.associationType!.icon,
                  label: 'Associated with',
                  value: studyPlan.associationType!.displayName,
                ),
              ],
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildTypeChip(ThemeData theme) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
      decoration: BoxDecoration(
        color: studyPlan.type.color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(20.r),
        border: Border.all(color: studyPlan.type.color.withValues(alpha: 0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(studyPlan.type.icon, size: 16.sp, color: studyPlan.type.color),
          SizedBox(width: 6.w),
          Text(
            studyPlan.type.displayName,
            style: theme.textTheme.labelMedium?.copyWith(
              color: studyPlan.type.color,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusChip(ThemeData theme) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 6.h),
      decoration: BoxDecoration(
        color: studyPlan.status.color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(16.r),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            studyPlan.status.icon,
            size: 14.sp,
            color: studyPlan.status.color,
          ),
          SizedBox(width: 4.w),
          Text(
            studyPlan.status.displayName,
            style: theme.textTheme.labelSmall?.copyWith(
              color: studyPlan.status.color,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPriorityChip(ThemeData theme) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 6.h),
      decoration: BoxDecoration(
        color: studyPlan.priority.color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(16.r),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            studyPlan.priority.icon,
            size: 14.sp,
            color: studyPlan.priority.color,
          ),
          SizedBox(width: 4.w),
          Text(
            studyPlan.priority.displayName,
            style: theme.textTheme.labelSmall?.copyWith(
              color: studyPlan.priority.color,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMandatoryBadge(ThemeData theme) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
      decoration: BoxDecoration(
        color: Colors.orange.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(Symbols.priority_high, size: 12.sp, color: Colors.orange),
          SizedBox(width: 4.w),
          Text(
            'MANDATORY',
            style: theme.textTheme.labelSmall?.copyWith(
              color: Colors.orange,
              fontWeight: FontWeight.w700,
              fontSize: 10.sp,
            ),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  String _formatDateRange() {
    if (studyPlan.startDate != null && studyPlan.endDate != null) {
      return '${_formatDate(studyPlan.startDate!)} - ${_formatDate(studyPlan.endDate!)}';
    } else if (studyPlan.startDate != null) {
      return 'From ${_formatDate(studyPlan.startDate!)}';
    } else if (studyPlan.endDate != null) {
      return 'Until ${_formatDate(studyPlan.endDate!)}';
    }
    return 'No dates set';
  }

  String _formatDuration(Duration duration) {
    if (duration.inDays > 0) {
      return '${duration.inDays} days';
    } else if (duration.inHours > 0) {
      return '${duration.inHours} hours';
    } else {
      return '${duration.inMinutes} minutes';
    }
  }
}

/// Info row widget for displaying key-value pairs
class _InfoRow extends StatelessWidget {
  final IconData icon;
  final String label;
  final String value;

  const _InfoRow({
    required this.icon,
    required this.label,
    required this.value,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(
          icon,
          size: 18.sp,
          color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
        ),
        SizedBox(width: 12.w),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                  fontWeight: FontWeight.w500,
                ),
              ),
              SizedBox(height: 2.h),
              Text(
                value,
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.colorScheme.onSurface,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
