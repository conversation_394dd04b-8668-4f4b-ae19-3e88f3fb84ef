import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_symbols_icons/symbols.dart';

import '../../../models/study_plan_models.dart';
import '../../../enums/study_plan_enums.dart';

/// Progress visualization section showing overall progress and section breakdown
class ProgressVisualizationSection extends ConsumerWidget {
  final StudyPlanModel studyPlan;

  const ProgressVisualizationSection({super.key, required this.studyPlan});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Section title
        Row(
          children: [
            Icon(
              Symbols.analytics,
              size: 24.sp,
              color: theme.colorScheme.primary,
            ),
            SizedBox(width: 8.w),
            Text(
              'Progress Overview',
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),

        SizedBox(height: 16.h),

        // Overall progress card
        _buildOverallProgressCard(theme),

        <PERSON><PERSON><PERSON><PERSON>(height: 16.h),

        // Progress breakdown
        _buildProgressBreakdown(theme),
      ],
    );
  }

  Widget _buildOverallProgressCard(ThemeData theme) {
    final progress = studyPlan.progress;
    final overallProgress = progress.overallCompletion;

    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(20.w),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            theme.colorScheme.primary.withValues(alpha: 0.1),
            theme.colorScheme.primary.withValues(alpha: 0.05),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16.r),
        border: Border.all(
          color: theme.colorScheme.primary.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        children: [
          // Progress circle and percentage
          Row(
            children: [
              // Circular progress indicator
              SizedBox(
                width: 80.w,
                height: 80.w,
                child: Stack(
                  children: [
                    CircularProgressIndicator(
                      value: overallProgress,
                      strokeWidth: 8.w,
                      backgroundColor: theme.colorScheme.outline.withValues(
                        alpha: 0.2,
                      ),
                      valueColor: AlwaysStoppedAnimation<Color>(
                        theme.colorScheme.primary,
                      ),
                    ),
                    Center(
                      child: Text(
                        '${(overallProgress * 100).toInt()}%',
                        style: theme.textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.w700,
                          color: theme.colorScheme.primary,
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              SizedBox(width: 20.w),

              // Progress details
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Overall Progress',
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    SizedBox(height: 8.h),
                    Text(
                      _getProgressDescription(overallProgress),
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: theme.colorScheme.onSurface.withValues(
                          alpha: 0.7,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),

          SizedBox(height: 20.h),

          // Progress stats row
          Row(
            children: [
              Expanded(
                child: _buildStatItem(
                  theme,
                  icon: Symbols.folder,
                  label: 'Sections',
                  value:
                      '${progress.completedSections}/${progress.totalSections}',
                ),
              ),
              Container(
                width: 1.w,
                height: 40.h,
                color: theme.colorScheme.outline.withValues(alpha: 0.3),
              ),
              Expanded(
                child: _buildStatItem(
                  theme,
                  icon: Symbols.task,
                  label: 'Tasks',
                  value: '${progress.completedTasks}/${progress.totalTasks}',
                ),
              ),
              Container(
                width: 1.w,
                height: 40.h,
                color: theme.colorScheme.outline.withValues(alpha: 0.3),
              ),
              Expanded(
                child: _buildStatItem(
                  theme,
                  icon: Symbols.schedule,
                  label: 'Time Spent',
                  value: _formatTimeSpent(
                    null,
                  ), // TODO: Add timeSpent field to StudyPlanProgress
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildProgressBreakdown(ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Section Progress',
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),

        SizedBox(height: 12.h),

        // Section progress list
        ...studyPlan.sections.map(
          (section) => _buildSectionProgressItem(theme, section),
        ),
      ],
    );
  }

  Widget _buildSectionProgressItem(ThemeData theme, StudyPlanSection section) {
    final sectionProgress = section.progress.completionPercentage / 100;

    return Container(
      margin: EdgeInsets.only(bottom: 12.h),
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(
          color: theme.colorScheme.outline.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section header
          Row(
            children: [
              Icon(
                section.type.icon,
                size: 20.sp,
                color: theme.colorScheme.primary,
              ),
              SizedBox(width: 8.w),
              Expanded(
                child: Text(
                  section.title,
                  style: theme.textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              Text(
                '${(sectionProgress * 100).toInt()}%',
                style: theme.textTheme.labelMedium?.copyWith(
                  color: theme.colorScheme.primary,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),

          SizedBox(height: 8.h),

          // Progress bar
          LinearProgressIndicator(
            value: sectionProgress,
            backgroundColor: theme.colorScheme.outline.withValues(alpha: 0.2),
            valueColor: AlwaysStoppedAnimation<Color>(
              theme.colorScheme.primary,
            ),
            borderRadius: BorderRadius.circular(2.r),
          ),

          SizedBox(height: 8.h),

          // Section stats
          Row(
            children: [
              Text(
                '${section.tasks.where((task) => task.status == TaskStatus.completed).length}/${section.tasks.length} tasks completed',
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                ),
              ),
              const Spacer(),
              if (section.estimatedDuration != null)
                Text(
                  _formatDuration(section.estimatedDuration!),
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                  ),
                ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(
    ThemeData theme, {
    required IconData icon,
    required String label,
    required String value,
  }) {
    return Column(
      children: [
        Icon(icon, size: 20.sp, color: theme.colorScheme.primary),
        SizedBox(height: 4.h),
        Text(
          value,
          style: theme.textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.w700,
            color: theme.colorScheme.primary,
          ),
        ),
        Text(
          label,
          style: theme.textTheme.bodySmall?.copyWith(
            color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
          ),
        ),
      ],
    );
  }

  String _getProgressDescription(double progress) {
    if (progress == 0) {
      return 'Ready to start your learning journey';
    } else if (progress < 0.25) {
      return 'Just getting started';
    } else if (progress < 0.5) {
      return 'Making good progress';
    } else if (progress < 0.75) {
      return 'More than halfway there';
    } else if (progress < 1.0) {
      return 'Almost finished!';
    } else {
      return 'Congratulations! Plan completed';
    }
  }

  String _formatTimeSpent(Duration? timeSpent) {
    if (timeSpent == null) return '0h';

    final hours = timeSpent.inHours;
    final minutes = timeSpent.inMinutes % 60;

    if (hours > 0) {
      return '${hours}h ${minutes}m';
    } else {
      return '${minutes}m';
    }
  }

  String _formatDuration(Duration duration) {
    final hours = duration.inHours;
    final minutes = duration.inMinutes % 60;

    if (hours > 0) {
      return '${hours}h ${minutes}m';
    } else {
      return '${minutes}m';
    }
  }
}
