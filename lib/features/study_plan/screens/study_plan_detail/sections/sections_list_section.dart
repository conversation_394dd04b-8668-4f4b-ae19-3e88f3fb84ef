import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_symbols_icons/symbols.dart';

import '../../../models/study_plan_models.dart';
import '../../../enums/study_plan_enums.dart';

/// Hierarchical sections list showing expandable sections and tasks
class SectionsListSection extends ConsumerStatefulWidget {
  final StudyPlanModel studyPlan;
  final Function(StudyPlanTask task)? onTaskTap;
  final Function(StudyPlanSection section)? onSectionTap;

  const SectionsListSection({
    super.key,
    required this.studyPlan,
    this.onTaskTap,
    this.onSectionTap,
  });

  @override
  ConsumerState<SectionsListSection> createState() =>
      _SectionsListSectionState();
}

class _SectionsListSectionState extends ConsumerState<SectionsListSection> {
  final Set<String> _expandedSections = <String>{};

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Section title
        Row(
          children: [
            Icon(Symbols.list, size: 24.sp, color: theme.colorScheme.primary),
            SizedBox(width: 8.w),
            Text(
              'Study Plan Structure',
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const Spacer(),
            TextButton.icon(
              onPressed: _toggleAllSections,
              icon: Icon(
                _areAllSectionsExpanded()
                    ? Symbols.unfold_less
                    : Symbols.unfold_more,
                size: 18.sp,
              ),
              label: Text(
                _areAllSectionsExpanded() ? 'Collapse All' : 'Expand All',
              ),
            ),
          ],
        ),

        SizedBox(height: 16.h),

        // Sections list
        ...widget.studyPlan.sections.map(
          (section) => _buildSectionItem(theme, section),
        ),
      ],
    );
  }

  Widget _buildSectionItem(ThemeData theme, StudyPlanSection section) {
    final isExpanded = _expandedSections.contains(section.id);
    final sectionProgress = section.progress.completionPercentage / 100;

    return Container(
      margin: EdgeInsets.only(bottom: 12.h),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(
          color: theme.colorScheme.outline.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        children: [
          // Section header
          InkWell(
            onTap: () => _toggleSection(section.id),
            borderRadius: BorderRadius.circular(12.r),
            child: Padding(
              padding: EdgeInsets.all(16.w),
              child: Row(
                children: [
                  // Expand/collapse icon
                  Icon(
                    isExpanded ? Symbols.expand_less : Symbols.expand_more,
                    size: 24.sp,
                    color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                  ),

                  SizedBox(width: 12.w),

                  // Section type icon
                  Container(
                    width: 32.w,
                    height: 32.w,
                    decoration: BoxDecoration(
                      color: section.type.color.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8.r),
                    ),
                    child: Icon(
                      section.type.icon,
                      size: 18.sp,
                      color: section.type.color,
                    ),
                  ),

                  SizedBox(width: 12.w),

                  // Section info
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          section.title,
                          style: theme.textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        if (section.description != null) ...[
                          SizedBox(height: 4.h),
                          Text(
                            section.description!,
                            style: theme.textTheme.bodySmall?.copyWith(
                              color: theme.colorScheme.onSurface.withValues(
                                alpha: 0.6,
                              ),
                            ),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                        SizedBox(height: 8.h),
                        // Progress bar
                        LinearProgressIndicator(
                          value: sectionProgress,
                          backgroundColor: theme.colorScheme.outline.withValues(
                            alpha: 0.2,
                          ),
                          valueColor: AlwaysStoppedAnimation<Color>(
                            section.type.color,
                          ),
                          borderRadius: BorderRadius.circular(2.r),
                        ),
                      ],
                    ),
                  ),

                  SizedBox(width: 12.w),

                  // Progress percentage and task count
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Text(
                        '${(sectionProgress * 100).toInt()}%',
                        style: theme.textTheme.labelLarge?.copyWith(
                          color: section.type.color,
                          fontWeight: FontWeight.w700,
                        ),
                      ),
                      SizedBox(height: 2.h),
                      Text(
                        '${section.tasks.where((task) => task.status == TaskStatus.completed).length}/${section.tasks.length} tasks',
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.colorScheme.onSurface.withValues(
                            alpha: 0.6,
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),

          // Expanded content
          if (isExpanded) ...[
            Divider(
              height: 1.h,
              color: theme.colorScheme.outline.withValues(alpha: 0.2),
            ),
            _buildSectionContent(theme, section),
          ],
        ],
      ),
    );
  }

  Widget _buildSectionContent(ThemeData theme, StudyPlanSection section) {
    return Padding(
      padding: EdgeInsets.all(16.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section details
          if (section.estimatedDuration != null ||
              section.startDate != null ||
              section.endDate != null) ...[
            Row(
              children: [
                if (section.estimatedDuration != null) ...[
                  Icon(
                    Symbols.timer,
                    size: 16.sp,
                    color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                  ),
                  SizedBox(width: 4.w),
                  Text(
                    _formatDuration(section.estimatedDuration!),
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                    ),
                  ),
                ],
                if (section.startDate != null || section.endDate != null) ...[
                  if (section.estimatedDuration != null) SizedBox(width: 16.w),
                  Icon(
                    Symbols.event,
                    size: 16.sp,
                    color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                  ),
                  SizedBox(width: 4.w),
                  Text(
                    _formatSectionDateRange(section),
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                    ),
                  ),
                ],
              ],
            ),
            SizedBox(height: 16.h),
          ],

          // Tasks list
          if (section.tasks.isNotEmpty) ...[
            Text(
              'Tasks',
              style: theme.textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: 8.h),
            ...section.tasks.map((task) => _buildTaskItem(theme, task)),
          ],

          // Sub-sections
          if (section.subSections.isNotEmpty) ...[
            if (section.tasks.isNotEmpty) SizedBox(height: 16.h),
            Text(
              'Sub-sections',
              style: theme.textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: 8.h),
            ...section.subSections.map(
              (subSection) => _buildSubSectionItem(theme, subSection),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildTaskItem(ThemeData theme, StudyPlanTask task) {
    return Container(
      margin: EdgeInsets.only(bottom: 8.h),
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(
          color: theme.colorScheme.outline.withValues(alpha: 0.1),
        ),
      ),
      child: InkWell(
        onTap: () => widget.onTaskTap?.call(task),
        borderRadius: BorderRadius.circular(8.r),
        child: Row(
          children: [
            // Task type icon
            Container(
              width: 24.w,
              height: 24.w,
              decoration: BoxDecoration(
                color: task.type.color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(6.r),
              ),
              child: Icon(task.type.icon, size: 14.sp, color: task.type.color),
            ),

            SizedBox(width: 12.w),

            // Task info
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    task.title,
                    style: theme.textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w500,
                      decoration: task.status == TaskStatus.completed
                          ? TextDecoration.lineThrough
                          : null,
                    ),
                  ),
                  if (task.dueDate != null) ...[
                    SizedBox(height: 2.h),
                    Text(
                      'Due: ${_formatDate(task.dueDate!)}',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.onSurface.withValues(
                          alpha: 0.6,
                        ),
                      ),
                    ),
                  ],
                ],
              ),
            ),

            // Task status
            Container(
              padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
              decoration: BoxDecoration(
                color: task.status.color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12.r),
              ),
              child: Text(
                task.status.displayName,
                style: theme.textTheme.labelSmall?.copyWith(
                  color: task.status.color,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSubSectionItem(ThemeData theme, StudyPlanSection subSection) {
    return Container(
      margin: EdgeInsets.only(bottom: 8.h),
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(
          color: theme.colorScheme.outline.withValues(alpha: 0.1),
        ),
      ),
      child: InkWell(
        onTap: () => widget.onSectionTap?.call(subSection),
        borderRadius: BorderRadius.circular(8.r),
        child: Row(
          children: [
            Icon(
              subSection.type.icon,
              size: 20.sp,
              color: subSection.type.color,
            ),
            SizedBox(width: 12.w),
            Expanded(
              child: Text(
                subSection.title,
                style: theme.textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
            Text(
              '${subSection.tasks.where((task) => task.status == TaskStatus.completed).length}/${subSection.tasks.length}',
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _toggleSection(String sectionId) {
    setState(() {
      if (_expandedSections.contains(sectionId)) {
        _expandedSections.remove(sectionId);
      } else {
        _expandedSections.add(sectionId);
      }
    });
  }

  void _toggleAllSections() {
    setState(() {
      if (_areAllSectionsExpanded()) {
        _expandedSections.clear();
      } else {
        _expandedSections.addAll(widget.studyPlan.sections.map((s) => s.id));
      }
    });
  }

  bool _areAllSectionsExpanded() {
    return _expandedSections.length == widget.studyPlan.sections.length;
  }

  String _formatDuration(Duration duration) {
    final hours = duration.inHours;
    final minutes = duration.inMinutes % 60;

    if (hours > 0) {
      return '${hours}h ${minutes}m';
    } else {
      return '${minutes}m';
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  String _formatSectionDateRange(StudyPlanSection section) {
    if (section.startDate != null && section.endDate != null) {
      return '${_formatDate(section.startDate!)} - ${_formatDate(section.endDate!)}';
    } else if (section.startDate != null) {
      return 'From ${_formatDate(section.startDate!)}';
    } else if (section.endDate != null) {
      return 'Until ${_formatDate(section.endDate!)}';
    }
    return 'No dates set';
  }
}
