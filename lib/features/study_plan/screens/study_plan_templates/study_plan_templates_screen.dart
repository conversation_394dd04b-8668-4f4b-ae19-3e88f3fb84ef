import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_symbols_icons/symbols.dart';
import 'package:go_router/go_router.dart';

import '../../../../core/routes/app_routes.dart';

import '../../../../core/widgets/responsive/responsive_padding.dart';
import '../../../debug/mock_data/mock_data.dart';
import '../../models/study_plan_models.dart';
import '../../enums/study_plan_enums.dart';
import '../../widgets/study_plan_view_toggle.dart';
import 'sections/template_categories_section.dart';
import 'sections/templates_grid_section.dart';

/// Study plan templates library screen with categories and preview
class StudyPlanTemplatesScreen extends ConsumerStatefulWidget {
  const StudyPlanTemplatesScreen({super.key});

  @override
  ConsumerState<StudyPlanTemplatesScreen> createState() =>
      _StudyPlanTemplatesScreenState();
}

class _StudyPlanTemplatesScreenState
    extends ConsumerState<StudyPlanTemplatesScreen> {
  // State variables
  String _searchQuery = '';
  StudyPlanType? _selectedCategory;
  StudyPlanViewMode _viewMode = StudyPlanViewMode.grid;

  // Mock data - will be replaced with actual providers
  final List<StudyPlanModel> _mockTemplates = mockStudyPlansList
      .where((plan) => plan.isTemplate)
      .toList();

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: theme.colorScheme.surface,
      appBar: _buildAppBar(theme),
      body: ResponsivePadding(
        mobile: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Search bar
            _buildSearchBar(theme),

            SizedBox(height: 16.h),

            // Template categories section
            TemplateCategoriesSection(
              selectedCategory: _selectedCategory,
              onCategoryChanged: _onCategoryChanged,
            ),

            SizedBox(height: 16.h),

            // View mode toggle and results count
            Row(
              children: [
                Text(
                  '${_getFilteredTemplates().length} templates',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const Spacer(),
                StudyPlanViewToggle(
                  viewMode: _viewMode,
                  onViewModeChanged: _onViewModeChanged,
                ),
              ],
            ),

            SizedBox(height: 16.h),

            // Templates grid/list
            Expanded(
              child: TemplatesGridSection(
                templates: _getFilteredTemplates(),
                viewMode: _viewMode,
                onTemplateTap: _onTemplateTap,
                onTemplatePreview: _onTemplatePreview,
                onTemplateUse: _onTemplateUse,
              ),
            ),
          ],
        ),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar(ThemeData theme) {
    return AppBar(
      title: const Text('Study Plan Templates'),
      elevation: 0,
      backgroundColor: theme.colorScheme.surface,
      actions: [
        IconButton(
          icon: const Icon(Symbols.help),
          onPressed: () {
            // TODO: Show templates help/guide
            debugPrint('Show templates help');
          },
        ),
        PopupMenuButton<String>(
          icon: const Icon(Symbols.more_vert),
          onSelected: _handleMenuAction,
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'create_template',
              child: Text('Create Template'),
            ),
            const PopupMenuItem(
              value: 'my_templates',
              child: Text('My Templates'),
            ),
            const PopupMenuItem(
              value: 'import',
              child: Text('Import Template'),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildSearchBar(ThemeData theme) {
    return Container(
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(
          color: theme.colorScheme.outline.withValues(alpha: 0.3),
        ),
      ),
      child: TextField(
        onChanged: _onSearchChanged,
        decoration: InputDecoration(
          hintText: 'Search templates...',
          prefixIcon: const Icon(Symbols.search),
          border: InputBorder.none,
          contentPadding: EdgeInsets.symmetric(
            horizontal: 16.w,
            vertical: 12.h,
          ),
        ),
      ),
    );
  }

  // Event handlers
  void _onSearchChanged(String query) {
    setState(() {
      _searchQuery = query;
    });
  }

  void _onCategoryChanged(StudyPlanType? category) {
    setState(() {
      _selectedCategory = category;
    });
  }

  void _onViewModeChanged(StudyPlanViewMode viewMode) {
    setState(() {
      _viewMode = viewMode;
    });
  }

  void _onTemplateTap(StudyPlanModel template) {
    // TODO: Navigate to template detail screen
    debugPrint('Tapped template: ${template.title}');
  }

  void _onTemplatePreview(StudyPlanModel template) {
    // TODO: Show template preview modal
    debugPrint('Preview template: ${template.title}');
  }

  void _onTemplateUse(StudyPlanModel template) {
    context.pushNamed(
      RouteNames.studyPlanCreate,
      queryParameters: {'templateId': template.id},
    );
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'create_template':
        context.pushNamed(RouteNames.studyPlanCreate);
        break;
      case 'my_templates':
        // TODO: Show user's templates
        debugPrint('Show my templates');
        break;
      case 'import':
        // TODO: Import template functionality
        debugPrint('Import template');
        break;
    }
  }

  // Filter logic
  List<StudyPlanModel> _getFilteredTemplates() {
    List<StudyPlanModel> filtered = List.from(_mockTemplates);

    // Apply search filter
    if (_searchQuery.isNotEmpty) {
      filtered = filtered.where((template) {
        return template.title.toLowerCase().contains(
              _searchQuery.toLowerCase(),
            ) ||
            (template.description?.toLowerCase().contains(
                  _searchQuery.toLowerCase(),
                ) ??
                false);
      }).toList();
    }

    // Apply category filter
    if (_selectedCategory != null) {
      filtered = filtered
          .where((template) => template.type == _selectedCategory)
          .toList();
    }

    return filtered;
  }
}
