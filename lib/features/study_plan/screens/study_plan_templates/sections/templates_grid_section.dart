import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_symbols_icons/symbols.dart';

import '../../../models/study_plan_models.dart';
import '../../../enums/study_plan_enums.dart';
import '../../../widgets/study_plan_view_toggle.dart';

/// Templates grid/list section displaying templates with preview and use actions
class TemplatesGridSection extends ConsumerWidget {
  final List<StudyPlanModel> templates;
  final StudyPlanViewMode viewMode;
  final Function(StudyPlanModel template) onTemplateTap;
  final Function(StudyPlanModel template) onTemplatePreview;
  final Function(StudyPlanModel template) onTemplateUse;

  const TemplatesGridSection({
    super.key,
    required this.templates,
    required this.viewMode,
    required this.onTemplateTap,
    required this.onTemplatePreview,
    required this.onTemplateUse,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    if (templates.isEmpty) {
      return _buildEmptyState(context);
    }

    return viewMode == StudyPlanViewMode.list
        ? _buildListView()
        : _buildGridView();
  }

  Widget _buildEmptyState(BuildContext context) {
    final theme = Theme.of(context);

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Symbols.library_books,
            size: 64.sp,
            color: theme.colorScheme.primary.withValues(alpha: 0.5),
          ),
          SizedBox(height: 16.h),
          Text(
            'No templates found',
            style: theme.textTheme.headlineSmall?.copyWith(
              color: theme.colorScheme.onSurface,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            'Try adjusting your search or category filters',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildListView() {
    return ListView.builder(
      itemCount: templates.length,
      itemBuilder: (context, index) {
        final template = templates[index];
        return Padding(
          padding: EdgeInsets.only(bottom: 12.h),
          child: _TemplateCard(
            template: template,
            isCompact: false,
            onTap: () => onTemplateTap(template),
            onPreview: () => onTemplatePreview(template),
            onUse: () => onTemplateUse(template),
          ),
        );
      },
    );
  }

  Widget _buildGridView() {
    return GridView.builder(
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 0.75,
        crossAxisSpacing: 12.w,
        mainAxisSpacing: 12.h,
      ),
      itemCount: templates.length,
      itemBuilder: (context, index) {
        final template = templates[index];
        return _TemplateCard(
          template: template,
          isCompact: true,
          onTap: () => onTemplateTap(template),
          onPreview: () => onTemplatePreview(template),
          onUse: () => onTemplateUse(template),
        );
      },
    );
  }
}

/// Template card widget
class _TemplateCard extends StatelessWidget {
  final StudyPlanModel template;
  final bool isCompact;
  final VoidCallback onTap;
  final VoidCallback onPreview;
  final VoidCallback onUse;

  const _TemplateCard({
    required this.template,
    required this.isCompact,
    required this.onTap,
    required this.onPreview,
    required this.onUse,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12.r)),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12.r),
        child: Container(
          padding: EdgeInsets.all(16.w),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12.r),
            border: Border(
              left: BorderSide(color: template.type.color, width: 4.w),
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with type and rating
              Row(
                children: [
                  Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: 8.w,
                      vertical: 4.h,
                    ),
                    decoration: BoxDecoration(
                      color: template.type.color.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(6.r),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          template.type.icon,
                          size: 12.sp,
                          color: template.type.color,
                        ),
                        SizedBox(width: 4.w),
                        Text(
                          template.type.displayName,
                          style: theme.textTheme.labelSmall?.copyWith(
                            color: template.type.color,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  ),
                  const Spacer(),
                  // Mock rating
                  Row(
                    children: [
                      Icon(Symbols.star, size: 14.sp, color: Colors.amber),
                      SizedBox(width: 2.w),
                      Text(
                        '4.5',
                        style: theme.textTheme.labelSmall?.copyWith(
                          color: theme.colorScheme.onSurface.withValues(
                            alpha: 0.7,
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),

              SizedBox(height: 12.h),

              // Title
              Text(
                template.title,
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
                maxLines: isCompact ? 2 : 3,
                overflow: TextOverflow.ellipsis,
              ),

              if (template.description != null && !isCompact) ...[
                SizedBox(height: 8.h),
                Text(
                  template.description!,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],

              const Spacer(),

              // Stats row
              Row(
                children: [
                  Icon(
                    Symbols.folder,
                    size: 14.sp,
                    color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                  ),
                  SizedBox(width: 4.w),
                  Text(
                    '${template.sections.length} sections',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                    ),
                  ),
                  SizedBox(width: 12.w),
                  Icon(
                    Symbols.download,
                    size: 14.sp,
                    color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                  ),
                  SizedBox(width: 4.w),
                  Text(
                    '1.2k', // Mock download count
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                    ),
                  ),
                ],
              ),

              SizedBox(height: 12.h),

              // Action buttons
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton.icon(
                      onPressed: onPreview,
                      icon: Icon(Symbols.visibility, size: 16.sp),
                      label: const Text('Preview'),
                      style: OutlinedButton.styleFrom(
                        padding: EdgeInsets.symmetric(vertical: 8.h),
                        textStyle: theme.textTheme.labelMedium,
                      ),
                    ),
                  ),
                  SizedBox(width: 8.w),
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: onUse,
                      icon: Icon(Symbols.add, size: 16.sp),
                      label: const Text('Use'),
                      style: ElevatedButton.styleFrom(
                        padding: EdgeInsets.symmetric(vertical: 8.h),
                        textStyle: theme.textTheme.labelMedium,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
