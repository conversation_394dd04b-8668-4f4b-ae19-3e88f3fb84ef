import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../enums/study_plan_enums.dart';

/// Template categories section with filter chips
class TemplateCategoriesSection extends ConsumerWidget {
  final StudyPlanType? selectedCategory;
  final Function(StudyPlanType? category) onCategoryChanged;

  const TemplateCategoriesSection({
    super.key,
    required this.selectedCategory,
    required this.onCategoryChanged,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Categories',
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: 12.h),
        SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: Row(
            children: [
              // "All" category chip
              _buildCategoryChip(
                context: context,
                theme: theme,
                label: 'All Templates',
                icon: Icons.all_inclusive,
                isSelected: selectedCategory == null,
                onTap: () => onCategoryChanged(null),
              ),

              SizedBox(width: 8.w),

              // Individual category filter chips
              ...StudyPlanType.values.map((type) {
                return Padding(
                  padding: EdgeInsets.only(right: 8.w),
                  child: _buildCategoryChip(
                    context: context,
                    theme: theme,
                    label: type.displayName,
                    icon: type.icon,
                    isSelected: selectedCategory == type,
                    onTap: () => onCategoryChanged(type),
                    color: type.color,
                  ),
                );
              }),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildCategoryChip({
    required BuildContext context,
    required ThemeData theme,
    required String label,
    required IconData icon,
    required bool isSelected,
    required VoidCallback onTap,
    Color? color,
  }) {
    final effectiveColor = color ?? theme.colorScheme.primary;

    return FilterChip(
      label: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 16.sp,
            color: isSelected
                ? effectiveColor.computeLuminance() > 0.5
                    ? Colors.black
                    : Colors.white
                : effectiveColor,
          ),
          SizedBox(width: 4.w),
          Text(
            label,
            style: theme.textTheme.labelMedium?.copyWith(
              color: isSelected
                  ? effectiveColor.computeLuminance() > 0.5
                      ? Colors.black
                      : Colors.white
                  : theme.colorScheme.onSurface,
              fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
            ),
          ),
        ],
      ),
      selected: isSelected,
      onSelected: (_) => onTap(),
      backgroundColor: theme.colorScheme.surface,
      selectedColor: effectiveColor.withValues(alpha: 0.2),
      checkmarkColor: effectiveColor,
      side: BorderSide(
        color: isSelected
            ? effectiveColor
            : theme.colorScheme.outline.withValues(alpha: 0.3),
        width: isSelected ? 1.5.w : 1.w,
      ),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20.r)),
      materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
      visualDensity: VisualDensity.compact,
    );
  }
}
