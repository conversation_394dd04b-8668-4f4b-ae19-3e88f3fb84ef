import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_symbols_icons/symbols.dart';

import '../../../models/study_plan_models.dart';
import '../../../widgets/study_plan_card.dart';

/// Content section displaying study plans in list view
class StudyPlansContentSection extends ConsumerWidget {
  /// List of study plans to display
  final List<StudyPlanModel> studyPlans;

  /// Callback when a study plan is tapped
  final Function(StudyPlanModel studyPlan) onStudyPlanTap;

  /// Callback when a menu action is selected
  final Function(StudyPlanModel studyPlan, String action)? onMenuAction;

  /// Whether the list is loading
  final bool isLoading;

  /// Error message if any
  final String? errorMessage;

  /// Callback to retry loading
  final VoidCallback? onRetry;

  const StudyPlansContentSection({
    super.key,
    required this.studyPlans,
    required this.onStudyPlanTap,
    this.onMenuAction,
    this.isLoading = false,
    this.errorMessage,
    this.onRetry,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    if (isLoading) {
      return _buildLoadingState();
    }

    if (errorMessage != null) {
      return _buildErrorState(context);
    }

    if (studyPlans.isEmpty) {
      return _buildEmptyState(context);
    }

    return _buildListView();
  }

  Widget _buildLoadingState() {
    return SliverFillRemaining(
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const CircularProgressIndicator(),
            SizedBox(height: 16.h),
            Text(
              'Loading study plans...',
              style: TextStyle(fontSize: 16.sp, color: Colors.grey[600]),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorState(BuildContext context) {
    final theme = Theme.of(context);

    return SliverFillRemaining(
      child: Center(
        child: Padding(
          padding: EdgeInsets.all(32.w),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Symbols.error, size: 64.sp, color: theme.colorScheme.error),
              SizedBox(height: 16.h),
              Text(
                'Failed to load study plans',
                style: theme.textTheme.headlineSmall?.copyWith(
                  color: theme.colorScheme.onSurface,
                ),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: 8.h),
              Text(
                errorMessage ?? 'An unexpected error occurred',
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                ),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: 24.h),
              ElevatedButton.icon(
                onPressed: onRetry,
                icon: const Icon(Symbols.refresh),
                label: const Text('Try Again'),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    final theme = Theme.of(context);

    return SliverFillRemaining(
      child: Center(
        child: Padding(
          padding: EdgeInsets.all(32.w),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Symbols.school,
                size: 64.sp,
                color: theme.colorScheme.primary.withValues(alpha: 0.5),
              ),
              SizedBox(height: 16.h),
              Text(
                'No study plans found',
                style: theme.textTheme.headlineSmall?.copyWith(
                  color: theme.colorScheme.onSurface,
                ),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: 8.h),
              Text(
                'Create your first study plan to get started with organized learning',
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                ),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: 24.h),
              ElevatedButton.icon(
                onPressed: () {
                  // TODO: Navigate to create study plan screen
                },
                icon: const Icon(Symbols.add),
                label: const Text('Create Study Plan'),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildListView() {
    return SliverList(
      delegate: SliverChildBuilderDelegate((context, index) {
        final studyPlan = studyPlans[index];
        return Padding(
          padding: EdgeInsets.only(bottom: 8.h),
          child: StudyPlanCard(
            studyPlan: studyPlan,
            onTap: () => onStudyPlanTap(studyPlan),
            onMenuAction: onMenuAction != null
                ? (action) => onMenuAction!(studyPlan, action)
                : null,
            isCompact: false,
          ),
        );
      }, childCount: studyPlans.length),
    );
  }
}
