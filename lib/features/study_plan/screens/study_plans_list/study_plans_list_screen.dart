import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:material_symbols_icons/symbols.dart';

import '../../../../core/routes/app_routes.dart';
import '../../controllers/study_plan_filter_controller.dart';
import '../../controllers/study_plan_crud_controller.dart';
import '../../enums/study_plan_enums.dart';
import '../../models/study_plan_models.dart';

import 'sections/filter_section.dart';
import 'sections/study_plans_content_section.dart';

/// Main study plans list screen with filtering, search, and categorization
class StudyPlansListScreen extends ConsumerStatefulWidget {
  /// Optional classroom ID to filter study plans by classroom
  final String? classroomId;

  const StudyPlansListScreen({super.key, this.classroomId});

  @override
  ConsumerState<StudyPlansListScreen> createState() =>
      _StudyPlansListScreenState();
}

class _StudyPlansListScreenState extends ConsumerState<StudyPlansListScreen> {
  bool _isSearchActive = false;

  @override
  void initState() {
    super.initState();
    // Set classroom filter if provided
    if (widget.classroomId != null) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        ref
            .read(classroomFilterProvider.notifier)
            .setClassroom(widget.classroomId);
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    // Watch filter providers
    final searchQuery = ref.watch(studyPlanSearchProvider);
    final selectedCategory = ref.watch(studyPlanCategoryFilterProvider);
    final selectedType = ref.watch(studyPlanTypeFilterProvider);
    final filteredStudyPlans = ref.watch(filteredStudyPlansProvider);
    final crudState = ref.watch(studyPlanCrudProvider);

    return Scaffold(
      backgroundColor: theme.colorScheme.surface,
      appBar: _buildAppBar(theme),
      body: CustomScrollView(
        slivers: [
          // Filter section
          SliverToBoxAdapter(
            child: FilterSection(
              searchQuery: searchQuery,
              onSearchChanged: _onSearchChanged,
              selectedCategory: selectedCategory,
              onCategoryChanged: _onCategoryChanged,
              selectedType: selectedType,
              onTypeChanged: _onTypeChanged,
              isSearchActive: _isSearchActive,
              onSearchFocusChanged: _onSearchFocusChanged,
            ),
          ),

          // Study plans content
          filteredStudyPlans.when(
            data: (studyPlans) => StudyPlansContentSection(
              studyPlans: studyPlans,
              onStudyPlanTap: _onStudyPlanTap,
              onMenuAction: _onMenuAction,
              isLoading: crudState.isLoading,
              errorMessage: null,
              onRetry: _onRetry,
            ),
            loading: () => const SliverToBoxAdapter(
              child: Center(child: CircularProgressIndicator()),
            ),
            error: (error, stackTrace) => SliverToBoxAdapter(
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Symbols.error,
                      size: 48.sp,
                      color: theme.colorScheme.error,
                    ),
                    SizedBox(height: 16.h),
                    Text(
                      'Failed to load study plans',
                      style: theme.textTheme.titleMedium,
                    ),
                    SizedBox(height: 8.h),
                    Text(
                      error.toString(),
                      style: theme.textTheme.bodySmall,
                      textAlign: TextAlign.center,
                    ),
                    SizedBox(height: 16.h),
                    ElevatedButton(
                      onPressed: _onRetry,
                      child: const Text('Retry'),
                    ),
                  ],
                ),
              ),
            ),
          ),

          // Bottom padding for FAB
          SliverToBoxAdapter(child: SizedBox(height: 80.h)),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _onCreateStudyPlan,
        child: const Icon(Symbols.add),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar(ThemeData theme) {
    return AppBar(
      title: const Text('Study Plans'),
      elevation: 0,
      backgroundColor: theme.colorScheme.surface,
      actions: [
        IconButton(
          icon: const Icon(Symbols.notifications),
          onPressed: () {
            // TODO: Show study plan notifications
          },
        ),
        IconButton(
          icon: const Icon(Symbols.more_vert),
          onPressed: () {
            _showMoreOptions(context);
          },
        ),
      ],
    );
  }

  // Event handlers
  void _onSearchChanged(String query) {
    ref.read(studyPlanSearchProvider.notifier).setQuery(query);
  }

  void _onCategoryChanged(PlanCategoryType? category) {
    ref.read(studyPlanCategoryFilterProvider.notifier).setCategory(category);
  }

  void _onTypeChanged(StudyPlanType? type) {
    ref.read(studyPlanTypeFilterProvider.notifier).setType(type);
  }

  void _onSearchFocusChanged(bool isActive) {
    setState(() {
      _isSearchActive = isActive;
    });
  }

  void _onStudyPlanTap(StudyPlanModel studyPlan) {
    context.pushNamed(
      RouteNames.studyPlanDetail,
      pathParameters: {'id': studyPlan.id},
    );
  }

  void _onMenuAction(StudyPlanModel studyPlan, String action) async {
    final crudHelper = ref.read(studyPlanCrudHelperProvider);

    switch (action) {
      case 'edit':
        context.pushNamed(
          RouteNames.studyPlanEdit,
          pathParameters: {'id': studyPlan.id},
        );
        break;
      case 'duplicate':
        final success = await crudHelper.duplicateStudyPlan(
          studyPlan.id,
          '${studyPlan.title} (Copy)',
        );
        if (success != null && mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Study plan duplicated: ${success.title}')),
          );
        } else if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Failed to duplicate study plan')),
          );
        }
        break;
      case 'share':
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Share functionality coming soon')),
        );
        break;
      case 'archive':
        final success = await crudHelper.archiveStudyPlan(studyPlan.id);
        if (success && mounted) {
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(const SnackBar(content: Text('Study plan archived')));
        } else if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Failed to archive study plan')),
          );
        }
        break;
      case 'delete':
        final confirmed = await _showDeleteConfirmation(studyPlan);
        if (confirmed && mounted) {
          final success = await crudHelper.deleteStudyPlan(studyPlan.id);
          if (success && mounted) {
            ScaffoldMessenger.of(
              context,
            ).showSnackBar(const SnackBar(content: Text('Study plan deleted')));
          } else if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('Failed to delete study plan')),
            );
          }
        }
        break;
      default:
        debugPrint(
          'Unknown menu action: $action for study plan: ${studyPlan.title}',
        );
    }
  }

  Future<bool> _showDeleteConfirmation(StudyPlanModel studyPlan) async {
    return await showDialog<bool>(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('Delete Study Plan'),
            content: Text(
              'Are you sure you want to delete "${studyPlan.title}"? This action cannot be undone.',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed: () => Navigator.of(context).pop(true),
                style: TextButton.styleFrom(
                  foregroundColor: Theme.of(context).colorScheme.error,
                ),
                child: const Text('Delete'),
              ),
            ],
          ),
        ) ??
        false;
  }

  void _onRetry() {
    ref.invalidate(filteredStudyPlansProvider);
  }

  void _onCreateStudyPlan() {
    context.pushNamed(RouteNames.studyPlanCreate);
  }

  void _showMoreOptions(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder: (context) => _buildMoreOptionsSheet(context),
    );
  }

  Widget _buildMoreOptionsSheet(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(16.w),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          ListTile(
            leading: const Icon(Symbols.library_books),
            title: const Text('Browse Templates'),
            onTap: () {
              Navigator.pop(context);
              context.pushNamed(RouteNames.studyPlanTemplates);
            },
          ),
          ListTile(
            leading: const Icon(Symbols.sort),
            title: const Text('Sort Options'),
            onTap: () {
              Navigator.pop(context);
              _showSortOptions(context);
            },
          ),
          ListTile(
            leading: const Icon(Symbols.settings),
            title: const Text('Settings'),
            onTap: () {
              Navigator.pop(context);
              // TODO: Navigate to settings
            },
          ),
        ],
      ),
    );
  }

  void _showSortOptions(BuildContext context) {
    // TODO: Implement sort options dialog
    debugPrint('Show sort options');
  }
}
