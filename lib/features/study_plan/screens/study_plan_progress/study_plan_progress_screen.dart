import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_symbols_icons/symbols.dart';

import '../../../../core/widgets/responsive/responsive_padding.dart';
import '../../../debug/mock_data/mock_data.dart';
import '../../models/study_plan_models.dart';
import 'sections/progress_dashboard_section.dart';
import 'sections/progress_charts_section.dart';
import 'sections/milestone_tracker_section.dart';
import 'sections/time_tracking_section.dart';

/// Detailed progress tracking screen with analytics and charts
class StudyPlanProgressScreen extends ConsumerStatefulWidget {
  final String studyPlanId;

  const StudyPlanProgressScreen({super.key, required this.studyPlanId});

  @override
  ConsumerState<StudyPlanProgressScreen> createState() =>
      _StudyPlanProgressScreenState();
}

class _StudyPlanProgressScreenState
    extends ConsumerState<StudyPlanProgressScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  // Mock data - will be replaced with actual providers
  StudyPlanModel? _mockStudyPlan;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    // Find the study plan by ID from mock data
    _mockStudyPlan = mockStudyPlansList.firstWhere(
      (plan) => plan.id == widget.studyPlanId,
      orElse: () =>
          mockStudyPlansList.first, // Fallback to first plan if ID not found
    );
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: theme.colorScheme.surface,
      appBar: _buildAppBar(theme),
      body: _mockStudyPlan == null
          ? _buildLoadingState()
          : Column(
              children: [
                // Tab bar
                Container(
                  color: theme.colorScheme.surface,
                  child: TabBar(
                    controller: _tabController,
                    tabs: const [
                      Tab(text: 'Overview'),
                      Tab(text: 'Charts'),
                      Tab(text: 'Milestones'),
                      Tab(text: 'Time'),
                    ],
                  ),
                ),

                // Tab content
                Expanded(
                  child: TabBarView(
                    controller: _tabController,
                    children: [
                      // Overview tab
                      ResponsivePadding(
                        mobile: EdgeInsets.all(16.w),
                        child: ProgressDashboardSection(
                          studyPlan: _mockStudyPlan!,
                        ),
                      ),

                      // Charts tab
                      ResponsivePadding(
                        mobile: EdgeInsets.all(16.w),
                        child: ProgressChartsSection(
                          studyPlan: _mockStudyPlan!,
                        ),
                      ),

                      // Milestones tab
                      ResponsivePadding(
                        mobile: EdgeInsets.all(16.w),
                        child: MilestoneTrackerSection(
                          studyPlan: _mockStudyPlan!,
                        ),
                      ),

                      // Time tracking tab
                      ResponsivePadding(
                        mobile: EdgeInsets.all(16.w),
                        child: TimeTrackingSection(studyPlan: _mockStudyPlan!),
                      ),
                    ],
                  ),
                ),
              ],
            ),
    );
  }

  PreferredSizeWidget _buildAppBar(ThemeData theme) {
    return AppBar(
      title: Text(_mockStudyPlan?.title ?? 'Progress Tracking'),
      elevation: 0,
      backgroundColor: theme.colorScheme.surface,
      actions: [
        IconButton(
          icon: const Icon(Symbols.download),
          onPressed: () {
            // TODO: Implement export report functionality
            debugPrint('Export progress report for: ${widget.studyPlanId}');
          },
        ),
        IconButton(
          icon: const Icon(Symbols.share),
          onPressed: () {
            // TODO: Implement share progress functionality
            debugPrint('Share progress for: ${widget.studyPlanId}');
          },
        ),
        PopupMenuButton<String>(
          icon: const Icon(Symbols.more_vert),
          onSelected: _handleMenuAction,
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'reset_progress',
              child: Text('Reset Progress'),
            ),
            const PopupMenuItem(value: 'set_goal', child: Text('Set Goal')),
            const PopupMenuItem(
              value: 'view_history',
              child: Text('View History'),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildLoadingState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const CircularProgressIndicator(),
          SizedBox(height: 16.h),
          Text(
            'Loading progress data...',
            style: TextStyle(fontSize: 16.sp, color: Colors.grey[600]),
          ),
          SizedBox(height: 8.h),
          Text(
            'Plan ID: ${widget.studyPlanId}',
            style: TextStyle(fontSize: 14.sp, color: Colors.grey[500]),
          ),
        ],
      ),
    );
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'reset_progress':
        // TODO: Reset progress functionality
        debugPrint('Reset progress for: ${widget.studyPlanId}');
        break;
      case 'set_goal':
        // TODO: Set goal functionality
        debugPrint('Set goal for: ${widget.studyPlanId}');
        break;
      case 'view_history':
        // TODO: View history functionality
        debugPrint('View history for: ${widget.studyPlanId}');
        break;
    }
  }
}
