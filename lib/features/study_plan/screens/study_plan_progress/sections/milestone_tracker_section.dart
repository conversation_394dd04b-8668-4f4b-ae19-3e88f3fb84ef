import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_symbols_icons/symbols.dart';

import '../../../models/study_plan_models.dart';

/// Milestone tracker section showing progress milestones and achievements
class MilestoneTrackerSection extends ConsumerWidget {
  final StudyPlanModel studyPlan;

  const MilestoneTrackerSection({super.key, required this.studyPlan});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);

    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Milestone progress overview
          _buildMilestoneOverview(theme),

          SizedBox(height: 24.h),

          // Milestone timeline
          _buildMilestoneTimeline(theme),

          SizedBox(height: 24.h),

          // Achievements section
          _buildAchievements(theme),
        ],
      ),
    );
  }

  Widget _buildMilestoneOverview(ThemeData theme) {
    final mockMilestones = _getMockMilestones();
    final completedMilestones = mockMilestones
        .where((m) => m['isCompleted'] == true)
        .length;

    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(20.w),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Colors.purple.withValues(alpha: 0.1),
            Colors.purple.withValues(alpha: 0.05),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16.r),
        border: Border.all(color: Colors.purple.withValues(alpha: 0.2)),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Container(
                width: 48.w,
                height: 48.w,
                decoration: BoxDecoration(
                  color: Colors.purple.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12.r),
                ),
                child: Icon(Symbols.flag, size: 24.sp, color: Colors.purple),
              ),
              SizedBox(width: 16.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Milestone Progress',
                      style: theme.textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    Text(
                      '$completedMilestones of ${mockMilestones.length} milestones completed',
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: theme.colorScheme.onSurface.withValues(
                          alpha: 0.7,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),

          SizedBox(height: 20.h),

          // Progress bar
          LinearProgressIndicator(
            value: completedMilestones / mockMilestones.length,
            backgroundColor: theme.colorScheme.outline.withValues(alpha: 0.2),
            valueColor: const AlwaysStoppedAnimation<Color>(Colors.purple),
            borderRadius: BorderRadius.circular(4.r),
            minHeight: 8.h,
          ),

          SizedBox(height: 16.h),

          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '${((completedMilestones / mockMilestones.length) * 100).toInt()}% Complete',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: Colors.purple,
                ),
              ),
              Text(
                '${mockMilestones.length - completedMilestones} remaining',
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildMilestoneTimeline(ThemeData theme) {
    final mockMilestones = _getMockMilestones();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Milestone Timeline',
          style: theme.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: 16.h),
        ...mockMilestones.asMap().entries.map((entry) {
          final index = entry.key;
          final milestone = entry.value;
          final isLast = index == mockMilestones.length - 1;

          return _buildMilestoneItem(theme, milestone, isLast);
        }),
      ],
    );
  }

  Widget _buildMilestoneItem(
    ThemeData theme,
    Map<String, dynamic> milestone,
    bool isLast,
  ) {
    final isCompleted = milestone['isCompleted'] as bool;
    final isCurrent = milestone['isCurrent'] as bool;

    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Timeline indicator
        Column(
          children: [
            Container(
              width: 24.w,
              height: 24.w,
              decoration: BoxDecoration(
                color: isCompleted
                    ? Colors.green
                    : isCurrent
                    ? Colors.blue
                    : theme.colorScheme.outline.withValues(alpha: 0.3),
                shape: BoxShape.circle,
                border: Border.all(
                  color: isCompleted
                      ? Colors.green
                      : isCurrent
                      ? Colors.blue
                      : theme.colorScheme.outline.withValues(alpha: 0.5),
                  width: 2.w,
                ),
              ),
              child: Icon(
                isCompleted
                    ? Symbols.check
                    : isCurrent
                    ? Symbols.radio_button_checked
                    : Symbols.radio_button_unchecked,
                size: 12.sp,
                color: isCompleted || isCurrent
                    ? Colors.white
                    : theme.colorScheme.outline,
              ),
            ),
            if (!isLast)
              Container(
                width: 2.w,
                height: 40.h,
                color: isCompleted
                    ? Colors.green.withValues(alpha: 0.3)
                    : theme.colorScheme.outline.withValues(alpha: 0.2),
              ),
          ],
        ),

        SizedBox(width: 16.w),

        // Milestone content
        Expanded(
          child: Container(
            margin: EdgeInsets.only(bottom: isLast ? 0 : 16.h),
            padding: EdgeInsets.all(16.w),
            decoration: BoxDecoration(
              color: theme.colorScheme.surface,
              borderRadius: BorderRadius.circular(12.r),
              border: Border.all(
                color: isCurrent
                    ? Colors.blue.withValues(alpha: 0.3)
                    : theme.colorScheme.outline.withValues(alpha: 0.2),
                width: isCurrent ? 2.w : 1.w,
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        milestone['title'],
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                          decoration: isCompleted
                              ? TextDecoration.lineThrough
                              : null,
                        ),
                      ),
                    ),
                    if (isCompleted)
                      Container(
                        padding: EdgeInsets.symmetric(
                          horizontal: 8.w,
                          vertical: 4.h,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.green.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(12.r),
                        ),
                        child: Text(
                          'Completed',
                          style: theme.textTheme.labelSmall?.copyWith(
                            color: Colors.green,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      )
                    else if (isCurrent)
                      Container(
                        padding: EdgeInsets.symmetric(
                          horizontal: 8.w,
                          vertical: 4.h,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.blue.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(12.r),
                        ),
                        child: Text(
                          'In Progress',
                          style: theme.textTheme.labelSmall?.copyWith(
                            color: Colors.blue,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                  ],
                ),
                SizedBox(height: 8.h),
                Text(
                  milestone['description'],
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                  ),
                ),
                SizedBox(height: 8.h),
                Row(
                  children: [
                    Icon(
                      Symbols.schedule,
                      size: 16.sp,
                      color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                    ),
                    SizedBox(width: 4.w),
                    Text(
                      milestone['dueDate'],
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.onSurface.withValues(
                          alpha: 0.6,
                        ),
                      ),
                    ),
                    SizedBox(width: 16.w),
                    Icon(Symbols.star, size: 16.sp, color: Colors.amber),
                    SizedBox(width: 4.w),
                    Text(
                      '${milestone['points']} points',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.onSurface.withValues(
                          alpha: 0.6,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildAchievements(ThemeData theme) {
    final mockAchievements = _getMockAchievements();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              'Recent Achievements',
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const Spacer(),
            TextButton(
              onPressed: () {
                // TODO: View all achievements
              },
              child: const Text('View All'),
            ),
          ],
        ),
        SizedBox(height: 16.h),
        ...mockAchievements.map(
          (achievement) => _buildAchievementItem(theme, achievement),
        ),
      ],
    );
  }

  Widget _buildAchievementItem(
    ThemeData theme,
    Map<String, dynamic> achievement,
  ) {
    return Container(
      margin: EdgeInsets.only(bottom: 12.h),
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(
          color: theme.colorScheme.outline.withValues(alpha: 0.2),
        ),
      ),
      child: Row(
        children: [
          Container(
            width: 48.w,
            height: 48.w,
            decoration: BoxDecoration(
              color: achievement['color'].withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12.r),
            ),
            child: Icon(
              achievement['icon'],
              size: 24.sp,
              color: achievement['color'],
            ),
          ),
          SizedBox(width: 16.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  achievement['title'],
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  achievement['description'],
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                  ),
                ),
                Text(
                  achievement['date'],
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                  ),
                ),
              ],
            ),
          ),
          Text(
            '+${achievement['points']}',
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w700,
              color: Colors.amber,
            ),
          ),
        ],
      ),
    );
  }

  List<Map<String, dynamic>> _getMockMilestones() {
    return [
      {
        'title': 'Complete Introduction Section',
        'description': 'Finish all tasks in the introduction section',
        'dueDate': 'Dec 15, 2024',
        'points': 100,
        'isCompleted': true,
        'isCurrent': false,
      },
      {
        'title': 'Master Basic Concepts',
        'description': 'Complete 80% of basic concept tasks with good scores',
        'dueDate': 'Dec 22, 2024',
        'points': 150,
        'isCompleted': false,
        'isCurrent': true,
      },
      {
        'title': 'Mid-term Assessment',
        'description': 'Pass the mid-term assessment with 75% or higher',
        'dueDate': 'Jan 5, 2025',
        'points': 200,
        'isCompleted': false,
        'isCurrent': false,
      },
      {
        'title': 'Advanced Topics',
        'description': 'Complete all advanced topic sections',
        'dueDate': 'Jan 20, 2025',
        'points': 250,
        'isCompleted': false,
        'isCurrent': false,
      },
    ];
  }

  List<Map<String, dynamic>> _getMockAchievements() {
    return [
      {
        'title': 'First Milestone',
        'description': 'Completed your first milestone',
        'date': '2 days ago',
        'points': 100,
        'icon': Symbols.flag,
        'color': Colors.green,
      },
      {
        'title': 'Study Streak',
        'description': 'Maintained a 7-day study streak',
        'date': '1 week ago',
        'points': 50,
        'icon': Symbols.local_fire_department,
        'color': Colors.orange,
      },
      {
        'title': 'Quick Learner',
        'description': 'Completed 5 tasks in one day',
        'date': '2 weeks ago',
        'points': 75,
        'icon': Symbols.speed,
        'color': Colors.blue,
      },
    ];
  }
}
