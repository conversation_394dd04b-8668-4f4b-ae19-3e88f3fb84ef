import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_symbols_icons/symbols.dart';

import '../../../models/study_plan_models.dart';

/// Progress dashboard section showing overall progress and key metrics
class ProgressDashboardSection extends ConsumerWidget {
  final StudyPlanModel studyPlan;

  const ProgressDashboardSection({super.key, required this.studyPlan});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);

    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Overall progress card
          _buildOverallProgressCard(theme),

          SizedBox(height: 24.h),

          // Quick stats grid
          _buildQuickStatsGrid(theme),

          SizedBox(height: 24.h),

          // Recent activity
          _buildRecentActivity(theme),

          Sized<PERSON>ox(height: 24.h),

          // Performance summary
          _buildPerformanceSummary(theme),
        ],
      ),
    );
  }

  Widget _buildOverallProgressCard(ThemeData theme) {
    final progress = studyPlan.progress;
    final overallProgress = progress.overallCompletion;

    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(24.w),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            theme.colorScheme.primary.withValues(alpha: 0.1),
            theme.colorScheme.primary.withValues(alpha: 0.05),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16.r),
        border: Border.all(
          color: theme.colorScheme.primary.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        children: [
          // Progress circle
          SizedBox(
            width: 120.w,
            height: 120.w,
            child: Stack(
              children: [
                CircularProgressIndicator(
                  value: overallProgress,
                  strokeWidth: 12.w,
                  backgroundColor: theme.colorScheme.outline.withValues(
                    alpha: 0.2,
                  ),
                  valueColor: AlwaysStoppedAnimation<Color>(
                    theme.colorScheme.primary,
                  ),
                ),
                Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        '${(overallProgress * 100).toInt()}%',
                        style: theme.textTheme.headlineMedium?.copyWith(
                          fontWeight: FontWeight.w700,
                          color: theme.colorScheme.primary,
                        ),
                      ),
                      Text(
                        'Complete',
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.colorScheme.onSurface.withValues(
                            alpha: 0.7,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          SizedBox(height: 24.h),

          // Progress description
          Text(
            _getProgressDescription(overallProgress),
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
            textAlign: TextAlign.center,
          ),

          SizedBox(height: 8.h),

          Text(
            'Keep up the great work! You\'re making excellent progress.',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildQuickStatsGrid(ThemeData theme) {
    final progress = studyPlan.progress;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Quick Stats',
          style: theme.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: 16.h),
        Row(
          children: [
            Expanded(
              child: _buildStatCard(
                theme,
                icon: Symbols.folder,
                title: 'Sections',
                value:
                    '${progress.completedSections}/${progress.totalSections}',
                subtitle: 'Completed',
                color: Colors.blue,
              ),
            ),
            SizedBox(width: 12.w),
            Expanded(
              child: _buildStatCard(
                theme,
                icon: Symbols.task,
                title: 'Tasks',
                value: '${progress.completedTasks}/${progress.totalTasks}',
                subtitle: 'Finished',
                color: Colors.green,
              ),
            ),
          ],
        ),
        SizedBox(height: 12.h),
        Row(
          children: [
            Expanded(
              child: _buildStatCard(
                theme,
                icon: Symbols.schedule,
                title: 'Study Time',
                value: '24h 30m', // Mock data
                subtitle: 'This week',
                color: Colors.orange,
              ),
            ),
            SizedBox(width: 12.w),
            Expanded(
              child: _buildStatCard(
                theme,
                icon: Symbols.trending_up,
                title: 'Streak',
                value: '7 days',
                subtitle: 'Current',
                color: Colors.purple,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildStatCard(
    ThemeData theme, {
    required IconData icon,
    required String title,
    required String value,
    required String subtitle,
    required Color color,
  }) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(
          color: theme.colorScheme.outline.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 32.w,
                height: 32.w,
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8.r),
                ),
                child: Icon(icon, size: 18.sp, color: color),
              ),
              const Spacer(),
              Text(
                title,
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                ),
              ),
            ],
          ),
          SizedBox(height: 12.h),
          Text(
            value,
            style: theme.textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.w700,
              color: color,
            ),
          ),
          Text(
            subtitle,
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRecentActivity(ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              'Recent Activity',
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const Spacer(),
            TextButton(
              onPressed: () {
                // TODO: View all activity
              },
              child: const Text('View All'),
            ),
          ],
        ),
        SizedBox(height: 16.h),
        // Mock activity items
        ..._getMockActivities().map(
          (activity) => _buildActivityItem(theme, activity),
        ),
      ],
    );
  }

  Widget _buildActivityItem(ThemeData theme, Map<String, dynamic> activity) {
    return Container(
      margin: EdgeInsets.only(bottom: 12.h),
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(
          color: theme.colorScheme.outline.withValues(alpha: 0.2),
        ),
      ),
      child: Row(
        children: [
          Container(
            width: 40.w,
            height: 40.w,
            decoration: BoxDecoration(
              color: activity['color'].withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(10.r),
            ),
            child: Icon(
              activity['icon'],
              size: 20.sp,
              color: activity['color'],
            ),
          ),
          SizedBox(width: 12.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  activity['title'],
                  style: theme.textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  activity['time'],
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPerformanceSummary(ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Performance Summary',
          style: theme.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: 16.h),
        Container(
          width: double.infinity,
          padding: EdgeInsets.all(16.w),
          decoration: BoxDecoration(
            color: theme.colorScheme.surface,
            borderRadius: BorderRadius.circular(12.r),
            border: Border.all(
              color: theme.colorScheme.outline.withValues(alpha: 0.2),
            ),
          ),
          child: Column(
            children: [
              Row(
                children: [
                  Icon(Symbols.trending_up, color: Colors.green, size: 20.sp),
                  SizedBox(width: 8.w),
                  Text(
                    'You\'re ahead of schedule!',
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: Colors.green,
                    ),
                  ),
                ],
              ),
              SizedBox(height: 8.h),
              Text(
                'You\'ve completed 15% more tasks than planned for this week. Keep up the excellent work!',
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  String _getProgressDescription(double progress) {
    if (progress == 0) {
      return 'Ready to start your learning journey';
    } else if (progress < 0.25) {
      return 'Just getting started';
    } else if (progress < 0.5) {
      return 'Making good progress';
    } else if (progress < 0.75) {
      return 'More than halfway there';
    } else if (progress < 1.0) {
      return 'Almost finished!';
    } else {
      return 'Congratulations! Plan completed';
    }
  }

  List<Map<String, dynamic>> _getMockActivities() {
    return [
      {
        'title': 'Completed "Introduction to Algorithms"',
        'time': '2 hours ago',
        'icon': Symbols.check_circle,
        'color': Colors.green,
      },
      {
        'title': 'Started "Data Structures" section',
        'time': '1 day ago',
        'icon': Symbols.play_circle,
        'color': Colors.blue,
      },
      {
        'title': 'Achieved 7-day study streak',
        'time': '2 days ago',
        'icon': Symbols.local_fire_department,
        'color': Colors.orange,
      },
    ];
  }
}
