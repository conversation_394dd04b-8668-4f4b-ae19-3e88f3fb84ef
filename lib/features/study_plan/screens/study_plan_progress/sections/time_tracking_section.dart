import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_symbols_icons/symbols.dart';

import '../../../models/study_plan_models.dart';

/// Time tracking section showing study time analytics and patterns
class TimeTrackingSection extends ConsumerWidget {
  final StudyPlanModel studyPlan;

  const TimeTrackingSection({
    super.key,
    required this.studyPlan,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);

    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Time summary cards
          _buildTimeSummaryCards(theme),

          SizedBox(height: 24.h),

          // Weekly time breakdown
          _buildWeeklyTimeBreakdown(theme),

          SizedBox(height: 24.h),

          // Study sessions
          _buildStudySessions(theme),

          SizedBox(height: 24.h),

          // Time goals
          _buildTimeGoals(theme),
        ],
      ),
    );
  }

  Widget _buildTimeSummaryCards(ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Time Summary',
          style: theme.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: 16.h),
        Row(
          children: [
            Expanded(
              child: _buildTimeCard(
                theme,
                title: 'Total Time',
                value: '24h 30m',
                subtitle: 'This week',
                icon: Symbols.schedule,
                color: Colors.blue,
              ),
            ),
            SizedBox(width: 12.w),
            Expanded(
              child: _buildTimeCard(
                theme,
                title: 'Avg. Session',
                value: '45 min',
                subtitle: 'Per session',
                icon: Symbols.timer,
                color: Colors.green,
              ),
            ),
          ],
        ),
        SizedBox(height: 12.h),
        Row(
          children: [
            Expanded(
              child: _buildTimeCard(
                theme,
                title: 'Focus Time',
                value: '22h 15m',
                subtitle: '91% focused',
                icon: Symbols.psychology,
                color: Colors.purple,
              ),
            ),
            SizedBox(width: 12.w),
            Expanded(
              child: _buildTimeCard(
                theme,
                title: 'Break Time',
                value: '2h 15m',
                subtitle: '9% breaks',
                icon: Symbols.coffee,
                color: Colors.orange,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildTimeCard(
    ThemeData theme, {
    required String title,
    required String value,
    required String subtitle,
    required IconData icon,
    required Color color,
  }) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(
          color: theme.colorScheme.outline.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 32.w,
                height: 32.w,
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8.r),
                ),
                child: Icon(
                  icon,
                  size: 18.sp,
                  color: color,
                ),
              ),
              const Spacer(),
              Text(
                title,
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                ),
              ),
            ],
          ),
          SizedBox(height: 12.h),
          Text(
            value,
            style: theme.textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.w700,
              color: color,
            ),
          ),
          Text(
            subtitle,
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildWeeklyTimeBreakdown(ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Weekly Breakdown',
          style: theme.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: 16.h),
        Container(
          width: double.infinity,
          padding: EdgeInsets.all(16.w),
          decoration: BoxDecoration(
            color: theme.colorScheme.surface,
            borderRadius: BorderRadius.circular(12.r),
            border: Border.all(
              color: theme.colorScheme.outline.withValues(alpha: 0.2),
            ),
          ),
          child: Column(
            children: [
              Row(
                children: [
                  Icon(
                    Symbols.bar_chart,
                    size: 20.sp,
                    color: theme.colorScheme.primary,
                  ),
                  SizedBox(width: 8.w),
                  Text(
                    'Daily Study Time',
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const Spacer(),
                  Text(
                    'Goal: 3h/day',
                    style: theme.textTheme.labelMedium?.copyWith(
                      color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                    ),
                  ),
                ],
              ),
              SizedBox(height: 16.h),
              ..._getMockDailyTimes().map((dayData) => _buildDayTimeBar(theme, dayData)),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildDayTimeBar(ThemeData theme, Map<String, dynamic> dayData) {
    final progress = dayData['hours'] / 3.0; // Goal is 3 hours
    final isToday = dayData['isToday'] as bool;

    return Container(
      margin: EdgeInsets.only(bottom: 12.h),
      child: Column(
        children: [
          Row(
            children: [
              SizedBox(
                width: 40.w,
                child: Text(
                  dayData['day'],
                  style: theme.textTheme.bodyMedium?.copyWith(
                    fontWeight: isToday ? FontWeight.w600 : FontWeight.normal,
                    color: isToday ? theme.colorScheme.primary : null,
                  ),
                ),
              ),
              SizedBox(width: 12.w),
              Expanded(
                child: LinearProgressIndicator(
                  value: progress.clamp(0.0, 1.0),
                  backgroundColor: theme.colorScheme.outline.withValues(alpha: 0.2),
                  valueColor: AlwaysStoppedAnimation<Color>(
                    progress >= 1.0 ? Colors.green : theme.colorScheme.primary,
                  ),
                  borderRadius: BorderRadius.circular(4.r),
                  minHeight: 8.h,
                ),
              ),
              SizedBox(width: 12.w),
              SizedBox(
                width: 50.w,
                child: Text(
                  '${dayData['hours'].toStringAsFixed(1)}h',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                    color: progress >= 1.0 ? Colors.green : null,
                  ),
                  textAlign: TextAlign.end,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStudySessions(ThemeData theme) {
    final mockSessions = _getMockStudySessions();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              'Recent Sessions',
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const Spacer(),
            TextButton(
              onPressed: () {
                // TODO: View all sessions
              },
              child: const Text('View All'),
            ),
          ],
        ),
        SizedBox(height: 16.h),
        ...mockSessions.map((session) => _buildSessionItem(theme, session)),
      ],
    );
  }

  Widget _buildSessionItem(ThemeData theme, Map<String, dynamic> session) {
    return Container(
      margin: EdgeInsets.only(bottom: 12.h),
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(
          color: theme.colorScheme.outline.withValues(alpha: 0.2),
        ),
      ),
      child: Row(
        children: [
          Container(
            width: 40.w,
            height: 40.w,
            decoration: BoxDecoration(
              color: session['color'].withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(10.r),
            ),
            child: Icon(
              session['icon'],
              size: 20.sp,
              color: session['color'],
            ),
          ),
          SizedBox(width: 12.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  session['title'],
                  style: theme.textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  '${session['duration']} • ${session['date']}',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
            decoration: BoxDecoration(
              color: session['color'].withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12.r),
            ),
            child: Text(
              '${session['focusScore']}%',
              style: theme.textTheme.labelSmall?.copyWith(
                color: session['color'],
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTimeGoals(ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              'Time Goals',
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const Spacer(),
            TextButton.icon(
              onPressed: () {
                // TODO: Edit goals
              },
              icon: Icon(Symbols.edit, size: 16.sp),
              label: const Text('Edit'),
            ),
          ],
        ),
        SizedBox(height: 16.h),
        Container(
          width: double.infinity,
          padding: EdgeInsets.all(16.w),
          decoration: BoxDecoration(
            color: theme.colorScheme.surface,
            borderRadius: BorderRadius.circular(12.r),
            border: Border.all(
              color: theme.colorScheme.outline.withValues(alpha: 0.2),
            ),
          ),
          child: Column(
            children: [
              _buildGoalItem(
                theme,
                title: 'Daily Goal',
                current: '2.5h',
                target: '3h',
                progress: 2.5 / 3.0,
                icon: Symbols.today,
              ),
              SizedBox(height: 16.h),
              _buildGoalItem(
                theme,
                title: 'Weekly Goal',
                current: '18h',
                target: '21h',
                progress: 18 / 21,
                icon: Symbols.calendar_view_week,
              ),
              SizedBox(height: 16.h),
              _buildGoalItem(
                theme,
                title: 'Monthly Goal',
                current: '72h',
                target: '90h',
                progress: 72 / 90,
                icon: Symbols.calendar_month,
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildGoalItem(
    ThemeData theme, {
    required String title,
    required String current,
    required String target,
    required double progress,
    required IconData icon,
  }) {
    final isOnTrack = progress >= 0.8;

    return Column(
      children: [
        Row(
          children: [
            Icon(
              icon,
              size: 20.sp,
              color: theme.colorScheme.primary,
            ),
            SizedBox(width: 8.w),
            Text(
              title,
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const Spacer(),
            Text(
              '$current / $target',
              style: theme.textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
                color: isOnTrack ? Colors.green : Colors.orange,
              ),
            ),
          ],
        ),
        SizedBox(height: 8.h),
        LinearProgressIndicator(
          value: progress.clamp(0.0, 1.0),
          backgroundColor: theme.colorScheme.outline.withValues(alpha: 0.2),
          valueColor: AlwaysStoppedAnimation<Color>(
            isOnTrack ? Colors.green : Colors.orange,
          ),
          borderRadius: BorderRadius.circular(4.r),
          minHeight: 6.h,
        ),
      ],
    );
  }

  List<Map<String, dynamic>> _getMockDailyTimes() {
    return [
      {'day': 'Mon', 'hours': 3.2, 'isToday': false},
      {'day': 'Tue', 'hours': 2.8, 'isToday': false},
      {'day': 'Wed', 'hours': 3.5, 'isToday': false},
      {'day': 'Thu', 'hours': 2.1, 'isToday': false},
      {'day': 'Fri', 'hours': 3.8, 'isToday': false},
      {'day': 'Sat', 'hours': 1.5, 'isToday': false},
      {'day': 'Sun', 'hours': 2.5, 'isToday': true},
    ];
  }

  List<Map<String, dynamic>> _getMockStudySessions() {
    return [
      {
        'title': 'Data Structures Study',
        'duration': '1h 30m',
        'date': 'Today',
        'focusScore': 92,
        'icon': Symbols.school,
        'color': Colors.blue,
      },
      {
        'title': 'Algorithm Practice',
        'duration': '45m',
        'date': 'Yesterday',
        'focusScore': 88,
        'icon': Symbols.code,
        'color': Colors.green,
      },
      {
        'title': 'Review Session',
        'duration': '30m',
        'date': '2 days ago',
        'focusScore': 95,
        'icon': Symbols.quiz,
        'color': Colors.purple,
      },
    ];
  }
}
