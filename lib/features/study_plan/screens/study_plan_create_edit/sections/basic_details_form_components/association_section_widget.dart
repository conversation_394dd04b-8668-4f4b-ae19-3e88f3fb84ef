import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_symbols_icons/symbols.dart';

import '../../../../enums/study_plan_enums.dart';

class AssociationSectionWidget extends StatelessWidget {
  final AssociationType? associationType;
  final String? associatedClassroomId;
  final String? associatedExamId;
  final Function(AssociationType?) onAssociationTypeChanged;
  final Function(String?) onClassroomChanged;
  final Function(String?) onExamChanged;

  const AssociationSectionWidget({
    super.key,
    required this.associationType,
    required this.associatedClassroomId,
    required this.associatedExamId,
    required this.onAssociationTypeChanged,
    required this.onClassroomChanged,
    required this.onExamChanged,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(Symbols.link, size: 20.sp, color: theme.colorScheme.primary),
            SizedBox(width: 8.w),
            Text(
              'Association',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        SizedBox(height: 8.h),
        Text(
          'Link this plan to a classroom, exam, or project (optional)',
          style: theme.textTheme.bodySmall?.copyWith(
            color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
          ),
        ),
        SizedBox(height: 12.h),

        // Association type selection
        DropdownButtonFormField<AssociationType>(
          value: associationType,
          decoration: InputDecoration(
            labelText: 'Association Type',
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12.r),
            ),
            contentPadding: EdgeInsets.symmetric(
              horizontal: 16.w,
              vertical: 12.h,
            ),
          ),
          items: [
            const DropdownMenuItem<AssociationType>(
              value: null,
              child: Text('None'),
            ),
            ...AssociationType.values.map((type) {
              return DropdownMenuItem<AssociationType>(
                value: type,
                child: Row(
                  children: [
                    Icon(type.icon, size: 16.sp),
                    SizedBox(width: 8.w),
                    Text(type.displayName),
                  ],
                ),
              );
            }),
          ],
          onChanged: onAssociationTypeChanged,
        ),

        // Show specific association fields based on type
        if (associationType != null) ...[
          SizedBox(height: 16.h),
          _buildSpecificAssociationField(theme),
        ],
      ],
    );
  }

  Widget _buildSpecificAssociationField(ThemeData theme) {
    switch (associationType!) {
      case AssociationType.classroom:
        return _buildClassroomSelector(theme);
      case AssociationType.exam:
        return _buildExamSelector(theme);
      case AssociationType.subject:
      case AssociationType.project:
      case AssociationType.none:
        return _buildGenericAssociationField(theme);
    }
  }

  Widget _buildClassroomSelector(ThemeData theme) {
    // TODO: Replace with actual classroom data
    final mockClassrooms = [
      {'id': '1', 'name': 'Mathematics Grade 10'},
      {'id': '2', 'name': 'Physics Grade 11'},
      {'id': '3', 'name': 'Chemistry Grade 12'},
    ];

    return DropdownButtonFormField<String>(
      value: associatedClassroomId,
      decoration: InputDecoration(
        labelText: 'Select Classroom',
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(12.r)),
        contentPadding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
      ),
      items: mockClassrooms.map((classroom) {
        return DropdownMenuItem<String>(
          value: classroom['id'],
          child: Text(classroom['name']!),
        );
      }).toList(),
      onChanged: onClassroomChanged,
    );
  }

  Widget _buildExamSelector(ThemeData theme) {
    // TODO: Replace with actual exam data
    final mockExams = [
      {'id': '1', 'name': 'Mid-term Mathematics'},
      {'id': '2', 'name': 'Final Physics Exam'},
      {'id': '3', 'name': 'Chemistry Quiz'},
    ];

    return DropdownButtonFormField<String>(
      value: associatedExamId,
      decoration: InputDecoration(
        labelText: 'Select Exam',
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(12.r)),
        contentPadding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
      ),
      items: mockExams.map((exam) {
        return DropdownMenuItem<String>(
          value: exam['id'],
          child: Text(exam['name']!),
        );
      }).toList(),
      onChanged: onExamChanged,
    );
  }

  Widget _buildGenericAssociationField(ThemeData theme) {
    return TextFormField(
      decoration: InputDecoration(
        labelText: 'Association Name',
        hintText: 'Enter ${associationType!.displayName.toLowerCase()} name',
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(12.r)),
        contentPadding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
      ),
      onChanged: (value) {
        // Store in appropriate field based on type
        if (associationType == AssociationType.subject) {
          onExamChanged(value); // Reuse exam field for subject
        } else {
          onClassroomChanged(value); // Reuse classroom field for project
        }
      },
    );
  }
}
