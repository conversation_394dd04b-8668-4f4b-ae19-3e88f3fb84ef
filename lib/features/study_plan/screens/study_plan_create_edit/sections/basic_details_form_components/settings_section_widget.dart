import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_symbols_icons/symbols.dart';

class SettingsSectionWidget extends StatelessWidget {
  final bool isMandatory;
  final bool canBeEditedAsTemplate;
  final Function(bool) onMandatoryChanged;
  final Function(bool) onTemplateChanged;

  const SettingsSectionWidget({
    super.key,
    required this.isMandatory,
    required this.canBeEditedAsTemplate,
    required this.onMandatoryChanged,
    required this.onTemplateChanged,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              Symbols.settings,
              size: 20.sp,
              color: theme.colorScheme.primary,
            ),
            SizedBox(width: 8.w),
            Text(
              'Settings',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        SizedBox(height: 12.h),

        // Mandatory setting
        SwitchListTile(
          title: const Text('Mandatory Plan'),
          subtitle: const Text('Students must complete this plan'),
          value: isMandatory,
          onChanged: onMandatoryChanged,
          contentPadding: EdgeInsets.zero,
        ),

        // Template setting
        SwitchListTile(
          title: const Text('Can be used as Template'),
          subtitle: const Text('Allow others to create plans from this'),
          value: canBeEditedAsTemplate,
          onChanged: onTemplateChanged,
          contentPadding: EdgeInsets.zero,
        ),
      ],
    );
  }
}
