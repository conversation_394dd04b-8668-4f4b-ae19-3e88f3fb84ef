import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_symbols_icons/symbols.dart';

class DurationSectionWidget extends StatelessWidget {
  final Duration? estimatedDuration;
  final DateTime? startDate;
  final DateTime? endDate;
  final Function(Duration?) onDurationChanged;
  final Function(DateTime?) onStartDateChanged;
  final Function(DateTime?) onEndDateChanged;

  const DurationSectionWidget({
    super.key,
    required this.estimatedDuration,
    required this.startDate,
    required this.endDate,
    required this.onDurationChanged,
    required this.onStartDateChanged,
    required this.onEndDateChanged,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              Symbols.schedule,
              size: 20.sp,
              color: theme.colorScheme.primary,
            ),
            SizedBox(width: 8.w),
            Text(
              'Duration & Timeline',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        SizedBox(height: 8.h),
        Text(
          'Choose a preset duration or set custom dates for your study plan',
          style: theme.textTheme.bodyMedium?.copyWith(
            color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
          ),
        ),
        SizedBox(height: 16.h),

        // Duration preset chips
        Wrap(
          spacing: 8.w,
          runSpacing: 8.h,
          children: [
            _buildDurationChip(theme, const Duration(days: 7), '1 Week'),
            _buildDurationChip(theme, const Duration(days: 14), '2 Weeks'),
            _buildDurationChip(theme, const Duration(days: 30), '1 Month'),
            _buildDurationChip(theme, const Duration(days: 60), '2 Months'),
            _buildDurationChip(theme, const Duration(days: 90), '3 Months'),
            _buildDurationChip(theme, const Duration(days: 180), '6 Months'),
          ],
        ),

        SizedBox(height: 16.h),

        // Date fields
        Row(
          children: [
            Expanded(
              child: _buildDateField('Start Date', startDate, (date) {
                onStartDateChanged(date);
                // Clear estimated duration when manually setting dates
                if (date != null) {
                  onDurationChanged(null);
                }
              }),
            ),
            SizedBox(width: 16.w),
            Expanded(
              child: _buildDateField('End Date', endDate, (date) {
                onEndDateChanged(date);
                // Clear estimated duration when manually setting dates
                if (date != null) {
                  onDurationChanged(null);
                }
              }),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildDurationChip(ThemeData theme, Duration duration, String label) {
    final isSelected = estimatedDuration == duration;

    return FilterChip(
      label: Text(label),
      selected: isSelected,
      onSelected: (selected) {
        if (selected) {
          onDurationChanged(duration);
          // Automatically set timeline based on duration
          final now = DateTime.now();
          onStartDateChanged(now);
          onEndDateChanged(now.add(duration));
        } else {
          onDurationChanged(null);
          // Clear timeline when deselecting
          onStartDateChanged(null);
          onEndDateChanged(null);
        }
      },
    );
  }

  Widget _buildDateField(
    String label,
    DateTime? value,
    Function(DateTime?) onChanged, {
    bool isOptional = false,
  }) {
    return Builder(
      builder: (context) {
        final theme = Theme.of(context);

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Text(
                  label,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
                if (isOptional) ...[
                  SizedBox(width: 4.w),
                  Text(
                    '(Optional)',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                    ),
                  ),
                ],
              ],
            ),
            SizedBox(height: 8.h),
            InkWell(
              onTap: () async {
                final date = await showDatePicker(
                  context: context,
                  initialDate: value ?? DateTime.now(),
                  firstDate: DateTime.now().subtract(const Duration(days: 30)),
                  lastDate: DateTime.now().add(const Duration(days: 365 * 2)),
                );
                onChanged(date);
              },
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
                decoration: BoxDecoration(
                  border: Border.all(color: theme.colorScheme.outline),
                  borderRadius: BorderRadius.circular(12.r),
                ),
                child: Row(
                  children: [
                    Icon(
                      Symbols.calendar_today,
                      size: 20.sp,
                      color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                    ),
                    SizedBox(width: 8.w),
                    Expanded(
                      child: Text(
                        value != null
                            ? '${value.day}/${value.month}/${value.year}'
                            : 'Select date',
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: value != null
                              ? theme.colorScheme.onSurface
                              : theme.colorScheme.onSurface.withValues(
                                  alpha: 0.5,
                                ),
                        ),
                      ),
                    ),
                    if (value != null)
                      GestureDetector(
                        onTap: () => onChanged(null),
                        child: Icon(
                          Symbols.close,
                          size: 16.sp,
                          color: theme.colorScheme.onSurface.withValues(
                            alpha: 0.5,
                          ),
                        ),
                      ),
                  ],
                ),
              ),
            ),
          ],
        );
      },
    );
  }
}
