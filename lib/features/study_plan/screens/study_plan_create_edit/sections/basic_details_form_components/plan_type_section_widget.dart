import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_symbols_icons/symbols.dart';

import '../../../../enums/study_plan_enums.dart';

class PlanTypeSectionWidget extends StatelessWidget {
  final StudyPlanType selectedType;
  final Function(StudyPlanType) onTypeChanged;

  const PlanTypeSectionWidget({
    super.key,
    required this.selectedType,
    required this.onTypeChanged,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              Symbols.category,
              size: 20.sp,
              color: theme.colorScheme.primary,
            ),
            SizedBox(width: 8.w),
            Text(
              'Plan Type',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(width: 4.w),
            Text(
              '*',
              style: TextStyle(color: theme.colorScheme.error, fontSize: 16.sp),
            ),
          ],
        ),
        Sized<PERSON>ox(height: 12.h),
        Wrap(
          spacing: 8.w,
          runSpacing: 8.h,
          children: StudyPlanType.values.map((type) {
            final isSelected = type == selectedType;
            return FilterChip(
              label: Text(type.displayName),
              selected: isSelected,
              onSelected: (selected) {
                if (selected) {
                  onTypeChanged(type);
                }
              },
              avatar: Icon(
                type.icon,
                size: 16.sp,
                color: isSelected
                    ? theme.colorScheme.onPrimary
                    : theme.colorScheme.primary,
              ),
            );
          }).toList(),
        ),
        SizedBox(height: 8.h),
        Text(
          selectedType.description,
          style: theme.textTheme.bodySmall?.copyWith(
            color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
          ),
        ),
      ],
    );
  }
}
