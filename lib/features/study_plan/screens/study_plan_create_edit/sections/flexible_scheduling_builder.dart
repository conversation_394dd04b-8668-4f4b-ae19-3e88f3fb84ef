import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_symbols_icons/symbols.dart';

import '../../../models/entity_schedule_manager.dart';
import '../../../models/study_plan_models.dart';

/// Widget for Step 3: Calendar-based Scheduling of study plan creation
class FlexibleSchedulingBuilder extends StatefulWidget {
  final Map<String, dynamic> formData;
  final Function(Map<String, dynamic>) onFormDataChanged;
  final bool isEditMode;

  const FlexibleSchedulingBuilder({
    super.key,
    required this.formData,
    required this.onFormDataChanged,
    this.isEditMode = false,
  });

  @override
  State<FlexibleSchedulingBuilder> createState() =>
      _FlexibleSchedulingBuilderState();
}

class _FlexibleSchedulingBuilderState extends State<FlexibleSchedulingBuilder> {
  bool _allowsScheduleEditing = false;
  DateTime _currentMonth = DateTime.now();
  DateTime? _selectedDate;

  // Entity assignments: Map<date, List<entityId>>
  Map<DateTime, List<String>> _entityAssignments = {};

  // All schedulable entities
  List<SchedulableEntity> _allEntities = [];
  List<SchedulableEntity> _unassignedEntities = [];

  @override
  void initState() {
    super.initState();
    _loadFormData();
  }

  void _loadFormData() {
    _allowsScheduleEditing = widget.formData['allowsScheduleEditing'] ?? false;

    // Load existing sections and extract all schedulable entities
    final sections =
        widget.formData['sections'] as List<StudyPlanSection>? ?? [];

    _allEntities = EntityScheduleManager.getAllSchedulableEntities(sections);
    _unassignedEntities = EntityScheduleManager.getUnscheduledEntities(
      sections,
    );

    // Load existing assignments if any
    final schedulingData =
        widget.formData['schedulingData'] as Map<String, dynamic>?;
    if (schedulingData != null) {
      _entityAssignments = _parseAssignments(
        schedulingData['entityAssignments'],
      );
    }
  }

  Map<DateTime, List<String>> _parseAssignments(dynamic data) {
    if (data == null) return {};
    final Map<DateTime, List<String>> result = {};

    if (data is Map<String, dynamic>) {
      for (final entry in data.entries) {
        final date = DateTime.tryParse(entry.key);
        if (date != null && entry.value is List) {
          result[date] = List<String>.from(entry.value);
        }
      }
    }
    return result;
  }

  void _onFormChanged() {
    final schedulingData = {
      'entityAssignments': _serializeAssignments(_entityAssignments),
    };

    final updatedData = {
      'allowsScheduleEditing': _allowsScheduleEditing,
      'schedulingData': schedulingData,
    };
    widget.onFormDataChanged(updatedData);
  }

  Map<String, List<String>> _serializeAssignments(
    Map<DateTime, List<String>> assignments,
  ) {
    final Map<String, List<String>> result = {};
    for (final entry in assignments.entries) {
      result[entry.key.toIso8601String()] = entry.value;
    }
    return result;
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Enable editing toggle
        _buildEditingToggle(theme),

        SizedBox(height: 24.h),

        _buildCalendarSection(theme),
        SizedBox(height: 24.h),
        _buildAssignmentSection(theme),
      ],
    );
  }

  Widget _buildEditingToggle(ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SwitchListTile(
          title: const Text('Allow Schedule Editing'),
          subtitle: const Text(
            'Students can assign tasks and sections to specific dates',
          ),
          value: _allowsScheduleEditing,
          onChanged: (value) {
            setState(() {
              _allowsScheduleEditing = value;
            });
            _onFormChanged();
          },
          contentPadding: EdgeInsets.zero,
        ),
      ],
    );
  }

  Widget _buildCalendarSection(ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Calendar View',
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: 8.h),
        Text(
          'Click on dates to assign tasks and sections. Color-coded dates show assignments.',
          style: theme.textTheme.bodyMedium?.copyWith(
            color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
          ),
        ),
        SizedBox(height: 16.h),

        // Month navigation
        _buildMonthNavigation(theme),
        SizedBox(height: 16.h),

        // Calendar grid
        _buildCalendarGrid(theme),
      ],
    );
  }

  Widget _buildMonthNavigation(ThemeData theme) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        IconButton(
          onPressed: () {
            setState(() {
              _currentMonth = DateTime(
                _currentMonth.year,
                _currentMonth.month - 1,
              );
            });
          },
          icon: const Icon(Symbols.chevron_left),
        ),
        Text(
          '${_getMonthName(_currentMonth.month)} ${_currentMonth.year}',
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        IconButton(
          onPressed: () {
            setState(() {
              _currentMonth = DateTime(
                _currentMonth.year,
                _currentMonth.month + 1,
              );
            });
          },
          icon: const Icon(Symbols.chevron_right),
        ),
      ],
    );
  }

  Widget _buildCalendarGrid(ThemeData theme) {
    final daysInMonth = DateTime(
      _currentMonth.year,
      _currentMonth.month + 1,
      0,
    ).day;
    final firstDayOfMonth = DateTime(
      _currentMonth.year,
      _currentMonth.month,
      1,
    );
    final startingWeekday = firstDayOfMonth.weekday;

    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(
          color: theme.colorScheme.outline.withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        children: [
          // Weekday headers
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
                .map(
                  (day) => Text(
                    day,
                    style: theme.textTheme.labelSmall?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                    ),
                  ),
                )
                .toList(),
          ),
          SizedBox(height: 8.h),

          // Calendar days
          ...List.generate(6, (weekIndex) {
            return Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: List.generate(7, (dayIndex) {
                final dayNumber =
                    weekIndex * 7 + dayIndex + 1 - (startingWeekday - 1);
                if (dayNumber < 1 || dayNumber > daysInMonth) {
                  return SizedBox(width: 32.w, height: 32.h);
                }

                final date = DateTime(
                  _currentMonth.year,
                  _currentMonth.month,
                  dayNumber,
                );
                return _buildCalendarDay(theme, date);
              }),
            );
          }),
        ],
      ),
    );
  }

  Widget _buildCalendarDay(ThemeData theme, DateTime date) {
    final hasAssignments = _hasAssignmentsOnDate(date);
    final isSelected =
        _selectedDate != null && _isSameDay(_selectedDate!, date);
    final isToday = _isSameDay(date, DateTime.now());

    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedDate = isSelected ? null : date;
        });
      },
      child: Container(
        width: 32.w,
        height: 32.h,
        decoration: BoxDecoration(
          color: isSelected
              ? theme.colorScheme.primary
              : hasAssignments
              ? theme.colorScheme.primaryContainer
              : null,
          borderRadius: BorderRadius.circular(16.r),
          border: isToday
              ? Border.all(color: theme.colorScheme.primary, width: 2)
              : null,
        ),
        child: Center(
          child: Text(
            date.day.toString(),
            style: theme.textTheme.bodySmall?.copyWith(
              color: isSelected
                  ? theme.colorScheme.onPrimary
                  : hasAssignments
                  ? theme.colorScheme.onPrimaryContainer
                  : theme.colorScheme.onSurface,
              fontWeight: isToday ? FontWeight.bold : FontWeight.normal,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildAssignmentSection(ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          _selectedDate != null
              ? 'Assignments for ${_formatDate(_selectedDate!)}'
              : 'Unassigned Items',
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: 16.h),

        if (_selectedDate != null) ...[
          _buildAssignedItems(theme),
          SizedBox(height: 16.h),
          _buildAssignButton(theme),
        ] else ...[
          _buildUnassignedItems(theme),
        ],
      ],
    );
  }

  Widget _buildAssignedItems(ThemeData theme) {
    final assignedEntityIds = _entityAssignments[_selectedDate] ?? [];

    if (assignedEntityIds.isEmpty) {
      return Container(
        padding: EdgeInsets.all(16.w),
        decoration: BoxDecoration(
          color: theme.colorScheme.surfaceContainerHighest.withValues(
            alpha: 0.3,
          ),
          borderRadius: BorderRadius.circular(8.r),
        ),
        child: Text(
          'No items assigned to this date',
          style: theme.textTheme.bodyMedium?.copyWith(
            color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
          ),
        ),
      );
    }

    return Column(
      children: assignedEntityIds
          .map((entityId) => _buildAssignedEntityCard(theme, entityId))
          .toList(),
    );
  }

  Widget _buildAssignedEntityCard(ThemeData theme, String entityId) {
    final entity = _allEntities.firstWhere(
      (e) => e.id == entityId,
      orElse: () => SchedulableEntity(
        id: entityId,
        title: 'Unknown Entity',
        type: SchedulableEntityType.section,
      ),
    );

    IconData icon;
    Color iconColor;

    switch (entity.type) {
      case SchedulableEntityType.section:
        icon = Symbols.folder;
        iconColor = theme.colorScheme.primary;
        break;
      case SchedulableEntityType.subSection:
        icon = Symbols.folder_open;
        iconColor = theme.colorScheme.secondary;
        break;
      case SchedulableEntityType.task:
        icon = Symbols.task_alt;
        iconColor = theme.colorScheme.tertiary;
        break;
    }

    return Card(
      child: ListTile(
        leading: Icon(icon, color: iconColor),
        title: Text(entity.title),
        subtitle: Text(entity.typeDisplayText),
        trailing: IconButton(
          icon: const Icon(Symbols.remove),
          onPressed: () => _unassignEntity(entityId),
        ),
      ),
    );
  }

  Widget _buildAssignButton(ThemeData theme) {
    return ElevatedButton.icon(
      onPressed: () => _showAssignmentDialog(),
      icon: const Icon(Symbols.add),
      label: const Text('Assign Items'),
    );
  }

  Widget _buildUnassignedItems(ThemeData theme) {
    if (_unassignedEntities.isEmpty) {
      return Container(
        padding: EdgeInsets.all(16.w),
        decoration: BoxDecoration(
          color: theme.colorScheme.surfaceContainerHighest.withValues(
            alpha: 0.3,
          ),
          borderRadius: BorderRadius.circular(8.r),
        ),
        child: Text(
          'All items have been assigned to dates',
          style: theme.textTheme.bodyMedium?.copyWith(
            color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
          ),
        ),
      );
    }

    return Column(
      children: _unassignedEntities.map((entity) {
        IconData icon;
        Color iconColor;

        switch (entity.type) {
          case SchedulableEntityType.section:
            icon = Symbols.folder;
            iconColor = theme.colorScheme.primary;
            break;
          case SchedulableEntityType.subSection:
            icon = Symbols.folder_open;
            iconColor = theme.colorScheme.secondary;
            break;
          case SchedulableEntityType.task:
            icon = Symbols.task_alt;
            iconColor = theme.colorScheme.tertiary;
            break;
        }

        return Card(
          child: ListTile(
            leading: Icon(icon, color: iconColor),
            title: Text(entity.title),
            subtitle: Text(entity.typeDisplayText),
            trailing: IconButton(
              icon: const Icon(Symbols.calendar_add_on),
              onPressed: () => _showDatePickerForEntity(entity),
            ),
          ),
        );
      }).toList(),
    );
  }

  // Helper methods
  bool _hasAssignmentsOnDate(DateTime date) {
    return _entityAssignments[date]?.isNotEmpty ?? false;
  }

  void _unassignEntity(String entityId) {
    setState(() {
      _entityAssignments[_selectedDate!]?.remove(entityId);
      if (_entityAssignments[_selectedDate!]?.isEmpty ?? false) {
        _entityAssignments.remove(_selectedDate!);
      }

      // Add entity back to unassigned list
      final entity = _allEntities.firstWhere((e) => e.id == entityId);
      if (!_unassignedEntities.contains(entity)) {
        _unassignedEntities.add(entity);
      }
    });
    _onFormChanged();
  }

  void _showDatePickerForEntity(SchedulableEntity entity) async {
    final selectedDate = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );

    if (selectedDate != null) {
      setState(() {
        _entityAssignments[selectedDate] = [
          ...(_entityAssignments[selectedDate] ?? []),
          entity.id,
        ];
        _unassignedEntities.removeWhere((e) => e.id == entity.id);
      });
      _onFormChanged();
    }
  }

  bool _isSameDay(DateTime date1, DateTime date2) {
    return date1.year == date2.year &&
        date1.month == date2.month &&
        date1.day == date2.day;
  }

  String _getMonthName(int month) {
    const months = [
      'January',
      'February',
      'March',
      'April',
      'May',
      'June',
      'July',
      'August',
      'September',
      'October',
      'November',
      'December',
    ];
    return months[month - 1];
  }

  String _formatDate(DateTime date) {
    return '${date.day} ${_getMonthName(date.month)} ${date.year}';
  }

  void _showAssignmentDialog() {
    // TODO: Implement assignment dialog
    // This would show a dialog with unassigned items to select from
  }
}
