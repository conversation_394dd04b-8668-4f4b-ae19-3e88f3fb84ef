import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_symbols_icons/symbols.dart';

import '../../../enums/study_plan_enums.dart';
import 'basic_details_form_components/custom_text_field_widget.dart';
import 'basic_details_form_components/plan_type_section_widget.dart';
import 'basic_details_form_components/settings_section_widget.dart';
import 'basic_details_form_components/association_section_widget.dart';
import 'basic_details_form_components/duration_section_widget.dart';

/// Form widget for Step 1: Basic Details of study plan creation
class BasicDetailsForm extends StatefulWidget {
  final Map<String, dynamic> formData;
  final Function(Map<String, dynamic>) onFormDataChanged;
  final bool isEditMode;

  const BasicDetailsForm({
    super.key,
    required this.formData,
    required this.onFormDataChanged,
    this.isEditMode = false,
  });

  @override
  State<BasicDetailsForm> createState() => _BasicDetailsFormState();
}

class _BasicDetailsFormState extends State<BasicDetailsForm> {
  late TextEditingController _titleController;
  late TextEditingController _descriptionController;

  StudyPlanType _selectedType = StudyPlanType.general;
  bool _isMandatory = false;
  bool _canBeEditedAsTemplate = false;
  AssociationType? _associationType;
  String? _associatedClassroomId;
  String? _associatedExamId;

  // Duration and timeline fields
  DateTime? _startDate;
  DateTime? _endDate;
  Duration? _estimatedDuration;

  @override
  void initState() {
    super.initState();
    _initializeControllers();
    _loadFormData();
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  void _initializeControllers() {
    _titleController = TextEditingController();
    _descriptionController = TextEditingController();

    // Listen to text changes
    _titleController.addListener(_onFormChanged);
    _descriptionController.addListener(_onFormChanged);
  }

  void _loadFormData() {
    _titleController.text = widget.formData['title'] ?? '';
    _descriptionController.text = widget.formData['description'] ?? '';
    _selectedType = widget.formData['type'] ?? StudyPlanType.general;
    _isMandatory = widget.formData['isMandatory'] ?? false;
    _canBeEditedAsTemplate = widget.formData['canBeEditedAsTemplate'] ?? false;
    _associationType = widget.formData['associationType'];
    _associatedClassroomId = widget.formData['associatedClassroomId'];
    _associatedExamId = widget.formData['associatedExamId'];

    // Load duration and timeline data
    _startDate = widget.formData['startDate'];
    _endDate = widget.formData['endDate'];
    _estimatedDuration = widget.formData['estimatedDuration'];
  }

  void _onFormChanged() {
    final updatedData = {
      'title': _titleController.text,
      'description': _descriptionController.text,
      'type': _selectedType,
      'isMandatory': _isMandatory,
      'canBeEditedAsTemplate': _canBeEditedAsTemplate,
      'associationType': _associationType,
      'associatedClassroomId': _associatedClassroomId,
      'associatedExamId': _associatedExamId,
      'startDate': _startDate,
      'endDate': _endDate,
      'estimatedDuration': _estimatedDuration,
    };

    widget.onFormDataChanged(updatedData);
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title field
          CustomTextFieldWidget(
            controller: _titleController,
            label: 'Plan Title',
            hint: 'Enter a descriptive title for your study plan',
            isRequired: true,
            icon: Symbols.title,
            onChanged: (_) => _onFormChanged(),
          ),

          SizedBox(height: 20.h),

          // Description field
          CustomTextFieldWidget(
            controller: _descriptionController,
            label: 'Description',
            hint: 'Describe the purpose and goals of this study plan',
            maxLines: 3,
            icon: Symbols.description,
            onChanged: (_) => _onFormChanged(),
          ),

          SizedBox(height: 24.h),

          // Duration & Timeline section
          DurationSectionWidget(
            estimatedDuration: _estimatedDuration,
            startDate: _startDate,
            endDate: _endDate,
            onDurationChanged: (duration) {
              setState(() {
                _estimatedDuration = duration;
              });
              _onFormChanged();
            },
            onStartDateChanged: (date) {
              setState(() {
                _startDate = date;
              });
              _onFormChanged();
            },
            onEndDateChanged: (date) {
              setState(() {
                _endDate = date;
              });
              _onFormChanged();
            },
          ),

          SizedBox(height: 24.h),

          // Plan type selection
          PlanTypeSectionWidget(
            selectedType: _selectedType,
            onTypeChanged: (type) {
              setState(() {
                _selectedType = type;
              });
              _onFormChanged();
            },
          ),

          SizedBox(height: 24.h),

          // Settings section
          SettingsSectionWidget(
            isMandatory: _isMandatory,
            canBeEditedAsTemplate: _canBeEditedAsTemplate,
            onMandatoryChanged: (value) {
              setState(() {
                _isMandatory = value;
              });
              _onFormChanged();
            },
            onTemplateChanged: (value) {
              setState(() {
                _canBeEditedAsTemplate = value;
              });
              _onFormChanged();
            },
          ),

          SizedBox(height: 24.h),

          // Association section
          AssociationSectionWidget(
            associationType: _associationType,
            associatedClassroomId: _associatedClassroomId,
            associatedExamId: _associatedExamId,
            onAssociationTypeChanged: (type) {
              setState(() {
                _associationType = type;
                // Reset associated IDs when type changes
                _associatedClassroomId = null;
                _associatedExamId = null;
              });
              _onFormChanged();
            },
            onClassroomChanged: (id) {
              setState(() {
                _associatedClassroomId = id;
              });
              _onFormChanged();
            },
            onExamChanged: (id) {
              setState(() {
                _associatedExamId = id;
              });
              _onFormChanged();
            },
          ),
        ],
      ),
    );
  }
}
