import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_symbols_icons/symbols.dart';

import '../../../../models/study_plan_models.dart';
import '../../../../enums/section_type.dart';
import 'schedule_chip_widget.dart';
import 'task_card_for_sub_section_widget.dart';

class SubSectionCardWidget extends StatelessWidget {
  final StudyPlanSection subSection;
  final int parentIndex;
  final Function(String action, StudyPlanSection subSection, int parentIndex)
  onSubSectionAction;
  final Function(
    String action,
    StudyPlanTask task,
    int parentIndex,
    String subSectionId,
  )?
  onTaskActionForSubSection;

  const SubSectionCardWidget({
    super.key,
    required this.subSection,
    required this.parentIndex,
    required this.onSubSectionAction,
    this.onTaskActionForSubSection,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Card(
      elevation: 1,
      margin: EdgeInsets.only(bottom: 8.h),
      child: ExpansionTile(
        leading: Icon(
          subSection.type.icon,
          color: theme.colorScheme.primary,
          size: 20.sp,
        ),
        title: Text(
          subSection.title,
          style: theme.textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w500,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (subSection.description != null)
              Text(
                subSection.description!,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                ),
              ),
            // Schedule display for subsection
            if (subSection.startDate != null ||
                subSection.endDate != null ||
                subSection.estimatedDuration != null ||
                (subSection.individualSchedule != null &&
                    subSection.individualSchedule!.isValid))
              Padding(
                padding: EdgeInsets.only(top: 4.h),
                child: ScheduleChipWidget(section: subSection),
              ),
          ],
        ),
        trailing: PopupMenuButton<String>(
          icon: Icon(Symbols.more_vert, size: 18.sp),
          onSelected: (action) =>
              onSubSectionAction(action, subSection, parentIndex),
          itemBuilder: (context) => [
            const PopupMenuItem(value: 'edit', child: Text('Edit')),
            const PopupMenuItem(value: 'add_task', child: Text('Add Task')),
            const PopupMenuItem(value: 'duplicate', child: Text('Duplicate')),
            const PopupMenuItem(value: 'delete', child: Text('Delete')),
          ],
        ),
        children: [
          // Tasks content for subsection
          Padding(
            padding: EdgeInsets.all(16.w),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Tasks section
                if (subSection.tasks.isNotEmpty) ...[
                  Row(
                    children: [
                      Icon(
                        Symbols.task_alt,
                        size: 16.sp,
                        color: theme.colorScheme.secondary,
                      ),
                      SizedBox(width: 8.w),
                      Text(
                        'Tasks',
                        style: theme.textTheme.labelMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                          color: theme.colorScheme.secondary,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 8.h),
                  for (final task in subSection.tasks) ...[
                    TaskCardForSubSectionWidget(
                      task: task,
                      parentIndex: parentIndex,
                      subSectionId: subSection.id,
                      onTaskAction:
                          onTaskActionForSubSection ??
                          (action, task, parentIndex, subSectionId) {
                            // Default empty handler if none provided
                          },
                    ),
                    SizedBox(height: 8.h),
                  ],
                  SizedBox(height: 8.h),
                ],

                // Add task button
                SizedBox(
                  width: double.infinity,
                  child: OutlinedButton.icon(
                    onPressed: () =>
                        onSubSectionAction('add_task', subSection, parentIndex),
                    icon: Icon(Symbols.add, size: 16.sp),
                    label: const Text('Add Task'),
                    style: OutlinedButton.styleFrom(
                      foregroundColor: theme.colorScheme.secondary,
                      side: BorderSide(color: theme.colorScheme.secondary),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
