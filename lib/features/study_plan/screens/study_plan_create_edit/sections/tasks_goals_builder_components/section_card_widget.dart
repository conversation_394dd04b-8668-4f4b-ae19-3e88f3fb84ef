import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_symbols_icons/symbols.dart';

import '../../../../models/study_plan_models.dart';
import '../../../../enums/section_type.dart';
import 'schedule_chip_widget.dart';
import 'task_card_widget.dart';
import 'sub_section_card_widget.dart';

class SectionCardWidget extends StatelessWidget {
  final StudyPlanSection section;
  final int index;
  final Function(String action, int index) onSectionAction;
  final Function(int index) onAddTask;
  final Function(int index) onAddSubSection;
  final Function(String action, StudyPlanTask task, int sectionIndex)
  onTaskAction;
  final Function(String action, StudyPlanSection subSection, int parentIndex)
  onSubSectionAction;
  final Function(
    String action,
    StudyPlanTask task,
    int parentIndex,
    String subSectionId,
  )?
  onTaskActionForSubSection;

  const SectionCardWidget({
    super.key,
    required this.section,
    required this.index,
    required this.onSectionAction,
    required this.onAddTask,
    required this.onAddSubSection,
    required this.onTaskAction,
    required this.onSubSectionAction,
    this.onTaskActionForSubSection,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Card(
      elevation: 2,
      child: ExpansionTile(
        leading: Icon(section.type.icon, color: theme.colorScheme.primary),
        title: Text(
          section.title,
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (section.description != null)
              Text(
                section.description!,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            // Schedule display
            if (section.startDate != null ||
                section.endDate != null ||
                section.estimatedDuration != null ||
                (section.individualSchedule != null &&
                    section.individualSchedule!.isValid))
              Padding(
                padding: EdgeInsets.only(top: 4.h),
                child: ScheduleChipWidget(section: section),
              ),
          ],
        ),
        trailing: PopupMenuButton<String>(
          icon: const Icon(Symbols.more_vert),
          onSelected: (action) => onSectionAction(action, index),
          itemBuilder: (context) => [
            const PopupMenuItem(value: 'edit', child: Text('Edit')),
            const PopupMenuItem(value: 'add_task', child: Text('Add Task')),
            const PopupMenuItem(
              value: 'add_subsection',
              child: Text('Add Sub-item'),
            ),
            const PopupMenuItem(value: 'duplicate', child: Text('Duplicate')),
            const PopupMenuItem(value: 'delete', child: Text('Delete')),
          ],
        ),
        children: [
          // Tasks and subsections content
          Padding(
            padding: EdgeInsets.all(16.w),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Tasks section
                if (section.tasks.isNotEmpty) ...[
                  Row(
                    children: [
                      Icon(
                        Symbols.task_alt,
                        size: 16.sp,
                        color: theme.colorScheme.secondary,
                      ),
                      SizedBox(width: 8.w),
                      Text(
                        'Tasks',
                        style: theme.textTheme.labelMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                          color: theme.colorScheme.secondary,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 8.h),
                  for (final task in section.tasks) ...[
                    TaskCardWidget(
                      task: task,
                      sectionIndex: index,
                      onTaskAction: onTaskAction,
                    ),
                    SizedBox(height: 8.h),
                  ],
                  SizedBox(height: 8.h),
                ],

                // Sub-sections
                if (section.subSections.isNotEmpty) ...[
                  Row(
                    children: [
                      Icon(
                        Symbols.folder,
                        size: 16.sp,
                        color: theme.colorScheme.primary,
                      ),
                      SizedBox(width: 8.w),
                      Text(
                        'Sub-sections',
                        style: theme.textTheme.labelMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                          color: theme.colorScheme.primary,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 8.h),
                  for (final subSection in section.subSections) ...[
                    SubSectionCardWidget(
                      subSection: subSection,
                      parentIndex: index,
                      onSubSectionAction: onSubSectionAction,
                      onTaskActionForSubSection: onTaskActionForSubSection,
                    ),
                    SizedBox(height: 8.h),
                  ],
                  SizedBox(height: 8.h),
                ],

                // Add buttons
                Row(
                  children: [
                    Expanded(
                      child: OutlinedButton.icon(
                        onPressed: () => onAddTask(index),
                        icon: Icon(Symbols.add, size: 16.sp),
                        label: const Text('Add Task'),
                        style: OutlinedButton.styleFrom(
                          foregroundColor: theme.colorScheme.secondary,
                          side: BorderSide(color: theme.colorScheme.secondary),
                        ),
                      ),
                    ),
                    SizedBox(width: 8.w),
                    Expanded(
                      child: OutlinedButton.icon(
                        onPressed: () => onAddSubSection(index),
                        icon: Icon(Symbols.add, size: 16.sp),
                        label: const Text('Add Sub-section'),
                        style: OutlinedButton.styleFrom(
                          foregroundColor: theme.colorScheme.primary,
                          side: BorderSide(color: theme.colorScheme.primary),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
