import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_symbols_icons/symbols.dart';

import '../../../../models/study_plan_models.dart';

class ScheduleChipWidget extends StatelessWidget {
  final StudyPlanSection section;

  const ScheduleChipWidget({super.key, required this.section});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    String scheduleText = '';
    IconData scheduleIcon = Symbols.schedule;

    // Prioritize individual scheduling information
    if (section.individualSchedule != null &&
        section.individualSchedule!.isValid) {
      scheduleText = section.individualSchedule!.displayText;
      switch (section.individualSchedule!.type) {
        case SchedulingType.date:
          scheduleIcon = Symbols.date_range;
          break;
        case SchedulingType.duration:
          scheduleIcon = Symbols.timer;
          break;
        case SchedulingType.none:
          scheduleIcon = Symbols.schedule;
          break;
      }
    } else if (section.estimatedDuration != null) {
      final days = section.estimatedDuration!.inDays;
      final hours = section.estimatedDuration!.inHours % 24;
      if (days > 0) {
        scheduleText = '${days}d ${hours}h';
      } else {
        scheduleText = '${hours}h';
      }
      scheduleIcon = Symbols.timer;
    } else if (section.startDate != null && section.endDate != null) {
      scheduleText =
          '${section.startDate!.day}/${section.startDate!.month} - ${section.endDate!.day}/${section.endDate!.month}';
      scheduleIcon = Symbols.date_range;
    } else if (section.endDate != null) {
      scheduleText = 'Due: ${section.endDate!.day}/${section.endDate!.month}';
      scheduleIcon = Symbols.event;
    }

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
      decoration: BoxDecoration(
        color: theme.colorScheme.secondaryContainer.withValues(alpha: 0.7),
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            scheduleIcon,
            size: 14.sp,
            color: theme.colorScheme.onSecondaryContainer,
          ),
          SizedBox(width: 4.w),
          Text(
            scheduleText,
            style: theme.textTheme.labelSmall?.copyWith(
              color: theme.colorScheme.onSecondaryContainer,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }
}
