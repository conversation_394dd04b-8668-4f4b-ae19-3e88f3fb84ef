import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_symbols_icons/symbols.dart';

import '../../../enums/study_plan_enums.dart';
import '../../../enums/study_item_type.dart';
import '../../../models/study_plan_models.dart';
import '../../../widgets/schedule_selection_widget.dart';
import 'tasks_goals_builder_components/section_card_widget.dart';

class TasksAndGoalsBuilder extends StatefulWidget {
  final Map<String, dynamic> formData;
  final Function(Map<String, dynamic>) onFormDataChanged;
  final bool isEditMode;

  const TasksAndGoalsBuilder({
    super.key,
    required this.formData,
    required this.onFormDataChanged,
    this.isEditMode = false,
  });

  @override
  State<TasksAndGoalsBuilder> createState() => _TasksAndGoalsBuilderState();
}

class _TasksAndGoalsBuilderState extends State<TasksAndGoalsBuilder> {
  List<StudyPlanSection> _sections = [];
  final TextEditingController _titleController = TextEditingController();
  final TextEditingController _descriptionController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _loadFormData();
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  void _loadFormData() {
    final sections = widget.formData['sections'] as List<StudyPlanSection>?;
    if (sections != null) {
      _sections = List.from(sections);
    }
  }

  void _onFormChanged() {
    final updatedData = {'sections': _sections};
    widget.onFormDataChanged(updatedData);
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header with enhanced add button
        Row(
          children: [
            Text(
              'Study Plan Structure',
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const Spacer(),
            FilledButton.icon(
              onPressed: _showAddItemDialog,
              icon: const Icon(Symbols.add),
              label: const Text('Add Item'),
            ),
          ],
        ),

        SizedBox(height: 16.h),

        // Enhanced instructions
        Container(
          padding: EdgeInsets.all(12.w),
          decoration: BoxDecoration(
            color: theme.colorScheme.primaryContainer.withValues(alpha: 0.3),
            borderRadius: BorderRadius.circular(8.r),
            border: Border.all(
              color: theme.colorScheme.primary.withValues(alpha: 0.3),
            ),
          ),
          child: Row(
            children: [
              Icon(Symbols.info, size: 20.sp, color: theme.colorScheme.primary),
              SizedBox(width: 8.w),
              Expanded(
                child: Text(
                  'Add chapters, topics, tasks, and more. Set flexible schedules with dates, deadlines, or durations.',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurface.withValues(alpha: 0.8),
                  ),
                ),
              ),
            ],
          ),
        ),

        SizedBox(height: 20.h),

        // Sections list
        _sections.isEmpty ? _buildEmptyState(theme) : _buildSectionsList(theme),
      ],
    );
  }

  Widget _buildEmptyState(ThemeData theme) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Symbols.auto_stories,
            size: 64.sp,
            color: theme.colorScheme.onSurface.withValues(alpha: 0.3),
          ),
          SizedBox(height: 16.h),
          Text(
            'Start Building Your Study Plan',
            style: theme.textTheme.titleMedium?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            'Add chapters, topics, tasks, and set flexible schedules',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
            ),
          ),
          SizedBox(height: 24.h),
          FilledButton.icon(
            onPressed: _showAddItemDialog,
            icon: const Icon(Symbols.add),
            label: const Text('Add First Item'),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionsList(ThemeData theme) {
    return Column(
      children: [
        for (int index = 0; index < _sections.length; index++) ...[
          SectionCardWidget(
            section: _sections[index],
            index: index,
            onSectionAction: _handleSectionAction,
            onAddTask: _showAddTaskToSectionDialog,
            onAddSubSection: _showAddSubSectionDialog,
            onTaskAction: _handleTaskAction,
            onSubSectionAction: _handleSubSectionAction,
            onTaskActionForSubSection: _handleTaskActionForSubSection,
          ),
          if (index < _sections.length - 1) SizedBox(height: 12.h),
        ],
      ],
    );
  }

  void _handleTaskActionForSubSection(
    String action,
    StudyPlanTask task,
    int parentIndex,
    String subSectionId,
  ) {
    switch (action) {
      case 'edit':
        _showEditTaskInSubSectionDialog(task, parentIndex, subSectionId);
        break;
      case 'duplicate':
        _duplicateTaskInSubSection(task, parentIndex, subSectionId);
        break;
      case 'delete':
        _deleteTaskFromSubSection(task, parentIndex, subSectionId);
        break;
    }
  }

  void _duplicateTaskInSubSection(
    StudyPlanTask task,
    int parentIndex,
    String subSectionId,
  ) {
    final duplicatedTask = task.copyWith(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      title: '${task.title} (Copy)',
    );

    setState(() {
      final section = _sections[parentIndex];
      final subSectionIndex = section.subSections.indexWhere(
        (s) => s.id == subSectionId,
      );
      if (subSectionIndex != -1) {
        final subSection = section.subSections[subSectionIndex];
        final updatedSubSection = subSection.copyWith(
          tasks: [...subSection.tasks, duplicatedTask],
        );
        final updatedSubSections = List<StudyPlanSection>.from(
          section.subSections,
        );
        updatedSubSections[subSectionIndex] = updatedSubSection;
        _sections[parentIndex] = section.copyWith(
          subSections: updatedSubSections,
        );
      }
    });
    _onFormChanged();
  }

  void _deleteTaskFromSubSection(
    StudyPlanTask task,
    int parentIndex,
    String subSectionId,
  ) {
    setState(() {
      final section = _sections[parentIndex];
      final subSectionIndex = section.subSections.indexWhere(
        (s) => s.id == subSectionId,
      );
      if (subSectionIndex != -1) {
        final subSection = section.subSections[subSectionIndex];
        final updatedTasks = subSection.tasks
            .where((t) => t.id != task.id)
            .toList();
        final updatedSubSection = subSection.copyWith(tasks: updatedTasks);
        final updatedSubSections = List<StudyPlanSection>.from(
          section.subSections,
        );
        updatedSubSections[subSectionIndex] = updatedSubSection;
        _sections[parentIndex] = section.copyWith(
          subSections: updatedSubSections,
        );
      }
    });
    _onFormChanged();
  }

  // Enhanced Add Item Dialog - Step 1: Choose Type
  void _showAddItemDialog() {
    showDialog(
      context: context,
      builder: (context) => _buildChooseTypeDialog(),
    );
  }

  Widget _buildChooseTypeDialog() {
    final theme = Theme.of(context);

    return AlertDialog(
      title: const Text('What would you like to add?'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Section option
          Card(
            child: InkWell(
              onTap: () {
                Navigator.of(context).pop();
                _showSectionTypeDialog();
              },
              borderRadius: BorderRadius.circular(12.r),
              child: Padding(
                padding: EdgeInsets.all(20.w),
                child: Column(
                  children: [
                    Icon(
                      Symbols.folder,
                      size: 48.sp,
                      color: theme.colorScheme.primary,
                    ),
                    SizedBox(height: 12.h),
                    Text(
                      'Section',
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    SizedBox(height: 4.h),
                    Text(
                      'Organize content into chapters, topics, or subjects',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.onSurface.withValues(
                          alpha: 0.7,
                        ),
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ),
          ),

          SizedBox(height: 16.h),

          // Task option
          Card(
            child: InkWell(
              onTap: () {
                Navigator.of(context).pop();
                _showTaskTypeDialog();
              },
              borderRadius: BorderRadius.circular(12.r),
              child: Padding(
                padding: EdgeInsets.all(20.w),
                child: Column(
                  children: [
                    Icon(
                      Symbols.task_alt,
                      size: 48.sp,
                      color: theme.colorScheme.secondary,
                    ),
                    SizedBox(height: 12.h),
                    Text(
                      'Task',
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    SizedBox(height: 4.h),
                    Text(
                      'Add specific activities like reading, practice, or assignments',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.onSurface.withValues(
                          alpha: 0.7,
                        ),
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
      ],
    );
  }

  // Step 2a: Choose Section Type
  void _showSectionTypeDialog() {
    showDialog(
      context: context,
      builder: (context) => _buildSectionTypeDialog(),
    );
  }

  Widget _buildSectionTypeDialog() {
    final theme = Theme.of(context);
    final sectionTypes = StudyItemTypeHelper.sectionTypes;

    return AlertDialog(
      title: const Text('Choose Section Type'),
      content: SizedBox(
        width: double.maxFinite,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            GridView.builder(
              shrinkWrap: true,
              gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                crossAxisSpacing: 8.w,
                mainAxisSpacing: 8.h,
                childAspectRatio: 2.5,
              ),
              itemCount: sectionTypes.length,
              itemBuilder: (context, index) {
                final type = sectionTypes[index];
                return Card(
                  child: InkWell(
                    onTap: () {
                      Navigator.of(context).pop();
                      _showItemDetailsDialog(type);
                    },
                    borderRadius: BorderRadius.circular(8.r),
                    child: Padding(
                      padding: EdgeInsets.all(8.w),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(type.icon, color: type.color, size: 20.sp),
                          SizedBox(width: 8.w),
                          Flexible(
                            child: Text(
                              type.displayName,
                              style: theme.textTheme.bodyMedium?.copyWith(
                                fontWeight: FontWeight.w500,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                );
              },
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Back'),
        ),
      ],
    );
  }

  // Step 2b: Choose Task Type
  void _showTaskTypeDialog() {
    showDialog(context: context, builder: (context) => _buildTaskTypeDialog());
  }

  Widget _buildTaskTypeDialog() {
    final theme = Theme.of(context);
    final taskTypes = StudyItemTypeHelper.taskTypes;

    return AlertDialog(
      title: const Text('Choose Task Type'),
      content: SizedBox(
        width: double.maxFinite,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            GridView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                crossAxisSpacing: 8.w,
                mainAxisSpacing: 8.h,
                childAspectRatio: 2.5,
              ),
              itemCount: taskTypes.length,
              itemBuilder: (context, index) {
                final type = taskTypes[index];
                return Card(
                  child: InkWell(
                    onTap: () {
                      Navigator.of(context).pop();
                      _showItemDetailsDialog(type);
                    },
                    borderRadius: BorderRadius.circular(8.r),
                    child: Padding(
                      padding: EdgeInsets.all(8.w),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(type.icon, color: type.color, size: 20.sp),
                          SizedBox(width: 8.w),
                          Flexible(
                            child: Text(
                              type.displayName,
                              style: theme.textTheme.bodyMedium?.copyWith(
                                fontWeight: FontWeight.w500,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                );
              },
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Back'),
        ),
      ],
    );
  }

  // Step 3: Item Details Dialog
  void _showItemDetailsDialog(StudyItemType type) {
    showDialog(
      context: context,
      builder: (context) => _buildSimpleItemDialog(type),
    );
  }

  Widget _buildSimpleItemDialog(StudyItemType type) {
    SchedulingType selectedScheduleType = SchedulingType.none;
    DateTime? selectedDate;
    DateTime? startDate;
    DateTime? endDate;
    Duration? duration;

    return StatefulBuilder(
      builder: (context, setDialogState) {
        return AlertDialog(
          title: Row(
            children: [
              Icon(type.icon, color: type.color),
              SizedBox(width: 8.w),
              Text('Add ${type.displayName}'),
            ],
          ),
          content: SizedBox(
            width: double.maxFinite,
            child: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  TextField(
                    controller: _titleController,
                    decoration: InputDecoration(
                      labelText: '${type.displayName} Title',
                      hintText: 'Enter ${type.displayName.toLowerCase()} title',
                      border: const OutlineInputBorder(),
                    ),
                    onChanged: (value) {
                      setDialogState(() {}); // Update button state
                    },
                  ),
                  SizedBox(height: 16.h),
                  TextField(
                    controller: _descriptionController,
                    decoration: const InputDecoration(
                      labelText: 'Description (Optional)',
                      hintText: 'Add a brief description',
                      border: OutlineInputBorder(),
                    ),
                    maxLines: 2,
                  ),
                  SizedBox(height: 16.h),

                  // Schedule selection for sections
                  if (StudyItemTypeHelper.sectionTypes.contains(type)) ...[
                    ScheduleSelectionWidget(
                      selectedType: selectedScheduleType,
                      onTypeChanged: (type) {
                        setDialogState(() {
                          selectedScheduleType = type;
                        });
                      },
                      selectedDate: selectedDate,
                      startDate: startDate,
                      endDate: endDate,
                      duration: duration,
                      onSelectedDateChanged: (date) {
                        setDialogState(() {
                          selectedDate = date;
                        });
                      },
                      onStartDateChanged: (date) {
                        setDialogState(() {
                          startDate = date;
                        });
                      },
                      onEndDateChanged: (date) {
                        setDialogState(() {
                          endDate = date;
                        });
                      },
                      onDurationChanged: (dur) {
                        setDialogState(() {
                          duration = dur;
                        });
                      },
                    ),
                  ],
                ],
              ),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                _titleController.clear();
                _descriptionController.clear();
                Navigator.of(context).pop();
              },
              child: const Text('Cancel'),
            ),
            FilledButton(
              onPressed: _titleController.text.isNotEmpty
                  ? () => _addItemWithSchedule(
                      type,
                      selectedScheduleType,
                      selectedDate,
                      startDate,
                      endDate,
                      duration,
                    )
                  : null,
              child: const Text('Add'),
            ),
          ],
        );
      },
    );
  }

  void _addItemWithSchedule(
    StudyItemType type,
    SchedulingType scheduleType,
    DateTime? selectedDate,
    DateTime? startDate,
    DateTime? endDate,
    Duration? duration,
  ) {
    final title = _titleController.text;
    final description = _descriptionController.text.isEmpty
        ? null
        : _descriptionController.text;

    FlexibleSchedule? schedule;

    // Create schedule based on type
    switch (scheduleType) {
      case SchedulingType.date:
        schedule = FlexibleSchedule.date(
          specificDate: selectedDate,
          startDate: startDate,
          endDate: endDate,
        );
        break;
      case SchedulingType.duration:
        if (duration != null) {
          schedule = FlexibleSchedule.duration(duration: duration);
        }
        break;
      case SchedulingType.none:
        schedule = FlexibleSchedule.none();
        break;
    }

    if (StudyItemTypeHelper.sectionTypes.contains(type)) {
      _addSection(type, title, description, schedule);
    } else {
      // For tasks, add to the first section or create a default section
      if (_sections.isEmpty) {
        _addSection(StudyItemType.chapter, 'Main Section', null, null);
      }
      _addTaskToSection(0, type, title, description, schedule);
    }

    _titleController.clear();
    _descriptionController.clear();
    Navigator.of(context).pop();
    _onFormChanged();
  }

  // Add task to specific section
  void _showAddTaskToSectionDialog(int sectionIndex) {
    showDialog(
      context: context,
      builder: (context) => _buildAddTaskToSectionDialog(sectionIndex),
    );
  }

  Widget _buildAddTaskToSectionDialog(int sectionIndex) {
    final theme = Theme.of(context);
    final taskTypes = StudyItemTypeHelper.taskTypes;
    final section = _sections[sectionIndex];

    return AlertDialog(
      title: Text('Add Task to ${section.title}'),
      content: SizedBox(
        width: double.maxFinite,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            GridView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                crossAxisSpacing: 8.w,
                mainAxisSpacing: 8.h,
                childAspectRatio: 2.5,
              ),
              itemCount: taskTypes.length,
              itemBuilder: (context, index) {
                final type = taskTypes[index];
                return Card(
                  child: InkWell(
                    onTap: () {
                      Navigator.of(context).pop();
                      _showItemDetailsDialogForSection(type, sectionIndex);
                    },
                    borderRadius: BorderRadius.circular(8.r),
                    child: Padding(
                      padding: EdgeInsets.all(8.w),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(type.icon, color: type.color, size: 20.sp),
                          SizedBox(width: 8.w),
                          Flexible(
                            child: Text(
                              type.displayName,
                              style: theme.textTheme.bodyMedium?.copyWith(
                                fontWeight: FontWeight.w500,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                );
              },
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
      ],
    );
  }

  // Item details dialog for adding to specific section
  void _showItemDetailsDialogForSection(StudyItemType type, int sectionIndex) {
    showDialog(
      context: context,
      builder: (context) =>
          _buildItemDetailsDialogForSection(type, sectionIndex),
    );
  }

  Widget _buildItemDetailsDialogForSection(
    StudyItemType type,
    int sectionIndex,
  ) {
    SchedulingType selectedScheduleType = SchedulingType.none;
    DateTime? selectedDate;
    DateTime? startDate;
    DateTime? endDate;
    Duration? duration;

    return StatefulBuilder(
      builder: (context, setDialogState) {
        return AlertDialog(
          title: Row(
            children: [
              Icon(type.icon, color: type.color),
              SizedBox(width: 8.w),
              Text('Add ${type.displayName}'),
            ],
          ),
          content: SizedBox(
            width: double.maxFinite,
            child: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Title input
                  TextField(
                    controller: _titleController,
                    decoration: InputDecoration(
                      labelText: '${type.displayName} Title',
                      hintText: 'Enter ${type.displayName.toLowerCase()} title',
                      border: const OutlineInputBorder(),
                    ),
                    onChanged: (value) {
                      setDialogState(
                        () {},
                      ); // Trigger rebuild to update button state
                    },
                  ),

                  SizedBox(height: 16.h),

                  // Description input
                  TextField(
                    controller: _descriptionController,
                    decoration: const InputDecoration(
                      labelText: 'Description (Optional)',
                      hintText: 'Add a brief description',
                      border: OutlineInputBorder(),
                    ),
                    maxLines: 2,
                  ),

                  SizedBox(height: 20.h),

                  // Schedule section
                  ScheduleSelectionWidget(
                    selectedType: selectedScheduleType,
                    onTypeChanged: (type) {
                      setDialogState(() {
                        selectedScheduleType = type;
                      });
                    },
                    selectedDate: selectedDate,
                    startDate: startDate,
                    endDate: endDate,
                    duration: duration,
                    onSelectedDateChanged: (date) {
                      setDialogState(() {
                        selectedDate = date;
                      });
                    },
                    onStartDateChanged: (date) {
                      setDialogState(() {
                        startDate = date;
                      });
                    },
                    onEndDateChanged: (date) {
                      setDialogState(() {
                        endDate = date;
                      });
                    },
                    onDurationChanged: (dur) {
                      setDialogState(() {
                        duration = dur;
                      });
                    },
                  ),
                ],
              ),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                _titleController.clear();
                _descriptionController.clear();
                Navigator.of(context).pop();
              },
              child: const Text('Cancel'),
            ),
            FilledButton(
              onPressed: _titleController.text.isNotEmpty
                  ? () => _addTaskToSpecificSectionWithSchedule(
                      type,
                      sectionIndex,
                      selectedScheduleType,
                      selectedDate,
                      startDate,
                      endDate,
                      duration,
                    )
                  : null,
              child: const Text('Add'),
            ),
          ],
        );
      },
    );
  }

  void _addTaskToSpecificSectionWithSchedule(
    StudyItemType type,
    int sectionIndex,
    SchedulingType scheduleType,
    DateTime? selectedDate,
    DateTime? startDate,
    DateTime? endDate,
    Duration? duration,
  ) {
    final title = _titleController.text;
    final description = _descriptionController.text.isEmpty
        ? null
        : _descriptionController.text;

    // Create flexible schedule based on type
    FlexibleSchedule? schedule;
    switch (scheduleType) {
      case SchedulingType.date:
        schedule = FlexibleSchedule.date(
          specificDate: selectedDate,
          startDate: startDate,
          endDate: endDate,
        );
        break;
      case SchedulingType.duration:
        if (duration != null) {
          schedule = FlexibleSchedule.duration(duration: duration);
        }
        break;
      case SchedulingType.none:
        schedule = FlexibleSchedule.none();
        break;
    }

    _addTaskToSection(sectionIndex, type, title, description, schedule);

    _titleController.clear();
    _descriptionController.clear();
    Navigator.of(context).pop();
    _onFormChanged();
  }

  // Add sub-section to specific section
  void _showAddSubSectionDialog(int sectionIndex) {
    showDialog(
      context: context,
      builder: (context) => _buildAddSubSectionDialog(sectionIndex),
    );
  }

  Widget _buildAddSubSectionDialog(int sectionIndex) {
    final theme = Theme.of(context);
    final sectionTypes = StudyItemTypeHelper.sectionTypes;
    final section = _sections[sectionIndex];

    return AlertDialog(
      title: Text('Add Sub-section to ${section.title}'),
      content: SizedBox(
        width: double.maxFinite,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            GridView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                crossAxisSpacing: 8.w,
                mainAxisSpacing: 8.h,
                childAspectRatio: 2.5,
              ),
              itemCount: sectionTypes.length,
              itemBuilder: (context, index) {
                final type = sectionTypes[index];
                return Card(
                  child: InkWell(
                    onTap: () {
                      Navigator.of(context).pop();
                      _showSubSectionDetailsDialog(type, sectionIndex);
                    },
                    borderRadius: BorderRadius.circular(8.r),
                    child: Padding(
                      padding: EdgeInsets.all(8.w),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(type.icon, color: type.color, size: 20.sp),
                          SizedBox(width: 8.w),
                          Flexible(
                            child: Text(
                              type.displayName,
                              style: theme.textTheme.bodyMedium?.copyWith(
                                fontWeight: FontWeight.w500,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                );
              },
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
      ],
    );
  }

  // Sub-section details dialog
  void _showSubSectionDetailsDialog(StudyItemType type, int sectionIndex) {
    showDialog(
      context: context,
      builder: (context) => _buildSubSectionDetailsDialog(type, sectionIndex),
    );
  }

  Widget _buildSubSectionDetailsDialog(StudyItemType type, int sectionIndex) {
    SchedulingType selectedScheduleType = SchedulingType.none;
    DateTime? selectedDate;
    DateTime? startDate;
    DateTime? endDate;
    Duration? duration;

    return StatefulBuilder(
      builder: (context, setDialogState) {
        return AlertDialog(
          title: Row(
            children: [
              Icon(type.icon, color: type.color),
              SizedBox(width: 8.w),
              Text('Add ${type.displayName} Sub-section'),
            ],
          ),
          content: SizedBox(
            width: double.maxFinite,
            child: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Title input
                  TextField(
                    controller: _titleController,
                    decoration: InputDecoration(
                      labelText: '${type.displayName} Title',
                      hintText: 'Enter ${type.displayName.toLowerCase()} title',
                      border: const OutlineInputBorder(),
                    ),
                    onChanged: (value) {
                      setDialogState(
                        () {},
                      ); // Trigger rebuild to update button state
                    },
                  ),

                  SizedBox(height: 16.h),

                  // Description input
                  TextField(
                    controller: _descriptionController,
                    decoration: const InputDecoration(
                      labelText: 'Description (Optional)',
                      hintText: 'Add a brief description',
                      border: OutlineInputBorder(),
                    ),
                    maxLines: 2,
                  ),

                  SizedBox(height: 20.h),

                  // Schedule section
                  ScheduleSelectionWidget(
                    selectedType: selectedScheduleType,
                    onTypeChanged: (type) {
                      setDialogState(() {
                        selectedScheduleType = type;
                      });
                    },
                    selectedDate: selectedDate,
                    startDate: startDate,
                    endDate: endDate,
                    duration: duration,
                    onSelectedDateChanged: (date) {
                      setDialogState(() {
                        selectedDate = date;
                      });
                    },
                    onStartDateChanged: (date) {
                      setDialogState(() {
                        startDate = date;
                      });
                    },
                    onEndDateChanged: (date) {
                      setDialogState(() {
                        endDate = date;
                      });
                    },
                    onDurationChanged: (dur) {
                      setDialogState(() {
                        duration = dur;
                      });
                    },
                  ),
                ],
              ),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                _titleController.clear();
                _descriptionController.clear();
                Navigator.of(context).pop();
              },
              child: const Text('Cancel'),
            ),
            FilledButton(
              onPressed: _titleController.text.isNotEmpty
                  ? () => _addSubSectionToSectionWithSchedule(
                      type,
                      sectionIndex,
                      selectedScheduleType,
                      selectedDate,
                      startDate,
                      endDate,
                      duration,
                    )
                  : null,
              child: const Text('Add'),
            ),
          ],
        );
      },
    );
  }

  void _addSubSectionToSectionWithSchedule(
    StudyItemType type,
    int sectionIndex,
    SchedulingType scheduleType,
    DateTime? selectedDate,
    DateTime? startDate,
    DateTime? endDate,
    Duration? duration,
  ) {
    final title = _titleController.text;
    final description = _descriptionController.text.isEmpty
        ? null
        : _descriptionController.text;

    // Extract dates from schedule type for the section model
    DateTime? sectionStartDate;
    DateTime? sectionEndDate;
    Duration? sectionDuration;

    switch (scheduleType) {
      case SchedulingType.date:
        if (startDate != null && endDate != null && startDate != endDate) {
          // Date range
          sectionStartDate = startDate;
          sectionEndDate = endDate;
        } else if (endDate != null && startDate == null) {
          // Deadline
          sectionEndDate = endDate;
        } else if (selectedDate != null) {
          // Specific date
          sectionStartDate = selectedDate;
          sectionEndDate = selectedDate;
        } else if (startDate != null) {
          // Single start date treated as specific date
          sectionStartDate = startDate;
          sectionEndDate = startDate;
        }
        break;
      case SchedulingType.duration:
        sectionDuration = duration;
        break;
      case SchedulingType.none:
        // No dates set
        break;
    }

    // Create flexible schedule from the scheduling data
    FlexibleSchedule? subSectionSchedule;
    switch (scheduleType) {
      case SchedulingType.date:
        subSectionSchedule = FlexibleSchedule.date(
          specificDate: selectedDate,
          startDate: startDate,
          endDate: endDate,
        );
        break;
      case SchedulingType.duration:
        if (duration != null) {
          subSectionSchedule = FlexibleSchedule.duration(duration: duration);
        }
        break;
      case SchedulingType.none:
        subSectionSchedule = FlexibleSchedule.none();
        break;
    }

    final newSubSection = StudyPlanSection(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      title: title,
      description: description,
      type: _convertToSectionType(type),
      tasks: [],
      subSections: [],
      order: _sections[sectionIndex].subSections.length,
      startDate: sectionStartDate,
      endDate: sectionEndDate,
      estimatedDuration: sectionDuration,
      individualSchedule: subSectionSchedule,
    );

    setState(() {
      _sections[sectionIndex] = _sections[sectionIndex].copyWith(
        subSections: [..._sections[sectionIndex].subSections, newSubSection],
      );
    });

    _titleController.clear();
    _descriptionController.clear();
    Navigator.of(context).pop();
    _onFormChanged();
  }

  // Helper methods

  void _addSection(
    StudyItemType type,
    String title,
    String? description,
    FlexibleSchedule? schedule,
  ) {
    final newSection = StudyPlanSection(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      title: title,
      description: description,
      type: _convertToSectionType(type),
      tasks: [],
      subSections: [],
      order: _sections.length,
      startDate: schedule?.specificDate ?? schedule?.dateRange?.startDate,
      endDate: schedule?.deadline ?? schedule?.dateRange?.endDate,
      estimatedDuration: schedule?.relativeDuration,
      individualSchedule: schedule,
    );

    setState(() {
      _sections.add(newSection);
    });
  }

  void _addTaskToSection(
    int sectionIndex,
    StudyItemType type,
    String title,
    String? description,
    FlexibleSchedule? schedule,
  ) {
    final newTask = StudyPlanTask(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      title: title,
      description: description,
      type: _convertToTaskType(type),
      priority: TaskPriority.normal,
      status: TaskStatus.notStarted,
      sectionId: _sections[sectionIndex].id,
      order: _sections[sectionIndex].tasks.length,
      dueDate: schedule?.deadline,
      startDate: schedule?.specificDate ?? schedule?.dateRange?.startDate,
      dateRange: schedule?.dateRange,
      estimatedDuration: schedule?.relativeDuration,
      individualSchedule: schedule,
    );

    setState(() {
      _sections[sectionIndex] = _sections[sectionIndex].copyWith(
        tasks: [..._sections[sectionIndex].tasks, newTask],
      );
    });
  }

  // Type conversion helpers
  SectionType _convertToSectionType(StudyItemType type) {
    switch (type) {
      case StudyItemType.chapter:
        return SectionType.chapter;
      case StudyItemType.subject:
        return SectionType.subject;
      case StudyItemType.topic:
        return SectionType.topic;
      case StudyItemType.unit:
      case StudyItemType.module:
      case StudyItemType.custom:
        return SectionType.custom;
      default:
        return SectionType.custom;
    }
  }

  TaskType _convertToTaskType(StudyItemType type) {
    switch (type) {
      case StudyItemType.reading:
        return TaskType.reading;
      case StudyItemType.practice:
        return TaskType.practice;
      case StudyItemType.assignment:
        return TaskType.assignment;
      case StudyItemType.review:
        return TaskType.review;
      case StudyItemType.test:
        return TaskType.test;
      case StudyItemType.project:
        return TaskType.project;
      case StudyItemType.research:
        return TaskType.research;
      case StudyItemType.custom:
      default:
        return TaskType.reading; // Default fallback
    }
  }

  // Action handlers
  void _handleSectionAction(String action, int index) {
    switch (action) {
      case 'edit':
        _showEditSectionDialog(index);
        break;
      case 'add_task':
        _showAddTaskToSectionDialog(index);
        break;
      case 'add_subsection':
        _showAddSubSectionDialog(index);
        break;
      case 'duplicate':
        _duplicateSection(index);
        break;
      case 'delete':
        _deleteSection(index);
        break;
    }
  }

  void _handleTaskAction(String action, StudyPlanTask task, int sectionIndex) {
    switch (action) {
      case 'edit':
        _showEditTaskDialog(task, sectionIndex);
        break;
      case 'duplicate':
        _duplicateTask(task, sectionIndex);
        break;
      case 'delete':
        _deleteTask(task, sectionIndex);
        break;
    }
  }

  void _handleSubSectionAction(
    String action,
    StudyPlanSection subSection,
    int parentIndex,
  ) {
    switch (action) {
      case 'edit':
        _showEditSubSectionDialog(subSection, parentIndex);
        break;
      case 'add_task':
        _showAddTaskToSubSectionDialog(parentIndex, subSection.id);
        break;
      case 'duplicate':
        _duplicateSubSection(subSection, parentIndex);
        break;
      case 'delete':
        _deleteSubSection(subSection, parentIndex);
        break;
    }
  }

  void _duplicateSection(int index) {
    final section = _sections[index];
    final duplicatedSection = section.copyWith(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      title: '${section.title} (Copy)',
      order: _sections.length,
    );

    setState(() {
      _sections.add(duplicatedSection);
    });
    _onFormChanged();
  }

  void _deleteSection(int index) {
    setState(() {
      _sections.removeAt(index);
    });
    _onFormChanged();
  }

  void _duplicateTask(StudyPlanTask task, int sectionIndex) {
    final duplicatedTask = task.copyWith(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      title: '${task.title} (Copy)',
      order: _sections[sectionIndex].tasks.length,
    );

    setState(() {
      _sections[sectionIndex] = _sections[sectionIndex].copyWith(
        tasks: [..._sections[sectionIndex].tasks, duplicatedTask],
      );
    });
    _onFormChanged();
  }

  void _deleteTask(StudyPlanTask task, int sectionIndex) {
    setState(() {
      final updatedTasks = _sections[sectionIndex].tasks
          .where((t) => t.id != task.id)
          .toList();
      _sections[sectionIndex] = _sections[sectionIndex].copyWith(
        tasks: updatedTasks,
      );
    });
    _onFormChanged();
  }

  // Add task to subsection
  void _showAddTaskToSubSectionDialog(int parentIndex, String subSectionId) {
    showDialog(
      context: context,
      builder: (context) =>
          _buildAddTaskToSubSectionDialog(parentIndex, subSectionId),
    );
  }

  Widget _buildAddTaskToSubSectionDialog(int parentIndex, String subSectionId) {
    final theme = Theme.of(context);
    final taskTypes = StudyItemTypeHelper.taskTypes;
    final section = _sections[parentIndex];
    final subSection = section.subSections.firstWhere(
      (s) => s.id == subSectionId,
    );

    return AlertDialog(
      title: Text('Add Task to ${subSection.title}'),
      content: SizedBox(
        width: double.maxFinite,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            GridView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                crossAxisSpacing: 8.w,
                mainAxisSpacing: 8.h,
                childAspectRatio: 2.5,
              ),
              itemCount: taskTypes.length,
              itemBuilder: (context, index) {
                final type = taskTypes[index];
                return Card(
                  child: InkWell(
                    onTap: () {
                      Navigator.of(context).pop();
                      _showTaskDetailsDialogForSubSection(
                        type,
                        parentIndex,
                        subSectionId,
                      );
                    },
                    borderRadius: BorderRadius.circular(8.r),
                    child: Padding(
                      padding: EdgeInsets.all(8.w),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(type.icon, color: type.color, size: 20.sp),
                          SizedBox(width: 8.w),
                          Flexible(
                            child: Text(
                              type.displayName,
                              style: theme.textTheme.bodyMedium?.copyWith(
                                fontWeight: FontWeight.w500,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                );
              },
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
      ],
    );
  }

  void _showTaskDetailsDialogForSubSection(
    StudyItemType type,
    int parentIndex,
    String subSectionId,
  ) {
    showDialog(
      context: context,
      builder: (context) =>
          _buildTaskDetailsDialogForSubSection(type, parentIndex, subSectionId),
    );
  }

  Widget _buildTaskDetailsDialogForSubSection(
    StudyItemType type,
    int parentIndex,
    String subSectionId,
  ) {
    SchedulingType selectedScheduleType = SchedulingType.none;
    DateTime? selectedDate;
    DateTime? startDate;
    DateTime? endDate;
    Duration? duration;

    return StatefulBuilder(
      builder: (context, setDialogState) {
        return AlertDialog(
          title: Row(
            children: [
              Icon(type.icon, color: type.color),
              SizedBox(width: 8.w),
              Text('Add ${type.displayName}'),
            ],
          ),
          content: SizedBox(
            width: double.maxFinite,
            child: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Title input
                  TextField(
                    controller: _titleController,
                    decoration: InputDecoration(
                      labelText: '${type.displayName} Title',
                      hintText: 'Enter ${type.displayName.toLowerCase()} title',
                      border: const OutlineInputBorder(),
                    ),
                    onChanged: (value) {
                      setDialogState(
                        () {},
                      ); // Trigger rebuild to update button state
                    },
                  ),

                  SizedBox(height: 16.h),

                  // Description input
                  TextField(
                    controller: _descriptionController,
                    decoration: const InputDecoration(
                      labelText: 'Description (Optional)',
                      hintText: 'Add a brief description',
                      border: OutlineInputBorder(),
                    ),
                    maxLines: 2,
                  ),

                  SizedBox(height: 20.h),

                  // Schedule section
                  ScheduleSelectionWidget(
                    selectedType: selectedScheduleType,
                    onTypeChanged: (type) {
                      setDialogState(() {
                        selectedScheduleType = type;
                      });
                    },
                    selectedDate: selectedDate,
                    startDate: startDate,
                    endDate: endDate,
                    duration: duration,
                    onSelectedDateChanged: (date) {
                      setDialogState(() {
                        selectedDate = date;
                      });
                    },
                    onStartDateChanged: (date) {
                      setDialogState(() {
                        startDate = date;
                      });
                    },
                    onEndDateChanged: (date) {
                      setDialogState(() {
                        endDate = date;
                      });
                    },
                    onDurationChanged: (dur) {
                      setDialogState(() {
                        duration = dur;
                      });
                    },
                  ),
                ],
              ),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                _titleController.clear();
                _descriptionController.clear();
                Navigator.of(context).pop();
              },
              child: const Text('Cancel'),
            ),
            FilledButton(
              onPressed: _titleController.text.isNotEmpty
                  ? () => _addTaskToSubSectionWithSchedule(
                      type,
                      parentIndex,
                      subSectionId,
                      selectedScheduleType,
                      selectedDate,
                      startDate,
                      endDate,
                      duration,
                    )
                  : null,
              child: const Text('Add'),
            ),
          ],
        );
      },
    );
  }

  void _addTaskToSubSectionWithSchedule(
    StudyItemType type,
    int parentIndex,
    String subSectionId,
    SchedulingType scheduleType,
    DateTime? selectedDate,
    DateTime? startDate,
    DateTime? endDate,
    Duration? duration,
  ) {
    final title = _titleController.text;
    final description = _descriptionController.text.isEmpty
        ? null
        : _descriptionController.text;

    // Create flexible schedule based on type
    FlexibleSchedule? schedule;
    switch (scheduleType) {
      case SchedulingType.date:
        schedule = FlexibleSchedule.date(
          specificDate: selectedDate,
          startDate: startDate,
          endDate: endDate,
        );
        break;
      case SchedulingType.duration:
        if (duration != null) {
          schedule = FlexibleSchedule.duration(duration: duration);
        }
        break;
      case SchedulingType.none:
        schedule = FlexibleSchedule.none();
        break;
    }

    _addTaskToSubSection(
      parentIndex,
      subSectionId,
      type,
      title,
      description,
      schedule,
    );

    _titleController.clear();
    _descriptionController.clear();
    Navigator.of(context).pop();
    _onFormChanged();
  }

  void _addTaskToSubSection(
    int parentIndex,
    String subSectionId,
    StudyItemType type,
    String title,
    String? description,
    FlexibleSchedule? schedule,
  ) {
    final newTask = StudyPlanTask(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      title: title,
      description: description,
      type: _convertToTaskType(type),
      priority: TaskPriority.normal,
      status: TaskStatus.notStarted,
      sectionId: subSectionId,
      order: 0, // Will be updated based on existing tasks
      dueDate: schedule?.deadline,
      startDate: schedule?.specificDate ?? schedule?.dateRange?.startDate,
      dateRange: schedule?.dateRange,
      estimatedDuration: schedule?.relativeDuration,
      individualSchedule: schedule,
    );

    setState(() {
      final section = _sections[parentIndex];
      final subSectionIndex = section.subSections.indexWhere(
        (s) => s.id == subSectionId,
      );
      if (subSectionIndex != -1) {
        final subSection = section.subSections[subSectionIndex];
        final updatedSubSection = subSection.copyWith(
          tasks: [...subSection.tasks, newTask],
        );
        final updatedSubSections = List<StudyPlanSection>.from(
          section.subSections,
        );
        updatedSubSections[subSectionIndex] = updatedSubSection;
        _sections[parentIndex] = section.copyWith(
          subSections: updatedSubSections,
        );
      }
    });
  }

  // Duplicate subsection
  void _duplicateSubSection(StudyPlanSection subSection, int parentIndex) {
    final duplicatedSubSection = subSection.copyWith(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      title: '${subSection.title} (Copy)',
      order: _sections[parentIndex].subSections.length,
    );

    setState(() {
      _sections[parentIndex] = _sections[parentIndex].copyWith(
        subSections: [
          ..._sections[parentIndex].subSections,
          duplicatedSubSection,
        ],
      );
    });
    _onFormChanged();
  }

  // Delete subsection
  void _deleteSubSection(StudyPlanSection subSection, int parentIndex) {
    setState(() {
      final updatedSubSections = _sections[parentIndex].subSections
          .where((s) => s.id != subSection.id)
          .toList();
      _sections[parentIndex] = _sections[parentIndex].copyWith(
        subSections: updatedSubSections,
      );
    });
    _onFormChanged();
  }

  // Edit functionality methods

  void _showEditSectionDialog(int sectionIndex) {
    final section = _sections[sectionIndex];
    _titleController.text = section.title;
    _descriptionController.text = section.description ?? '';

    showDialog(
      context: context,
      builder: (context) => _buildEditSectionDialog(sectionIndex),
    );
  }

  Widget _buildEditSectionDialog(int sectionIndex) {
    final section = _sections[sectionIndex];

    return AlertDialog(
      title: Text('Edit ${section.type.displayName}'),
      content: SizedBox(
        width: double.maxFinite,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: _titleController,
              decoration: const InputDecoration(
                labelText: 'Title',
                border: OutlineInputBorder(),
              ),
            ),
            SizedBox(height: 16.h),
            TextField(
              controller: _descriptionController,
              decoration: const InputDecoration(
                labelText: 'Description (Optional)',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () {
            _titleController.clear();
            _descriptionController.clear();
            Navigator.of(context).pop();
          },
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: () => _updateSection(sectionIndex),
          child: const Text('Update'),
        ),
      ],
    );
  }

  void _updateSection(int sectionIndex) {
    final title = _titleController.text.trim();
    if (title.isEmpty) return;

    final description = _descriptionController.text.trim().isEmpty
        ? null
        : _descriptionController.text.trim();

    setState(() {
      _sections[sectionIndex] = _sections[sectionIndex].copyWith(
        title: title,
        description: description,
      );
    });

    _titleController.clear();
    _descriptionController.clear();
    Navigator.of(context).pop();
    _onFormChanged();
  }

  void _showEditTaskDialog(StudyPlanTask task, int sectionIndex) {
    _titleController.text = task.title;
    _descriptionController.text = task.description ?? '';

    showDialog(
      context: context,
      builder: (context) => _buildEditTaskDialog(task, sectionIndex),
    );
  }

  Widget _buildEditTaskDialog(StudyPlanTask task, int sectionIndex) {
    return AlertDialog(
      title: Text('Edit ${task.type.displayName}'),
      content: SizedBox(
        width: double.maxFinite,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: _titleController,
              decoration: const InputDecoration(
                labelText: 'Title',
                border: OutlineInputBorder(),
              ),
            ),
            SizedBox(height: 16.h),
            TextField(
              controller: _descriptionController,
              decoration: const InputDecoration(
                labelText: 'Description (Optional)',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () {
            _titleController.clear();
            _descriptionController.clear();
            Navigator.of(context).pop();
          },
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: () => _updateTask(task, sectionIndex),
          child: const Text('Update'),
        ),
      ],
    );
  }

  void _updateTask(StudyPlanTask task, int sectionIndex) {
    final title = _titleController.text.trim();
    if (title.isEmpty) return;

    final description = _descriptionController.text.trim().isEmpty
        ? null
        : _descriptionController.text.trim();

    final updatedTask = task.copyWith(title: title, description: description);

    setState(() {
      final tasks = _sections[sectionIndex].tasks;
      final taskIndex = tasks.indexWhere((t) => t.id == task.id);
      if (taskIndex != -1) {
        final updatedTasks = List<StudyPlanTask>.from(tasks);
        updatedTasks[taskIndex] = updatedTask;
        _sections[sectionIndex] = _sections[sectionIndex].copyWith(
          tasks: updatedTasks,
        );
      }
    });

    _titleController.clear();
    _descriptionController.clear();
    Navigator.of(context).pop();
    _onFormChanged();
  }

  void _showEditSubSectionDialog(StudyPlanSection subSection, int parentIndex) {
    _titleController.text = subSection.title;
    _descriptionController.text = subSection.description ?? '';

    showDialog(
      context: context,
      builder: (context) => _buildEditSubSectionDialog(subSection, parentIndex),
    );
  }

  Widget _buildEditSubSectionDialog(
    StudyPlanSection subSection,
    int parentIndex,
  ) {
    return AlertDialog(
      title: Text('Edit ${subSection.type.displayName}'),
      content: SizedBox(
        width: double.maxFinite,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: _titleController,
              decoration: const InputDecoration(
                labelText: 'Title',
                border: OutlineInputBorder(),
              ),
            ),
            SizedBox(height: 16.h),
            TextField(
              controller: _descriptionController,
              decoration: const InputDecoration(
                labelText: 'Description (Optional)',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () {
            _titleController.clear();
            _descriptionController.clear();
            Navigator.of(context).pop();
          },
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: () => _updateSubSection(subSection, parentIndex),
          child: const Text('Update'),
        ),
      ],
    );
  }

  void _updateSubSection(StudyPlanSection subSection, int parentIndex) {
    final title = _titleController.text.trim();
    if (title.isEmpty) return;

    final description = _descriptionController.text.trim().isEmpty
        ? null
        : _descriptionController.text.trim();

    final updatedSubSection = subSection.copyWith(
      title: title,
      description: description,
    );

    setState(() {
      final subSections = _sections[parentIndex].subSections;
      final subSectionIndex = subSections.indexWhere(
        (s) => s.id == subSection.id,
      );
      if (subSectionIndex != -1) {
        final updatedSubSections = List<StudyPlanSection>.from(subSections);
        updatedSubSections[subSectionIndex] = updatedSubSection;
        _sections[parentIndex] = _sections[parentIndex].copyWith(
          subSections: updatedSubSections,
        );
      }
    });

    _titleController.clear();
    _descriptionController.clear();
    Navigator.of(context).pop();
    _onFormChanged();
  }

  void _showEditTaskInSubSectionDialog(
    StudyPlanTask task,
    int parentIndex,
    String subSectionId,
  ) {
    _titleController.text = task.title;
    _descriptionController.text = task.description ?? '';

    showDialog(
      context: context,
      builder: (context) =>
          _buildEditTaskInSubSectionDialog(task, parentIndex, subSectionId),
    );
  }

  Widget _buildEditTaskInSubSectionDialog(
    StudyPlanTask task,
    int parentIndex,
    String subSectionId,
  ) {
    return AlertDialog(
      title: Text('Edit ${task.type.displayName}'),
      content: SizedBox(
        width: double.maxFinite,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: _titleController,
              decoration: const InputDecoration(
                labelText: 'Title',
                border: OutlineInputBorder(),
              ),
            ),
            SizedBox(height: 16.h),
            TextField(
              controller: _descriptionController,
              decoration: const InputDecoration(
                labelText: 'Description (Optional)',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () {
            _titleController.clear();
            _descriptionController.clear();
            Navigator.of(context).pop();
          },
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: () =>
              _updateTaskInSubSection(task, parentIndex, subSectionId),
          child: const Text('Update'),
        ),
      ],
    );
  }

  void _updateTaskInSubSection(
    StudyPlanTask task,
    int parentIndex,
    String subSectionId,
  ) {
    final title = _titleController.text.trim();
    if (title.isEmpty) return;

    final description = _descriptionController.text.trim().isEmpty
        ? null
        : _descriptionController.text.trim();

    final updatedTask = task.copyWith(title: title, description: description);

    setState(() {
      final subSections = _sections[parentIndex].subSections;
      final subSectionIndex = subSections.indexWhere(
        (s) => s.id == subSectionId,
      );
      if (subSectionIndex != -1) {
        final tasks = subSections[subSectionIndex].tasks;
        final taskIndex = tasks.indexWhere((t) => t.id == task.id);
        if (taskIndex != -1) {
          final updatedTasks = List<StudyPlanTask>.from(tasks);
          updatedTasks[taskIndex] = updatedTask;
          final updatedSubSections = List<StudyPlanSection>.from(subSections);
          updatedSubSections[subSectionIndex] = subSections[subSectionIndex]
              .copyWith(tasks: updatedTasks);
          _sections[parentIndex] = _sections[parentIndex].copyWith(
            subSections: updatedSubSections,
          );
        }
      }
    });

    _titleController.clear();
    _descriptionController.clear();
    Navigator.of(context).pop();
    _onFormChanged();
  }
}
