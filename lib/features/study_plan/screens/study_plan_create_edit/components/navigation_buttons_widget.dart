import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../enums/study_plan_enums.dart';

class NavigationButtonsWidget extends StatelessWidget {
  final PlanCreationStep currentStep;
  final Map<String, dynamic> formData;
  final String? studyPlanId;
  final VoidCallback onPrevious;
  final VoidCallback onNext;

  const NavigationButtonsWidget({
    super.key,
    required this.currentStep,
    required this.formData,
    required this.studyPlanId,
    required this.onPrevious,
    required this.onNext,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        border: Border(
          top: BorderSide(
            color: theme.colorScheme.outline.withValues(alpha: 0.2),
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          // Previous button
          if (currentStep != PlanCreationStep.basicDetails)
            Expanded(
              child: OutlinedButton(
                onPressed: onPrevious,
                child: const Text('Previous'),
              ),
            ),

          if (currentStep != PlanCreationStep.basicDetails)
            SizedBox(width: 16.w),

          // Next/Skip button
          Expanded(
            child: FilledButton(
              onPressed: _canProceed() ? onNext : null,
              child: Text(_getNextButtonText()),
            ),
          ),
        ],
      ),
    );
  }

  bool _canProceed() {
    return _isStepValid(currentStep) || currentStep.canBeSkipped;
  }

  bool _isStepValid(PlanCreationStep step) {
    switch (step) {
      case PlanCreationStep.basicDetails:
        return formData['title']?.isNotEmpty == true;
      case PlanCreationStep.tasksAndGoals:
        return formData['sections']?.isNotEmpty == true;
      case PlanCreationStep.scheduling:
        return true; // Optional step
      case PlanCreationStep.preview:
        return true; // Always valid
    }
  }

  String _getNextButtonText() {
    if (currentStep == PlanCreationStep.preview) {
      return studyPlanId != null ? 'Update Plan' : 'Create Plan';
    } else if (currentStep.canBeSkipped && !_isStepValid(currentStep)) {
      return 'Skip';
    } else {
      return 'Next';
    }
  }
}
