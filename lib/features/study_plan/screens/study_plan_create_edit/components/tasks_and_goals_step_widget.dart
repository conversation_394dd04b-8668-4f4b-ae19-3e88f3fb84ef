import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../sections/tasks_and_goals_builder.dart';

class TasksAndGoalsStepWidget extends StatelessWidget {
  final Map<String, dynamic> formData;
  final Function(Map<String, dynamic>) onFormDataChanged;
  final bool isEditMode;

  const TasksAndGoalsStepWidget({
    super.key,
    required this.formData,
    required this.onFormDataChanged,
    required this.isEditMode,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Padding(
      padding: EdgeInsets.all(16.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Tasks & Goals',
            style: theme.textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            'Build the content structure with sections, tasks, and goals for your study plan.',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
            ),
          ),
          SizedBox(height: 24.h),

          // Tasks and goals builder
          Expanded(
            child: SingleChildScrollView(
              child: TasksAndGoalsBuilder(
                formData: formData,
                onFormDataChanged: (data) {
                  SchedulerBinding.instance.addPostFrameCallback((_) {
                    onFormDataChanged(data);
                  });
                },
                isEditMode: isEditMode,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
