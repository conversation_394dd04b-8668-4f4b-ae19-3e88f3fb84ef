import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../sections/basic_details_form.dart';

class BasicDetailsStepWidget extends StatelessWidget {
  final Map<String, dynamic> formData;
  final Function(Map<String, dynamic>) onFormDataChanged;
  final bool isEditMode;

  const BasicDetailsStepWidget({
    super.key,
    required this.formData,
    required this.onFormDataChanged,
    required this.isEditMode,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Padding(
      padding: EdgeInsets.all(16.w),
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Basic Details',
              style: theme.textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 8.h),
            Text(
              'Set up the basic information and settings for your study plan.',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
              ),
            ),
            SizedBox(height: 24.h),

            // Basic details form
            BasicDetailsForm(
              formData: formData,
              onFormDataChanged: (data) {
                SchedulerBinding.instance.addPostFrameCallback((_) {
                  onFormDataChanged(data);
                });
              },
              isEditMode: isEditMode,
            ),
          ],
        ),
      ),
    );
  }
}
