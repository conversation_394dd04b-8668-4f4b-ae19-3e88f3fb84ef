import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_symbols_icons/symbols.dart';

import '../../../enums/study_plan_enums.dart';
import '../../../models/study_plan_models.dart';

class PreviewStepWidget extends StatelessWidget {
  final Map<String, dynamic> formData;

  const PreviewStepWidget({super.key, required this.formData});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Padding(
      padding: EdgeInsets.all(16.w),
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Text(
              'Preview',
              style: theme.textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 8.h),
            Text(
              'Review your study plan before saving. You can make changes later.',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
              ),
            ),
            SizedBox(height: 24.h),

            // Basic Details Section
            _buildBasicDetailsSection(context, theme),
            SizedBox(height: 24.h),

            // Content Structure Section (combines tasks/goals and scheduling)
            _buildContentStructureSection(context, theme),
            SizedBox(height: 24.h),

            // Summary Section
            _buildSummarySection(context, theme),
          ],
        ),
      ),
    );
  }

  Widget _buildBasicDetailsSection(BuildContext context, ThemeData theme) {
    return _buildSection(
      context,
      theme,
      title: 'Basic Details',
      icon: Symbols.info,
      children: [
        _buildDetailRow(
          context,
          theme,
          'Title',
          formData['title']?.toString() ?? 'Untitled Plan',
        ),
        if (formData['description']?.toString().isNotEmpty == true)
          _buildDetailRow(
            context,
            theme,
            'Description',
            formData['description'].toString(),
          ),
        _buildDetailRow(
          context,
          theme,
          'Type',
          _getStudyPlanTypeDisplayName(formData['type']),
        ),
        _buildDetailRow(
          context,
          theme,
          'Mandatory',
          formData['isMandatory'] == true ? 'Yes' : 'No',
        ),
        _buildDetailRow(
          context,
          theme,
          'Can be used as template',
          formData['canBeEditedAsTemplate'] == true ? 'Yes' : 'No',
        ),
        if (formData['associationType'] != null)
          _buildDetailRow(
            context,
            theme,
            'Association',
            _getAssociationDisplayName(formData['associationType']),
          ),
        if (formData['startDate'] != null)
          _buildDetailRow(
            context,
            theme,
            'Start Date',
            _formatDate(formData['startDate']),
          ),
        if (formData['endDate'] != null)
          _buildDetailRow(
            context,
            theme,
            'End Date',
            _formatDate(formData['endDate']),
          ),
        if (formData['estimatedDuration'] != null)
          _buildDetailRow(
            context,
            theme,
            'Estimated Duration',
            _formatDuration(formData['estimatedDuration']),
          ),
      ],
    );
  }

  Widget _buildContentStructureSection(BuildContext context, ThemeData theme) {
    final sections = formData['sections'] as List<StudyPlanSection>? ?? [];
    final allowsScheduleEditing = formData['allowsScheduleEditing'] ?? false;
    final schedulingData = formData['schedulingData'] as Map<String, dynamic>?;

    return _buildSection(
      context,
      theme,
      title: 'Content Structure',
      icon: Symbols.account_tree,
      children: [
        // Sections overview
        if (sections.isEmpty)
          _buildEmptyState(context, theme, 'No sections added yet')
        else ...[
          // Show sections
          ...sections.map(
            (section) => _buildSectionPreview(context, theme, section),
          ),

          SizedBox(height: 16.h),

          // Scheduling summary
          Container(
            padding: EdgeInsets.all(12.w),
            decoration: BoxDecoration(
              color: theme.colorScheme.primaryContainer.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(8.r),
              border: Border.all(
                color: theme.colorScheme.primary.withValues(alpha: 0.3),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      Symbols.schedule,
                      size: 16.sp,
                      color: theme.colorScheme.primary,
                    ),
                    SizedBox(width: 8.w),
                    Text(
                      'Scheduling',
                      style: theme.textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: theme.colorScheme.primary,
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 8.h),
                _buildDetailRow(
                  context,
                  theme,
                  'Calendar-based scheduling',
                  allowsScheduleEditing ? 'Enabled' : 'Disabled',
                ),
                if (allowsScheduleEditing && schedulingData != null) ...[
                  _buildDetailRow(
                    context,
                    theme,
                    'Task assignments',
                    _getAssignmentCount(schedulingData['taskAssignments']),
                  ),
                  _buildDetailRow(
                    context,
                    theme,
                    'Section assignments',
                    _getAssignmentCount(schedulingData['sectionAssignments']),
                  ),
                ] else if (allowsScheduleEditing)
                  Text(
                    'No scheduling assignments configured yet',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
                      fontStyle: FontStyle.italic,
                    ),
                  )
                else
                  Text(
                    'Scheduling is optional and not configured',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
                      fontStyle: FontStyle.italic,
                    ),
                  ),
              ],
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildSummarySection(BuildContext context, ThemeData theme) {
    final sections = formData['sections'] as List<StudyPlanSection>? ?? [];
    final totalTasks = _getTotalTaskCount(sections);
    final totalSubSections = _getTotalSubSectionCount(sections);

    return _buildSection(
      context,
      theme,
      title: 'Summary',
      icon: Symbols.summarize,
      children: [
        _buildDetailRow(context, theme, 'Total Sections', '${sections.length}'),
        _buildDetailRow(
          context,
          theme,
          'Total Sub-sections',
          '$totalSubSections',
        ),
        _buildDetailRow(context, theme, 'Total Tasks', '$totalTasks'),
        if (formData['estimatedDuration'] != null)
          _buildDetailRow(
            context,
            theme,
            'Estimated Duration',
            _formatDuration(formData['estimatedDuration']),
          ),
      ],
    );
  }

  // Utility methods
  Widget _buildSection(
    BuildContext context,
    ThemeData theme, {
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(
          color: theme.colorScheme.outline.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section header
          Container(
            padding: EdgeInsets.all(16.w),
            decoration: BoxDecoration(
              color: theme.colorScheme.surfaceContainerHighest.withValues(
                alpha: 0.3,
              ),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(12.r),
                topRight: Radius.circular(12.r),
              ),
            ),
            child: Row(
              children: [
                Icon(icon, size: 20.sp, color: theme.colorScheme.primary),
                SizedBox(width: 8.w),
                Text(
                  title,
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: theme.colorScheme.primary,
                  ),
                ),
              ],
            ),
          ),

          // Section content
          Padding(
            padding: EdgeInsets.all(16.w),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: children,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(
    BuildContext context,
    ThemeData theme,
    String label,
    String value,
  ) {
    return Padding(
      padding: EdgeInsets.only(bottom: 12.h),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120.w,
            child: Text(
              label,
              style: theme.textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
                color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
              ),
            ),
          ),
          SizedBox(width: 16.w),
          Expanded(child: Text(value, style: theme.textTheme.bodyMedium)),
        ],
      ),
    );
  }

  Widget _buildEmptyState(
    BuildContext context,
    ThemeData theme,
    String message,
  ) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(
          color: theme.colorScheme.outline.withValues(alpha: 0.2),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Symbols.info,
            size: 16.sp,
            color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
          ),
          SizedBox(width: 8.w),
          Text(
            message,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
              fontStyle: FontStyle.italic,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionPreview(
    BuildContext context,
    ThemeData theme,
    StudyPlanSection section,
  ) {
    return Container(
      margin: EdgeInsets.only(bottom: 12.h),
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceContainerHighest.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(
          color: theme.colorScheme.outline.withValues(alpha: 0.1),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section title and type
          Row(
            children: [
              Icon(
                _getSectionTypeIcon(section.type),
                size: 16.sp,
                color: theme.colorScheme.primary,
              ),
              SizedBox(width: 8.w),
              Expanded(
                child: Text(
                  section.title,
                  style: theme.textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              Container(
                padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 2.h),
                decoration: BoxDecoration(
                  color: theme.colorScheme.secondary.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(12.r),
                ),
                child: Text(
                  section.type.displayName,
                  style: theme.textTheme.labelSmall?.copyWith(
                    color: theme.colorScheme.secondary,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),

          if (section.description?.isNotEmpty == true) ...[
            SizedBox(height: 8.h),
            Text(
              section.description!,
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
              ),
            ),
          ],

          // Tasks count
          if (section.tasks.isNotEmpty) ...[
            SizedBox(height: 8.h),
            Row(
              children: [
                Icon(
                  Symbols.task_alt,
                  size: 14.sp,
                  color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
                ),
                SizedBox(width: 4.w),
                Text(
                  '${section.tasks.length} task${section.tasks.length == 1 ? '' : 's'}',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
                  ),
                ),
              ],
            ),
          ],

          // Sub-sections count
          if (section.subSections.isNotEmpty) ...[
            SizedBox(height: 4.h),
            Row(
              children: [
                Icon(
                  Symbols.folder,
                  size: 14.sp,
                  color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
                ),
                SizedBox(width: 4.w),
                Text(
                  '${section.subSections.length} sub-section${section.subSections.length == 1 ? '' : 's'}',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
                  ),
                ),
              ],
            ),
          ],

          // Individual scheduling information
          if (section.individualSchedule != null &&
              section.individualSchedule!.isValid) ...[
            SizedBox(height: 8.h),
            Container(
              padding: EdgeInsets.all(8.w),
              decoration: BoxDecoration(
                color: theme.colorScheme.primary.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(6.r),
              ),
              child: Row(
                children: [
                  Icon(
                    Symbols.schedule,
                    size: 14.sp,
                    color: theme.colorScheme.primary,
                  ),
                  SizedBox(width: 6.w),
                  Expanded(
                    child: Text(
                      'Schedule: ${section.individualSchedule!.displayText}',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.primary,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],

          // Show sub-sections with their individual schedules
          if (section.subSections.isNotEmpty) ...[
            SizedBox(height: 8.h),
            ...section.subSections.map(
              (subSection) =>
                  _buildSubSectionPreview(context, theme, subSection),
            ),
          ],

          // Show tasks with their individual schedules
          if (section.tasks.isNotEmpty) ...[
            SizedBox(height: 8.h),
            ...section.tasks.map(
              (task) => _buildTaskPreview(context, theme, task),
            ),
          ],
        ],
      ),
    );
  }

  // Helper methods for data formatting
  String _getStudyPlanTypeDisplayName(dynamic type) {
    if (type is StudyPlanType) {
      return type.displayName;
    }
    return type?.toString() ?? 'Not specified';
  }

  String _getAssociationDisplayName(dynamic associationType) {
    if (associationType is AssociationType) {
      return associationType.displayName;
    }
    return associationType?.toString() ?? 'Not specified';
  }

  String _formatDate(dynamic date) {
    if (date is DateTime) {
      return '${date.day}/${date.month}/${date.year}';
    }
    return date?.toString() ?? 'Not specified';
  }

  String _formatDuration(dynamic duration) {
    if (duration is Duration) {
      final hours = duration.inHours;
      final minutes = duration.inMinutes % 60;
      if (hours > 0) {
        return '${hours}h ${minutes}m';
      } else {
        return '${minutes}m';
      }
    }
    return duration?.toString() ?? 'Not specified';
  }

  String _getAssignmentCount(dynamic assignments) {
    if (assignments is Map) {
      int totalCount = 0;
      for (final value in assignments.values) {
        if (value is List) {
          totalCount += value.length;
        }
      }
      return '$totalCount assignment${totalCount == 1 ? '' : 's'}';
    }
    return '0 assignments';
  }

  int _getTotalTaskCount(List<StudyPlanSection> sections) {
    int count = 0;
    for (final section in sections) {
      count += section.tasks.length;
      for (final subSection in section.subSections) {
        count += subSection.tasks.length;
      }
    }
    return count;
  }

  int _getTotalSubSectionCount(List<StudyPlanSection> sections) {
    int count = 0;
    for (final section in sections) {
      count += section.subSections.length;
    }
    return count;
  }

  IconData _getSectionTypeIcon(SectionType type) {
    switch (type) {
      case SectionType.subject:
        return Symbols.school;
      case SectionType.topic:
        return Symbols.topic;
      case SectionType.chapter:
        return Symbols.menu_book;
      case SectionType.skill:
        return Symbols.psychology;
      case SectionType.custom:
        return Symbols.folder;
    }
  }

  Widget _buildSubSectionPreview(
    BuildContext context,
    ThemeData theme,
    StudyPlanSection subSection,
  ) {
    return Container(
      margin: EdgeInsets.only(bottom: 8.h, left: 16.w),
      padding: EdgeInsets.all(8.w),
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceContainerHighest.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(6.r),
        border: Border.all(
          color: theme.colorScheme.outline.withValues(alpha: 0.1),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Symbols.folder_open,
                size: 14.sp,
                color: theme.colorScheme.secondary,
              ),
              SizedBox(width: 6.w),
              Expanded(
                child: Text(
                  subSection.title,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),

          if (subSection.individualSchedule != null &&
              subSection.individualSchedule!.isValid) ...[
            SizedBox(height: 4.h),
            Row(
              children: [
                Icon(
                  Symbols.schedule,
                  size: 12.sp,
                  color: theme.colorScheme.secondary,
                ),
                SizedBox(width: 4.w),
                Text(
                  subSection.individualSchedule!.displayText,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.secondary,
                  ),
                ),
              ],
            ),
          ],

          if (subSection.tasks.isNotEmpty) ...[
            SizedBox(height: 6.h),
            ...subSection.tasks.map(
              (task) => _buildTaskPreview(context, theme, task, isNested: true),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildTaskPreview(
    BuildContext context,
    ThemeData theme,
    StudyPlanTask task, {
    bool isNested = false,
  }) {
    return Container(
      margin: EdgeInsets.only(bottom: 4.h, left: isNested ? 16.w : 0),
      padding: EdgeInsets.all(6.w),
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceContainerHighest.withValues(
          alpha: 0.05,
        ),
        borderRadius: BorderRadius.circular(4.r),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Symbols.task_alt,
                size: 12.sp,
                color: theme.colorScheme.tertiary,
              ),
              SizedBox(width: 4.w),
              Expanded(
                child: Text(
                  task.title,
                  style: theme.textTheme.bodySmall?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),

          if (task.individualSchedule != null &&
              task.individualSchedule!.isValid) ...[
            SizedBox(height: 2.h),
            Row(
              children: [
                Icon(
                  Symbols.schedule,
                  size: 10.sp,
                  color: theme.colorScheme.tertiary,
                ),
                SizedBox(width: 4.w),
                Text(
                  task.individualSchedule!.displayText,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.tertiary,
                    fontSize: 10.sp,
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }
}
