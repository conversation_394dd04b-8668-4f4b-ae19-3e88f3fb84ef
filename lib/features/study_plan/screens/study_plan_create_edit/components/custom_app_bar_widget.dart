import 'package:flutter/material.dart';
import 'package:material_symbols_icons/symbols.dart';

import '../../../enums/study_plan_enums.dart';

class CustomAppBarWidget extends StatelessWidget implements PreferredSizeWidget {
  final String? studyPlanId;
  final String? templateId;
  final bool isSaving;
  final Map<String, dynamic> formData;
  final VoidCallback onCancel;
  final VoidCallback onSave;

  const CustomAppBarWidget({
    super.key,
    required this.studyPlanId,
    required this.templateId,
    required this.isSaving,
    required this.formData,
    required this.onCancel,
    required this.onSave,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isEdit = studyPlanId != null;
    final isFromTemplate = templateId != null;

    String title;
    if (isEdit) {
      title = 'Edit Study Plan';
    } else if (isFromTemplate) {
      title = 'Create from Template';
    } else {
      title = 'Create Study Plan';
    }

    return AppBar(
      title: Text(title),
      elevation: 0,
      backgroundColor: theme.colorScheme.surface,
      leading: IconButton(
        icon: const Icon(Symbols.close),
        onPressed: onCancel,
      ),
      actions: [
        if (isSaving)
          const Padding(
            padding: EdgeInsets.all(16.0),
            child: SizedBox(
              width: 20,
              height: 20,
              child: CircularProgressIndicator(strokeWidth: 2),
            ),
          )
        else
          TextButton(
            onPressed: _canSave() ? onSave : null,
            child: Text(
              isEdit ? 'Update' : 'Save',
              style: TextStyle(
                color: _canSave()
                    ? theme.colorScheme.primary
                    : theme.colorScheme.onSurface.withValues(alpha: 0.5),
              ),
            ),
          ),
      ],
    );
  }

  bool _canSave() {
    // Must have completed required steps
    return _isStepValid(PlanCreationStep.basicDetails) &&
        _isStepValid(PlanCreationStep.tasksAndGoals);
  }

  bool _isStepValid(PlanCreationStep step) {
    switch (step) {
      case PlanCreationStep.basicDetails:
        return formData['title']?.isNotEmpty == true;
      case PlanCreationStep.tasksAndGoals:
        return formData['sections']?.isNotEmpty == true;
      case PlanCreationStep.scheduling:
        return true; // Optional step
      case PlanCreationStep.preview:
        return true; // Always valid
    }
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}
