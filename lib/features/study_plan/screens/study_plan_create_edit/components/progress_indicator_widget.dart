import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_symbols_icons/symbols.dart';

import '../../../enums/study_plan_enums.dart';

class ProgressIndicatorWidget extends StatelessWidget {
  final PlanCreationStep currentStep;

  const ProgressIndicatorWidget({super.key, required this.currentStep});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        border: Border(
          bottom: BorderSide(
            color: theme.colorScheme.outline.withValues(alpha: 0.2),
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: PlanCreationStep.values.map((step) {
          final isActive = step == currentStep;
          final isCompleted = step.stepNumber < currentStep.stepNumber;

          return Expanded(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Step indicator
                Container(
                  width: 32.w,
                  height: 32.w,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: isCompleted
                        ? theme.colorScheme.primary
                        : isActive
                        ? theme.colorScheme.primary.withValues(alpha: 0.2)
                        : theme.colorScheme.outline.withValues(alpha: 0.2),
                    border: Border.all(
                      color: isActive || isCompleted
                          ? theme.colorScheme.primary
                          : theme.colorScheme.outline.withValues(alpha: 0.5),
                      width: 2,
                    ),
                  ),
                  child: Icon(
                    isCompleted ? Symbols.check : step.icon,
                    size: 16.sp,
                    color: isCompleted || isActive
                        ? theme.colorScheme.primary
                        : theme.colorScheme.onSurface.withValues(alpha: 0.5),
                  ),
                ),

                SizedBox(height: 8.h),

                // Step label
                Text(
                  step.displayName,
                  style: theme.textTheme.labelSmall?.copyWith(
                    color: isActive || isCompleted
                        ? theme.colorScheme.primary
                        : theme.colorScheme.onSurface.withValues(alpha: 0.7),
                    fontWeight: isActive ? FontWeight.w600 : FontWeight.normal,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          );
        }).toList(),
      ),
    );
  }
}
