import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../enums/study_plan_enums.dart';
import '../../models/study_plan_models.dart';
import '../../../debug/mock_data/mock_data.dart';
import 'components/custom_app_bar_widget.dart';
import 'components/progress_indicator_widget.dart';
import 'components/navigation_buttons_widget.dart';
import 'components/basic_details_step_widget.dart';
import 'components/tasks_and_goals_step_widget.dart';
import 'components/scheduling_step_widget.dart';
import 'components/preview_step_widget.dart';

/// Screen for creating or editing study plans with 4-step wizard
class StudyPlanCreateEditScreen extends ConsumerStatefulWidget {
  final String? studyPlanId; // null for create, id for edit
  final String? templateId; // optional template to start from

  const StudyPlanCreateEditScreen({
    super.key,
    this.studyPlanId,
    this.templateId,
  });

  @override
  ConsumerState<StudyPlanCreateEditScreen> createState() =>
      _StudyPlanCreateEditScreenState();
}

class _StudyPlanCreateEditScreenState
    extends ConsumerState<StudyPlanCreateEditScreen> {
  // Current step in the wizard
  PlanCreationStep _currentStep = PlanCreationStep.basicDetails;

  // Page controller for step navigation
  late PageController _pageController;

  // Form data storage
  final Map<String, dynamic> _formData = {};

  // Loading states
  bool _isLoading = false;
  bool _isSaving = false;

  // Existing plan data (for edit mode)
  StudyPlanModel? _existingPlan;
  StudyPlanModel? _templatePlan;

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
    _loadInitialData();
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  /// Load initial data for edit mode or template mode
  void _loadInitialData() async {
    if (widget.studyPlanId != null || widget.templateId != null) {
      setState(() => _isLoading = true);

      try {
        // Load existing plan for edit mode
        if (widget.studyPlanId != null) {
          _existingPlan = mockStudyPlansList.firstWhere(
            (plan) => plan.id == widget.studyPlanId,
            orElse: () => mockStudyPlansList.first,
          );
          _populateFormFromPlan(_existingPlan!);
        }

        // Load template for template mode
        if (widget.templateId != null) {
          _templatePlan = mockStudyPlansList.firstWhere(
            (plan) => plan.id == widget.templateId && plan.isTemplate,
            orElse: () => mockStudyPlansList.where((p) => p.isTemplate).first,
          );
          _populateFormFromTemplate(_templatePlan!);
        }
      } catch (e) {
        debugPrint('Error loading initial data: $e');
      } finally {
        setState(() => _isLoading = false);
      }
    }
  }

  /// Populate form data from existing plan
  void _populateFormFromPlan(StudyPlanModel plan) {
    _formData.addAll({
      'title': plan.title,
      'description': plan.description,
      'type': plan.type,
      'isMandatory': plan.isMandatory,
      'canBeEditedAsTemplate': plan.canBeEditedAsTemplate,
      'associationType': plan.associationType,
      'associatedClassroomId': plan.associatedClassroomId,
      'associatedExamId': plan.associatedExamId,
      'startDate': plan.startDate,
      'endDate': plan.endDate,
      'estimatedDuration': plan.estimatedDuration,
      'hasFlexibleScheduling': plan.hasFlexibleScheduling,
      'sections': plan.sections,
      'settings': plan.settings,
    });
  }

  /// Populate form data from template
  void _populateFormFromTemplate(StudyPlanModel template) {
    _formData.addAll({
      'title': '${template.title} (Copy)',
      'description': template.description,
      'type': template.type,
      'sections': template.sections,
      'settings': template.settings,
      // Don't copy template-specific fields
      'isMandatory': false,
      'canBeEditedAsTemplate': false,
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    if (_isLoading) {
      return Scaffold(
        backgroundColor: theme.colorScheme.surface,
        appBar: CustomAppBarWidget(
          studyPlanId: widget.studyPlanId,
          templateId: widget.templateId,
          isSaving: _isSaving,
          formData: _formData,
          onCancel: _onCancel,
          onSave: _onSave,
        ),
        body: const Center(child: CircularProgressIndicator()),
      );
    }

    return Scaffold(
      backgroundColor: theme.colorScheme.surface,
      appBar: CustomAppBarWidget(
        studyPlanId: widget.studyPlanId,
        templateId: widget.templateId,
        isSaving: _isSaving,
        formData: _formData,
        onCancel: _onCancel,
        onSave: _onSave,
      ),
      body: Column(
        children: [
          // Progress indicator
          ProgressIndicatorWidget(currentStep: _currentStep),

          // Step content
          Expanded(
            child: PageView(
              controller: _pageController,
              onPageChanged: _onPageChanged,
              children: [
                BasicDetailsStepWidget(
                  formData: _formData,
                  onFormDataChanged: (data) {
                    SchedulerBinding.instance.addPostFrameCallback((_) {
                      if (mounted) {
                        setState(() {
                          _formData.addAll(data);
                        });
                      }
                    });
                  },
                  isEditMode: widget.studyPlanId != null,
                ),
                TasksAndGoalsStepWidget(
                  formData: _formData,
                  onFormDataChanged: (data) {
                    SchedulerBinding.instance.addPostFrameCallback((_) {
                      if (mounted) {
                        setState(() {
                          _formData.addAll(data);
                        });
                      }
                    });
                  },
                  isEditMode: widget.studyPlanId != null,
                ),
                SchedulingStepWidget(
                  formData: _formData,
                  onFormDataChanged: (data) {
                    SchedulerBinding.instance.addPostFrameCallback((_) {
                      if (mounted) {
                        setState(() {
                          _formData.addAll(data);
                        });
                      }
                    });
                  },
                  isEditMode: widget.studyPlanId != null,
                ),
                PreviewStepWidget(formData: _formData),
              ],
            ),
          ),

          // Navigation buttons
          NavigationButtonsWidget(
            currentStep: _currentStep,
            formData: _formData,
            studyPlanId: widget.studyPlanId,
            onPrevious: _onPrevious,
            onNext: _onNext,
          ),
        ],
      ),
    );
  }

  // Event handlers
  void _onPageChanged(int index) {
    final step = PlanCreationStepHelper.getStepByPageIndex(index);
    if (step != null) {
      setState(() {
        _currentStep = step;
      });
    }
  }

  void _onPrevious() {
    if (_currentStep.stepNumber > 1) {
      final previousStep = PlanCreationStep.values.firstWhere(
        (step) => step.stepNumber == _currentStep.stepNumber - 1,
      );
      _navigateToStep(previousStep);
    }
  }

  void _onNext() {
    if (_currentStep == PlanCreationStep.preview) {
      _onSave();
    } else if (_currentStep.canBeSkipped && !_isStepValid(_currentStep)) {
      _skipToNextStep();
    } else {
      final nextStep = PlanCreationStep.values.firstWhere(
        (step) => step.stepNumber == _currentStep.stepNumber + 1,
      );
      _navigateToStep(nextStep);
    }
  }

  void _skipToNextStep() {
    final nextStep = PlanCreationStep.values.firstWhere(
      (step) => step.stepNumber == _currentStep.stepNumber + 1,
    );
    _navigateToStep(nextStep);
  }

  void _navigateToStep(PlanCreationStep step) {
    setState(() {
      _currentStep = step;
    });
    _pageController.animateToPage(
      step.pageIndex,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
  }

  void _onCancel() {
    // Show confirmation dialog if there are unsaved changes
    if (_hasUnsavedChanges()) {
      _showCancelConfirmationDialog();
    } else {
      context.pop();
    }
  }

  void _onSave() async {
    if (!_canSave()) return;

    setState(() => _isSaving = true);

    try {
      // TODO: Implement actual save logic
      await Future.delayed(const Duration(seconds: 2)); // Simulate save

      if (mounted) {
        // Navigate back to list or detail screen
        context.pop();

        // Show success message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              widget.studyPlanId != null
                  ? 'Study plan updated successfully'
                  : 'Study plan created successfully',
            ),
            backgroundColor: Theme.of(context).colorScheme.primary,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error saving study plan: $e'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isSaving = false);
      }
    }
  }

  // Helper methods
  bool _canSave() {
    // Must have completed required steps
    return _isStepValid(PlanCreationStep.basicDetails) &&
        _isStepValid(PlanCreationStep.tasksAndGoals);
  }

  bool _isStepValid(PlanCreationStep step) {
    switch (step) {
      case PlanCreationStep.basicDetails:
        return _formData['title']?.isNotEmpty == true;
      case PlanCreationStep.tasksAndGoals:
        return _formData['sections']?.isNotEmpty == true;
      case PlanCreationStep.scheduling:
        return true; // Optional step
      case PlanCreationStep.preview:
        return true; // Always valid
    }
  }

  bool _hasUnsavedChanges() {
    return _formData.isNotEmpty;
  }

  void _showCancelConfirmationDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Discard Changes?'),
        content: const Text(
          'You have unsaved changes. Are you sure you want to discard them?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              context.pop();
            },
            child: const Text('Discard'),
          ),
        ],
      ),
    );
  }
}
