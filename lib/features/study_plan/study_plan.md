# Study Plan - Scholara

The **Study Plan** module is a comprehensive academic planning system that enables students, teachers, parents, and admins to create, manage, and track structured learning plans. It supports various plan types from short-term exam preparation to long-term academic goals with hierarchical organization of subjects, sections, and tasks.

---

## Core Concepts

### 1. Study Plan Types

- **Short Term**: Daily/weekly focused plans (1-4 weeks)
- **Long Term**: Semester/yearly academic plans (3-12 months)
- **Exam Preparation**: Targeted exam-focused plans
- **Revision**: Review and reinforcement plans
- **Class Plan**: Teacher-created classroom plans
- **Self Study**: Personal learning plans
- **Project Based**: Specific project completion plans
- **General**: Can be a general plan for unspecified cases

### 2. Hierarchical Structure

```
Study Plan
├── Sections (Subjects/Topics)
│   ├── Sub-sections (Chapters/Units)
│   │   ├── Tasks/Goals (Specific activities)
│   │   │   ├── Sub-tasks (Detailed steps)
│   │   │   └── Resources (Materials/Links)
│   │   └── Milestones (Progress markers)
│   └── Assessments (Tests/Evaluations)
└── Timeline & Progress Tracking
```

### 3. Creator & Assignment System

- **Creators**: Admin, Teacher, Parent, Student
- **Assignees**: Individual students, groups, entire classes
- **Self-Assignment**: Students can create personal plans
- **Collaborative**: Multiple contributors to plan development
- **Template**: A Plan can be created as template for other students. For example a teacher created a plan with all the syllabus but didn't specify the proper dates for tasks and goals so student can do it at own pace and convenience

### 4. Plan Creation

- A plan could be created from scratch or choosing a template.
- A Plan could be made in basically 4 steps:
  - 1. **Basic Details**: Name, Description, Duration, Type, Creating for?, Mandatory or not, association (like classroom or maybe exam), Can be edited like template?
  - 2. **Tasks and Goals**: Define all the subjects, tasks and goals. Also create and define all the sections and sub-sections.
  - 3. **Scheduling**: Calendar-based scheduling where students can assign tasks and sections to specific dates. This is an optional step that can be enabled/disabled. When enabled, provides a calendar interface for visual assignment of study items to dates. Remember for this one, I need a UX such that it could handle long term as well as short term goals effectively.
  4. **Preview**: As the name suggests, a basic preview or overview of the plan.

---

## Individual Entity Management

The Study Plan feature supports **individual entity management**, allowing each section, sub-section, and task to have its own properties and scheduling capabilities while maintaining the hierarchical relationship.

### Key Capabilities

#### 1. Individual Scheduling

- **Sections**: Can have their own time ranges, durations, and scheduling properties
- **Sub-sections**: Independent scheduling separate from parent sections
- **Tasks**: Individual scheduling properties regardless of parent section/sub-section

#### 2. Hierarchical Structure with Independence

- Maintain nested hierarchy (sub-sections belong to sections, tasks belong to sub-sections)
- Each entity can be individually configured and managed
- No restrictions on creating nested sub-sections within sections

#### 3. Individual Entity Operations

- **Edit**: Modify properties of sections, sub-sections, and tasks independently
- **Schedule**: Assign individual time ranges and durations to any entity type
- **Manage**: Duplicate, delete, and organize entities at any level

#### 4. Unified Scheduling Interface

- Calendar-based scheduling supports all entity types
- Visual assignment of dates to sections, sub-sections, AND tasks
- Entity-specific scheduling information display

#### 5. Comprehensive Preview

- Display individual scheduling information for all entity types
- Show nested structure while clearly indicating each entity's properties
- Hierarchical view with individual entity details

### Technical Implementation

The individual entity management is powered by:

- `EntityScheduleManager`: Centralized management of schedulable entities
- `SchedulableEntity`: Unified wrapper for all entity types
- `FlexibleSchedule`: Individual scheduling properties for each entity
- Enhanced UI components that support entity-specific operations

---

## Use Cases

### 1. Teacher-Created Class Plans

Teachers create comprehensive study plans for their classes covering curriculum topics, assignments, and assessments with structured timelines.

### 2. Student Self-Study Plans

Students create personal study plans for exam preparation, skill development, or academic improvement with customizable goals and schedules.

### 3. Parent-Guided Study Plans

Parents create or collaborate on study plans to support their child's academic progress with structured learning activities.

### 4. Exam Preparation Plans

Focused plans for specific exams with targeted revision schedules, practice tests, and performance tracking.

### 5. Long-term Academic Plans

Semester or yearly plans covering multiple subjects with milestone tracking and progress analytics.

### 6. Collaborative Group Plans

Study groups create shared plans for project work, group studies, or peer learning activities.

---

## Core Models

### StudyPlanModel

```dart
class StudyPlanModel {
  final String id;
  final String title;
  final String? description;
  final StudyPlanType type;
  final StudyPlanStatus status;
  final String creatorId;
  final String? creatorName;
  final UserRole creatorRole;

  // Assignment & Access
  final List<String> assignedUserIds;
  final String? classId;
  final String? subjectId;
  final AssignmentScope scope;
  final bool isCollaborative;
  final bool isMandatory; // Added: Whether plan is mandatory
  final bool canBeEditedAsTemplate; // Added: Can be used as template

  // Association (Added based on user requirements)
  final String? associatedExamId; // Link to exam if applicable
  final String? associatedClassroomId; // Link to classroom if applicable
  final AssociationType? associationType; // Type of association

  // Timeline
  final DateTime createdAt;
  final DateTime? startDate;
  final DateTime? endDate;
  final Duration? estimatedDuration;
  final bool hasFlexibleScheduling; // Added: Whether scheduling is flexible
  final bool allowsScheduleEditing; // Added: Whether students can edit schedule assignments

  // Structure
  final List<StudyPlanSection> sections;
  final StudyPlanSettings settings;
  final StudyPlanProgress progress;

  // Template System (Enhanced)
  final bool isTemplate;
  final String? templateId;
  final bool allowsCustomScheduling; // Added: Template allows custom scheduling
  final TemplateSettings? templateSettings; // Added: Template-specific settings

  // Creation Process (Added: 4-step creation tracking)
  final PlanCreationStep currentCreationStep;
  final bool isCreationComplete;
  final Map<String, dynamic>? creationStepData; // Store step-specific data

  // Metadata
  final List<String> tags;
  final StudyPlanPriority priority;
  final PlanOrigin origin; // Added: How plan was created (scratch/template)
}
```

### StudyPlanSection

```dart
class StudyPlanSection {
  final String id;
  final String title;
  final String? description;
  final SectionType type; // subject, topic, chapter, custom
  final int order;
  final String? subjectId;

  // Hierarchy
  final String? parentSectionId;
  final List<StudyPlanSection> subSections;
  final List<StudyPlanTask> tasks;
  final List<StudyPlanMilestone> milestones;

  // Timeline & Individual Scheduling
  final DateTime? startDate;
  final DateTime? endDate;
  final Duration? estimatedDuration;
  final FlexibleSchedule? individualSchedule; // Individual scheduling properties

  // Progress
  final SectionProgress progress;
  final bool isCompleted;
  final DateTime? completedAt;
}
```

### StudyPlanTask

```dart
class StudyPlanTask {
  final String id;
  final String title;
  final String? description;
  final TaskType type; // reading, practice, assignment, review, test
  final TaskPriority priority;
  final TaskStatus status;

  // Assignment
  final String sectionId;
  final int order;
  final List<String> assignedUserIds;

  // Timeline (Enhanced for flexible scheduling)
  final DateTime? dueDate;
  final DateTime? startDate; // Added: Optional start date
  final DateRange? dateRange; // Added: Date range for flexible tasks
  final Duration? estimatedDuration;
  final DateTime? completedAt;
  final bool hasFlexibleDeadline; // Added: Whether deadline is flexible
  final FlexibleSchedule? individualSchedule; // Individual scheduling properties

  // Resources
  final List<TaskResource> resources;
  final List<String> attachmentIds;
  final String? homeworkId; // Link to homework if applicable

  // Progress
  final TaskProgress progress;
  final List<TaskSubmission> submissions;
  final String? feedback;
}
```

### TemplateSettings (Added: Template-specific configuration)

```dart
class TemplateSettings {
  final bool allowCustomScheduling;
  final bool allowTaskModification;
  final bool allowSectionModification;
  final List<String> editableFields;
  final Map<String, dynamic> defaultValues;
  final String? instructionsForUsers;
}
```

### DateRange (Added: Flexible date range support)

```dart
class DateRange {
  final DateTime startDate;
  final DateTime endDate;
  final bool isFlexible;
  final String? description;
}
```

### EntityScheduleManager (Added: Individual entity scheduling management)

```dart
class EntityScheduleManager {
  /// Get all schedulable entities from a list of sections
  static List<SchedulableEntity> getAllSchedulableEntities(List<StudyPlanSection> sections);

  /// Get unscheduled entities (entities without individual schedules)
  static List<SchedulableEntity> getUnscheduledEntities(List<StudyPlanSection> sections);

  /// Get scheduled entities grouped by date
  static Map<DateTime, List<SchedulableEntity>> getScheduledEntitiesByDate(List<StudyPlanSection> sections);

  /// Update schedule for a specific entity
  static List<StudyPlanSection> updateEntitySchedule(
    List<StudyPlanSection> sections,
    String entityId,
    SchedulableEntityType entityType,
    FlexibleSchedule? schedule,
  );
}
```

### SchedulableEntity (Added: Unified entity wrapper)

```dart
class SchedulableEntity {
  final String id;
  final String title;
  final String? description;
  final SchedulableEntityType type; // section, subSection, task
  final String? parentId;
  final FlexibleSchedule? schedule;
  final dynamic originalEntity; // Store the original entity for reference

  /// Create from StudyPlanSection
  factory SchedulableEntity.fromSection(StudyPlanSection section);

  /// Create from StudyPlanSection as sub-section
  factory SchedulableEntity.fromSubSection(StudyPlanSection subSection, String parentId);

  /// Create from StudyPlanTask
  factory SchedulableEntity.fromTask(StudyPlanTask task, String parentId);

  /// Get dates when this entity is scheduled
  List<DateTime> getScheduleDates();
}
```

### SchedulableEntityType (Added: Entity type enumeration)

```dart
enum SchedulableEntityType {
  section('Section'),
  subSection('Sub-section'),
  task('Task');

  const SchedulableEntityType(this.displayName);
  final String displayName;
}
```

---

## Core Screens

### 1. Study Plans List Screen

**Route**: `/study-plans`

Main dashboard showing all study plans with filtering and search capabilities.

**Features**:

- Grid/List view toggle
- Filter by type, status, creator, subject
- Search by title/description
- Sort by date, priority, progress
- Quick actions (duplicate, archive, share)
- Divide based on different things like, if the plan was create by the user itself, assigned/Shared by someone, or part of a feature user is member of like classroom.

**Widgets**:

- `StudyPlanCard`: Plan overview with progress
- `StudyPlanFilters`: Filter and sort controls
- `StudyPlanSearchBar`: Search functionality
- `CreatePlanFAB`: Floating action button

### 2. Study Plan Detail Screen

**Route**: `/study-plans/{planId}`

Daily-focused view of a study plan emphasizing today's tasks and weekly goals for enhanced productivity.

**Features**:

- Plan overview and metadata
- Today's tasks with status, priority, and progress
- Weekly goals with 7-day calendar view
- Progress visualization
- Navigation to detailed structure view
- Export/share options

**Widgets**:

- `StudyPlanHeader`: Title, progress, actions
- `TodaysTasksSection`: Today's scheduled tasks
- `WeeklyGoalsSection`: Weekly goals with calendar
- `ProgressVisualizationSection`: Visual progress tracking
- `StructureNavigationButton`: Link to structure screen

### 3. Study Plan Structure Screen

**Route**: `/study-plans/{planId}/structure`

Dedicated view for managing the hierarchical structure of sections and tasks within a study plan.

**Features**:

- Plan structure overview with statistics
- Hierarchical section/task management
- Expandable sections with task lists
- Structure editing and reordering capabilities
- Section and task management actions

**Widgets**:

- `PlanInfoHeader`: Structure overview with statistics
- `SectionsListSection`: Expandable hierarchical sections
- `StructureActions`: Edit, reorder, and management tools

### 4. Create/Edit Study Plan Screen

**Route**: `/study-plans/create` or `/study-plans/{planId}/edit`

Form-based interface for creating or editing study plans with 4-step creation wizard.

**4-Step Creation Process**:

1. **Basic Details**: Name, Description, Type, Creating for?, Mandatory or not, association (classroom/exam), Can be edited as template?
2. **Scheduling** (Optional): Enable calendar-based scheduling and assign individual schedules to sections, sub-sections, AND tasks
3. **Tasks and Goals**: Define subjects, tasks, goals, sections, and sub-sections with individual entity management
4. **Preview**: Overview and finalization with individual scheduling display for all entity types

**Features**:

- 4-step creation wizard with progress tracking
- Template selection (from scratch or template)
- **Individual Entity Scheduling**: Each section, sub-section, and task can have its own schedule properties
- Calendar-based scheduling with visual date assignment interface for all entity types
- Duration suggestions with common timeframes and descriptions
- Dynamic section/task builder with full hierarchy support (unlimited nested sub-sections)
- **Individual Entity Management**: Edit, duplicate, and manage properties for sections, sub-sections, and tasks independently
- Flexible timeline configuration (optional step)
- Association settings (classroom, exam, subject, project)
- Template creation capabilities
- Preview mode with comprehensive overview showing individual schedules for all entities

**Widgets**:

- `PlanCreationWizard`: 4-step form with progress indicator
- `BasicDetailsForm`: Step 1 - Plan metadata and settings (no duration)
- `FlexibleSchedulingBuilder`: Step 2 - Individual entity scheduling with calendar-based assignment for sections, sub-sections, and tasks
- `TasksAndGoalsBuilder`: Step 3 - Hierarchical content creation with individual entity management and edit capabilities
- `PlanPreview`: Step 4 - Final review with individual scheduling display for all entity types
- `TemplateSelector`: Choose from existing templates
- `AssociationSelector`: Link to classroom/exam/project
- `EntityScheduleManager`: Helper class for managing individual entity scheduling across all entity types
- `SchedulableEntity`: Wrapper class for unified handling of sections, sub-sections, and tasks

### 5. Study Plan Templates Screen

**Route**: `/study-plans/templates`

Library of pre-built study plan templates for quick plan creation.

**Features**:

- Template categories
- Preview templates
- Customize before use
- Save custom templates
- Share templates

**Widgets**:

- `TemplateCard`: Template preview
- `TemplateCategories`: Category filters
- `TemplatePreview`: Detailed template view
- `CustomizeTemplate`: Template modification

### 6. Study Plan Progress Screen

**Route**: `/study-plans/{planId}/progress`

Detailed progress tracking and analytics for study plans.

**Features**:

- Progress charts and graphs
- Time tracking
- Performance analytics
- Milestone tracking
- Export reports

**Widgets**:

- `ProgressDashboard`: Overview metrics
- `ProgressCharts`: Visual analytics
- `MilestoneTracker`: Milestone progress
- `TimeTracker`: Time spent tracking
- `PerformanceMetrics`: Achievement stats

---

## Enums

### StudyPlanType

- `shortTerm`: 1-4 weeks focused plans
- `longTerm`: 3-12 months academic plans
- `examPreparation`: Targeted exam plans
- `revision`: Review and reinforcement
- `classPlan`: Teacher-created classroom plans
- `selfStudy`: Personal learning plans
- `projectBased`: Project completion plans
- `general`: General plan for unspecified cases

### StudyPlanStatus

- `draft`: Plan being created
- `active`: Currently running plan
- `paused`: Temporarily stopped
- `completed`: Successfully finished
- `cancelled`: Discontinued plan
- `archived`: Stored for reference

### TaskType

- `reading`: Reading assignments
- `practice`: Practice exercises
- `assignment`: Homework/assignments
- `review`: Review sessions
- `test`: Tests/quizzes
- `project`: Project work
- `research`: Research activities

### SectionType

- `subject`: Subject-based sections
- `topic`: Topic-based organization
- `chapter`: Chapter-wise structure
- `skill`: Skill-based grouping
- `custom`: User-defined sections

### PlanCreationStep (Added: 4-step creation process)

- `basicDetails`: Step 1 - Name, Description, Duration, Type, etc.
- `tasksAndGoals`: Step 2 - Define subjects, tasks, goals, sections
- `scheduling`: Step 3 - Calendar-based assignment of tasks/sections to dates (optional)
- `preview`: Step 4 - Review and finalize the plan

### AssociationType (Added: Plan associations)

- `classroom`: Associated with a classroom
- `exam`: Associated with an exam
- `subject`: Associated with a specific subject
- `project`: Associated with a project
- `none`: No specific association

### PlanOrigin (Added: Plan creation source)

- `scratch`: Created from scratch
- `template`: Created from template
- `duplicate`: Duplicated from existing plan
- `import`: Imported from external source

### PlanCategoryType (Added: List view categorization)

- `createdByUser`: Plans created by the current user
- `assignedToUser`: Plans assigned/shared with the user
- `classroomBased`: Plans from classrooms user is member of
- `templates`: Available templates

---

## Phase-wise Development Plan

### Phase 1: Models, Enums & Mock Data ⏳

- Create all study plan models and enums
- Generate comprehensive mock data with various plan types
- Set up Firebase collection structure
- Create hierarchical data relationships

### Phase 2: UI/UX Implementation

- Build core screens with responsive design
- Implement study plan list with filtering
- Create detail view with hierarchical display
- Add create/edit screens with wizard interface
- Build progress tracking screens

### Phase 3: State Management & Backend ✅

**Repository Layer Implementation:**

- **StudyPlanRepository**: Comprehensive Firebase Firestore integration
  - User-specific study plans (created by user, assigned to user, classroom-based)
  - Template study plans for reusability
  - Search functionality with title, description, and tags
  - Batch operations for bulk updates
  - Real-time Firestore listeners for collaborative features
  - Soft delete with `isActive` flag and archive functionality

**State Management (Riverpod Providers):**

- **StudyPlanController**: Main controller with atomic providers
  - `userStudyPlansProvider`, `studyPlanByIdProvider`, `templateStudyPlansProvider`
  - Type and status filtering providers
  - Search functionality with real-time stream providers
- **StudyPlanFilterController**: Advanced filtering and search
  - Type, status, category, and classroom filtering
  - Combined filter results with active filter count
- **StudyPlanCrudController**: CRUD operations with error handling
  - StateNotifier for operation states with proper provider invalidation
- **StudyPlanRealtimeController**: Real-time updates and intelligent caching
  - Real-time streams with cache management and expiry

**Backend Integration:**

- Firebase collections structure with comprehensive metadata
- Real-time synchronization for collaborative editing
- Error handling with logging throughout all operations
- Automatic cache invalidation on data mutations
- Add collaboration features

### Phase 4: Advanced Features

- Progress tracking and analytics
- Template system
- Notification system
- Integration with homework/digital library
- Export and sharing capabilities

### Phase 5: Testing & Polish

- Comprehensive unit and widget tests
- Performance optimization
- Accessibility improvements
- Documentation updates
- Bug fixes and refinements

---

## Integration Points

### 1. Digital Library Integration

- Attach resources and materials to tasks
- Link study materials to sections
- File sharing within collaborative plans

### 2. Homework Module Integration

- Convert homework to study plan tasks
- Link assignments to plan milestones
- Sync due dates and progress

### 3. Classroom Integration

- Class-wide study plans
- Teacher assignment workflows
- Student progress monitoring

### 4. User Management Integration

- Role-based plan creation permissions
- Student-teacher-parent collaboration
- Assignment and access controls

### 5. Notification System

- Plan milestone reminders
- Task due date notifications
- Progress update alerts
- Collaboration notifications

---

## Technical Considerations

### 1. Data Structure

- Hierarchical document structure in Firestore
- Efficient querying for large plan datasets
- Real-time synchronization for collaboration
- Offline support for plan access

### 2. Performance

- Lazy loading of plan sections
- Pagination for large task lists
- Caching strategies for frequently accessed plans
- Optimized queries for progress calculations

### 3. Scalability

- Support for plans with hundreds of tasks
- Efficient collaboration for multiple users
- Template system for quick plan creation
- Bulk operations for plan management

### 4. Security

- Role-based access controls
- Plan sharing permissions
- Data privacy for personal plans
- Audit trails for plan modifications

---

## Future Enhancements

### 1. AI-Powered Features

- Intelligent plan suggestions
- Automatic task scheduling
- Performance prediction
- Personalized recommendations

### 2. Advanced Analytics

- Learning pattern analysis
- Productivity insights
- Comparative performance metrics
- Predictive progress modeling

### 3. Gamification

- Achievement badges
- Progress streaks
- Leaderboards
- Reward systems

### 4. Mobile Optimization

- Offline plan access
- Push notifications
- Widget support
- Quick task updates

---

This comprehensive study plan system will provide a robust foundation for academic planning and progress tracking while maintaining flexibility for various educational contexts and user needs.
