import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:logger/logger.dart';

import '../../../core/constants/firebase_collections.dart';
import '../enums/study_plan_enums.dart';
import '../models/study_plan_models.dart';

/// Repository for managing study plan data with Firebase Firestore
class StudyPlanRepository {
  static final StudyPlanRepository _instance = StudyPlanRepository._internal();
  factory StudyPlanRepository() => _instance;
  StudyPlanRepository._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final Logger _logger = Logger();

  // Collection references
  final String _studyPlansCollection = FirebaseCollections.studyPlans;
  // Note: _sectionsCollection and _tasksCollection will be used in future phases
  // final String _sectionsCollection = FirebaseCollections.studyPlanSections;
  // final String _tasksCollection = FirebaseCollections.studyPlanTasks;

  /// Get all study plans for a specific user
  /// Includes plans created by user, assigned to user, and from user's classrooms
  Future<List<StudyPlanModel>> getStudyPlansForUser(String userId) async {
    try {
      _logger.i('Fetching study plans for user: $userId');

      final List<StudyPlanModel> allPlans = [];

      // Query 1: Plans created by user
      final createdQuery = _firestore
          .collection(_studyPlansCollection)
          .where('creatorId', isEqualTo: userId);

      final createdSnapshot = await createdQuery.get();
      allPlans.addAll(
        createdSnapshot.docs.map(
          (doc) => StudyPlanModel.fromJson({'id': doc.id, ...doc.data()}),
        ),
      );

      // Query 2: Plans assigned to user
      final assignedQuery = _firestore
          .collection(_studyPlansCollection)
          .where('assignedUserIds', arrayContains: userId);

      final assignedSnapshot = await assignedQuery.get();
      allPlans.addAll(
        assignedSnapshot.docs.map(
          (doc) => StudyPlanModel.fromJson({'id': doc.id, ...doc.data()}),
        ),
      );

      // Remove duplicates based on ID
      final uniquePlans = <String, StudyPlanModel>{};
      for (final plan in allPlans) {
        uniquePlans[plan.id] = plan;
      }

      final result = uniquePlans.values.toList();
      _logger.i(
        'Successfully fetched ${result.length} study plans for user $userId',
      );
      return result;
    } catch (e, stackTrace) {
      _logger.e(
        'Error fetching study plans for user: $e',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// Get study plans for a specific classroom
  Future<List<StudyPlanModel>> getStudyPlansForClassroom(
    String classroomId,
  ) async {
    try {
      _logger.i('Fetching study plans for classroom: $classroomId');

      final querySnapshot = await _firestore
          .collection(_studyPlansCollection)
          .where('classId', isEqualTo: classroomId)
          .orderBy('createdAt', descending: true)
          .get();

      final plans = querySnapshot.docs
          .map((doc) => StudyPlanModel.fromJson({'id': doc.id, ...doc.data()}))
          .toList();

      _logger.i(
        'Successfully fetched ${plans.length} study plans for classroom $classroomId',
      );
      return plans;
    } catch (e, stackTrace) {
      _logger.e(
        'Error fetching study plans for classroom: $e',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// Get study plans by type
  Future<List<StudyPlanModel>> getStudyPlansByType(
    String userId,
    StudyPlanType type,
  ) async {
    try {
      _logger.i(
        'Fetching study plans by type: ${type.displayName} for user: $userId',
      );

      // Get all user plans first, then filter by type
      final allPlans = await getStudyPlansForUser(userId);
      final filteredPlans = allPlans
          .where((plan) => plan.type == type)
          .toList();

      _logger.i(
        'Successfully fetched ${filteredPlans.length} ${type.displayName} plans',
      );
      return filteredPlans;
    } catch (e, stackTrace) {
      _logger.e(
        'Error fetching study plans by type: $e',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// Get study plans by status
  Future<List<StudyPlanModel>> getStudyPlansByStatus(
    String userId,
    StudyPlanStatus status,
  ) async {
    try {
      _logger.i(
        'Fetching study plans by status: ${status.displayName} for user: $userId',
      );

      // Get all user plans first, then filter by status
      final allPlans = await getStudyPlansForUser(userId);
      final filteredPlans = allPlans
          .where((plan) => plan.status == status)
          .toList();

      _logger.i(
        'Successfully fetched ${filteredPlans.length} ${status.displayName} plans',
      );
      return filteredPlans;
    } catch (e, stackTrace) {
      _logger.e(
        'Error fetching study plans by status: $e',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// Get template study plans
  Future<List<StudyPlanModel>> getTemplateStudyPlans() async {
    try {
      _logger.i('Fetching template study plans');

      final querySnapshot = await _firestore
          .collection(_studyPlansCollection)
          .where('isTemplate', isEqualTo: true)
          .orderBy('createdAt', descending: true)
          .get();

      final templates = querySnapshot.docs
          .map((doc) => StudyPlanModel.fromJson({'id': doc.id, ...doc.data()}))
          .toList();

      _logger.i(
        'Successfully fetched ${templates.length} template study plans',
      );
      return templates;
    } catch (e, stackTrace) {
      _logger.e(
        'Error fetching template study plans: $e',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// Get a specific study plan by ID
  Future<StudyPlanModel?> getStudyPlanById(String planId) async {
    try {
      _logger.i('Fetching study plan by ID: $planId');

      final doc = await _firestore
          .collection(_studyPlansCollection)
          .doc(planId)
          .get();

      if (!doc.exists) {
        _logger.w('Study plan not found: $planId');
        return null;
      }

      final plan = StudyPlanModel.fromJson({
        'id': doc.id,
        ...doc.data() as Map<String, dynamic>,
      });

      _logger.i('Successfully fetched study plan: ${plan.title}');
      return plan;
    } catch (e, stackTrace) {
      _logger.e(
        'Error fetching study plan by ID: $e',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// Create a new study plan
  Future<StudyPlanModel> createStudyPlan(StudyPlanModel plan) async {
    try {
      _logger.i('Creating new study plan: ${plan.title}');

      final docRef = _firestore.collection(_studyPlansCollection).doc(plan.id);
      await docRef.set(plan.toJson());

      _logger.i('Successfully created study plan: ${plan.title}');
      return plan;
    } catch (e, stackTrace) {
      _logger.e(
        'Error creating study plan: $e',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// Update an existing study plan
  Future<StudyPlanModel> updateStudyPlan(StudyPlanModel plan) async {
    try {
      _logger.i('Updating study plan: ${plan.title}');

      final docRef = _firestore.collection(_studyPlansCollection).doc(plan.id);
      await docRef.update(plan.toJson());

      _logger.i('Successfully updated study plan: ${plan.title}');
      return plan;
    } catch (e, stackTrace) {
      _logger.e(
        'Error updating study plan: $e',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// Delete a study plan (hard delete)
  Future<void> deleteStudyPlan(String planId) async {
    try {
      _logger.i('Deleting study plan: $planId');

      await _firestore.collection(_studyPlansCollection).doc(planId).delete();

      _logger.i('Successfully deleted study plan: $planId');
    } catch (e, stackTrace) {
      _logger.e(
        'Error deleting study plan: $e',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// Archive a study plan
  Future<void> archiveStudyPlan(String planId) async {
    try {
      _logger.i('Archiving study plan: $planId');

      await _firestore.collection(_studyPlansCollection).doc(planId).update({
        'status': StudyPlanStatus.archived.name,
        'archivedAt': DateTime.now().toIso8601String(),
      });

      _logger.i('Successfully archived study plan: $planId');
    } catch (e, stackTrace) {
      _logger.e(
        'Error archiving study plan: $e',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// Duplicate a study plan
  Future<StudyPlanModel> duplicateStudyPlan(
    String planId,
    String newTitle,
    String userId,
  ) async {
    try {
      _logger.i('Duplicating study plan: $planId');

      final originalPlan = await getStudyPlanById(planId);
      if (originalPlan == null) {
        throw Exception('Study plan not found: $planId');
      }

      final duplicatedPlan = originalPlan.copyWith(
        id: _firestore.collection(_studyPlansCollection).doc().id,
        title: newTitle,
        creatorId: userId,
        status: StudyPlanStatus.draft,
        createdAt: DateTime.now(),
        origin: PlanOrigin.duplicate,
        templateId: originalPlan.isTemplate ? originalPlan.id : null,
      );

      await createStudyPlan(duplicatedPlan);

      _logger.i('Successfully duplicated study plan: $newTitle');
      return duplicatedPlan;
    } catch (e, stackTrace) {
      _logger.e(
        'Error duplicating study plan: $e',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// Search study plans by title or description
  Future<List<StudyPlanModel>> searchStudyPlans(
    String userId,
    String query,
  ) async {
    try {
      _logger.i('Searching study plans for user: $userId with query: $query');

      // Get all user plans first, then filter by search query
      final allPlans = await getStudyPlansForUser(userId);
      final searchQuery = query.toLowerCase().trim();

      if (searchQuery.isEmpty) {
        return allPlans;
      }

      final filteredPlans = allPlans.where((plan) {
        final titleMatch = plan.title.toLowerCase().contains(searchQuery);
        final descriptionMatch =
            plan.description?.toLowerCase().contains(searchQuery) ?? false;
        final tagsMatch = plan.tags.any(
          (tag) => tag.toLowerCase().contains(searchQuery),
        );

        return titleMatch || descriptionMatch || tagsMatch;
      }).toList();

      _logger.i(
        'Found ${filteredPlans.length} study plans matching query: $query',
      );
      return filteredPlans;
    } catch (e, stackTrace) {
      _logger.e(
        'Error searching study plans: $e',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// Update study plan progress
  Future<void> updateStudyPlanProgress(
    String planId,
    StudyPlanProgress progress,
  ) async {
    try {
      _logger.i('Updating progress for study plan: $planId');

      await _firestore.collection(_studyPlansCollection).doc(planId).update({
        'progress': progress.toJson(),
        'lastUpdated': DateTime.now().toIso8601String(),
      });

      _logger.i('Successfully updated progress for study plan: $planId');
    } catch (e, stackTrace) {
      _logger.e(
        'Error updating study plan progress: $e',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// Update study plan status
  Future<void> updateStudyPlanStatus(
    String planId,
    StudyPlanStatus status,
  ) async {
    try {
      _logger.i(
        'Updating status for study plan: $planId to ${status.displayName}',
      );

      final updateData = <String, dynamic>{
        'status': status.name,
        'lastUpdated': DateTime.now().toIso8601String(),
      };

      // Add status-specific timestamps
      switch (status) {
        case StudyPlanStatus.active:
          updateData['activatedAt'] = DateTime.now().toIso8601String();
          break;
        case StudyPlanStatus.completed:
          updateData['completedAt'] = DateTime.now().toIso8601String();
          break;
        case StudyPlanStatus.paused:
          updateData['pausedAt'] = DateTime.now().toIso8601String();
          break;
        case StudyPlanStatus.cancelled:
          updateData['cancelledAt'] = DateTime.now().toIso8601String();
          break;
        case StudyPlanStatus.archived:
          updateData['archivedAt'] = DateTime.now().toIso8601String();
          break;
        default:
          break;
      }

      await _firestore
          .collection(_studyPlansCollection)
          .doc(planId)
          .update(updateData);

      _logger.i('Successfully updated status for study plan: $planId');
    } catch (e, stackTrace) {
      _logger.e(
        'Error updating study plan status: $e',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// Get study plans with real-time updates
  Stream<List<StudyPlanModel>> getStudyPlansStream(String userId) {
    try {
      _logger.i(
        'Setting up real-time stream for study plans for user: $userId',
      );

      // Stream for plans created by user
      final createdStream = _firestore
          .collection(_studyPlansCollection)
          .where('creatorId', isEqualTo: userId)
          .snapshots();

      // Stream for plans assigned to user
      final assignedStream = _firestore
          .collection(_studyPlansCollection)
          .where('assignedUserIds', arrayContains: userId)
          .snapshots();

      // Combine both streams
      return createdStream.asyncMap((createdSnapshot) async {
        final assignedSnapshot = await assignedStream.first;

        final allPlans = <StudyPlanModel>[];

        // Add created plans
        allPlans.addAll(
          createdSnapshot.docs.map(
            (doc) => StudyPlanModel.fromJson({'id': doc.id, ...doc.data()}),
          ),
        );

        // Add assigned plans
        allPlans.addAll(
          assignedSnapshot.docs.map(
            (doc) => StudyPlanModel.fromJson({'id': doc.id, ...doc.data()}),
          ),
        );

        // Remove duplicates based on ID
        final uniquePlans = <String, StudyPlanModel>{};
        for (final plan in allPlans) {
          uniquePlans[plan.id] = plan;
        }

        return uniquePlans.values.toList();
      });
    } catch (e, stackTrace) {
      _logger.e(
        'Error setting up study plans stream: $e',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// Get study plan by ID with real-time updates
  Stream<StudyPlanModel?> getStudyPlanByIdStream(String planId) {
    try {
      _logger.i('Setting up real-time stream for study plan: $planId');

      return _firestore
          .collection(_studyPlansCollection)
          .doc(planId)
          .snapshots()
          .map((doc) {
            if (!doc.exists) {
              return null;
            }
            return StudyPlanModel.fromJson({
              'id': doc.id,
              ...doc.data() as Map<String, dynamic>,
            });
          });
    } catch (e, stackTrace) {
      _logger.e(
        'Error setting up study plan stream: $e',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// Batch update multiple study plans
  Future<void> batchUpdateStudyPlans(List<StudyPlanModel> plans) async {
    try {
      _logger.i('Batch updating ${plans.length} study plans');

      final batch = _firestore.batch();

      for (final plan in plans) {
        final docRef = _firestore
            .collection(_studyPlansCollection)
            .doc(plan.id);
        batch.update(docRef, plan.toJson());
      }

      await batch.commit();

      _logger.i('Successfully batch updated ${plans.length} study plans');
    } catch (e, stackTrace) {
      _logger.e(
        'Error batch updating study plans: $e',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }
}
