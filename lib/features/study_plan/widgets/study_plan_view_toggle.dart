import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_symbols_icons/symbols.dart';

/// Enum for different view modes
enum StudyPlanViewMode { list, grid }

/// Widget for toggling between list and grid view modes
class StudyPlanViewToggle extends StatelessWidget {
  /// Current view mode
  final StudyPlanViewMode viewMode;

  /// Callback when view mode changes
  final ValueChanged<StudyPlanViewMode> onViewModeChanged;

  /// Whether to show labels
  final bool showLabels;

  const StudyPlanViewToggle({
    super.key,
    required this.viewMode,
    required this.onViewModeChanged,
    this.showLabels = false,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(
          color: theme.colorScheme.outline.withValues(alpha: 0.3),
          width: 1.w,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildToggleButton(
            context: context,
            theme: theme,
            mode: StudyPlanViewMode.list,
            icon: Symbols.view_list,
            label: 'List',
            isSelected: viewMode == StudyPlanViewMode.list,
            onTap: () => onViewModeChanged(StudyPlanViewMode.list),
          ),
          _buildToggleButton(
            context: context,
            theme: theme,
            mode: StudyPlanViewMode.grid,
            icon: Symbols.grid_view,
            label: 'Grid',
            isSelected: viewMode == StudyPlanViewMode.grid,
            onTap: () => onViewModeChanged(StudyPlanViewMode.grid),
          ),
        ],
      ),
    );
  }

  Widget _buildToggleButton({
    required BuildContext context,
    required ThemeData theme,
    required StudyPlanViewMode mode,
    required IconData icon,
    required String label,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return Material(
      color: isSelected
          ? theme.colorScheme.primary.withValues(alpha: 0.1)
          : Colors.transparent,
      borderRadius: BorderRadius.circular(6.r),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(6.r),
        child: Container(
          padding: EdgeInsets.symmetric(
            horizontal: showLabels ? 12.w : 8.w,
            vertical: 8.h,
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                icon,
                size: 20.sp,
                color: isSelected
                    ? theme.colorScheme.primary
                    : theme.colorScheme.onSurface.withValues(alpha: 0.6),
              ),
              if (showLabels) ...[
                SizedBox(width: 4.w),
                Text(
                  label,
                  style: theme.textTheme.labelMedium?.copyWith(
                    color: isSelected
                        ? theme.colorScheme.primary
                        : theme.colorScheme.onSurface.withValues(alpha: 0.6),
                    fontWeight: isSelected
                        ? FontWeight.w600
                        : FontWeight.normal,
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
}

/// Compact view toggle for use in app bars
class CompactStudyPlanViewToggle extends StatelessWidget {
  /// Current view mode
  final StudyPlanViewMode viewMode;

  /// Callback when view mode changes
  final ValueChanged<StudyPlanViewMode> onViewModeChanged;

  const CompactStudyPlanViewToggle({
    super.key,
    required this.viewMode,
    required this.onViewModeChanged,
  });

  @override
  Widget build(BuildContext context) {
    return IconButton(
      icon: Icon(
        viewMode == StudyPlanViewMode.list
            ? Symbols.grid_view
            : Symbols.view_list,
        size: 24.sp,
      ),
      onPressed: () {
        final newMode = viewMode == StudyPlanViewMode.list
            ? StudyPlanViewMode.grid
            : StudyPlanViewMode.list;
        onViewModeChanged(newMode);
      },
      tooltip: viewMode == StudyPlanViewMode.list
          ? 'Switch to grid view'
          : 'Switch to list view',
    );
  }
}

/// Extension for view mode utilities
extension StudyPlanViewModeExtension on StudyPlanViewMode {
  /// Display name for the view mode
  String get displayName {
    switch (this) {
      case StudyPlanViewMode.list:
        return 'List';
      case StudyPlanViewMode.grid:
        return 'Grid';
    }
  }

  /// Icon for the view mode
  IconData get icon {
    switch (this) {
      case StudyPlanViewMode.list:
        return Symbols.view_list;
      case StudyPlanViewMode.grid:
        return Symbols.grid_view;
    }
  }

  /// Icon for switching to this view mode
  IconData get switchIcon {
    switch (this) {
      case StudyPlanViewMode.list:
        return Symbols.grid_view; // Show grid icon to switch to list
      case StudyPlanViewMode.grid:
        return Symbols.view_list; // Show list icon to switch to grid
    }
  }
}
