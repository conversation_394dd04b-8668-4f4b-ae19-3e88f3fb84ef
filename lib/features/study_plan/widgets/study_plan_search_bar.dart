import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_symbols_icons/symbols.dart';

/// Search bar widget for study plans with clear functionality
class StudyPlanSearchBar extends StatefulWidget {
  /// Current search query
  final String searchQuery;

  /// Callback when search query changes
  final ValueChanged<String> onSearchChanged;

  /// Callback when search is cleared
  final VoidCallback? onSearchClear;

  /// Hint text for the search field
  final String hintText;

  /// Whether the search bar is active/focused
  final bool isActive;

  /// Callback when search bar focus changes
  final ValueChanged<bool>? onFocusChanged;

  const StudyPlanSearchBar({
    super.key,
    required this.searchQuery,
    required this.onSearchChanged,
    this.onSearchClear,
    this.hintText = 'Search study plans...',
    this.isActive = false,
    this.onFocusChanged,
  });

  @override
  State<StudyPlanSearchBar> createState() => _StudyPlanSearchBarState();
}

class _StudyPlanSearchBarState extends State<StudyPlanSearchBar> {
  late TextEditingController _controller;
  late FocusNode _focusNode;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.searchQuery);
    _focusNode = FocusNode();
    
    _focusNode.addListener(() {
      widget.onFocusChanged?.call(_focusNode.hasFocus);
    });
  }

  @override
  void didUpdateWidget(StudyPlanSearchBar oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.searchQuery != widget.searchQuery) {
      _controller.text = widget.searchQuery;
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(
          color: widget.isActive
              ? theme.colorScheme.primary
              : theme.colorScheme.outline.withValues(alpha: 0.3),
          width: widget.isActive ? 2.w : 1.w,
        ),
        boxShadow: widget.isActive
            ? [
                BoxShadow(
                  color: theme.colorScheme.primary.withValues(alpha: 0.1),
                  blurRadius: 8.r,
                  offset: Offset(0, 2.h),
                ),
              ]
            : null,
      ),
      child: TextField(
        controller: _controller,
        focusNode: _focusNode,
        onChanged: widget.onSearchChanged,
        decoration: InputDecoration(
          hintText: widget.hintText,
          hintStyle: theme.textTheme.bodyMedium?.copyWith(
            color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
          ),
          prefixIcon: Icon(
            Symbols.search,
            color: widget.isActive
                ? theme.colorScheme.primary
                : theme.colorScheme.onSurface.withValues(alpha: 0.5),
            size: 20.sp,
          ),
          suffixIcon: widget.searchQuery.isNotEmpty
              ? IconButton(
                  icon: Icon(
                    Symbols.close,
                    color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                    size: 20.sp,
                  ),
                  onPressed: () {
                    _controller.clear();
                    widget.onSearchChanged('');
                    widget.onSearchClear?.call();
                  },
                )
              : null,
          border: InputBorder.none,
          contentPadding: EdgeInsets.symmetric(
            horizontal: 16.w,
            vertical: 12.h,
          ),
        ),
        style: theme.textTheme.bodyMedium,
        textInputAction: TextInputAction.search,
      ),
    );
  }
}

/// Compact search bar for use in app bars or tight spaces
class CompactStudyPlanSearchBar extends StatelessWidget {
  /// Current search query
  final String searchQuery;

  /// Callback when search query changes
  final ValueChanged<String> onSearchChanged;

  /// Callback when search is cleared
  final VoidCallback? onSearchClear;

  /// Hint text for the search field
  final String hintText;

  const CompactStudyPlanSearchBar({
    super.key,
    required this.searchQuery,
    required this.onSearchChanged,
    this.onSearchClear,
    this.hintText = 'Search...',
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      height: 40.h,
      margin: EdgeInsets.symmetric(horizontal: 8.w),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface.withValues(alpha: 0.8),
        borderRadius: BorderRadius.circular(20.r),
        border: Border.all(
          color: theme.colorScheme.outline.withValues(alpha: 0.3),
          width: 1.w,
        ),
      ),
      child: TextField(
        onChanged: onSearchChanged,
        decoration: InputDecoration(
          hintText: hintText,
          hintStyle: theme.textTheme.bodySmall?.copyWith(
            color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
          ),
          prefixIcon: Icon(
            Symbols.search,
            color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
            size: 18.sp,
          ),
          suffixIcon: searchQuery.isNotEmpty
              ? IconButton(
                  icon: Icon(
                    Symbols.close,
                    color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                    size: 16.sp,
                  ),
                  onPressed: () {
                    onSearchChanged('');
                    onSearchClear?.call();
                  },
                )
              : null,
          border: InputBorder.none,
          contentPadding: EdgeInsets.symmetric(
            horizontal: 12.w,
            vertical: 8.h,
          ),
        ),
        style: theme.textTheme.bodySmall,
        textInputAction: TextInputAction.search,
      ),
    );
  }
}
