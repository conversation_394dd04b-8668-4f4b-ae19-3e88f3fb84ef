import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_symbols_icons/symbols.dart';

import '../models/study_plan_models.dart';
import '../enums/study_plan_enums.dart';

/// Card widget displaying study plan information with progress and actions
class StudyPlanCard extends ConsumerWidget {
  /// The study plan data to display
  final StudyPlanModel studyPlan;

  /// Callback when the card is tapped
  final VoidCallback onTap;

  /// Callback when a menu action is selected
  final Function(String action)? onMenuAction;

  /// Whether to show in compact mode (for grid view)
  final bool isCompact;

  const StudyPlanCard({
    super.key,
    required this.studyPlan,
    required this.onTap,
    this.onMenuAction,
    this.isCompact = false,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);

    return Card(
      margin: isCompact
          ? EdgeInsets.zero
          : EdgeInsets.symmetric(horizontal: 16.w, vertical: 6.h),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12.r)),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12.r),
        child: Container(
          padding: EdgeInsets.all(16.w),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12.r),
            border: Border(
              left: BorderSide(color: studyPlan.type.color, width: 4.w),
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: isCompact ? MainAxisSize.min : MainAxisSize.max,
            children: [
              // Header row with type and status
              Row(
                children: [
                  // Plan type chip
                  Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: 8.w,
                      vertical: 4.h,
                    ),
                    decoration: BoxDecoration(
                      color: studyPlan.type.color.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(6.r),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          studyPlan.type.icon,
                          size: 12.sp,
                          color: studyPlan.type.color,
                        ),
                        SizedBox(width: 4.w),
                        Text(
                          studyPlan.type.displayName,
                          style: theme.textTheme.labelSmall?.copyWith(
                            color: studyPlan.type.color,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  ),

                  const Spacer(),

                  // Status chip
                  _StatusChip(status: studyPlan.status),

                  if (onMenuAction != null) ...[
                    SizedBox(width: 8.w),
                    _buildMenuButton(theme),
                  ],
                ],
              ),

              SizedBox(height: 12.h),

              // Title
              Text(
                studyPlan.title,
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
                maxLines: isCompact ? 2 : 3,
                overflow: TextOverflow.ellipsis,
              ),

              if (studyPlan.description != null && !isCompact) ...[
                SizedBox(height: 8.h),
                Text(
                  studyPlan.description!,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],

              SizedBox(height: 12.h),

              // Progress bar
              _buildProgressBar(theme),

              SizedBox(height: 12.h),

              // Bottom row with dates and creator info
              Row(
                children: [
                  // Date info
                  Row(
                    children: [
                      Icon(
                        Symbols.schedule,
                        size: 16.sp,
                        color: theme.colorScheme.onSurface.withValues(
                          alpha: 0.6,
                        ),
                      ),
                      SizedBox(width: 4.w),
                      Text(
                        _formatDateRange(),
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.colorScheme.onSurface.withValues(
                            alpha: 0.6,
                          ),
                        ),
                      ),
                    ],
                  ),

                  const Spacer(),

                  // Creator info (if not created by current user)
                  if (studyPlan.creatorName != null && !isCompact)
                    Row(
                      children: [
                        Icon(
                          studyPlan.creatorRole.icon,
                          size: 14.sp,
                          color: theme.colorScheme.onSurface.withValues(
                            alpha: 0.6,
                          ),
                        ),
                        SizedBox(width: 4.w),
                        Text(
                          studyPlan.creatorName!,
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: theme.colorScheme.onSurface.withValues(
                              alpha: 0.6,
                            ),
                          ),
                        ),
                      ],
                    ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildMenuButton(ThemeData theme) {
    return PopupMenuButton<String>(
      icon: Icon(
        Symbols.more_vert,
        size: 20.sp,
        color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
      ),
      onSelected: onMenuAction,
      itemBuilder: (context) => [
        const PopupMenuItem(value: 'edit', child: Text('Edit')),
        const PopupMenuItem(value: 'duplicate', child: Text('Duplicate')),
        const PopupMenuItem(value: 'share', child: Text('Share')),
        const PopupMenuItem(value: 'archive', child: Text('Archive')),
      ],
    );
  }

  Widget _buildProgressBar(ThemeData theme) {
    final progress = studyPlan.progress.overallCompletion;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Progress',
              style: theme.textTheme.labelMedium?.copyWith(
                color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
              ),
            ),
            Text(
              '${(progress * 100).toInt()}%',
              style: theme.textTheme.labelMedium?.copyWith(
                color: theme.colorScheme.primary,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        SizedBox(height: 4.h),
        LinearProgressIndicator(
          value: progress,
          backgroundColor: theme.colorScheme.outline.withValues(alpha: 0.2),
          valueColor: AlwaysStoppedAnimation<Color>(theme.colorScheme.primary),
          borderRadius: BorderRadius.circular(2.r),
        ),
      ],
    );
  }

  String _formatDateRange() {
    if (studyPlan.startDate != null && studyPlan.endDate != null) {
      final start = studyPlan.startDate!;
      final end = studyPlan.endDate!;

      if (start.year == end.year && start.month == end.month) {
        return '${start.day}-${end.day} ${_getMonthName(start.month)}';
      } else {
        return '${start.day} ${_getMonthName(start.month)} - ${end.day} ${_getMonthName(end.month)}';
      }
    } else if (studyPlan.startDate != null) {
      final start = studyPlan.startDate!;
      return 'From ${start.day} ${_getMonthName(start.month)}';
    } else if (studyPlan.endDate != null) {
      final end = studyPlan.endDate!;
      return 'Until ${end.day} ${_getMonthName(end.month)}';
    } else {
      return 'No dates set';
    }
  }

  String _getMonthName(int month) {
    const months = [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec',
    ];
    return months[month - 1];
  }
}

/// Status chip widget for study plan status
class _StatusChip extends StatelessWidget {
  final StudyPlanStatus status;

  const _StatusChip({required this.status});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
      decoration: BoxDecoration(
        color: status.color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(6.r),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(status.icon, size: 12.sp, color: status.color),
          SizedBox(width: 4.w),
          Text(
            status.displayName,
            style: theme.textTheme.labelSmall?.copyWith(
              color: status.color,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }
}
