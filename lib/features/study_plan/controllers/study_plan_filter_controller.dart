import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logger/logger.dart';

import '../enums/study_plan_enums.dart';
import '../models/study_plan_models.dart';
import 'study_plan_controller.dart';

/// Logger for study plan filter controller
final _logger = Logger();

/// State notifier for study plan type filter
class StudyPlanTypeFilterNotifier extends StateNotifier<StudyPlanType?> {
  StudyPlanTypeFilterNotifier() : super(null);

  void setType(StudyPlanType? type) {
    _logger.i('Setting study plan type filter to: ${type?.displayName ?? 'All'}');
    state = type;
  }

  void clearType() {
    _logger.i('Clearing study plan type filter');
    state = null;
  }
}

/// Provider for study plan type filter state
final studyPlanTypeFilterProvider = StateNotifierProvider<StudyPlanTypeFilterNotifier, StudyPlanType?>(
  (ref) {
    return StudyPlanTypeFilterNotifier();
  },
);

/// State notifier for study plan status filter
class StudyPlanStatusFilterNotifier extends StateNotifier<StudyPlanStatus?> {
  StudyPlanStatusFilterNotifier() : super(null);

  void setStatus(StudyPlanStatus? status) {
    _logger.i('Setting study plan status filter to: ${status?.displayName ?? 'All'}');
    state = status;
  }

  void clearStatus() {
    _logger.i('Clearing study plan status filter');
    state = null;
  }
}

/// Provider for study plan status filter state
final studyPlanStatusFilterProvider = StateNotifierProvider<StudyPlanStatusFilterNotifier, StudyPlanStatus?>(
  (ref) {
    return StudyPlanStatusFilterNotifier();
  },
);

/// State notifier for study plan category filter
class StudyPlanCategoryFilterNotifier extends StateNotifier<PlanCategoryType?> {
  StudyPlanCategoryFilterNotifier() : super(null);

  void setCategory(PlanCategoryType? category) {
    _logger.i('Setting study plan category filter to: ${category?.displayName ?? 'All'}');
    state = category;
  }

  void clearCategory() {
    _logger.i('Clearing study plan category filter');
    state = null;
  }
}

/// Provider for study plan category filter state
final studyPlanCategoryFilterProvider = StateNotifierProvider<StudyPlanCategoryFilterNotifier, PlanCategoryType?>(
  (ref) {
    return StudyPlanCategoryFilterNotifier();
  },
);

/// State notifier for classroom filter
class ClassroomFilterNotifier extends StateNotifier<String?> {
  ClassroomFilterNotifier() : super(null);

  void setClassroom(String? classroomId) {
    _logger.i('Setting classroom filter to: $classroomId');
    state = classroomId;
  }

  void clearClassroom() {
    _logger.i('Clearing classroom filter');
    state = null;
  }
}

/// Provider for classroom filter state
final classroomFilterProvider = StateNotifierProvider<ClassroomFilterNotifier, String?>(
  (ref) {
    return ClassroomFilterNotifier();
  },
);

/// State notifier for search query
class StudyPlanSearchNotifier extends StateNotifier<String> {
  StudyPlanSearchNotifier() : super('');

  void setQuery(String query) {
    _logger.i('Setting study plan search query to: $query');
    state = query.trim();
  }

  void clearQuery() {
    _logger.i('Clearing study plan search query');
    state = '';
  }
}

/// Provider for study plan search state
final studyPlanSearchProvider = StateNotifierProvider<StudyPlanSearchNotifier, String>(
  (ref) {
    return StudyPlanSearchNotifier();
  },
);

/// Provider that combines filter state and returns filtered study plans
final filteredStudyPlansProvider = FutureProvider<List<StudyPlanModel>>((ref) async {
  final typeFilter = ref.watch(studyPlanTypeFilterProvider);
  final statusFilter = ref.watch(studyPlanStatusFilterProvider);
  final categoryFilter = ref.watch(studyPlanCategoryFilterProvider);
  final classroomFilter = ref.watch(classroomFilterProvider);
  final searchQuery = ref.watch(studyPlanSearchProvider);
  final userId = ref.read(currentUserIdProvider);

  try {
    _logger.i('Applying filters to study plans');

    // Get base data based on classroom filter
    List<StudyPlanModel> studyPlans;
    if (classroomFilter != null) {
      studyPlans = await ref.read(classroomStudyPlansProvider(classroomFilter).future);
    } else {
      studyPlans = await ref.read(userStudyPlansProvider.future);
    }

    // Apply search filter
    if (searchQuery.isNotEmpty) {
      final searchLower = searchQuery.toLowerCase();
      studyPlans = studyPlans.where((plan) {
        final titleMatch = plan.title.toLowerCase().contains(searchLower);
        final descriptionMatch = plan.description?.toLowerCase().contains(searchLower) ?? false;
        final tagsMatch = plan.tags.any((tag) => tag.toLowerCase().contains(searchLower));
        return titleMatch || descriptionMatch || tagsMatch;
      }).toList();
    }

    // Apply type filter
    if (typeFilter != null) {
      studyPlans = studyPlans.where((plan) => plan.type == typeFilter).toList();
    }

    // Apply status filter
    if (statusFilter != null) {
      studyPlans = studyPlans.where((plan) => plan.status == statusFilter).toList();
    }

    // Apply category filter
    if (categoryFilter != null) {
      studyPlans = studyPlans.where((plan) {
        switch (categoryFilter) {
          case PlanCategoryType.createdByUser:
            return plan.creatorId == userId;
          case PlanCategoryType.assignedToUser:
            return plan.assignedUserIds.contains(userId) && plan.creatorId != userId;
          case PlanCategoryType.classroomBased:
            return plan.classId != null;
          case PlanCategoryType.templates:
            return plan.isTemplate;
        }
      }).toList();
    }

    _logger.i('Filtered study plans: ${studyPlans.length} results');
    return studyPlans;
  } catch (e) {
    _logger.e('Error filtering study plans: $e');
    rethrow;
  }
});

/// Provider for active filter count
final activeFilterCountProvider = Provider<int>((ref) {
  final typeFilter = ref.watch(studyPlanTypeFilterProvider);
  final statusFilter = ref.watch(studyPlanStatusFilterProvider);
  final categoryFilter = ref.watch(studyPlanCategoryFilterProvider);
  final classroomFilter = ref.watch(classroomFilterProvider);
  final searchQuery = ref.watch(studyPlanSearchProvider);

  int count = 0;
  if (typeFilter != null) count++;
  if (statusFilter != null) count++;
  if (categoryFilter != null) count++;
  if (classroomFilter != null) count++;
  if (searchQuery.isNotEmpty) count++;

  return count;
});

/// Provider for filter summary text
final filterSummaryProvider = Provider<String>((ref) {
  final typeFilter = ref.watch(studyPlanTypeFilterProvider);
  final statusFilter = ref.watch(studyPlanStatusFilterProvider);
  final categoryFilter = ref.watch(studyPlanCategoryFilterProvider);
  final classroomFilter = ref.watch(classroomFilterProvider);
  final searchQuery = ref.watch(studyPlanSearchProvider);

  final filters = <String>[];

  if (searchQuery.isNotEmpty) {
    filters.add('Search: "$searchQuery"');
  }
  if (typeFilter != null) {
    filters.add('Type: ${typeFilter.displayName}');
  }
  if (statusFilter != null) {
    filters.add('Status: ${statusFilter.displayName}');
  }
  if (categoryFilter != null) {
    filters.add('Category: ${categoryFilter.displayName}');
  }
  if (classroomFilter != null) {
    filters.add('Classroom');
  }

  if (filters.isEmpty) {
    return 'All study plans';
  }

  return filters.join(' • ');
});

/// Clear all filters
void clearAllFilters(WidgetRef ref) {
  _logger.i('Clearing all study plan filters');
  ref.read(studyPlanTypeFilterProvider.notifier).clearType();
  ref.read(studyPlanStatusFilterProvider.notifier).clearStatus();
  ref.read(studyPlanCategoryFilterProvider.notifier).clearCategory();
  ref.read(classroomFilterProvider.notifier).clearClassroom();
  ref.read(studyPlanSearchProvider.notifier).clearQuery();
}

/// Study plan filter service for managing filter state
class StudyPlanFilterService {
  StudyPlanFilterService();

  /// Apply multiple filters at once
  void applyFilters(
    WidgetRef ref, {
    StudyPlanType? type,
    StudyPlanStatus? status,
    PlanCategoryType? category,
    String? classroomId,
    String? searchQuery,
  }) {
    _logger.i('Applying multiple study plan filters');

    if (type != null) {
      ref.read(studyPlanTypeFilterProvider.notifier).setType(type);
    }
    if (status != null) {
      ref.read(studyPlanStatusFilterProvider.notifier).setStatus(status);
    }
    if (category != null) {
      ref.read(studyPlanCategoryFilterProvider.notifier).setCategory(category);
    }
    if (classroomId != null) {
      ref.read(classroomFilterProvider.notifier).setClassroom(classroomId);
    }
    if (searchQuery != null) {
      ref.read(studyPlanSearchProvider.notifier).setQuery(searchQuery);
    }
  }

  /// Get current filter state
  Map<String, dynamic> getCurrentFilters(WidgetRef ref) {
    return {
      'type': ref.read(studyPlanTypeFilterProvider),
      'status': ref.read(studyPlanStatusFilterProvider),
      'category': ref.read(studyPlanCategoryFilterProvider),
      'classroomId': ref.read(classroomFilterProvider),
      'searchQuery': ref.read(studyPlanSearchProvider),
    };
  }

  /// Check if any filters are active
  bool hasActiveFilters(WidgetRef ref) {
    return ref.read(activeFilterCountProvider) > 0;
  }
}

/// Provider for the filter service
final studyPlanFilterServiceProvider = Provider<StudyPlanFilterService>((ref) {
  return StudyPlanFilterService();
});
