import 'dart:async';

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logger/logger.dart';

import '../models/study_plan_models.dart';
import 'study_plan_controller.dart';

/// Logger for study plan real-time controller
final _logger = Logger();

/// State notifier for managing real-time study plan updates
class StudyPlanRealtimeNotifier
    extends StateNotifier<AsyncValue<List<StudyPlanModel>>> {
  StudyPlanRealtimeNotifier(this._ref) : super(const AsyncValue.loading()) {
    _initialize();
  }

  final Ref _ref;
  StreamSubscription<List<StudyPlanModel>>? _subscription;

  void _initialize() {
    final userId = _ref.read(currentUserIdProvider);
    _logger.i('Initializing real-time study plans for user: $userId');

    // Listen to real-time updates
    final repository = _ref.read(studyPlanRepositoryProvider);
    _subscription = repository
        .getStudyPlansStream(userId)
        .listen(
          (studyPlans) {
            _logger.i(
              'Received real-time update: ${studyPlans.length} study plans',
            );
            state = AsyncValue.data(studyPlans);
          },
          onError: (error, stackTrace) {
            _logger.e('Error in real-time study plans stream: $error');
            state = AsyncValue.error(error, stackTrace);
          },
        );
  }

  @override
  void dispose() {
    _subscription?.cancel();
    super.dispose();
  }

  /// Manually refresh the data
  void refresh() {
    _logger.i('Manually refreshing real-time study plans');
    _subscription?.cancel();
    state = const AsyncValue.loading();
    _initialize();
  }

  /// Get current study plans synchronously
  List<StudyPlanModel> get currentStudyPlans {
    return state.when(
      data: (plans) => plans,
      loading: () => [],
      error: (_, __) => [],
    );
  }

  /// Check if data is available
  bool get hasData => state.hasValue && state.value != null;

  /// Check if there's an error
  bool get hasError => state.hasError;

  /// Get error message
  String? get errorMessage => state.hasError ? state.error.toString() : null;
}

/// Provider for real-time study plans
final studyPlanRealtimeProvider =
    StateNotifierProvider<
      StudyPlanRealtimeNotifier,
      AsyncValue<List<StudyPlanModel>>
    >((ref) {
      return StudyPlanRealtimeNotifier(ref);
    });

/// State notifier for managing real-time study plan by ID
class StudyPlanByIdRealtimeNotifier
    extends StateNotifier<AsyncValue<StudyPlanModel?>> {
  StudyPlanByIdRealtimeNotifier(this._ref, this._planId)
    : super(const AsyncValue.loading()) {
    _initialize();
  }

  final Ref _ref;
  final String _planId;
  StreamSubscription<StudyPlanModel?>? _subscription;

  void _initialize() {
    _logger.i('Initializing real-time study plan for ID: $_planId');

    // Listen to real-time updates for specific plan
    final repository = _ref.read(studyPlanRepositoryProvider);
    _subscription = repository
        .getStudyPlanByIdStream(_planId)
        .listen(
          (studyPlan) {
            _logger.i('Received real-time update for plan: $_planId');
            state = AsyncValue.data(studyPlan);
          },
          onError: (error, stackTrace) {
            _logger.e('Error in real-time study plan stream: $error');
            state = AsyncValue.error(error, stackTrace);
          },
        );
  }

  @override
  void dispose() {
    _subscription?.cancel();
    super.dispose();
  }

  /// Manually refresh the data
  void refresh() {
    _logger.i('Manually refreshing real-time study plan: $_planId');
    _subscription?.cancel();
    state = const AsyncValue.loading();
    _initialize();
  }

  /// Get current study plan synchronously
  StudyPlanModel? get currentStudyPlan {
    return state.when(
      data: (plan) => plan,
      loading: () => null,
      error: (_, __) => null,
    );
  }

  /// Check if data is available
  bool get hasData => state.hasValue;

  /// Check if there's an error
  bool get hasError => state.hasError;

  /// Get error message
  String? get errorMessage => state.hasError ? state.error.toString() : null;
}

/// Provider for real-time study plan by ID
final studyPlanByIdRealtimeProvider =
    StateNotifierProvider.family<
      StudyPlanByIdRealtimeNotifier,
      AsyncValue<StudyPlanModel?>,
      String
    >((ref, planId) {
      return StudyPlanByIdRealtimeNotifier(ref, planId);
    });

/// Cache manager for study plans
class StudyPlanCacheManager {
  static final StudyPlanCacheManager _instance =
      StudyPlanCacheManager._internal();
  factory StudyPlanCacheManager() => _instance;
  StudyPlanCacheManager._internal();

  final Map<String, StudyPlanModel> _cache = {};
  final Map<String, DateTime> _cacheTimestamps = {};
  final Duration _cacheExpiry = const Duration(minutes: 5);

  /// Add study plan to cache
  void cacheStudyPlan(StudyPlanModel studyPlan) {
    _cache[studyPlan.id] = studyPlan;
    _cacheTimestamps[studyPlan.id] = DateTime.now();
    _logger.d('Cached study plan: ${studyPlan.id}');
  }

  /// Get study plan from cache
  StudyPlanModel? getCachedStudyPlan(String planId) {
    final timestamp = _cacheTimestamps[planId];
    if (timestamp == null) return null;

    // Check if cache is expired
    if (DateTime.now().difference(timestamp) > _cacheExpiry) {
      _cache.remove(planId);
      _cacheTimestamps.remove(planId);
      _logger.d('Cache expired for study plan: $planId');
      return null;
    }

    final cachedPlan = _cache[planId];
    if (cachedPlan != null) {
      _logger.d('Retrieved cached study plan: $planId');
    }
    return cachedPlan;
  }

  /// Cache multiple study plans
  void cacheStudyPlans(List<StudyPlanModel> studyPlans) {
    for (final plan in studyPlans) {
      cacheStudyPlan(plan);
    }
    _logger.d('Cached ${studyPlans.length} study plans');
  }

  /// Remove study plan from cache
  void removeCachedStudyPlan(String planId) {
    _cache.remove(planId);
    _cacheTimestamps.remove(planId);
    _logger.d('Removed cached study plan: $planId');
  }

  /// Clear all cached study plans
  void clearCache() {
    _cache.clear();
    _cacheTimestamps.clear();
    _logger.d('Cleared all cached study plans');
  }

  /// Get cache statistics
  Map<String, dynamic> getCacheStats() {
    final now = DateTime.now();
    final validEntries = _cacheTimestamps.values
        .where((timestamp) => now.difference(timestamp) <= _cacheExpiry)
        .length;

    return {
      'totalEntries': _cache.length,
      'validEntries': validEntries,
      'expiredEntries': _cache.length - validEntries,
      'cacheHitRate': validEntries / (_cache.isNotEmpty ? _cache.length : 1),
    };
  }

  /// Clean expired entries
  void cleanExpiredEntries() {
    final now = DateTime.now();
    final expiredKeys = <String>[];

    for (final entry in _cacheTimestamps.entries) {
      if (now.difference(entry.value) > _cacheExpiry) {
        expiredKeys.add(entry.key);
      }
    }

    for (final key in expiredKeys) {
      _cache.remove(key);
      _cacheTimestamps.remove(key);
    }

    if (expiredKeys.isNotEmpty) {
      _logger.d('Cleaned ${expiredKeys.length} expired cache entries');
    }
  }
}

/// Provider for study plan cache manager
final studyPlanCacheManagerProvider = Provider<StudyPlanCacheManager>((ref) {
  return StudyPlanCacheManager();
});

/// Enhanced study plan provider with caching
final cachedStudyPlanByIdProvider =
    FutureProvider.family<StudyPlanModel?, String>((ref, planId) async {
      final cacheManager = ref.read(studyPlanCacheManagerProvider);

      // Try to get from cache first
      final cachedPlan = cacheManager.getCachedStudyPlan(planId);
      if (cachedPlan != null) {
        _logger.d('Returning cached study plan: $planId');
        return cachedPlan;
      }

      // If not in cache, fetch from repository
      try {
        final repository = ref.read(studyPlanRepositoryProvider);
        final studyPlan = await repository.getStudyPlanById(planId);

        if (studyPlan != null) {
          cacheManager.cacheStudyPlan(studyPlan);
        }

        return studyPlan;
      } catch (e) {
        _logger.e('Error fetching study plan: $e');
        rethrow;
      }
    });

/// Enhanced user study plans provider with caching
final cachedUserStudyPlansProvider = FutureProvider<List<StudyPlanModel>>((
  ref,
) async {
  final cacheManager = ref.read(studyPlanCacheManagerProvider);

  try {
    final repository = ref.read(studyPlanRepositoryProvider);
    final userId = ref.read(currentUserIdProvider);

    final studyPlans = await repository.getStudyPlansForUser(userId);

    // Cache all fetched plans
    cacheManager.cacheStudyPlans(studyPlans);

    return studyPlans;
  } catch (e) {
    _logger.e('Error fetching user study plans: $e');
    rethrow;
  }
});

/// Provider for cache statistics
final cacheStatsProvider = Provider<Map<String, dynamic>>((ref) {
  final cacheManager = ref.read(studyPlanCacheManagerProvider);
  return cacheManager.getCacheStats();
});

/// Helper class for managing real-time updates and caching
class StudyPlanRealtimeHelper {
  final Ref _ref;

  StudyPlanRealtimeHelper(this._ref);

  /// Get real-time study plans
  AsyncValue<List<StudyPlanModel>> get realtimeStudyPlans =>
      _ref.watch(studyPlanRealtimeProvider);

  /// Get real-time study plan by ID
  AsyncValue<StudyPlanModel?> realtimeStudyPlanById(String planId) =>
      _ref.watch(studyPlanByIdRealtimeProvider(planId));

  /// Refresh real-time data
  void refreshRealtimeData() {
    _ref.read(studyPlanRealtimeProvider.notifier).refresh();
  }

  /// Refresh specific study plan
  void refreshStudyPlan(String planId) {
    _ref.read(studyPlanByIdRealtimeProvider(planId).notifier).refresh();
  }

  /// Clear cache
  void clearCache() {
    _ref.read(studyPlanCacheManagerProvider).clearCache();
  }

  /// Clean expired cache entries
  void cleanCache() {
    _ref.read(studyPlanCacheManagerProvider).cleanExpiredEntries();
  }

  /// Get cache statistics
  Map<String, dynamic> get cacheStats => _ref.read(cacheStatsProvider);
}

/// Provider for real-time helper
final studyPlanRealtimeHelperProvider = Provider<StudyPlanRealtimeHelper>((
  ref,
) {
  return StudyPlanRealtimeHelper(ref);
});
