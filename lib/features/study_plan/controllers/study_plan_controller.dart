import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logger/logger.dart';

import '../../../core/providers/auth_providers.dart';
import '../enums/study_plan_enums.dart';
import '../models/study_plan_models.dart';
import '../repositories/study_plan_repository.dart';

/// Logger for study plan controller
final _logger = Logger();

/// Provider for the study plan repository instance
final studyPlanRepositoryProvider = Provider<StudyPlanRepository>((ref) {
  return StudyPlanRepository();
});

/// Provider for current user ID from authentication
final currentUserIdProvider = Provider<String>((ref) {
  // For testing purposes, use the test user ID
  // TODO: Remove this when authentication is fully implemented
  const testUserId = 'XRTanMcAUWSMq3mrRvve2Y9IMP12';

  final authState = ref.watch(authStateProvider);
  return authState.when(
    data: (state) => state.user?.id ?? testUserId,
    loading: () => testUserId,
    error: (_, __) => testUserId,
  );
});

/// Provider to fetch all study plans for the current user
final userStudyPlansProvider = FutureProvider<List<StudyPlanModel>>((
  ref,
) async {
  final repository = ref.read(studyPlanRepositoryProvider);
  final userId = ref.read(currentUserIdProvider);

  try {
    _logger.i('Fetching study plans for user: $userId');
    final studyPlans = await repository.getStudyPlansForUser(userId);
    _logger.i(
      'Successfully fetched ${studyPlans.length} study plans for user $userId',
    );
    return studyPlans;
  } catch (e) {
    _logger.e('Error fetching study plans for user: $e');
    rethrow;
  }
});

/// Provider to fetch study plans for a specific classroom
final classroomStudyPlansProvider =
    FutureProvider.family<List<StudyPlanModel>, String>((
      ref,
      classroomId,
    ) async {
      final repository = ref.read(studyPlanRepositoryProvider);

      try {
        _logger.i('Fetching study plans for classroom: $classroomId');
        final studyPlans = await repository.getStudyPlansForClassroom(
          classroomId,
        );
        _logger.i(
          'Successfully fetched ${studyPlans.length} study plans for classroom $classroomId',
        );
        return studyPlans;
      } catch (e) {
        _logger.e('Error fetching study plans for classroom: $e');
        rethrow;
      }
    });

/// Provider to fetch study plans by type
final studyPlansByTypeProvider =
    FutureProvider.family<List<StudyPlanModel>, StudyPlanType>((
      ref,
      type,
    ) async {
      final repository = ref.read(studyPlanRepositoryProvider);
      final userId = ref.read(currentUserIdProvider);

      try {
        _logger.i(
          'Fetching study plans by type: ${type.displayName} for user: $userId',
        );
        final studyPlans = await repository.getStudyPlansByType(userId, type);
        _logger.i(
          'Successfully fetched ${studyPlans.length} ${type.displayName} plans',
        );
        return studyPlans;
      } catch (e) {
        _logger.e('Error fetching study plans by type: $e');
        rethrow;
      }
    });

/// Provider to fetch study plans by status
final studyPlansByStatusProvider =
    FutureProvider.family<List<StudyPlanModel>, StudyPlanStatus>((
      ref,
      status,
    ) async {
      final repository = ref.read(studyPlanRepositoryProvider);
      final userId = ref.read(currentUserIdProvider);

      try {
        _logger.i(
          'Fetching study plans by status: ${status.displayName} for user: $userId',
        );
        final studyPlans = await repository.getStudyPlansByStatus(
          userId,
          status,
        );
        _logger.i(
          'Successfully fetched ${studyPlans.length} ${status.displayName} plans',
        );
        return studyPlans;
      } catch (e) {
        _logger.e('Error fetching study plans by status: $e');
        rethrow;
      }
    });

/// Provider to fetch template study plans
final templateStudyPlansProvider = FutureProvider<List<StudyPlanModel>>((
  ref,
) async {
  final repository = ref.read(studyPlanRepositoryProvider);

  try {
    _logger.i('Fetching template study plans');
    final templates = await repository.getTemplateStudyPlans();
    _logger.i('Successfully fetched ${templates.length} template study plans');
    return templates;
  } catch (e) {
    _logger.e('Error fetching template study plans: $e');
    rethrow;
  }
});

/// Provider to fetch a specific study plan by ID
final studyPlanByIdProvider = FutureProvider.family<StudyPlanModel?, String>((
  ref,
  planId,
) async {
  final repository = ref.read(studyPlanRepositoryProvider);

  try {
    _logger.i('Fetching study plan by ID: $planId');
    final studyPlan = await repository.getStudyPlanById(planId);
    if (studyPlan != null) {
      _logger.i('Successfully fetched study plan: ${studyPlan.title}');
    } else {
      _logger.w('Study plan not found: $planId');
    }
    return studyPlan;
  } catch (e) {
    _logger.e('Error fetching study plan by ID: $e');
    rethrow;
  }
});

/// Provider to search study plans
final searchStudyPlansProvider =
    FutureProvider.family<List<StudyPlanModel>, String>((ref, query) async {
      final repository = ref.read(studyPlanRepositoryProvider);
      final userId = ref.read(currentUserIdProvider);

      try {
        _logger.i('Searching study plans for user: $userId with query: $query');
        final studyPlans = await repository.searchStudyPlans(userId, query);
        _logger.i(
          'Found ${studyPlans.length} study plans matching query: $query',
        );
        return studyPlans;
      } catch (e) {
        _logger.e('Error searching study plans: $e');
        rethrow;
      }
    });

/// Provider to create a new study plan
final createStudyPlanProvider =
    FutureProvider.family<StudyPlanModel, StudyPlanModel>((
      ref,
      studyPlan,
    ) async {
      final repository = ref.read(studyPlanRepositoryProvider);

      try {
        _logger.i('Creating new study plan: ${studyPlan.title}');
        final createdPlan = await repository.createStudyPlan(studyPlan);

        // Invalidate relevant providers
        ref.invalidate(userStudyPlansProvider);
        if (studyPlan.classId != null) {
          ref.invalidate(classroomStudyPlansProvider(studyPlan.classId!));
        }
        ref.invalidate(studyPlansByTypeProvider(studyPlan.type));
        ref.invalidate(studyPlansByStatusProvider(studyPlan.status));

        _logger.i('Successfully created study plan: ${createdPlan.title}');
        return createdPlan;
      } catch (e) {
        _logger.e('Error creating study plan: $e');
        rethrow;
      }
    });

/// Provider to update a study plan
final updateStudyPlanProvider =
    FutureProvider.family<StudyPlanModel, StudyPlanModel>((
      ref,
      studyPlan,
    ) async {
      final repository = ref.read(studyPlanRepositoryProvider);

      try {
        _logger.i('Updating study plan: ${studyPlan.title}');
        final updatedPlan = await repository.updateStudyPlan(studyPlan);

        // Invalidate relevant providers
        ref.invalidate(userStudyPlansProvider);
        ref.invalidate(studyPlanByIdProvider(studyPlan.id));
        if (studyPlan.classId != null) {
          ref.invalidate(classroomStudyPlansProvider(studyPlan.classId!));
        }
        ref.invalidate(studyPlansByTypeProvider(studyPlan.type));
        ref.invalidate(studyPlansByStatusProvider(studyPlan.status));

        _logger.i('Successfully updated study plan: ${updatedPlan.title}');
        return updatedPlan;
      } catch (e) {
        _logger.e('Error updating study plan: $e');
        rethrow;
      }
    });

/// Provider to delete a study plan
final deleteStudyPlanProvider = FutureProvider.family<void, String>((
  ref,
  planId,
) async {
  final repository = ref.read(studyPlanRepositoryProvider);

  try {
    _logger.i('Deleting study plan: $planId');
    await repository.deleteStudyPlan(planId);

    // Invalidate relevant providers
    ref.invalidate(userStudyPlansProvider);
    ref.invalidate(studyPlanByIdProvider(planId));

    _logger.i('Successfully deleted study plan: $planId');
  } catch (e) {
    _logger.e('Error deleting study plan: $e');
    rethrow;
  }
});

/// Provider to archive a study plan
final archiveStudyPlanProvider = FutureProvider.family<void, String>((
  ref,
  planId,
) async {
  final repository = ref.read(studyPlanRepositoryProvider);

  try {
    _logger.i('Archiving study plan: $planId');
    await repository.archiveStudyPlan(planId);

    // Invalidate relevant providers
    ref.invalidate(userStudyPlansProvider);
    ref.invalidate(studyPlanByIdProvider(planId));

    _logger.i('Successfully archived study plan: $planId');
  } catch (e) {
    _logger.e('Error archiving study plan: $e');
    rethrow;
  }
});

/// Provider to duplicate a study plan
final duplicateStudyPlanProvider =
    FutureProvider.family<StudyPlanModel, Map<String, String>>((
      ref,
      params,
    ) async {
      final repository = ref.read(studyPlanRepositoryProvider);
      final planId = params['planId']!;
      final newTitle = params['newTitle']!;
      final userId = ref.read(currentUserIdProvider);

      try {
        _logger.i('Duplicating study plan: $planId');
        final duplicatedPlan = await repository.duplicateStudyPlan(
          planId,
          newTitle,
          userId,
        );

        // Invalidate relevant providers
        ref.invalidate(userStudyPlansProvider);
        ref.invalidate(studyPlansByTypeProvider(duplicatedPlan.type));
        ref.invalidate(studyPlansByStatusProvider(duplicatedPlan.status));

        _logger.i(
          'Successfully duplicated study plan: ${duplicatedPlan.title}',
        );
        return duplicatedPlan;
      } catch (e) {
        _logger.e('Error duplicating study plan: $e');
        rethrow;
      }
    });

/// Provider to update study plan status
final updateStudyPlanStatusProvider =
    FutureProvider.family<void, Map<String, dynamic>>((ref, params) async {
      final repository = ref.read(studyPlanRepositoryProvider);
      final planId = params['planId'] as String;
      final status = params['status'] as StudyPlanStatus;

      try {
        _logger.i(
          'Updating status for study plan: $planId to ${status.displayName}',
        );
        await repository.updateStudyPlanStatus(planId, status);

        // Invalidate relevant providers
        ref.invalidate(userStudyPlansProvider);
        ref.invalidate(studyPlanByIdProvider(planId));
        ref.invalidate(studyPlansByStatusProvider(status));

        _logger.i('Successfully updated status for study plan: $planId');
      } catch (e) {
        _logger.e('Error updating study plan status: $e');
        rethrow;
      }
    });

/// Provider to update study plan progress
final updateStudyPlanProgressProvider =
    FutureProvider.family<void, Map<String, dynamic>>((ref, params) async {
      final repository = ref.read(studyPlanRepositoryProvider);
      final planId = params['planId'] as String;
      final progress = params['progress'] as StudyPlanProgress;

      try {
        _logger.i('Updating progress for study plan: $planId');
        await repository.updateStudyPlanProgress(planId, progress);

        // Invalidate relevant providers
        ref.invalidate(userStudyPlansProvider);
        ref.invalidate(studyPlanByIdProvider(planId));

        _logger.i('Successfully updated progress for study plan: $planId');
      } catch (e) {
        _logger.e('Error updating study plan progress: $e');
        rethrow;
      }
    });

/// Provider for real-time study plans stream
final userStudyPlansStreamProvider = StreamProvider<List<StudyPlanModel>>((
  ref,
) {
  final repository = ref.read(studyPlanRepositoryProvider);
  final userId = ref.read(currentUserIdProvider);

  try {
    _logger.i('Setting up real-time stream for study plans for user: $userId');
    return repository.getStudyPlansStream(userId);
  } catch (e) {
    _logger.e('Error setting up study plans stream: $e');
    rethrow;
  }
});

/// Provider for real-time study plan by ID stream
final studyPlanByIdStreamProvider =
    StreamProvider.family<StudyPlanModel?, String>((ref, planId) {
      final repository = ref.read(studyPlanRepositoryProvider);

      try {
        _logger.i('Setting up real-time stream for study plan: $planId');
        return repository.getStudyPlanByIdStream(planId);
      } catch (e) {
        _logger.e('Error setting up study plan stream: $e');
        rethrow;
      }
    });

/// Class to handle study plan operations with proper state invalidation
class StudyPlanOperations {
  final StudyPlanRepository _repository;
  final Ref _ref;

  StudyPlanOperations(this._repository, this._ref);

  /// Create a new study plan
  Future<StudyPlanModel> createStudyPlan(StudyPlanModel studyPlan) async {
    try {
      final createdPlan = await _repository.createStudyPlan(studyPlan);

      // Invalidate relevant providers
      _ref.invalidate(userStudyPlansProvider);
      _ref.invalidate(userStudyPlansStreamProvider);
      if (studyPlan.classId != null) {
        _ref.invalidate(classroomStudyPlansProvider(studyPlan.classId!));
      }
      _ref.invalidate(studyPlansByTypeProvider(studyPlan.type));
      _ref.invalidate(studyPlansByStatusProvider(studyPlan.status));

      return createdPlan;
    } catch (e) {
      _logger.e('Error creating study plan: $e');
      rethrow;
    }
  }

  /// Update an existing study plan
  Future<StudyPlanModel> updateStudyPlan(StudyPlanModel studyPlan) async {
    try {
      final updatedPlan = await _repository.updateStudyPlan(studyPlan);

      // Invalidate relevant providers
      _ref.invalidate(userStudyPlansProvider);
      _ref.invalidate(userStudyPlansStreamProvider);
      _ref.invalidate(studyPlanByIdProvider(studyPlan.id));
      _ref.invalidate(studyPlanByIdStreamProvider(studyPlan.id));
      if (studyPlan.classId != null) {
        _ref.invalidate(classroomStudyPlansProvider(studyPlan.classId!));
      }
      _ref.invalidate(studyPlansByTypeProvider(studyPlan.type));
      _ref.invalidate(studyPlansByStatusProvider(studyPlan.status));

      return updatedPlan;
    } catch (e) {
      _logger.e('Error updating study plan: $e');
      rethrow;
    }
  }

  /// Delete a study plan
  Future<void> deleteStudyPlan(String planId) async {
    try {
      await _repository.deleteStudyPlan(planId);

      // Invalidate relevant providers
      _ref.invalidate(userStudyPlansProvider);
      _ref.invalidate(userStudyPlansStreamProvider);
      _ref.invalidate(studyPlanByIdProvider(planId));
      _ref.invalidate(studyPlanByIdStreamProvider(planId));
    } catch (e) {
      _logger.e('Error deleting study plan: $e');
      rethrow;
    }
  }

  /// Archive a study plan
  Future<void> archiveStudyPlan(String planId) async {
    try {
      await _repository.archiveStudyPlan(planId);

      // Invalidate relevant providers
      _ref.invalidate(userStudyPlansProvider);
      _ref.invalidate(userStudyPlansStreamProvider);
      _ref.invalidate(studyPlanByIdProvider(planId));
      _ref.invalidate(studyPlanByIdStreamProvider(planId));
    } catch (e) {
      _logger.e('Error archiving study plan: $e');
      rethrow;
    }
  }

  /// Update study plan status
  Future<void> updateStudyPlanStatus(
    String planId,
    StudyPlanStatus status,
  ) async {
    try {
      await _repository.updateStudyPlanStatus(planId, status);

      // Invalidate relevant providers
      _ref.invalidate(userStudyPlansProvider);
      _ref.invalidate(userStudyPlansStreamProvider);
      _ref.invalidate(studyPlanByIdProvider(planId));
      _ref.invalidate(studyPlanByIdStreamProvider(planId));
      _ref.invalidate(studyPlansByStatusProvider(status));
    } catch (e) {
      _logger.e('Error updating study plan status: $e');
      rethrow;
    }
  }

  /// Update study plan progress
  Future<void> updateStudyPlanProgress(
    String planId,
    StudyPlanProgress progress,
  ) async {
    try {
      await _repository.updateStudyPlanProgress(planId, progress);

      // Invalidate relevant providers
      _ref.invalidate(userStudyPlansProvider);
      _ref.invalidate(userStudyPlansStreamProvider);
      _ref.invalidate(studyPlanByIdProvider(planId));
      _ref.invalidate(studyPlanByIdStreamProvider(planId));
    } catch (e) {
      _logger.e('Error updating study plan progress: $e');
      rethrow;
    }
  }
}

/// Provider for study plan operations
final studyPlanOperationsProvider = Provider<StudyPlanOperations>((ref) {
  final repository = ref.read(studyPlanRepositoryProvider);
  return StudyPlanOperations(repository, ref);
});
