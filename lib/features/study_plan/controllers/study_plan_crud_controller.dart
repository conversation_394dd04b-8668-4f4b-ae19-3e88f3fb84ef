import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logger/logger.dart';

import '../enums/study_plan_enums.dart';
import '../models/study_plan_models.dart';
import 'study_plan_controller.dart';

/// Logger for study plan CRUD controller
final _logger = Logger();

/// State notifier for managing study plan CRUD operations
class StudyPlanCrudNotifier extends StateNotifier<AsyncValue<void>> {
  StudyPlanCrudNotifier(this._ref) : super(const AsyncValue.data(null));

  final Ref _ref;

  /// Create a new study plan
  Future<StudyPlanModel> createStudyPlan(StudyPlanModel studyPlan) async {
    state = const AsyncValue.loading();

    try {
      _logger.i('Creating study plan: ${studyPlan.title}');
      final operations = _ref.read(studyPlanOperationsProvider);
      final createdPlan = await operations.createStudyPlan(studyPlan);

      state = const AsyncValue.data(null);
      _logger.i('Successfully created study plan: ${createdPlan.title}');
      return createdPlan;
    } catch (e, stackTrace) {
      _logger.e('Error creating study plan: $e');
      state = AsyncValue.error(e, stackTrace);
      rethrow;
    }
  }

  /// Update an existing study plan
  Future<StudyPlanModel> updateStudyPlan(StudyPlanModel studyPlan) async {
    state = const AsyncValue.loading();

    try {
      _logger.i('Updating study plan: ${studyPlan.title}');
      final operations = _ref.read(studyPlanOperationsProvider);
      final updatedPlan = await operations.updateStudyPlan(studyPlan);

      state = const AsyncValue.data(null);
      _logger.i('Successfully updated study plan: ${updatedPlan.title}');
      return updatedPlan;
    } catch (e, stackTrace) {
      _logger.e('Error updating study plan: $e');
      state = AsyncValue.error(e, stackTrace);
      rethrow;
    }
  }

  /// Delete a study plan
  Future<void> deleteStudyPlan(String planId) async {
    state = const AsyncValue.loading();

    try {
      _logger.i('Deleting study plan: $planId');
      final operations = _ref.read(studyPlanOperationsProvider);
      await operations.deleteStudyPlan(planId);

      state = const AsyncValue.data(null);
      _logger.i('Successfully deleted study plan: $planId');
    } catch (e, stackTrace) {
      _logger.e('Error deleting study plan: $e');
      state = AsyncValue.error(e, stackTrace);
      rethrow;
    }
  }

  /// Archive a study plan
  Future<void> archiveStudyPlan(String planId) async {
    state = const AsyncValue.loading();

    try {
      _logger.i('Archiving study plan: $planId');
      final operations = _ref.read(studyPlanOperationsProvider);
      await operations.archiveStudyPlan(planId);

      state = const AsyncValue.data(null);
      _logger.i('Successfully archived study plan: $planId');
    } catch (e, stackTrace) {
      _logger.e('Error archiving study plan: $e');
      state = AsyncValue.error(e, stackTrace);
      rethrow;
    }
  }

  /// Update study plan status
  Future<void> updateStudyPlanStatus(
    String planId,
    StudyPlanStatus status,
  ) async {
    state = const AsyncValue.loading();

    try {
      _logger.i(
        'Updating status for study plan: $planId to ${status.displayName}',
      );
      final operations = _ref.read(studyPlanOperationsProvider);
      await operations.updateStudyPlanStatus(planId, status);

      state = const AsyncValue.data(null);
      _logger.i('Successfully updated status for study plan: $planId');
    } catch (e, stackTrace) {
      _logger.e('Error updating study plan status: $e');
      state = AsyncValue.error(e, stackTrace);
      rethrow;
    }
  }

  /// Update study plan progress
  Future<void> updateStudyPlanProgress(
    String planId,
    StudyPlanProgress progress,
  ) async {
    state = const AsyncValue.loading();

    try {
      _logger.i('Updating progress for study plan: $planId');
      final operations = _ref.read(studyPlanOperationsProvider);
      await operations.updateStudyPlanProgress(planId, progress);

      state = const AsyncValue.data(null);
      _logger.i('Successfully updated progress for study plan: $planId');
    } catch (e, stackTrace) {
      _logger.e('Error updating study plan progress: $e');
      state = AsyncValue.error(e, stackTrace);
      rethrow;
    }
  }

  /// Duplicate a study plan
  Future<StudyPlanModel> duplicateStudyPlan(
    String planId,
    String newTitle,
  ) async {
    state = const AsyncValue.loading();

    try {
      _logger.i('Duplicating study plan: $planId');
      final repository = _ref.read(studyPlanRepositoryProvider);
      final userId = _ref.read(currentUserIdProvider);

      final duplicatedPlan = await repository.duplicateStudyPlan(
        planId,
        newTitle,
        userId,
      );

      // Invalidate relevant providers
      _ref.invalidate(userStudyPlansProvider);
      _ref.invalidate(studyPlansByTypeProvider(duplicatedPlan.type));
      _ref.invalidate(studyPlansByStatusProvider(duplicatedPlan.status));

      state = const AsyncValue.data(null);
      _logger.i('Successfully duplicated study plan: ${duplicatedPlan.title}');
      return duplicatedPlan;
    } catch (e, stackTrace) {
      _logger.e('Error duplicating study plan: $e');
      state = AsyncValue.error(e, stackTrace);
      rethrow;
    }
  }

  /// Batch update multiple study plans
  Future<void> batchUpdateStudyPlans(List<StudyPlanModel> plans) async {
    state = const AsyncValue.loading();

    try {
      _logger.i('Batch updating ${plans.length} study plans');
      final repository = _ref.read(studyPlanRepositoryProvider);
      await repository.batchUpdateStudyPlans(plans);

      // Invalidate relevant providers
      _ref.invalidate(userStudyPlansProvider);
      for (final plan in plans) {
        _ref.invalidate(studyPlanByIdProvider(plan.id));
        if (plan.classId != null) {
          _ref.invalidate(classroomStudyPlansProvider(plan.classId!));
        }
      }

      state = const AsyncValue.data(null);
      _logger.i('Successfully batch updated ${plans.length} study plans');
    } catch (e, stackTrace) {
      _logger.e('Error batch updating study plans: $e');
      state = AsyncValue.error(e, stackTrace);
      rethrow;
    }
  }

  /// Clear any error state
  void clearError() {
    if (state.hasError) {
      state = const AsyncValue.data(null);
    }
  }
}

/// Provider for study plan CRUD operations
final studyPlanCrudProvider =
    StateNotifierProvider<StudyPlanCrudNotifier, AsyncValue<void>>((ref) {
      return StudyPlanCrudNotifier(ref);
    });

/// Helper class for study plan CRUD operations with error handling
class StudyPlanCrudHelper {
  final Ref _ref;

  StudyPlanCrudHelper(this._ref);

  /// Create a study plan with error handling
  Future<StudyPlanModel?> createStudyPlan(StudyPlanModel studyPlan) async {
    try {
      final notifier = _ref.read(studyPlanCrudProvider.notifier);
      return await notifier.createStudyPlan(studyPlan);
    } catch (e) {
      _logger.e('Failed to create study plan: $e');
      return null;
    }
  }

  /// Update a study plan with error handling
  Future<StudyPlanModel?> updateStudyPlan(StudyPlanModel studyPlan) async {
    try {
      final notifier = _ref.read(studyPlanCrudProvider.notifier);
      return await notifier.updateStudyPlan(studyPlan);
    } catch (e) {
      _logger.e('Failed to update study plan: $e');
      return null;
    }
  }

  /// Delete a study plan with error handling
  Future<bool> deleteStudyPlan(String planId) async {
    try {
      final notifier = _ref.read(studyPlanCrudProvider.notifier);
      await notifier.deleteStudyPlan(planId);
      return true;
    } catch (e) {
      _logger.e('Failed to delete study plan: $e');
      return false;
    }
  }

  /// Archive a study plan with error handling
  Future<bool> archiveStudyPlan(String planId) async {
    try {
      final notifier = _ref.read(studyPlanCrudProvider.notifier);
      await notifier.archiveStudyPlan(planId);
      return true;
    } catch (e) {
      _logger.e('Failed to archive study plan: $e');
      return false;
    }
  }

  /// Update study plan status with error handling
  Future<bool> updateStudyPlanStatus(
    String planId,
    StudyPlanStatus status,
  ) async {
    try {
      final notifier = _ref.read(studyPlanCrudProvider.notifier);
      await notifier.updateStudyPlanStatus(planId, status);
      return true;
    } catch (e) {
      _logger.e('Failed to update study plan status: $e');
      return false;
    }
  }

  /// Update study plan progress with error handling
  Future<bool> updateStudyPlanProgress(
    String planId,
    StudyPlanProgress progress,
  ) async {
    try {
      final notifier = _ref.read(studyPlanCrudProvider.notifier);
      await notifier.updateStudyPlanProgress(planId, progress);
      return true;
    } catch (e) {
      _logger.e('Failed to update study plan progress: $e');
      return false;
    }
  }

  /// Duplicate a study plan with error handling
  Future<StudyPlanModel?> duplicateStudyPlan(
    String planId,
    String newTitle,
  ) async {
    try {
      final notifier = _ref.read(studyPlanCrudProvider.notifier);
      return await notifier.duplicateStudyPlan(planId, newTitle);
    } catch (e) {
      _logger.e('Failed to duplicate study plan: $e');
      return null;
    }
  }

  /// Get current operation state
  AsyncValue<void> get operationState => _ref.read(studyPlanCrudProvider);

  /// Check if any operation is in progress
  bool get isLoading => operationState.isLoading;

  /// Check if there's an error
  bool get hasError => operationState.hasError;

  /// Get error message if any
  String? get errorMessage =>
      operationState.hasError ? operationState.error.toString() : null;

  /// Clear any error state
  void clearError() {
    _ref.read(studyPlanCrudProvider.notifier).clearError();
  }
}

/// Provider for study plan CRUD helper
final studyPlanCrudHelperProvider = Provider<StudyPlanCrudHelper>((ref) {
  return StudyPlanCrudHelper(ref);
});
