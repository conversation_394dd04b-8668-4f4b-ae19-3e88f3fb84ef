import 'package:flutter/material.dart';

/// Enum representing different user roles in the study plan system
enum UserRole {
  admin,
  teacher,
  parent,
  student,
}

extension UserRoleExtension on UserRole {
  /// Display name for the user role
  String get displayName {
    switch (this) {
      case UserRole.admin:
        return 'Admin';
      case UserRole.teacher:
        return 'Teacher';
      case UserRole.parent:
        return 'Parent';
      case UserRole.student:
        return 'Student';
    }
  }

  /// Description for the user role
  String get description {
    switch (this) {
      case UserRole.admin:
        return 'System administrator with full access';
      case UserRole.teacher:
        return 'Teacher who can create and manage class plans';
      case UserRole.parent:
        return 'Parent who can create and monitor child plans';
      case UserRole.student:
        return 'Student who can create personal plans and follow assigned plans';
    }
  }

  /// Icon for the user role
  IconData get icon {
    switch (this) {
      case UserRole.admin:
        return Icons.admin_panel_settings;
      case UserRole.teacher:
        return Icons.school;
      case UserRole.parent:
        return Icons.family_restroom;
      case UserRole.student:
        return Icons.person;
    }
  }

  /// Color associated with the user role
  Color get color {
    switch (this) {
      case UserRole.admin:
        return Colors.red;
      case UserRole.teacher:
        return Colors.blue;
      case UserRole.parent:
        return Colors.green;
      case UserRole.student:
        return Colors.orange;
    }
  }

  /// Whether this role can create study plans
  bool get canCreatePlans {
    switch (this) {
      case UserRole.admin:
      case UserRole.teacher:
      case UserRole.parent:
      case UserRole.student:
        return true;
    }
  }

  /// Whether this role can assign plans to others
  bool get canAssignPlans {
    switch (this) {
      case UserRole.admin:
      case UserRole.teacher:
      case UserRole.parent:
        return true;
      case UserRole.student:
        return false;
    }
  }

  /// Whether this role can create templates
  bool get canCreateTemplates {
    switch (this) {
      case UserRole.admin:
      case UserRole.teacher:
        return true;
      case UserRole.parent:
      case UserRole.student:
        return false;
    }
  }

  /// Whether this role can manage classroom plans
  bool get canManageClassroomPlans {
    switch (this) {
      case UserRole.admin:
      case UserRole.teacher:
        return true;
      case UserRole.parent:
      case UserRole.student:
        return false;
    }
  }

  /// Whether this role can view all plans in organization
  bool get canViewAllPlans {
    switch (this) {
      case UserRole.admin:
        return true;
      case UserRole.teacher:
      case UserRole.parent:
      case UserRole.student:
        return false;
    }
  }

  /// Whether this role can edit other users' plans
  bool get canEditOthersPlans {
    switch (this) {
      case UserRole.admin:
        return true;
      case UserRole.teacher:
      case UserRole.parent:
      case UserRole.student:
        return false; // Depends on specific permissions
    }
  }

  /// Priority level for role hierarchy (lower = higher priority)
  int get hierarchyLevel {
    switch (this) {
      case UserRole.admin:
        return 1;
      case UserRole.teacher:
        return 2;
      case UserRole.parent:
        return 3;
      case UserRole.student:
        return 4;
    }
  }

  /// Whether this role is higher than another role in hierarchy
  bool isHigherThan(UserRole other) {
    return hierarchyLevel < other.hierarchyLevel;
  }

  /// Whether this role is lower than another role in hierarchy
  bool isLowerThan(UserRole other) {
    return hierarchyLevel > other.hierarchyLevel;
  }

  /// Whether this role can supervise another role
  bool canSupervise(UserRole other) {
    switch (this) {
      case UserRole.admin:
        return true; // Admin can supervise everyone
      case UserRole.teacher:
        return other == UserRole.student;
      case UserRole.parent:
        return other == UserRole.student; // Parent can supervise their children
      case UserRole.student:
        return false; // Students cannot supervise others
    }
  }
}

/// Helper methods for UserRole
class UserRoleHelper {
  /// Get all user roles
  static List<UserRole> get allRoles => UserRole.values;

  /// Get roles that can create plans
  static List<UserRole> get planCreatorRoles =>
      UserRole.values.where((role) => role.canCreatePlans).toList();

  /// Get roles that can assign plans
  static List<UserRole> get planAssignerRoles =>
      UserRole.values.where((role) => role.canAssignPlans).toList();

  /// Get roles that can create templates
  static List<UserRole> get templateCreatorRoles =>
      UserRole.values.where((role) => role.canCreateTemplates).toList();

  /// Get roles that can manage classroom plans
  static List<UserRole> get classroomManagerRoles =>
      UserRole.values.where((role) => role.canManageClassroomPlans).toList();

  /// Get roles sorted by hierarchy (highest to lowest)
  static List<UserRole> get rolesByHierarchy {
    final roles = List<UserRole>.from(UserRole.values);
    roles.sort((a, b) => a.hierarchyLevel.compareTo(b.hierarchyLevel));
    return roles;
  }

  /// Get user role from string
  static UserRole? fromString(String value) {
    try {
      return UserRole.values.firstWhere(
        (role) => role.name == value,
      );
    } catch (e) {
      return null;
    }
  }

  /// Check if a role can perform an action on another user's plan
  static bool canPerformAction({
    required UserRole userRole,
    required UserRole planCreatorRole,
    required String action,
    bool isAssignedUser = false,
    bool isCollaborator = false,
  }) {
    // Admin can do everything
    if (userRole == UserRole.admin) return true;

    // User can always edit their own plans
    if (userRole == planCreatorRole) return true;

    // Check specific actions
    switch (action) {
      case 'view':
        return isAssignedUser || isCollaborator || userRole.canSupervise(planCreatorRole);
      case 'edit':
        return isCollaborator || (isAssignedUser && userRole.canSupervise(planCreatorRole));
      case 'delete':
        return userRole.isHigherThan(planCreatorRole);
      case 'assign':
        return userRole.canAssignPlans && userRole.canSupervise(planCreatorRole);
      default:
        return false;
    }
  }
}
