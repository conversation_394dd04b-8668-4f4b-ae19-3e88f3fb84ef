import 'package:flutter/material.dart';

/// Enum representing different types of tasks in a study plan
enum TaskType {
  reading,
  practice,
  assignment,
  review,
  test,
  project,
  research,
}

extension TaskTypeExtension on TaskType {
  /// Display name for the task type
  String get displayName {
    switch (this) {
      case TaskType.reading:
        return 'Reading';
      case TaskType.practice:
        return 'Practice';
      case TaskType.assignment:
        return 'Assignment';
      case TaskType.review:
        return 'Review';
      case TaskType.test:
        return 'Test';
      case TaskType.project:
        return 'Project';
      case TaskType.research:
        return 'Research';
    }
  }

  /// Description for the task type
  String get description {
    switch (this) {
      case TaskType.reading:
        return 'Reading assignments';
      case TaskType.practice:
        return 'Practice exercises';
      case TaskType.assignment:
        return 'Homework/assignments';
      case TaskType.review:
        return 'Review sessions';
      case TaskType.test:
        return 'Tests/quizzes';
      case TaskType.project:
        return 'Project work';
      case TaskType.research:
        return 'Research activities';
    }
  }

  /// Icon for the task type
  IconData get icon {
    switch (this) {
      case TaskType.reading:
        return Icons.menu_book;
      case TaskType.practice:
        return Icons.fitness_center;
      case TaskType.assignment:
        return Icons.assignment;
      case TaskType.review:
        return Icons.rate_review;
      case TaskType.test:
        return Icons.quiz;
      case TaskType.project:
        return Icons.work;
      case TaskType.research:
        return Icons.search;
    }
  }

  /// Color associated with the task type
  Color get color {
    switch (this) {
      case TaskType.reading:
        return Colors.blue;
      case TaskType.practice:
        return Colors.green;
      case TaskType.assignment:
        return Colors.orange;
      case TaskType.review:
        return Colors.purple;
      case TaskType.test:
        return Colors.red;
      case TaskType.project:
        return Colors.brown;
      case TaskType.research:
        return Colors.teal;
    }
  }

  /// Typical estimated duration in minutes
  int get typicalDurationMinutes {
    switch (this) {
      case TaskType.reading:
        return 30;
      case TaskType.practice:
        return 45;
      case TaskType.assignment:
        return 60;
      case TaskType.review:
        return 20;
      case TaskType.test:
        return 90;
      case TaskType.project:
        return 120;
      case TaskType.research:
        return 60;
    }
  }

  /// Whether this task type typically requires submission
  bool get requiresSubmission {
    switch (this) {
      case TaskType.assignment:
      case TaskType.test:
      case TaskType.project:
        return true;
      case TaskType.reading:
      case TaskType.practice:
      case TaskType.review:
      case TaskType.research:
        return false;
    }
  }

  /// Whether this task type supports attachments
  bool get supportsAttachments {
    switch (this) {
      case TaskType.assignment:
      case TaskType.project:
      case TaskType.research:
      case TaskType.reading:
        return true;
      case TaskType.practice:
      case TaskType.review:
      case TaskType.test:
        return false;
    }
  }

  /// Whether this task type can be linked to homework
  bool get canLinkToHomework {
    switch (this) {
      case TaskType.assignment:
      case TaskType.test:
      case TaskType.project:
        return true;
      case TaskType.reading:
      case TaskType.practice:
      case TaskType.review:
      case TaskType.research:
        return false;
    }
  }

  /// Priority level for this task type (1-5, 5 being highest)
  int get defaultPriority {
    switch (this) {
      case TaskType.test:
        return 5;
      case TaskType.assignment:
        return 4;
      case TaskType.project:
        return 4;
      case TaskType.review:
        return 3;
      case TaskType.practice:
        return 3;
      case TaskType.reading:
        return 2;
      case TaskType.research:
        return 2;
    }
  }
}

/// Helper methods for TaskType
class TaskTypeHelper {
  /// Get all task types
  static List<TaskType> get allTypes => TaskType.values;

  /// Get task types that require submission
  static List<TaskType> get submissionRequiredTypes =>
      TaskType.values.where((type) => type.requiresSubmission).toList();

  /// Get task types that support attachments
  static List<TaskType> get attachmentSupportedTypes =>
      TaskType.values.where((type) => type.supportsAttachments).toList();

  /// Get task types that can link to homework
  static List<TaskType> get homeworkLinkableTypes =>
      TaskType.values.where((type) => type.canLinkToHomework).toList();

  /// Get task types sorted by default priority (highest first)
  static List<TaskType> get typesByPriority {
    final types = List<TaskType>.from(TaskType.values);
    types.sort((a, b) => b.defaultPriority.compareTo(a.defaultPriority));
    return types;
  }

  /// Get task type from string
  static TaskType? fromString(String value) {
    try {
      return TaskType.values.firstWhere(
        (type) => type.name == value,
      );
    } catch (e) {
      return null;
    }
  }

  /// Get estimated duration for multiple tasks
  static Duration calculateTotalDuration(List<TaskType> taskTypes) {
    final totalMinutes = taskTypes.fold<int>(
      0,
      (sum, type) => sum + type.typicalDurationMinutes,
    );
    return Duration(minutes: totalMinutes);
  }
}
