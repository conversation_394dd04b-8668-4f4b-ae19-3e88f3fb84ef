import 'package:flutter/material.dart';
import 'package:material_symbols_icons/symbols.dart';

/// Enum representing different types of study plan items (sections, subsections, tasks)
enum StudyItemType {
  // Section types
  chapter,
  subject,
  topic,
  unit,
  module,
  
  // Task types
  reading,
  practice,
  assignment,
  review,
  test,
  project,
  research,
  
  // Custom
  custom,
}

extension StudyItemTypeExtension on StudyItemType {
  /// Display name for the item type
  String get displayName {
    switch (this) {
      case StudyItemType.chapter:
        return 'Chapter';
      case StudyItemType.subject:
        return 'Subject';
      case StudyItemType.topic:
        return 'Topic';
      case StudyItemType.unit:
        return 'Unit';
      case StudyItemType.module:
        return 'Module';
      case StudyItemType.reading:
        return 'Reading';
      case StudyItemType.practice:
        return 'Practice';
      case StudyItemType.assignment:
        return 'Assignment';
      case StudyItemType.review:
        return 'Review';
      case StudyItemType.test:
        return 'Test';
      case StudyItemType.project:
        return 'Project';
      case StudyItemType.research:
        return 'Research';
      case StudyItemType.custom:
        return 'Custom';
    }
  }

  /// Description for the item type
  String get description {
    switch (this) {
      case StudyItemType.chapter:
        return 'A chapter from a textbook or course material';
      case StudyItemType.subject:
        return 'A complete subject or course';
      case StudyItemType.topic:
        return 'A specific topic within a subject';
      case StudyItemType.unit:
        return 'A unit of study covering related concepts';
      case StudyItemType.module:
        return 'A self-contained learning module';
      case StudyItemType.reading:
        return 'Reading assignments and materials';
      case StudyItemType.practice:
        return 'Practice exercises and problems';
      case StudyItemType.assignment:
        return 'Homework and assignments';
      case StudyItemType.review:
        return 'Review sessions and revision';
      case StudyItemType.test:
        return 'Tests, quizzes, and examinations';
      case StudyItemType.project:
        return 'Project work and presentations';
      case StudyItemType.research:
        return 'Research activities and investigations';
      case StudyItemType.custom:
        return 'Custom study item';
    }
  }

  /// Icon for the item type
  IconData get icon {
    switch (this) {
      case StudyItemType.chapter:
        return Symbols.menu_book;
      case StudyItemType.subject:
        return Symbols.book;
      case StudyItemType.topic:
        return Symbols.topic;
      case StudyItemType.unit:
        return Symbols.library_books;
      case StudyItemType.module:
        return Symbols.view_module;
      case StudyItemType.reading:
        return Symbols.chrome_reader_mode;
      case StudyItemType.practice:
        return Symbols.fitness_center;
      case StudyItemType.assignment:
        return Symbols.assignment;
      case StudyItemType.review:
        return Symbols.rate_review;
      case StudyItemType.test:
        return Symbols.quiz;
      case StudyItemType.project:
        return Symbols.work;
      case StudyItemType.research:
        return Symbols.search;
      case StudyItemType.custom:
        return Symbols.folder;
    }
  }

  /// Color associated with the item type
  Color get color {
    switch (this) {
      case StudyItemType.chapter:
        return Colors.orange;
      case StudyItemType.subject:
        return Colors.blue;
      case StudyItemType.topic:
        return Colors.green;
      case StudyItemType.unit:
        return Colors.purple;
      case StudyItemType.module:
        return Colors.indigo;
      case StudyItemType.reading:
        return Colors.blue;
      case StudyItemType.practice:
        return Colors.green;
      case StudyItemType.assignment:
        return Colors.orange;
      case StudyItemType.review:
        return Colors.purple;
      case StudyItemType.test:
        return Colors.red;
      case StudyItemType.project:
        return Colors.brown;
      case StudyItemType.research:
        return Colors.teal;
      case StudyItemType.custom:
        return Colors.grey;
    }
  }

  /// Whether this type can be used as a section
  bool get canBeSection {
    switch (this) {
      case StudyItemType.chapter:
      case StudyItemType.subject:
      case StudyItemType.topic:
      case StudyItemType.unit:
      case StudyItemType.module:
      case StudyItemType.custom:
        return true;
      case StudyItemType.reading:
      case StudyItemType.practice:
      case StudyItemType.assignment:
      case StudyItemType.review:
      case StudyItemType.test:
      case StudyItemType.project:
      case StudyItemType.research:
        return false;
    }
  }

  /// Whether this type can be used as a task
  bool get canBeTask {
    switch (this) {
      case StudyItemType.reading:
      case StudyItemType.practice:
      case StudyItemType.assignment:
      case StudyItemType.review:
      case StudyItemType.test:
      case StudyItemType.project:
      case StudyItemType.research:
      case StudyItemType.custom:
        return true;
      case StudyItemType.chapter:
      case StudyItemType.subject:
      case StudyItemType.topic:
      case StudyItemType.unit:
      case StudyItemType.module:
        return false;
    }
  }

  /// Whether this type supports hierarchical structure
  bool get supportsHierarchy {
    switch (this) {
      case StudyItemType.chapter:
      case StudyItemType.subject:
      case StudyItemType.topic:
      case StudyItemType.unit:
      case StudyItemType.module:
      case StudyItemType.custom:
        return true;
      case StudyItemType.reading:
      case StudyItemType.practice:
      case StudyItemType.assignment:
      case StudyItemType.review:
      case StudyItemType.test:
      case StudyItemType.project:
      case StudyItemType.research:
        return false;
    }
  }

  /// Typical estimated duration in minutes
  int get typicalDurationMinutes {
    switch (this) {
      case StudyItemType.chapter:
        return 180; // 3 hours
      case StudyItemType.subject:
        return 600; // 10 hours
      case StudyItemType.topic:
        return 90; // 1.5 hours
      case StudyItemType.unit:
        return 240; // 4 hours
      case StudyItemType.module:
        return 120; // 2 hours
      case StudyItemType.reading:
        return 30;
      case StudyItemType.practice:
        return 45;
      case StudyItemType.assignment:
        return 60;
      case StudyItemType.review:
        return 20;
      case StudyItemType.test:
        return 90;
      case StudyItemType.project:
        return 120;
      case StudyItemType.research:
        return 60;
      case StudyItemType.custom:
        return 30;
    }
  }

  /// Default priority level (1-5, 5 being highest)
  int get defaultPriority {
    switch (this) {
      case StudyItemType.test:
        return 5;
      case StudyItemType.assignment:
      case StudyItemType.project:
        return 4;
      case StudyItemType.chapter:
      case StudyItemType.review:
        return 3;
      case StudyItemType.subject:
      case StudyItemType.topic:
      case StudyItemType.unit:
      case StudyItemType.module:
      case StudyItemType.practice:
        return 3;
      case StudyItemType.reading:
      case StudyItemType.research:
        return 2;
      case StudyItemType.custom:
        return 3;
    }
  }
}

/// Helper methods for StudyItemType
class StudyItemTypeHelper {
  /// Get all section types
  static List<StudyItemType> get sectionTypes =>
      StudyItemType.values.where((type) => type.canBeSection).toList();

  /// Get all task types
  static List<StudyItemType> get taskTypes =>
      StudyItemType.values.where((type) => type.canBeTask).toList();

  /// Get types that support hierarchy
  static List<StudyItemType> get hierarchicalTypes =>
      StudyItemType.values.where((type) => type.supportsHierarchy).toList();

  /// Get types sorted by default priority (highest first)
  static List<StudyItemType> get typesByPriority {
    final types = List<StudyItemType>.from(StudyItemType.values);
    types.sort((a, b) => b.defaultPriority.compareTo(a.defaultPriority));
    return types;
  }

  /// Get type from string
  static StudyItemType? fromString(String value) {
    try {
      return StudyItemType.values.firstWhere(
        (type) => type.name == value,
      );
    } catch (e) {
      return null;
    }
  }

  /// Get recommended types for a given context
  static List<StudyItemType> getRecommendedTypes({
    bool forSections = false,
    bool forTasks = false,
    bool needsHierarchy = false,
  }) {
    return StudyItemType.values.where((type) {
      if (forSections && !type.canBeSection) return false;
      if (forTasks && !type.canBeTask) return false;
      if (needsHierarchy && !type.supportsHierarchy) return false;
      return true;
    }).toList();
  }
}
