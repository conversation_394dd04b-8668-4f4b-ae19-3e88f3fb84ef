import 'package:flutter/material.dart';

/// Enum representing different types of sections in a study plan
enum SectionType {
  subject,
  topic,
  chapter,
  skill,
  custom,
}

extension SectionTypeExtension on SectionType {
  /// Display name for the section type
  String get displayName {
    switch (this) {
      case SectionType.subject:
        return 'Subject';
      case SectionType.topic:
        return 'Topic';
      case SectionType.chapter:
        return 'Chapter';
      case SectionType.skill:
        return 'Skill';
      case SectionType.custom:
        return 'Custom';
    }
  }

  /// Description for the section type
  String get description {
    switch (this) {
      case SectionType.subject:
        return 'Subject-based sections';
      case SectionType.topic:
        return 'Topic-based organization';
      case SectionType.chapter:
        return 'Chapter-wise structure';
      case SectionType.skill:
        return 'Skill-based grouping';
      case SectionType.custom:
        return 'User-defined sections';
    }
  }

  /// Icon for the section type
  IconData get icon {
    switch (this) {
      case SectionType.subject:
        return Icons.book;
      case SectionType.topic:
        return Icons.topic;
      case SectionType.chapter:
        return Icons.menu_book;
      case SectionType.skill:
        return Icons.psychology;
      case SectionType.custom:
        return Icons.folder;
    }
  }

  /// Color associated with the section type
  Color get color {
    switch (this) {
      case SectionType.subject:
        return Colors.blue;
      case SectionType.topic:
        return Colors.green;
      case SectionType.chapter:
        return Colors.orange;
      case SectionType.skill:
        return Colors.purple;
      case SectionType.custom:
        return Colors.grey;
    }
  }

  /// Whether this section type supports hierarchical structure
  bool get supportsHierarchy {
    switch (this) {
      case SectionType.subject:
      case SectionType.topic:
      case SectionType.chapter:
      case SectionType.custom:
        return true;
      case SectionType.skill:
        return false; // Skills are typically flat
    }
  }

  /// Whether this section type requires a subject association
  bool get requiresSubjectAssociation {
    switch (this) {
      case SectionType.subject:
      case SectionType.topic:
      case SectionType.chapter:
        return true;
      case SectionType.skill:
      case SectionType.custom:
        return false;
    }
  }

  /// Maximum recommended depth for this section type
  int get maxRecommendedDepth {
    switch (this) {
      case SectionType.subject:
        return 4; // Subject > Topic > Chapter > Sub-chapter
      case SectionType.topic:
        return 3; // Topic > Sub-topic > Detail
      case SectionType.chapter:
        return 3; // Chapter > Section > Sub-section
      case SectionType.skill:
        return 2; // Skill > Sub-skill
      case SectionType.custom:
        return 5; // Flexible depth
    }
  }

  /// Typical ordering priority (lower number = higher priority)
  int get defaultOrder {
    switch (this) {
      case SectionType.subject:
        return 1;
      case SectionType.topic:
        return 2;
      case SectionType.chapter:
        return 3;
      case SectionType.skill:
        return 4;
      case SectionType.custom:
        return 5;
    }
  }

  /// Whether this section type supports progress tracking
  bool get supportsProgressTracking {
    return true; // All section types support progress tracking
  }

  /// Whether this section type can contain tasks directly
  bool get canContainTasks {
    return true; // All section types can contain tasks
  }

  /// Whether this section type can contain milestones
  bool get canContainMilestones {
    switch (this) {
      case SectionType.subject:
      case SectionType.topic:
      case SectionType.chapter:
      case SectionType.custom:
        return true;
      case SectionType.skill:
        return false; // Skills typically don't have milestones
    }
  }
}

/// Helper methods for SectionType
class SectionTypeHelper {
  /// Get all section types
  static List<SectionType> get allTypes => SectionType.values;

  /// Get section types that support hierarchy
  static List<SectionType> get hierarchicalTypes =>
      SectionType.values.where((type) => type.supportsHierarchy).toList();

  /// Get section types that require subject association
  static List<SectionType> get subjectAssociatedTypes =>
      SectionType.values.where((type) => type.requiresSubjectAssociation).toList();

  /// Get section types that can contain milestones
  static List<SectionType> get milestoneCapableTypes =>
      SectionType.values.where((type) => type.canContainMilestones).toList();

  /// Get section types sorted by default order
  static List<SectionType> get typesByOrder {
    final types = List<SectionType>.from(SectionType.values);
    types.sort((a, b) => a.defaultOrder.compareTo(b.defaultOrder));
    return types;
  }

  /// Get section type from string
  static SectionType? fromString(String value) {
    try {
      return SectionType.values.firstWhere(
        (type) => type.name == value,
      );
    } catch (e) {
      return null;
    }
  }

  /// Validate section depth for given type
  static bool validateDepth(SectionType type, int depth) {
    return depth <= type.maxRecommendedDepth;
  }

  /// Get recommended section types for a given context
  static List<SectionType> getRecommendedTypes({
    bool needsHierarchy = false,
    bool needsSubjectAssociation = false,
    bool needsMilestones = false,
  }) {
    return SectionType.values.where((type) {
      if (needsHierarchy && !type.supportsHierarchy) return false;
      if (needsSubjectAssociation && !type.requiresSubjectAssociation) return false;
      if (needsMilestones && !type.canContainMilestones) return false;
      return true;
    }).toList();
  }
}
