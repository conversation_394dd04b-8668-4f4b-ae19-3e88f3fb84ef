import 'package:flutter/material.dart';

/// Enum representing study plan priority levels
enum StudyPlanPriority {
  low,
  normal,
  high,
  urgent,
}

extension StudyPlanPriorityExtension on StudyPlanPriority {
  /// Display name for the study plan priority
  String get displayName {
    switch (this) {
      case StudyPlanPriority.low:
        return 'Low';
      case StudyPlanPriority.normal:
        return 'Normal';
      case StudyPlanPriority.high:
        return 'High';
      case StudyPlanPriority.urgent:
        return 'Urgent';
    }
  }

  /// Description for the study plan priority
  String get description {
    switch (this) {
      case StudyPlanPriority.low:
        return 'Low priority - can be done when time permits';
      case StudyPlanPriority.normal:
        return 'Normal priority - standard importance';
      case StudyPlanPriority.high:
        return 'High priority - important and should be prioritized';
      case StudyPlanPriority.urgent:
        return 'Urgent priority - requires immediate attention';
    }
  }

  /// Icon for the study plan priority
  IconData get icon {
    switch (this) {
      case StudyPlanPriority.low:
        return Icons.keyboard_arrow_down;
      case StudyPlanPriority.normal:
        return Icons.remove;
      case StudyPlanPriority.high:
        return Icons.keyboard_arrow_up;
      case StudyPlanPriority.urgent:
        return Icons.priority_high;
    }
  }

  /// Color associated with the study plan priority
  Color get color {
    switch (this) {
      case StudyPlanPriority.low:
        return Colors.green;
      case StudyPlanPriority.normal:
        return Colors.blue;
      case StudyPlanPriority.high:
        return Colors.orange;
      case StudyPlanPriority.urgent:
        return Colors.red;
    }
  }

  /// Numeric value for sorting (higher = more urgent)
  int get value {
    switch (this) {
      case StudyPlanPriority.low:
        return 1;
      case StudyPlanPriority.normal:
        return 2;
      case StudyPlanPriority.high:
        return 3;
      case StudyPlanPriority.urgent:
        return 4;
    }
  }

  /// Weight for priority calculations (higher = more important)
  double get weight {
    switch (this) {
      case StudyPlanPriority.low:
        return 0.5;
      case StudyPlanPriority.normal:
        return 1.0;
      case StudyPlanPriority.high:
        return 1.5;
      case StudyPlanPriority.urgent:
        return 2.0;
    }
  }

  /// Whether this priority requires immediate attention
  bool get requiresImmediateAttention {
    return this == StudyPlanPriority.urgent;
  }

  /// Whether this priority is above normal
  bool get isAboveNormal {
    return value > StudyPlanPriority.normal.value;
  }

  /// Whether this priority is below normal
  bool get isBelowNormal {
    return value < StudyPlanPriority.normal.value;
  }

  /// Whether this priority is higher than another priority
  bool isHigherThan(StudyPlanPriority other) {
    return value > other.value;
  }

  /// Whether this priority is lower than another priority
  bool isLowerThan(StudyPlanPriority other) {
    return value < other.value;
  }

  /// Get recommended notification frequency based on priority
  Duration get recommendedNotificationFrequency {
    switch (this) {
      case StudyPlanPriority.low:
        return const Duration(days: 7); // Weekly
      case StudyPlanPriority.normal:
        return const Duration(days: 3); // Every 3 days
      case StudyPlanPriority.high:
        return const Duration(days: 1); // Daily
      case StudyPlanPriority.urgent:
        return const Duration(hours: 6); // Every 6 hours
    }
  }

  /// Get recommended deadline buffer based on priority
  Duration get recommendedDeadlineBuffer {
    switch (this) {
      case StudyPlanPriority.low:
        return const Duration(days: 7); // 1 week buffer
      case StudyPlanPriority.normal:
        return const Duration(days: 3); // 3 days buffer
      case StudyPlanPriority.high:
        return const Duration(days: 1); // 1 day buffer
      case StudyPlanPriority.urgent:
        return const Duration(hours: 6); // 6 hours buffer
    }
  }
}

/// Helper methods for StudyPlanPriority
class StudyPlanPriorityHelper {
  /// Get all study plan priorities
  static List<StudyPlanPriority> get allPriorities => StudyPlanPriority.values;

  /// Get priorities sorted by value (low to high)
  static List<StudyPlanPriority> get prioritiesByValue {
    final priorities = List<StudyPlanPriority>.from(StudyPlanPriority.values);
    priorities.sort((a, b) => a.value.compareTo(b.value));
    return priorities;
  }

  /// Get priorities sorted by urgency (urgent to low)
  static List<StudyPlanPriority> get prioritiesByUrgency {
    final priorities = List<StudyPlanPriority>.from(StudyPlanPriority.values);
    priorities.sort((a, b) => b.value.compareTo(a.value));
    return priorities;
  }

  /// Get high priority levels (high and urgent)
  static List<StudyPlanPriority> get highPriorities => [
        StudyPlanPriority.high,
        StudyPlanPriority.urgent,
      ];

  /// Get low priority levels (low and normal)
  static List<StudyPlanPriority> get lowPriorities => [
        StudyPlanPriority.low,
        StudyPlanPriority.normal,
      ];

  /// Get priorities that require immediate attention
  static List<StudyPlanPriority> get urgentPriorities =>
      StudyPlanPriority.values
          .where((priority) => priority.requiresImmediateAttention)
          .toList();

  /// Get study plan priority from string
  static StudyPlanPriority? fromString(String value) {
    try {
      return StudyPlanPriority.values.firstWhere(
        (priority) => priority.name == value,
      );
    } catch (e) {
      return null;
    }
  }

  /// Get study plan priority from numeric value
  static StudyPlanPriority? fromValue(int value) {
    try {
      return StudyPlanPriority.values.firstWhere(
        (priority) => priority.value == value,
      );
    } catch (e) {
      return null;
    }
  }

  /// Calculate average priority from a list of priorities
  static StudyPlanPriority calculateAveragePriority(List<StudyPlanPriority> priorities) {
    if (priorities.isEmpty) return StudyPlanPriority.normal;

    final totalValue = priorities.fold<int>(0, (sum, priority) => sum + priority.value);
    final averageValue = (totalValue / priorities.length).round();

    return fromValue(averageValue.clamp(1, 4)) ?? StudyPlanPriority.normal;
  }

  /// Get recommended priority based on deadline urgency
  static StudyPlanPriority getRecommendedPriorityForDeadline(DateTime deadline) {
    final now = DateTime.now();
    final timeUntilDeadline = deadline.difference(now);

    if (timeUntilDeadline.inDays <= 1) {
      return StudyPlanPriority.urgent;
    } else if (timeUntilDeadline.inDays <= 3) {
      return StudyPlanPriority.high;
    } else if (timeUntilDeadline.inDays <= 7) {
      return StudyPlanPriority.normal;
    } else {
      return StudyPlanPriority.low;
    }
  }

  /// Filter priorities by minimum level
  static List<StudyPlanPriority> filterByMinimumLevel(StudyPlanPriority minimumLevel) {
    return StudyPlanPriority.values
        .where((priority) => priority.value >= minimumLevel.value)
        .toList();
  }

  /// Filter priorities by maximum level
  static List<StudyPlanPriority> filterByMaximumLevel(StudyPlanPriority maximumLevel) {
    return StudyPlanPriority.values
        .where((priority) => priority.value <= maximumLevel.value)
        .toList();
  }
}
