import 'package:flutter/material.dart';

/// Enum representing different status states of a study plan task
enum TaskStatus {
  notStarted,
  inProgress,
  completed,
  cancelled,
  overdue,
}

extension TaskStatusExtension on TaskStatus {
  /// Display name for the task status
  String get displayName {
    switch (this) {
      case TaskStatus.notStarted:
        return 'Not Started';
      case TaskStatus.inProgress:
        return 'In Progress';
      case TaskStatus.completed:
        return 'Completed';
      case TaskStatus.cancelled:
        return 'Cancelled';
      case TaskStatus.overdue:
        return 'Overdue';
    }
  }

  /// Description for the task status
  String get description {
    switch (this) {
      case TaskStatus.notStarted:
        return 'Task has not been started yet';
      case TaskStatus.inProgress:
        return 'Task is currently being worked on';
      case TaskStatus.completed:
        return 'Task has been successfully completed';
      case TaskStatus.cancelled:
        return 'Task has been cancelled and will not be completed';
      case TaskStatus.overdue:
        return 'Task is past its due date and incomplete';
    }
  }

  /// Icon for the task status
  IconData get icon {
    switch (this) {
      case TaskStatus.notStarted:
        return Icons.radio_button_unchecked;
      case TaskStatus.inProgress:
        return Icons.hourglass_empty;
      case TaskStatus.completed:
        return Icons.check_circle;
      case TaskStatus.cancelled:
        return Icons.cancel;
      case TaskStatus.overdue:
        return Icons.warning;
    }
  }

  /// Color associated with the task status
  Color get color {
    switch (this) {
      case TaskStatus.notStarted:
        return Colors.grey;
      case TaskStatus.inProgress:
        return Colors.blue;
      case TaskStatus.completed:
        return Colors.green;
      case TaskStatus.cancelled:
        return Colors.red;
      case TaskStatus.overdue:
        return Colors.orange;
    }
  }

  /// Whether the task can be edited in this status
  bool get canBeEdited {
    switch (this) {
      case TaskStatus.notStarted:
      case TaskStatus.inProgress:
      case TaskStatus.overdue:
        return true;
      case TaskStatus.completed:
      case TaskStatus.cancelled:
        return false;
    }
  }

  /// Whether the task can be started from this status
  bool get canBeStarted {
    switch (this) {
      case TaskStatus.notStarted:
      case TaskStatus.overdue:
        return true;
      case TaskStatus.inProgress:
      case TaskStatus.completed:
      case TaskStatus.cancelled:
        return false;
    }
  }

  /// Whether the task can be completed from this status
  bool get canBeCompleted {
    switch (this) {
      case TaskStatus.inProgress:
      case TaskStatus.overdue:
        return true;
      case TaskStatus.notStarted:
      case TaskStatus.completed:
      case TaskStatus.cancelled:
        return false;
    }
  }

  /// Whether the task can be cancelled from this status
  bool get canBeCancelled {
    switch (this) {
      case TaskStatus.notStarted:
      case TaskStatus.inProgress:
      case TaskStatus.overdue:
        return true;
      case TaskStatus.completed:
      case TaskStatus.cancelled:
        return false;
    }
  }

  /// Whether the task can be reset from this status
  bool get canBeReset {
    switch (this) {
      case TaskStatus.completed:
      case TaskStatus.cancelled:
        return true;
      case TaskStatus.notStarted:
      case TaskStatus.inProgress:
      case TaskStatus.overdue:
        return false;
    }
  }

  /// Whether the task is in a final state
  bool get isFinalState {
    switch (this) {
      case TaskStatus.completed:
      case TaskStatus.cancelled:
        return true;
      case TaskStatus.notStarted:
      case TaskStatus.inProgress:
      case TaskStatus.overdue:
        return false;
    }
  }

  /// Whether the task is currently active (being worked on)
  bool get isActive {
    switch (this) {
      case TaskStatus.inProgress:
      case TaskStatus.overdue:
        return true;
      case TaskStatus.notStarted:
      case TaskStatus.completed:
      case TaskStatus.cancelled:
        return false;
    }
  }

  /// Whether the task is pending (not yet started or in progress)
  bool get isPending {
    switch (this) {
      case TaskStatus.notStarted:
      case TaskStatus.inProgress:
      case TaskStatus.overdue:
        return true;
      case TaskStatus.completed:
      case TaskStatus.cancelled:
        return false;
    }
  }

  /// Whether the task requires attention
  bool get requiresAttention {
    switch (this) {
      case TaskStatus.overdue:
        return true;
      case TaskStatus.notStarted:
      case TaskStatus.inProgress:
      case TaskStatus.completed:
      case TaskStatus.cancelled:
        return false;
    }
  }

  /// Whether the task contributes to progress calculations
  bool get contributesToProgress {
    switch (this) {
      case TaskStatus.notStarted:
      case TaskStatus.inProgress:
      case TaskStatus.completed:
      case TaskStatus.overdue:
        return true;
      case TaskStatus.cancelled:
        return false; // Cancelled tasks don't count toward progress
    }
  }

  /// Progress percentage for this status
  double get progressPercentage {
    switch (this) {
      case TaskStatus.notStarted:
        return 0.0;
      case TaskStatus.inProgress:
        return 50.0; // Assume 50% when in progress
      case TaskStatus.completed:
        return 100.0;
      case TaskStatus.cancelled:
        return 0.0; // Cancelled tasks have no progress
      case TaskStatus.overdue:
        return 25.0; // Assume some progress was made
    }
  }

  /// Priority level for sorting (higher = more urgent)
  int get sortPriority {
    switch (this) {
      case TaskStatus.overdue:
        return 5; // Highest priority
      case TaskStatus.inProgress:
        return 4;
      case TaskStatus.notStarted:
        return 3;
      case TaskStatus.completed:
        return 2;
      case TaskStatus.cancelled:
        return 1; // Lowest priority
    }
  }

  /// Whether this status should show notifications
  bool get shouldNotify {
    switch (this) {
      case TaskStatus.overdue:
        return true;
      case TaskStatus.notStarted:
      case TaskStatus.inProgress:
      case TaskStatus.completed:
      case TaskStatus.cancelled:
        return false;
    }
  }
}

/// Helper methods for TaskStatus
class TaskStatusHelper {
  /// Get all task statuses
  static List<TaskStatus> get allStatuses => TaskStatus.values;

  /// Get active statuses (not started, in progress, overdue)
  static List<TaskStatus> get activeStatuses => [
        TaskStatus.notStarted,
        TaskStatus.inProgress,
        TaskStatus.overdue,
      ];

  /// Get completed statuses (completed, cancelled)
  static List<TaskStatus> get completedStatuses => [
        TaskStatus.completed,
        TaskStatus.cancelled,
      ];

  /// Get editable statuses
  static List<TaskStatus> get editableStatuses =>
      TaskStatus.values.where((status) => status.canBeEdited).toList();

  /// Get final statuses
  static List<TaskStatus> get finalStatuses =>
      TaskStatus.values.where((status) => status.isFinalState).toList();

  /// Get statuses that require attention
  static List<TaskStatus> get attentionRequiredStatuses =>
      TaskStatus.values.where((status) => status.requiresAttention).toList();

  /// Get statuses that contribute to progress
  static List<TaskStatus> get progressContributingStatuses =>
      TaskStatus.values.where((status) => status.contributesToProgress).toList();

  /// Get statuses sorted by priority (most urgent first)
  static List<TaskStatus> get statusesByPriority {
    final statuses = List<TaskStatus>.from(TaskStatus.values);
    statuses.sort((a, b) => b.sortPriority.compareTo(a.sortPriority));
    return statuses;
  }

  /// Get task status from string
  static TaskStatus? fromString(String value) {
    try {
      return TaskStatus.values.firstWhere(
        (status) => status.name == value,
      );
    } catch (e) {
      return null;
    }
  }

  /// Get next possible statuses from current status
  static List<TaskStatus> getNextPossibleStatuses(TaskStatus currentStatus) {
    final possibleStatuses = <TaskStatus>[];
    
    for (final status in TaskStatus.values) {
      if (status == currentStatus) continue;
      
      switch (status) {
        case TaskStatus.inProgress:
          if (currentStatus.canBeStarted) possibleStatuses.add(status);
          break;
        case TaskStatus.completed:
          if (currentStatus.canBeCompleted) possibleStatuses.add(status);
          break;
        case TaskStatus.cancelled:
          if (currentStatus.canBeCancelled) possibleStatuses.add(status);
          break;
        case TaskStatus.notStarted:
          if (currentStatus.canBeReset) possibleStatuses.add(status);
          break;
        case TaskStatus.overdue:
          // Overdue is typically set automatically, not manually
          break;
      }
    }
    
    return possibleStatuses;
  }

  /// Calculate overall progress from a list of task statuses
  static double calculateOverallProgress(List<TaskStatus> statuses) {
    if (statuses.isEmpty) return 0.0;

    final contributingStatuses = statuses.where((status) => status.contributesToProgress).toList();
    if (contributingStatuses.isEmpty) return 0.0;

    final totalProgress = contributingStatuses.fold<double>(
      0.0,
      (sum, status) => sum + status.progressPercentage,
    );

    return totalProgress / contributingStatuses.length;
  }

  /// Get status distribution statistics
  static Map<TaskStatus, int> getStatusDistribution(List<TaskStatus> statuses) {
    final distribution = <TaskStatus, int>{};
    
    for (final status in TaskStatus.values) {
      distribution[status] = statuses.where((s) => s == status).length;
    }
    
    return distribution;
  }

  /// Check if a task should be marked as overdue
  static bool shouldBeOverdue({
    required TaskStatus currentStatus,
    required DateTime? dueDate,
    DateTime? now,
  }) {
    now ??= DateTime.now();
    
    if (dueDate == null) return false;
    if (currentStatus.isFinalState) return false;
    if (currentStatus == TaskStatus.overdue) return true;
    
    return now.isAfter(dueDate) && currentStatus.isPending;
  }

  /// Get recommended status based on progress percentage
  static TaskStatus getRecommendedStatusFromProgress(double progressPercentage) {
    if (progressPercentage >= 100.0) {
      return TaskStatus.completed;
    } else if (progressPercentage > 0.0) {
      return TaskStatus.inProgress;
    } else {
      return TaskStatus.notStarted;
    }
  }

  /// Filter statuses by criteria
  static List<TaskStatus> filterStatuses({
    bool? isActive,
    bool? isFinal,
    bool? canBeEdited,
    bool? requiresAttention,
  }) {
    return TaskStatus.values.where((status) {
      if (isActive != null && status.isActive != isActive) return false;
      if (isFinal != null && status.isFinalState != isFinal) return false;
      if (canBeEdited != null && status.canBeEdited != canBeEdited) return false;
      if (requiresAttention != null && status.requiresAttention != requiresAttention) return false;
      return true;
    }).toList();
  }
}
