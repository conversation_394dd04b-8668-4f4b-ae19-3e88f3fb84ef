import '../enums/study_plan_enums.dart';
import 'date_range.dart';
import 'flexible_schedule.dart';
import 'task_resource.dart';
import 'task_submission.dart';

/// Model representing a task within a study plan section
class StudyPlanTask {
  final String id;
  final String title;
  final String? description;
  final TaskType type;
  final TaskPriority priority;
  final TaskStatus status;

  // Assignment
  final String sectionId;
  final int order;
  final List<String> assignedUserIds;

  // Timeline (Enhanced for flexible scheduling)
  final DateTime? dueDate;
  final DateTime? startDate;
  final DateRange? dateRange;
  final Duration? estimatedDuration;
  final DateTime? completedAt;
  final bool hasFlexibleDeadline;
  final FlexibleSchedule? individualSchedule;

  // Resources
  final List<TaskResource> resources;
  final List<String> attachmentIds;
  final String? homeworkId;

  // Progress
  final double progressPercentage;
  final List<TaskSubmission> submissions;
  final String? feedback;
  final Map<String, dynamic>? metadata;

  const StudyPlanTask({
    required this.id,
    required this.title,
    this.description,
    required this.type,
    this.priority = TaskPriority.normal,
    this.status = TaskStatus.notStarted,
    required this.sectionId,
    this.order = 0,
    this.assignedUserIds = const [],
    this.dueDate,
    this.startDate,
    this.dateRange,
    this.estimatedDuration,
    this.completedAt,
    this.hasFlexibleDeadline = false,
    this.individualSchedule,
    this.resources = const [],
    this.attachmentIds = const [],
    this.homeworkId,
    this.progressPercentage = 0.0,
    this.submissions = const [],
    this.feedback,
    this.metadata,
  });

  /// Create StudyPlanTask from JSON
  factory StudyPlanTask.fromJson(Map<String, dynamic> json) {
    return StudyPlanTask(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String?,
      type: TaskType.values.firstWhere((e) => e.name == json['type']),
      priority: TaskPriority.values.firstWhere(
        (e) => e.name == json['priority'],
        orElse: () => TaskPriority.normal,
      ),
      status: TaskStatus.values.firstWhere(
        (e) => e.name == json['status'],
        orElse: () => TaskStatus.notStarted,
      ),
      sectionId: json['sectionId'] as String,
      order: json['order'] as int? ?? 0,
      assignedUserIds: List<String>.from(
        json['assignedUserIds'] as List? ?? [],
      ),
      dueDate: json['dueDate'] != null
          ? DateTime.parse(json['dueDate'] as String)
          : null,
      startDate: json['startDate'] != null
          ? DateTime.parse(json['startDate'] as String)
          : null,
      dateRange: json['dateRange'] != null
          ? DateRange.fromJson(json['dateRange'] as Map<String, dynamic>)
          : null,
      estimatedDuration: json['estimatedDuration'] != null
          ? Duration(minutes: json['estimatedDuration'] as int)
          : null,
      completedAt: json['completedAt'] != null
          ? DateTime.parse(json['completedAt'] as String)
          : null,
      hasFlexibleDeadline: json['hasFlexibleDeadline'] as bool? ?? false,
      individualSchedule: json['individualSchedule'] != null
          ? FlexibleSchedule.fromJson(
              json['individualSchedule'] as Map<String, dynamic>,
            )
          : null,
      resources:
          (json['resources'] as List?)
              ?.map((e) => TaskResource.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      attachmentIds: List<String>.from(json['attachmentIds'] as List? ?? []),
      homeworkId: json['homeworkId'] as String?,
      progressPercentage:
          (json['progressPercentage'] as num?)?.toDouble() ?? 0.0,
      submissions:
          (json['submissions'] as List?)
              ?.map((e) => TaskSubmission.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      feedback: json['feedback'] as String?,
      metadata: json['metadata'] as Map<String, dynamic>?,
    );
  }

  /// Convert StudyPlanTask to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      if (description != null) 'description': description,
      'type': type.name,
      'priority': priority.name,
      'status': status.name,
      'sectionId': sectionId,
      'order': order,
      'assignedUserIds': assignedUserIds,
      if (dueDate != null) 'dueDate': dueDate!.toIso8601String(),
      if (startDate != null) 'startDate': startDate!.toIso8601String(),
      if (dateRange != null) 'dateRange': dateRange!.toJson(),
      if (estimatedDuration != null)
        'estimatedDuration': estimatedDuration!.inMinutes,
      if (completedAt != null) 'completedAt': completedAt!.toIso8601String(),
      'hasFlexibleDeadline': hasFlexibleDeadline,
      if (individualSchedule != null)
        'individualSchedule': individualSchedule!.toJson(),
      'resources': resources.map((e) => e.toJson()).toList(),
      'attachmentIds': attachmentIds,
      if (homeworkId != null) 'homeworkId': homeworkId,
      'progressPercentage': progressPercentage,
      'submissions': submissions.map((e) => e.toJson()).toList(),
      if (feedback != null) 'feedback': feedback,
      if (metadata != null) 'metadata': metadata,
    };
  }

  /// Create a copy with updated fields
  StudyPlanTask copyWith({
    String? id,
    String? title,
    String? description,
    TaskType? type,
    TaskPriority? priority,
    TaskStatus? status,
    String? sectionId,
    int? order,
    List<String>? assignedUserIds,
    DateTime? dueDate,
    DateTime? startDate,
    DateRange? dateRange,
    Duration? estimatedDuration,
    DateTime? completedAt,
    bool? hasFlexibleDeadline,
    FlexibleSchedule? individualSchedule,
    List<TaskResource>? resources,
    List<String>? attachmentIds,
    String? homeworkId,
    double? progressPercentage,
    List<TaskSubmission>? submissions,
    String? feedback,
    Map<String, dynamic>? metadata,
  }) {
    return StudyPlanTask(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      type: type ?? this.type,
      priority: priority ?? this.priority,
      status: status ?? this.status,
      sectionId: sectionId ?? this.sectionId,
      order: order ?? this.order,
      assignedUserIds: assignedUserIds ?? this.assignedUserIds,
      dueDate: dueDate ?? this.dueDate,
      startDate: startDate ?? this.startDate,
      dateRange: dateRange ?? this.dateRange,
      estimatedDuration: estimatedDuration ?? this.estimatedDuration,
      completedAt: completedAt ?? this.completedAt,
      hasFlexibleDeadline: hasFlexibleDeadline ?? this.hasFlexibleDeadline,
      individualSchedule: individualSchedule ?? this.individualSchedule,
      resources: resources ?? this.resources,
      attachmentIds: attachmentIds ?? this.attachmentIds,
      homeworkId: homeworkId ?? this.homeworkId,
      progressPercentage: progressPercentage ?? this.progressPercentage,
      submissions: submissions ?? this.submissions,
      feedback: feedback ?? this.feedback,
      metadata: metadata ?? this.metadata,
    );
  }

  /// Check if task is overdue
  bool get isOverdue {
    if (dueDate == null || status == TaskStatus.completed) return false;
    return DateTime.now().isAfter(dueDate!);
  }

  /// Check if task is completed
  bool get isCompleted => status == TaskStatus.completed;

  /// Check if task is in progress
  bool get isInProgress => status == TaskStatus.inProgress;

  /// Get effective due date (from dueDate or dateRange)
  DateTime? get effectiveDueDate {
    if (dueDate != null) return dueDate;
    if (dateRange != null) return dateRange!.endDate;
    return null;
  }

  /// Get effective start date (from startDate or dateRange)
  DateTime? get effectiveStartDate {
    if (startDate != null) return startDate;
    if (dateRange != null) return dateRange!.startDate;
    return null;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is StudyPlanTask &&
        other.id == id &&
        other.title == title &&
        other.description == description &&
        other.type == type &&
        other.priority == priority &&
        other.status == status &&
        other.sectionId == sectionId &&
        other.order == order &&
        _listEquals(other.assignedUserIds, assignedUserIds) &&
        other.dueDate == dueDate &&
        other.startDate == startDate &&
        other.dateRange == dateRange &&
        other.estimatedDuration == estimatedDuration &&
        other.completedAt == completedAt &&
        other.hasFlexibleDeadline == hasFlexibleDeadline &&
        _listEquals(other.resources, resources) &&
        _listEquals(other.attachmentIds, attachmentIds) &&
        other.homeworkId == homeworkId &&
        other.progressPercentage == progressPercentage &&
        _listEquals(other.submissions, submissions) &&
        other.feedback == feedback &&
        _mapEquals(other.metadata, metadata);
  }

  @override
  int get hashCode {
    return Object.hash(
      id,
      title,
      description,
      type,
      priority,
      status,
      sectionId,
      order,
      Object.hashAll(assignedUserIds),
      dueDate,
      startDate,
      dateRange,
      estimatedDuration,
      completedAt,
      hasFlexibleDeadline,
      Object.hashAll(resources),
      Object.hashAll(attachmentIds),
      Object.hash(
        homeworkId,
        progressPercentage,
        Object.hashAll(submissions),
        feedback,
      ),
    );
  }

  bool _listEquals<T>(List<T>? a, List<T>? b) {
    if (a == null) return b == null;
    if (b == null || a.length != b.length) return false;
    for (int index = 0; index < a.length; index += 1) {
      if (a[index] != b[index]) return false;
    }
    return true;
  }

  bool _mapEquals<K, V>(Map<K, V>? a, Map<K, V>? b) {
    if (a == null) return b == null;
    if (b == null || a.length != b.length) return false;
    for (final key in a.keys) {
      if (!b.containsKey(key) || a[key] != b[key]) return false;
    }
    return true;
  }

  @override
  String toString() {
    return 'StudyPlanTask(id: $id, title: $title, type: ${type.displayName}, status: ${status.displayName})';
  }
}
