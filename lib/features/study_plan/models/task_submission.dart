/// Model representing a submission for a study plan task
class TaskSubmission {
  final String id;
  final String taskId;
  final String submittedBy;
  final String? submitterName;
  final DateTime submittedAt;
  final DateTime? updatedAt;
  final TaskSubmissionStatus status;
  final String? content; // Text content or notes
  final List<String> attachmentIds; // References to Digital Library files
  final double? grade;
  final double? maxGrade;
  final String? feedback;
  final String? feedbackBy;
  final DateTime? feedbackAt;
  final Duration? timeSpent;
  final Map<String, dynamic>? metadata;

  const TaskSubmission({
    required this.id,
    required this.taskId,
    required this.submittedBy,
    this.submitterName,
    required this.submittedAt,
    this.updatedAt,
    this.status = TaskSubmissionStatus.submitted,
    this.content,
    this.attachmentIds = const [],
    this.grade,
    this.maxGrade,
    this.feedback,
    this.feedbackBy,
    this.feedbackAt,
    this.timeSpent,
    this.metadata,
  });

  /// Create TaskSubmission from JSON
  factory TaskSubmission.fromJson(Map<String, dynamic> json) {
    return TaskSubmission(
      id: json['id'] as String,
      taskId: json['taskId'] as String,
      submittedBy: json['submittedBy'] as String,
      submitterName: json['submitterName'] as String?,
      submittedAt: DateTime.parse(json['submittedAt'] as String),
      updatedAt: json['updatedAt'] != null
          ? DateTime.parse(json['updatedAt'] as String)
          : null,
      status: TaskSubmissionStatus.values.firstWhere(
        (e) => e.name == json['status'],
        orElse: () => TaskSubmissionStatus.submitted,
      ),
      content: json['content'] as String?,
      attachmentIds: List<String>.from(json['attachmentIds'] as List? ?? []),
      grade: (json['grade'] as num?)?.toDouble(),
      maxGrade: (json['maxGrade'] as num?)?.toDouble(),
      feedback: json['feedback'] as String?,
      feedbackBy: json['feedbackBy'] as String?,
      feedbackAt: json['feedbackAt'] != null
          ? DateTime.parse(json['feedbackAt'] as String)
          : null,
      timeSpent: json['timeSpent'] != null
          ? Duration(minutes: json['timeSpent'] as int)
          : null,
      metadata: json['metadata'] as Map<String, dynamic>?,
    );
  }

  /// Convert TaskSubmission to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'taskId': taskId,
      'submittedBy': submittedBy,
      if (submitterName != null) 'submitterName': submitterName,
      'submittedAt': submittedAt.toIso8601String(),
      if (updatedAt != null) 'updatedAt': updatedAt!.toIso8601String(),
      'status': status.name,
      if (content != null) 'content': content,
      'attachmentIds': attachmentIds,
      if (grade != null) 'grade': grade,
      if (maxGrade != null) 'maxGrade': maxGrade,
      if (feedback != null) 'feedback': feedback,
      if (feedbackBy != null) 'feedbackBy': feedbackBy,
      if (feedbackAt != null) 'feedbackAt': feedbackAt!.toIso8601String(),
      if (timeSpent != null) 'timeSpent': timeSpent!.inMinutes,
      if (metadata != null) 'metadata': metadata,
    };
  }

  /// Create a copy with updated fields
  TaskSubmission copyWith({
    String? id,
    String? taskId,
    String? submittedBy,
    String? submitterName,
    DateTime? submittedAt,
    DateTime? updatedAt,
    TaskSubmissionStatus? status,
    String? content,
    List<String>? attachmentIds,
    double? grade,
    double? maxGrade,
    String? feedback,
    String? feedbackBy,
    DateTime? feedbackAt,
    Duration? timeSpent,
    Map<String, dynamic>? metadata,
  }) {
    return TaskSubmission(
      id: id ?? this.id,
      taskId: taskId ?? this.taskId,
      submittedBy: submittedBy ?? this.submittedBy,
      submitterName: submitterName ?? this.submitterName,
      submittedAt: submittedAt ?? this.submittedAt,
      updatedAt: updatedAt ?? this.updatedAt,
      status: status ?? this.status,
      content: content ?? this.content,
      attachmentIds: attachmentIds ?? this.attachmentIds,
      grade: grade ?? this.grade,
      maxGrade: maxGrade ?? this.maxGrade,
      feedback: feedback ?? this.feedback,
      feedbackBy: feedbackBy ?? this.feedbackBy,
      feedbackAt: feedbackAt ?? this.feedbackAt,
      timeSpent: timeSpent ?? this.timeSpent,
      metadata: metadata ?? this.metadata,
    );
  }

  /// Check if submission has been graded
  bool get isGraded => grade != null;

  /// Check if submission has feedback
  bool get hasFeedback => feedback != null && feedback!.isNotEmpty;

  /// Check if submission has attachments
  bool get hasAttachments => attachmentIds.isNotEmpty;

  /// Check if submission has content
  bool get hasContent => content != null && content!.isNotEmpty;

  /// Check if submission has been updated since initial submission
  bool get hasBeenUpdated => updatedAt != null && updatedAt!.isAfter(submittedAt);

  /// Get grade percentage (if both grade and maxGrade are available)
  double? get gradePercentage {
    if (grade == null || maxGrade == null || maxGrade == 0) return null;
    return (grade! / maxGrade!) * 100;
  }

  /// Get formatted grade string
  String get formattedGrade {
    if (!isGraded) return 'Not graded';
    if (maxGrade != null) {
      return '${grade!.toStringAsFixed(1)}/${maxGrade!.toStringAsFixed(1)}';
    }
    return grade!.toStringAsFixed(1);
  }

  /// Get formatted time spent
  String get formattedTimeSpent {
    if (timeSpent == null) return 'Not tracked';
    
    final hours = timeSpent!.inHours;
    final minutes = timeSpent!.inMinutes % 60;
    
    if (hours > 0) {
      return '${hours}h ${minutes}m';
    } else {
      return '${minutes}m';
    }
  }

  /// Check if submission is late (requires due date comparison)
  bool isLate(DateTime? dueDate) {
    if (dueDate == null) return false;
    return submittedAt.isAfter(dueDate);
  }

  /// Get days since submission
  int get daysSinceSubmission {
    return DateTime.now().difference(submittedAt).inDays;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is TaskSubmission && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'TaskSubmission(id: $id, taskId: $taskId, status: ${status.name}, isGraded: $isGraded)';
  }
}

/// Enum representing different statuses of task submissions
enum TaskSubmissionStatus {
  draft,
  submitted,
  underReview,
  graded,
  returned,
  resubmitted,
}

extension TaskSubmissionStatusExtension on TaskSubmissionStatus {
  /// Display name for the submission status
  String get displayName {
    switch (this) {
      case TaskSubmissionStatus.draft:
        return 'Draft';
      case TaskSubmissionStatus.submitted:
        return 'Submitted';
      case TaskSubmissionStatus.underReview:
        return 'Under Review';
      case TaskSubmissionStatus.graded:
        return 'Graded';
      case TaskSubmissionStatus.returned:
        return 'Returned';
      case TaskSubmissionStatus.resubmitted:
        return 'Resubmitted';
    }
  }

  /// Description for the submission status
  String get description {
    switch (this) {
      case TaskSubmissionStatus.draft:
        return 'Submission is being prepared';
      case TaskSubmissionStatus.submitted:
        return 'Submission has been submitted for review';
      case TaskSubmissionStatus.underReview:
        return 'Submission is being reviewed';
      case TaskSubmissionStatus.graded:
        return 'Submission has been graded';
      case TaskSubmissionStatus.returned:
        return 'Submission has been returned for revision';
      case TaskSubmissionStatus.resubmitted:
        return 'Submission has been resubmitted after revision';
    }
  }

  /// Whether the submission can be edited in this status
  bool get canBeEdited {
    switch (this) {
      case TaskSubmissionStatus.draft:
      case TaskSubmissionStatus.returned:
        return true;
      case TaskSubmissionStatus.submitted:
      case TaskSubmissionStatus.underReview:
      case TaskSubmissionStatus.graded:
      case TaskSubmissionStatus.resubmitted:
        return false;
    }
  }

  /// Whether the submission is in a final state
  bool get isFinalState {
    return this == TaskSubmissionStatus.graded;
  }

  /// Whether the submission is pending review
  bool get isPendingReview {
    switch (this) {
      case TaskSubmissionStatus.submitted:
      case TaskSubmissionStatus.underReview:
      case TaskSubmissionStatus.resubmitted:
        return true;
      case TaskSubmissionStatus.draft:
      case TaskSubmissionStatus.graded:
      case TaskSubmissionStatus.returned:
        return false;
    }
  }
}
