/// Model representing a resource attached to a study plan task
class TaskResource {
  final String id;
  final String title;
  final String? description;
  final TaskResourceType type;
  final String url;
  final String? fileId; // Reference to Digital Library file
  final String? mimeType;
  final int? fileSizeBytes;
  final String? thumbnailUrl;
  final bool isRequired;
  final int order;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final String createdBy;
  final Map<String, dynamic>? metadata;

  const TaskResource({
    required this.id,
    required this.title,
    this.description,
    required this.type,
    required this.url,
    this.fileId,
    this.mimeType,
    this.fileSizeBytes,
    this.thumbnailUrl,
    this.isRequired = false,
    this.order = 0,
    required this.createdAt,
    this.updatedAt,
    required this.createdBy,
    this.metadata,
  });

  /// Create TaskResource from JSON
  factory TaskResource.fromJson(Map<String, dynamic> json) {
    return TaskResource(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String?,
      type: TaskResourceType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => TaskResourceType.link,
      ),
      url: json['url'] as String,
      fileId: json['fileId'] as String?,
      mimeType: json['mimeType'] as String?,
      fileSizeBytes: json['fileSizeBytes'] as int?,
      thumbnailUrl: json['thumbnailUrl'] as String?,
      isRequired: json['isRequired'] as bool? ?? false,
      order: json['order'] as int? ?? 0,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] != null
          ? DateTime.parse(json['updatedAt'] as String)
          : null,
      createdBy: json['createdBy'] as String,
      metadata: json['metadata'] as Map<String, dynamic>?,
    );
  }

  /// Convert TaskResource to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      if (description != null) 'description': description,
      'type': type.name,
      'url': url,
      if (fileId != null) 'fileId': fileId,
      if (mimeType != null) 'mimeType': mimeType,
      if (fileSizeBytes != null) 'fileSizeBytes': fileSizeBytes,
      if (thumbnailUrl != null) 'thumbnailUrl': thumbnailUrl,
      'isRequired': isRequired,
      'order': order,
      'createdAt': createdAt.toIso8601String(),
      if (updatedAt != null) 'updatedAt': updatedAt!.toIso8601String(),
      'createdBy': createdBy,
      if (metadata != null) 'metadata': metadata,
    };
  }

  /// Create a copy with updated fields
  TaskResource copyWith({
    String? id,
    String? title,
    String? description,
    TaskResourceType? type,
    String? url,
    String? fileId,
    String? mimeType,
    int? fileSizeBytes,
    String? thumbnailUrl,
    bool? isRequired,
    int? order,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? createdBy,
    Map<String, dynamic>? metadata,
  }) {
    return TaskResource(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      type: type ?? this.type,
      url: url ?? this.url,
      fileId: fileId ?? this.fileId,
      mimeType: mimeType ?? this.mimeType,
      fileSizeBytes: fileSizeBytes ?? this.fileSizeBytes,
      thumbnailUrl: thumbnailUrl ?? this.thumbnailUrl,
      isRequired: isRequired ?? this.isRequired,
      order: order ?? this.order,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      createdBy: createdBy ?? this.createdBy,
      metadata: metadata ?? this.metadata,
    );
  }

  /// Get file size in human readable format
  String get formattedFileSize {
    if (fileSizeBytes == null) return 'Unknown size';
    
    final bytes = fileSizeBytes!;
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }

  /// Check if resource is a file (has fileId)
  bool get isFile => fileId != null;

  /// Check if resource is an external link
  bool get isExternalLink => fileId == null && url.startsWith('http');

  /// Check if resource has been updated since creation
  bool get hasBeenUpdated => updatedAt != null && updatedAt!.isAfter(createdAt);

  /// Get effective display URL (thumbnail if available, otherwise main URL)
  String get displayUrl => thumbnailUrl ?? url;

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is TaskResource && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'TaskResource(id: $id, title: $title, type: ${type.name}, isRequired: $isRequired)';
  }
}

/// Enum representing different types of task resources
enum TaskResourceType {
  link,
  document,
  video,
  audio,
  image,
  presentation,
  spreadsheet,
  archive,
  other,
}

extension TaskResourceTypeExtension on TaskResourceType {
  /// Display name for the resource type
  String get displayName {
    switch (this) {
      case TaskResourceType.link:
        return 'Link';
      case TaskResourceType.document:
        return 'Document';
      case TaskResourceType.video:
        return 'Video';
      case TaskResourceType.audio:
        return 'Audio';
      case TaskResourceType.image:
        return 'Image';
      case TaskResourceType.presentation:
        return 'Presentation';
      case TaskResourceType.spreadsheet:
        return 'Spreadsheet';
      case TaskResourceType.archive:
        return 'Archive';
      case TaskResourceType.other:
        return 'Other';
    }
  }

  /// Get resource type from MIME type
  static TaskResourceType fromMimeType(String? mimeType) {
    if (mimeType == null) return TaskResourceType.other;
    
    if (mimeType.startsWith('image/')) return TaskResourceType.image;
    if (mimeType.startsWith('video/')) return TaskResourceType.video;
    if (mimeType.startsWith('audio/')) return TaskResourceType.audio;
    
    switch (mimeType) {
      case 'application/pdf':
      case 'application/msword':
      case 'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
      case 'text/plain':
      case 'text/rtf':
        return TaskResourceType.document;
      
      case 'application/vnd.ms-powerpoint':
      case 'application/vnd.openxmlformats-officedocument.presentationml.presentation':
        return TaskResourceType.presentation;
      
      case 'application/vnd.ms-excel':
      case 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet':
      case 'text/csv':
        return TaskResourceType.spreadsheet;
      
      case 'application/zip':
      case 'application/x-rar-compressed':
      case 'application/x-7z-compressed':
      case 'application/gzip':
        return TaskResourceType.archive;
      
      default:
        return TaskResourceType.other;
    }
  }

  /// Get file extension patterns for this resource type
  List<String> get fileExtensions {
    switch (this) {
      case TaskResourceType.document:
        return ['pdf', 'doc', 'docx', 'txt', 'rtf'];
      case TaskResourceType.video:
        return ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm'];
      case TaskResourceType.audio:
        return ['mp3', 'wav', 'aac', 'ogg', 'flac'];
      case TaskResourceType.image:
        return ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'webp'];
      case TaskResourceType.presentation:
        return ['ppt', 'pptx', 'odp'];
      case TaskResourceType.spreadsheet:
        return ['xls', 'xlsx', 'csv', 'ods'];
      case TaskResourceType.archive:
        return ['zip', 'rar', '7z', 'tar', 'gz'];
      case TaskResourceType.link:
      case TaskResourceType.other:
        return [];
    }
  }
}
