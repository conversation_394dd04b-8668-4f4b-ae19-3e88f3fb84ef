import 'date_range.dart';

/// Enum representing different types of scheduling options
enum SchedulingType { none, duration, date }

/// Enum for entity types that can have individual scheduling
enum SchedulableEntityType {
  section('Section'),
  subSection('Sub-section'),
  task('Task');

  const SchedulableEntityType(this.displayName);

  final String displayName;
}

extension SchedulingTypeExtension on SchedulingType {
  String get displayName {
    switch (this) {
      case SchedulingType.none:
        return 'None';
      case SchedulingType.duration:
        return 'Duration';
      case SchedulingType.date:
        return 'Date';
    }
  }

  String get description {
    switch (this) {
      case SchedulingType.none:
        return 'No specific timing required';
      case SchedulingType.duration:
        return 'Set how long this will take';
      case SchedulingType.date:
        return 'Set specific dates or deadlines';
    }
  }
}

/// Model representing flexible scheduling options for sections and tasks
class FlexibleSchedule {
  final SchedulingType type;
  final DateTime? specificDate;
  final DateRange? dateRange;
  final DateTime? deadline;
  final Duration? relativeDuration;
  final String? durationDescription; // e.g., "2 days", "1 week"
  final bool isFlexible;
  final String? notes;

  const FlexibleSchedule({
    required this.type,
    this.specificDate,
    this.dateRange,
    this.deadline,
    this.relativeDuration,
    this.durationDescription,
    this.isFlexible = false,
    this.notes,
  });

  /// Create a date-based schedule (can be specific date, range, or deadline)
  factory FlexibleSchedule.date({
    DateTime? specificDate,
    DateTime? startDate,
    DateTime? endDate,
    bool isFlexible = false,
    String? notes,
  }) {
    DateRange? dateRange;
    DateTime? deadline;

    // Determine the type based on which dates are provided
    if (startDate != null && endDate != null && startDate != endDate) {
      // Date range
      dateRange = DateRange(startDate: startDate, endDate: endDate);
    } else if (endDate != null && startDate == null) {
      // Deadline
      deadline = endDate;
    } else if (specificDate != null) {
      // Specific date
      // Use specificDate as is
    } else if (startDate != null) {
      // Single start date treated as specific date
      specificDate = startDate;
    }

    return FlexibleSchedule(
      type: SchedulingType.date,
      specificDate: specificDate,
      dateRange: dateRange,
      deadline: deadline,
      isFlexible: isFlexible,
      notes: notes,
    );
  }

  /// Create a duration-based schedule
  factory FlexibleSchedule.duration({
    required Duration duration,
    String? description,
    bool isFlexible = true,
    String? notes,
  }) {
    return FlexibleSchedule(
      type: SchedulingType.duration,
      relativeDuration: duration,
      durationDescription: description,
      isFlexible: isFlexible,
      notes: notes,
    );
  }

  /// Create a no schedule option
  factory FlexibleSchedule.none({String? notes}) {
    return FlexibleSchedule(type: SchedulingType.none, notes: notes);
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'type': type.name,
      if (specificDate != null) 'specificDate': specificDate!.toIso8601String(),
      if (dateRange != null) 'dateRange': dateRange!.toJson(),
      if (deadline != null) 'deadline': deadline!.toIso8601String(),
      if (relativeDuration != null)
        'relativeDuration': relativeDuration!.inMinutes,
      if (durationDescription != null)
        'durationDescription': durationDescription,
      'isFlexible': isFlexible,
      if (notes != null) 'notes': notes,
    };
  }

  /// Create from JSON
  factory FlexibleSchedule.fromJson(Map<String, dynamic> json) {
    final type = SchedulingType.values.firstWhere(
      (e) => e.name == json['type'],
    );

    return FlexibleSchedule(
      type: type,
      specificDate: json['specificDate'] != null
          ? DateTime.parse(json['specificDate'])
          : null,
      dateRange: json['dateRange'] != null
          ? DateRange.fromJson(json['dateRange'])
          : null,
      deadline: json['deadline'] != null
          ? DateTime.parse(json['deadline'])
          : null,
      relativeDuration: json['relativeDuration'] != null
          ? Duration(minutes: json['relativeDuration'])
          : null,
      durationDescription: json['durationDescription'],
      isFlexible: json['isFlexible'] ?? false,
      notes: json['notes'],
    );
  }

  /// Create a copy with updated fields
  FlexibleSchedule copyWith({
    SchedulingType? type,
    DateTime? specificDate,
    DateRange? dateRange,
    DateTime? deadline,
    Duration? relativeDuration,
    String? durationDescription,
    bool? isFlexible,
    String? notes,
  }) {
    return FlexibleSchedule(
      type: type ?? this.type,
      specificDate: specificDate ?? this.specificDate,
      dateRange: dateRange ?? this.dateRange,
      deadline: deadline ?? this.deadline,
      relativeDuration: relativeDuration ?? this.relativeDuration,
      durationDescription: durationDescription ?? this.durationDescription,
      isFlexible: isFlexible ?? this.isFlexible,
      notes: notes ?? this.notes,
    );
  }

  /// Get display text for the schedule
  String get displayText {
    switch (type) {
      case SchedulingType.date:
        if (dateRange != null) {
          return dateRange!.displayText;
        } else if (deadline != null) {
          return 'Due: ${deadline!.toString().split(' ')[0]}';
        } else if (specificDate != null) {
          return specificDate!.toString().split(' ')[0];
        } else {
          return 'No date set';
        }
      case SchedulingType.duration:
        return durationDescription ?? '${relativeDuration?.inDays ?? 0} days';
      case SchedulingType.none:
        return 'No schedule';
    }
  }

  /// Get the schedule type based on the date fields
  String get dateScheduleType {
    if (type != SchedulingType.date) return '';

    if (dateRange != null) {
      return 'Date Range';
    } else if (deadline != null) {
      return 'Deadline';
    } else if (specificDate != null) {
      return 'Specific Date';
    } else {
      return 'Date';
    }
  }

  /// Check if the schedule is valid
  bool get isValid {
    switch (type) {
      case SchedulingType.date:
        return specificDate != null ||
            (dateRange != null && dateRange!.isValid) ||
            deadline != null;
      case SchedulingType.duration:
        return relativeDuration != null;
      case SchedulingType.none:
        return true;
    }
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is FlexibleSchedule &&
        other.type == type &&
        other.specificDate == specificDate &&
        other.dateRange == dateRange &&
        other.deadline == deadline &&
        other.relativeDuration == relativeDuration &&
        other.durationDescription == durationDescription &&
        other.isFlexible == isFlexible &&
        other.notes == notes;
  }

  @override
  int get hashCode {
    return Object.hash(
      type,
      specificDate,
      dateRange,
      deadline,
      relativeDuration,
      durationDescription,
      isFlexible,
      notes,
    );
  }

  @override
  String toString() {
    return 'FlexibleSchedule(type: $type, displayText: $displayText)';
  }
}
