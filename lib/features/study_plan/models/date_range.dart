/// Model representing a flexible date range for study plan tasks and sections
class DateRange {
  final DateTime startDate;
  final DateTime endDate;
  final bool isFlexible;
  final String? description;

  const DateRange({
    required this.startDate,
    required this.endDate,
    this.isFlexible = false,
    this.description,
  });

  /// Create a DateRange from JSON
  factory DateRange.fromJson(Map<String, dynamic> json) {
    return DateRange(
      startDate: DateTime.parse(json['startDate'] as String),
      endDate: DateTime.parse(json['endDate'] as String),
      isFlexible: json['isFlexible'] as bool? ?? false,
      description: json['description'] as String?,
    );
  }

  /// Convert DateRange to JSON
  Map<String, dynamic> toJson() {
    return {
      'startDate': startDate.toIso8601String(),
      'endDate': endDate.toIso8601String(),
      'isFlexible': isFlexible,
      if (description != null) 'description': description,
    };
  }

  /// Create a copy with updated fields
  DateRange copyWith({
    DateTime? startDate,
    DateTime? endDate,
    bool? isFlexible,
    String? description,
  }) {
    return DateRange(
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      isFlexible: isFlexible ?? this.isFlexible,
      description: description ?? this.description,
    );
  }

  /// Get the duration of this date range
  Duration get duration => endDate.difference(startDate);

  /// Check if a given date falls within this range
  bool contains(DateTime date) {
    return date.isAfter(startDate.subtract(const Duration(days: 1))) &&
        date.isBefore(endDate.add(const Duration(days: 1)));
  }

  /// Check if this date range overlaps with another
  bool overlapsWith(DateRange other) {
    return startDate.isBefore(other.endDate) &&
        endDate.isAfter(other.startDate);
  }

  /// Check if this date range is valid (start before end)
  bool get isValid => startDate.isBefore(endDate);

  /// Get a human-readable description of the date range
  String get displayText {
    if (description != null && description!.isNotEmpty) {
      return description!;
    }

    final start = startDate.toString().split(' ')[0];
    final end = endDate.toString().split(' ')[0];

    if (isFlexible) {
      return 'Flexible: $start - $end';
    } else {
      return '$start - $end';
    }
  }

  /// Create a flexible date range
  factory DateRange.flexible({
    required DateTime startDate,
    required DateTime endDate,
    String? description,
  }) {
    return DateRange(
      startDate: startDate,
      endDate: endDate,
      isFlexible: true,
      description: description,
    );
  }

  /// Create a fixed date range
  factory DateRange.fixed({
    required DateTime startDate,
    required DateTime endDate,
    String? description,
  }) {
    return DateRange(
      startDate: startDate,
      endDate: endDate,
      isFlexible: false,
      description: description,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is DateRange &&
        other.startDate == startDate &&
        other.endDate == endDate &&
        other.isFlexible == isFlexible &&
        other.description == description;
  }

  @override
  int get hashCode {
    return Object.hash(startDate, endDate, isFlexible, description);
  }

  @override
  String toString() {
    return 'DateRange(startDate: $startDate, endDate: $endDate, isFlexible: $isFlexible, description: $description)';
  }
}
