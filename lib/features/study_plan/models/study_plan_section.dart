import '../enums/study_plan_enums.dart';
import 'date_range.dart';
import 'flexible_schedule.dart';
import 'study_plan_task.dart';

/// Model representing progress of a section
class SectionProgress {
  final double completionPercentage;
  final int totalTasks;
  final int completedTasks;
  final int overdueTasks;
  final DateTime? lastUpdated;

  const SectionProgress({
    this.completionPercentage = 0.0,
    this.totalTasks = 0,
    this.completedTasks = 0,
    this.overdueTasks = 0,
    this.lastUpdated,
  });

  factory SectionProgress.fromJson(Map<String, dynamic> json) {
    return SectionProgress(
      completionPercentage:
          (json['completionPercentage'] as num?)?.toDouble() ?? 0.0,
      totalTasks: json['totalTasks'] as int? ?? 0,
      completedTasks: json['completedTasks'] as int? ?? 0,
      overdueTasks: json['overdueTasks'] as int? ?? 0,
      lastUpdated: json['lastUpdated'] != null
          ? DateTime.parse(json['lastUpdated'] as String)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'completionPercentage': completionPercentage,
      'totalTasks': totalTasks,
      'completedTasks': completedTasks,
      'overdueTasks': overdueTasks,
      if (lastUpdated != null) 'lastUpdated': lastUpdated!.toIso8601String(),
    };
  }

  SectionProgress copyWith({
    double? completionPercentage,
    int? totalTasks,
    int? completedTasks,
    int? overdueTasks,
    DateTime? lastUpdated,
  }) {
    return SectionProgress(
      completionPercentage: completionPercentage ?? this.completionPercentage,
      totalTasks: totalTasks ?? this.totalTasks,
      completedTasks: completedTasks ?? this.completedTasks,
      overdueTasks: overdueTasks ?? this.overdueTasks,
      lastUpdated: lastUpdated ?? this.lastUpdated,
    );
  }

  /// Calculate progress from tasks
  static SectionProgress fromTasks(List<StudyPlanTask> tasks) {
    final totalTasks = tasks.length;
    final completedTasks = tasks.where((task) => task.isCompleted).length;
    final overdueTasks = tasks.where((task) => task.isOverdue).length;
    final completionPercentage = totalTasks > 0
        ? (completedTasks / totalTasks) * 100
        : 0.0;

    return SectionProgress(
      completionPercentage: completionPercentage,
      totalTasks: totalTasks,
      completedTasks: completedTasks,
      overdueTasks: overdueTasks,
      lastUpdated: DateTime.now(),
    );
  }
}

/// Model representing a milestone within a section
class StudyPlanMilestone {
  final String id;
  final String title;
  final String? description;
  final DateTime? targetDate;
  final bool isCompleted;
  final DateTime? completedAt;
  final int order;

  const StudyPlanMilestone({
    required this.id,
    required this.title,
    this.description,
    this.targetDate,
    this.isCompleted = false,
    this.completedAt,
    this.order = 0,
  });

  factory StudyPlanMilestone.fromJson(Map<String, dynamic> json) {
    return StudyPlanMilestone(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String?,
      targetDate: json['targetDate'] != null
          ? DateTime.parse(json['targetDate'] as String)
          : null,
      isCompleted: json['isCompleted'] as bool? ?? false,
      completedAt: json['completedAt'] != null
          ? DateTime.parse(json['completedAt'] as String)
          : null,
      order: json['order'] as int? ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      if (description != null) 'description': description,
      if (targetDate != null) 'targetDate': targetDate!.toIso8601String(),
      'isCompleted': isCompleted,
      if (completedAt != null) 'completedAt': completedAt!.toIso8601String(),
      'order': order,
    };
  }

  StudyPlanMilestone copyWith({
    String? id,
    String? title,
    String? description,
    DateTime? targetDate,
    bool? isCompleted,
    DateTime? completedAt,
    int? order,
  }) {
    return StudyPlanMilestone(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      targetDate: targetDate ?? this.targetDate,
      isCompleted: isCompleted ?? this.isCompleted,
      completedAt: completedAt ?? this.completedAt,
      order: order ?? this.order,
    );
  }
}

/// Model representing a section within a study plan
class StudyPlanSection {
  final String id;
  final String title;
  final String? description;
  final SectionType type;
  final int order;
  final String? subjectId;

  // Hierarchy
  final String? parentSectionId;
  final List<StudyPlanSection> subSections;
  final List<StudyPlanTask> tasks;
  final List<StudyPlanMilestone> milestones;

  // Timeline & Individual Scheduling
  final DateTime? startDate;
  final DateTime? endDate;
  final DateRange? dateRange;
  final Duration? estimatedDuration;
  final FlexibleSchedule? individualSchedule;

  // Progress
  final SectionProgress progress;
  final bool isCompleted;
  final DateTime? completedAt;

  // Metadata
  final Map<String, dynamic>? metadata;

  const StudyPlanSection({
    required this.id,
    required this.title,
    this.description,
    required this.type,
    this.order = 0,
    this.subjectId,
    this.parentSectionId,
    this.subSections = const [],
    this.tasks = const [],
    this.milestones = const [],
    this.startDate,
    this.endDate,
    this.dateRange,
    this.estimatedDuration,
    this.individualSchedule,
    this.progress = const SectionProgress(),
    this.isCompleted = false,
    this.completedAt,
    this.metadata,
  });

  /// Create StudyPlanSection from JSON
  factory StudyPlanSection.fromJson(Map<String, dynamic> json) {
    return StudyPlanSection(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String?,
      type: SectionType.values.firstWhere((e) => e.name == json['type']),
      order: json['order'] as int? ?? 0,
      subjectId: json['subjectId'] as String?,
      parentSectionId: json['parentSectionId'] as String?,
      subSections:
          (json['subSections'] as List?)
              ?.map((e) => StudyPlanSection.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      tasks:
          (json['tasks'] as List?)
              ?.map((e) => StudyPlanTask.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      milestones:
          (json['milestones'] as List?)
              ?.map(
                (e) => StudyPlanMilestone.fromJson(e as Map<String, dynamic>),
              )
              .toList() ??
          [],
      startDate: json['startDate'] != null
          ? DateTime.parse(json['startDate'] as String)
          : null,
      endDate: json['endDate'] != null
          ? DateTime.parse(json['endDate'] as String)
          : null,
      dateRange: json['dateRange'] != null
          ? DateRange.fromJson(json['dateRange'] as Map<String, dynamic>)
          : null,
      estimatedDuration: json['estimatedDuration'] != null
          ? Duration(minutes: json['estimatedDuration'] as int)
          : null,
      individualSchedule: json['individualSchedule'] != null
          ? FlexibleSchedule.fromJson(
              json['individualSchedule'] as Map<String, dynamic>,
            )
          : null,
      progress: json['progress'] != null
          ? SectionProgress.fromJson(json['progress'] as Map<String, dynamic>)
          : const SectionProgress(),
      isCompleted: json['isCompleted'] as bool? ?? false,
      completedAt: json['completedAt'] != null
          ? DateTime.parse(json['completedAt'] as String)
          : null,
      metadata: json['metadata'] as Map<String, dynamic>?,
    );
  }

  /// Convert StudyPlanSection to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      if (description != null) 'description': description,
      'type': type.name,
      'order': order,
      if (subjectId != null) 'subjectId': subjectId,
      if (parentSectionId != null) 'parentSectionId': parentSectionId,
      'subSections': subSections.map((e) => e.toJson()).toList(),
      'tasks': tasks.map((e) => e.toJson()).toList(),
      'milestones': milestones.map((e) => e.toJson()).toList(),
      if (startDate != null) 'startDate': startDate!.toIso8601String(),
      if (endDate != null) 'endDate': endDate!.toIso8601String(),
      if (dateRange != null) 'dateRange': dateRange!.toJson(),
      if (estimatedDuration != null)
        'estimatedDuration': estimatedDuration!.inMinutes,
      if (individualSchedule != null)
        'individualSchedule': individualSchedule!.toJson(),
      'progress': progress.toJson(),
      'isCompleted': isCompleted,
      if (completedAt != null) 'completedAt': completedAt!.toIso8601String(),
      if (metadata != null) 'metadata': metadata,
    };
  }

  /// Create a copy with updated fields
  StudyPlanSection copyWith({
    String? id,
    String? title,
    String? description,
    SectionType? type,
    int? order,
    String? subjectId,
    String? parentSectionId,
    List<StudyPlanSection>? subSections,
    List<StudyPlanTask>? tasks,
    List<StudyPlanMilestone>? milestones,
    DateTime? startDate,
    DateTime? endDate,
    DateRange? dateRange,
    Duration? estimatedDuration,
    FlexibleSchedule? individualSchedule,
    SectionProgress? progress,
    bool? isCompleted,
    DateTime? completedAt,
    Map<String, dynamic>? metadata,
  }) {
    return StudyPlanSection(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      type: type ?? this.type,
      order: order ?? this.order,
      subjectId: subjectId ?? this.subjectId,
      parentSectionId: parentSectionId ?? this.parentSectionId,
      subSections: subSections ?? this.subSections,
      tasks: tasks ?? this.tasks,
      milestones: milestones ?? this.milestones,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      dateRange: dateRange ?? this.dateRange,
      estimatedDuration: estimatedDuration ?? this.estimatedDuration,
      individualSchedule: individualSchedule ?? this.individualSchedule,
      progress: progress ?? this.progress,
      isCompleted: isCompleted ?? this.isCompleted,
      completedAt: completedAt ?? this.completedAt,
      metadata: metadata ?? this.metadata,
    );
  }

  /// Get all tasks including from sub-sections
  List<StudyPlanTask> get allTasks {
    final allTasks = List<StudyPlanTask>.from(tasks);
    for (final subSection in subSections) {
      allTasks.addAll(subSection.allTasks);
    }
    return allTasks;
  }

  /// Get section depth in hierarchy
  int get depth {
    if (parentSectionId == null) return 0;
    // This would need to be calculated with parent section reference
    return 1; // Simplified for now
  }

  /// Check if section has sub-sections
  bool get hasSubSections => subSections.isNotEmpty;

  /// Check if section has tasks
  bool get hasTasks => tasks.isNotEmpty;

  /// Check if section has milestones
  bool get hasMilestones => milestones.isNotEmpty;

  /// Calculate updated progress from current tasks
  SectionProgress calculateProgress() {
    return SectionProgress.fromTasks(allTasks);
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is StudyPlanSection && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'StudyPlanSection(id: $id, title: $title, type: ${type.displayName}, tasksCount: ${tasks.length})';
  }
}
