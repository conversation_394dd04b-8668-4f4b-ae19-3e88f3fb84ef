import 'flexible_schedule.dart';
import 'study_plan_section.dart';
import 'study_plan_task.dart';

/// Helper class for managing individual entity scheduling
class EntityScheduleManager {
  /// Get all schedulable entities from a list of sections
  static List<SchedulableEntity> getAllSchedulableEntities(
    List<StudyPlanSection> sections,
  ) {
    final List<SchedulableEntity> entities = [];

    for (final section in sections) {
      // Add the section itself
      entities.add(SchedulableEntity.fromSection(section));

      // Add sub-sections
      for (final subSection in section.subSections) {
        entities.add(SchedulableEntity.fromSubSection(subSection, section.id));
        
        // Add tasks from sub-sections
        for (final task in subSection.tasks) {
          entities.add(SchedulableEntity.fromTask(task, subSection.id));
        }
      }

      // Add tasks directly under the section
      for (final task in section.tasks) {
        entities.add(SchedulableEntity.fromTask(task, section.id));
      }
    }

    return entities;
  }

  /// Get unscheduled entities (entities without individual schedules)
  static List<SchedulableEntity> getUnscheduledEntities(
    List<StudyPlanSection> sections,
  ) {
    return getAllSchedulableEntities(sections)
        .where((entity) => entity.schedule == null || !entity.schedule!.isValid)
        .toList();
  }

  /// Get scheduled entities grouped by date
  static Map<DateTime, List<SchedulableEntity>> getScheduledEntitiesByDate(
    List<StudyPlanSection> sections,
  ) {
    final Map<DateTime, List<SchedulableEntity>> groupedEntities = {};
    final scheduledEntities = getAllSchedulableEntities(sections)
        .where((entity) => entity.schedule != null && entity.schedule!.isValid)
        .toList();

    for (final entity in scheduledEntities) {
      final dates = entity.getScheduleDates();
      for (final date in dates) {
        groupedEntities[date] = [...(groupedEntities[date] ?? []), entity];
      }
    }

    return groupedEntities;
  }

  /// Update schedule for a specific entity
  static List<StudyPlanSection> updateEntitySchedule(
    List<StudyPlanSection> sections,
    String entityId,
    SchedulableEntityType entityType,
    FlexibleSchedule? schedule,
  ) {
    return sections.map((section) {
      if (entityType == SchedulableEntityType.section && section.id == entityId) {
        return section.copyWith(individualSchedule: schedule);
      }

      // Check sub-sections
      final updatedSubSections = section.subSections.map((subSection) {
        if (entityType == SchedulableEntityType.subSection && subSection.id == entityId) {
          return subSection.copyWith(individualSchedule: schedule);
        }

        // Check tasks in sub-section
        if (entityType == SchedulableEntityType.task) {
          final updatedTasks = subSection.tasks.map((task) {
            if (task.id == entityId) {
              return task.copyWith(individualSchedule: schedule);
            }
            return task;
          }).toList();

          return subSection.copyWith(tasks: updatedTasks);
        }

        return subSection;
      }).toList();

      // Check tasks directly under section
      final updatedTasks = section.tasks.map((task) {
        if (entityType == SchedulableEntityType.task && task.id == entityId) {
          return task.copyWith(individualSchedule: schedule);
        }
        return task;
      }).toList();

      return section.copyWith(
        subSections: updatedSubSections,
        tasks: updatedTasks,
      );
    }).toList();
  }
}

/// Wrapper class for schedulable entities
class SchedulableEntity {
  final String id;
  final String title;
  final String? description;
  final SchedulableEntityType type;
  final String? parentId;
  final FlexibleSchedule? schedule;
  final dynamic originalEntity; // Store the original entity for reference

  const SchedulableEntity({
    required this.id,
    required this.title,
    this.description,
    required this.type,
    this.parentId,
    this.schedule,
    this.originalEntity,
  });

  /// Create from StudyPlanSection
  factory SchedulableEntity.fromSection(StudyPlanSection section) {
    return SchedulableEntity(
      id: section.id,
      title: section.title,
      description: section.description,
      type: SchedulableEntityType.section,
      schedule: section.individualSchedule,
      originalEntity: section,
    );
  }

  /// Create from StudyPlanSection as sub-section
  factory SchedulableEntity.fromSubSection(StudyPlanSection subSection, String parentId) {
    return SchedulableEntity(
      id: subSection.id,
      title: subSection.title,
      description: subSection.description,
      type: SchedulableEntityType.subSection,
      parentId: parentId,
      schedule: subSection.individualSchedule,
      originalEntity: subSection,
    );
  }

  /// Create from StudyPlanTask
  factory SchedulableEntity.fromTask(StudyPlanTask task, String parentId) {
    return SchedulableEntity(
      id: task.id,
      title: task.title,
      description: task.description,
      type: SchedulableEntityType.task,
      parentId: parentId,
      schedule: task.individualSchedule,
      originalEntity: task,
    );
  }

  /// Get dates when this entity is scheduled
  List<DateTime> getScheduleDates() {
    if (schedule == null || !schedule!.isValid) return [];

    switch (schedule!.type) {
      case SchedulingType.date:
        final List<DateTime> dates = [];
        if (schedule!.specificDate != null) {
          dates.add(schedule!.specificDate!);
        }
        if (schedule!.dateRange != null) {
          // For date ranges, we could add all dates in the range
          // For now, just add start and end dates
          dates.add(schedule!.dateRange!.startDate);
          if (schedule!.dateRange!.startDate != schedule!.dateRange!.endDate) {
            dates.add(schedule!.dateRange!.endDate);
          }
        }
        if (schedule!.deadline != null) {
          dates.add(schedule!.deadline!);
        }
        return dates;
      case SchedulingType.duration:
      case SchedulingType.none:
        return [];
    }
  }

  /// Get display text for the entity type
  String get typeDisplayText => type.displayName;

  /// Check if entity has a valid schedule
  bool get hasValidSchedule => schedule != null && schedule!.isValid;

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is SchedulableEntity && other.id == id && other.type == type;
  }

  @override
  int get hashCode => Object.hash(id, type);

  @override
  String toString() {
    return 'SchedulableEntity(id: $id, title: $title, type: $type)';
  }
}
