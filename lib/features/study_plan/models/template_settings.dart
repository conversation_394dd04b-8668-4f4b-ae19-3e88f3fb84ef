/// Model representing template-specific configuration for study plans
class TemplateSettings {
  final bool allowCustomScheduling;
  final bool allowTaskModification;
  final bool allowSectionModification;
  final List<String> editableFields;
  final Map<String, dynamic> defaultValues;
  final String? instructionsForUsers;

  const TemplateSettings({
    this.allowCustomScheduling = true,
    this.allowTaskModification = true,
    this.allowSectionModification = true,
    this.editableFields = const [],
    this.defaultValues = const {},
    this.instructionsForUsers,
  });

  /// Create TemplateSettings from JSON
  factory TemplateSettings.fromJson(Map<String, dynamic> json) {
    return TemplateSettings(
      allowCustomScheduling: json['allowCustomScheduling'] as bool? ?? true,
      allowTaskModification: json['allowTaskModification'] as bool? ?? true,
      allowSectionModification:
          json['allowSectionModification'] as bool? ?? true,
      editableFields: List<String>.from(json['editableFields'] as List? ?? []),
      defaultValues: Map<String, dynamic>.from(
        json['defaultValues'] as Map? ?? {},
      ),
      instructionsForUsers: json['instructionsForUsers'] as String?,
    );
  }

  /// Convert TemplateSettings to JSON
  Map<String, dynamic> toJson() {
    return {
      'allowCustomScheduling': allowCustomScheduling,
      'allowTaskModification': allowTaskModification,
      'allowSectionModification': allowSectionModification,
      'editableFields': editableFields,
      'defaultValues': defaultValues,
      if (instructionsForUsers != null)
        'instructionsForUsers': instructionsForUsers,
    };
  }

  /// Create a copy with updated fields
  TemplateSettings copyWith({
    bool? allowCustomScheduling,
    bool? allowTaskModification,
    bool? allowSectionModification,
    List<String>? editableFields,
    Map<String, dynamic>? defaultValues,
    String? instructionsForUsers,
  }) {
    return TemplateSettings(
      allowCustomScheduling:
          allowCustomScheduling ?? this.allowCustomScheduling,
      allowTaskModification:
          allowTaskModification ?? this.allowTaskModification,
      allowSectionModification:
          allowSectionModification ?? this.allowSectionModification,
      editableFields: editableFields ?? this.editableFields,
      defaultValues: defaultValues ?? this.defaultValues,
      instructionsForUsers: instructionsForUsers ?? this.instructionsForUsers,
    );
  }

  /// Check if a specific field is editable
  bool isFieldEditable(String fieldName) {
    return editableFields.isEmpty || editableFields.contains(fieldName);
  }

  /// Get default value for a field
  T? getDefaultValue<T>(String fieldName) {
    return defaultValues[fieldName] as T?;
  }

  /// Add an editable field
  TemplateSettings addEditableField(String fieldName) {
    if (editableFields.contains(fieldName)) return this;

    return copyWith(editableFields: [...editableFields, fieldName]);
  }

  /// Remove an editable field
  TemplateSettings removeEditableField(String fieldName) {
    return copyWith(
      editableFields: editableFields
          .where((field) => field != fieldName)
          .toList(),
    );
  }

  /// Set default value for a field
  TemplateSettings setDefaultValue(String fieldName, dynamic value) {
    final newDefaults = Map<String, dynamic>.from(defaultValues);
    newDefaults[fieldName] = value;

    return copyWith(defaultValues: newDefaults);
  }

  /// Remove default value for a field
  TemplateSettings removeDefaultValue(String fieldName) {
    final newDefaults = Map<String, dynamic>.from(defaultValues);
    newDefaults.remove(fieldName);

    return copyWith(defaultValues: newDefaults);
  }

  /// Check if template allows any modifications
  bool get allowsAnyModification {
    return allowCustomScheduling ||
        allowTaskModification ||
        allowSectionModification;
  }

  /// Check if template is fully locked (no modifications allowed)
  bool get isFullyLocked {
    return !allowCustomScheduling &&
        !allowTaskModification &&
        !allowSectionModification;
  }

  /// Create default template settings (allows all modifications)
  factory TemplateSettings.defaultSettings() {
    return const TemplateSettings(
      allowCustomScheduling: true,
      allowTaskModification: true,
      allowSectionModification: true,
      editableFields: [],
      defaultValues: {},
    );
  }

  /// Create locked template settings (no modifications allowed)
  factory TemplateSettings.locked({String? instructions}) {
    return TemplateSettings(
      allowCustomScheduling: false,
      allowTaskModification: false,
      allowSectionModification: false,
      editableFields: const [],
      defaultValues: const {},
      instructionsForUsers: instructions,
    );
  }

  /// Create template settings with custom scheduling only
  factory TemplateSettings.schedulingOnly({String? instructions}) {
    return TemplateSettings(
      allowCustomScheduling: true,
      allowTaskModification: false,
      allowSectionModification: false,
      editableFields: const [],
      defaultValues: const {},
      instructionsForUsers: instructions,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is TemplateSettings &&
        other.allowCustomScheduling == allowCustomScheduling &&
        other.allowTaskModification == allowTaskModification &&
        other.allowSectionModification == allowSectionModification &&
        _listEquals(other.editableFields, editableFields) &&
        _mapEquals(other.defaultValues, defaultValues) &&
        other.instructionsForUsers == instructionsForUsers;
  }

  @override
  int get hashCode {
    return Object.hash(
      allowCustomScheduling,
      allowTaskModification,
      allowSectionModification,
      Object.hashAll(editableFields),
      defaultValues.isNotEmpty ? Object.hashAll(defaultValues.entries) : null,
      instructionsForUsers,
    );
  }

  bool _listEquals<T>(List<T>? a, List<T>? b) {
    if (a == null) return b == null;
    if (b == null || a.length != b.length) return false;
    for (int index = 0; index < a.length; index += 1) {
      if (a[index] != b[index]) return false;
    }
    return true;
  }

  bool _mapEquals<K, V>(Map<K, V>? a, Map<K, V>? b) {
    if (a == null) return b == null;
    if (b == null || a.length != b.length) return false;
    for (final key in a.keys) {
      if (!b.containsKey(key) || a[key] != b[key]) return false;
    }
    return true;
  }

  @override
  String toString() {
    return 'TemplateSettings(allowCustomScheduling: $allowCustomScheduling, allowTaskModification: $allowTaskModification, allowSectionModification: $allowSectionModification, editableFields: $editableFields, instructionsForUsers: $instructionsForUsers)';
  }
}
