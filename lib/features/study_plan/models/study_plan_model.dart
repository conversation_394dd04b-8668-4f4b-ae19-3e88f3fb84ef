import '../enums/study_plan_enums.dart';
import 'template_settings.dart';
import 'study_plan_section.dart';
import 'study_plan_task.dart';

/// Model representing study plan progress
class StudyPlanProgress {
  final double overallCompletion;
  final int totalSections;
  final int completedSections;
  final int totalTasks;
  final int completedTasks;
  final int overdueTasks;
  final DateTime? lastUpdated;
  final Map<String, double> sectionProgress;

  const StudyPlanProgress({
    this.overallCompletion = 0.0,
    this.totalSections = 0,
    this.completedSections = 0,
    this.totalTasks = 0,
    this.completedTasks = 0,
    this.overdueTasks = 0,
    this.lastUpdated,
    this.sectionProgress = const {},
  });

  factory StudyPlanProgress.fromJson(Map<String, dynamic> json) {
    return StudyPlanProgress(
      overallCompletion: (json['overallCompletion'] as num?)?.toDouble() ?? 0.0,
      totalSections: json['totalSections'] as int? ?? 0,
      completedSections: json['completedSections'] as int? ?? 0,
      totalTasks: json['totalTasks'] as int? ?? 0,
      completedTasks: json['completedTasks'] as int? ?? 0,
      overdueTasks: json['overdueTasks'] as int? ?? 0,
      lastUpdated: json['lastUpdated'] != null
          ? DateTime.parse(json['lastUpdated'] as String)
          : null,
      sectionProgress: Map<String, double>.from(
        json['sectionProgress'] as Map? ?? {},
      ),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'overallCompletion': overallCompletion,
      'totalSections': totalSections,
      'completedSections': completedSections,
      'totalTasks': totalTasks,
      'completedTasks': completedTasks,
      'overdueTasks': overdueTasks,
      if (lastUpdated != null) 'lastUpdated': lastUpdated!.toIso8601String(),
      'sectionProgress': sectionProgress,
    };
  }

  StudyPlanProgress copyWith({
    double? overallCompletion,
    int? totalSections,
    int? completedSections,
    int? totalTasks,
    int? completedTasks,
    int? overdueTasks,
    DateTime? lastUpdated,
    Map<String, double>? sectionProgress,
  }) {
    return StudyPlanProgress(
      overallCompletion: overallCompletion ?? this.overallCompletion,
      totalSections: totalSections ?? this.totalSections,
      completedSections: completedSections ?? this.completedSections,
      totalTasks: totalTasks ?? this.totalTasks,
      completedTasks: completedTasks ?? this.completedTasks,
      overdueTasks: overdueTasks ?? this.overdueTasks,
      lastUpdated: lastUpdated ?? this.lastUpdated,
      sectionProgress: sectionProgress ?? this.sectionProgress,
    );
  }
}

/// Model representing study plan settings
class StudyPlanSettings {
  final bool allowCollaboration;
  final bool sendNotifications;
  final bool trackProgress;
  final bool allowComments;
  final Map<String, dynamic> customSettings;

  const StudyPlanSettings({
    this.allowCollaboration = false,
    this.sendNotifications = true,
    this.trackProgress = true,
    this.allowComments = false,
    this.customSettings = const {},
  });

  factory StudyPlanSettings.fromJson(Map<String, dynamic> json) {
    return StudyPlanSettings(
      allowCollaboration: json['allowCollaboration'] as bool? ?? false,
      sendNotifications: json['sendNotifications'] as bool? ?? true,
      trackProgress: json['trackProgress'] as bool? ?? true,
      allowComments: json['allowComments'] as bool? ?? false,
      customSettings: Map<String, dynamic>.from(
        json['customSettings'] as Map? ?? {},
      ),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'allowCollaboration': allowCollaboration,
      'sendNotifications': sendNotifications,
      'trackProgress': trackProgress,
      'allowComments': allowComments,
      'customSettings': customSettings,
    };
  }

  StudyPlanSettings copyWith({
    bool? allowCollaboration,
    bool? sendNotifications,
    bool? trackProgress,
    bool? allowComments,
    Map<String, dynamic>? customSettings,
  }) {
    return StudyPlanSettings(
      allowCollaboration: allowCollaboration ?? this.allowCollaboration,
      sendNotifications: sendNotifications ?? this.sendNotifications,
      trackProgress: trackProgress ?? this.trackProgress,
      allowComments: allowComments ?? this.allowComments,
      customSettings: customSettings ?? this.customSettings,
    );
  }
}

/// Main model representing a study plan
class StudyPlanModel {
  final String id;
  final String title;
  final String? description;
  final StudyPlanType type;
  final StudyPlanStatus status;
  final String creatorId;
  final String? creatorName;
  final UserRole creatorRole;

  // Assignment & Access
  final List<String> assignedUserIds;
  final String? classId;
  final String? subjectId;
  final AssignmentScope scope;
  final bool isCollaborative;
  final bool isMandatory;
  final bool canBeEditedAsTemplate;

  // Association (Added based on user requirements)
  final String? associatedExamId;
  final String? associatedClassroomId;
  final AssociationType? associationType;

  // Timeline
  final DateTime createdAt;
  final DateTime? startDate;
  final DateTime? endDate;
  final Duration? estimatedDuration;
  final bool hasFlexibleScheduling;
  final bool allowsScheduleEditing;

  // Structure
  final List<StudyPlanSection> sections;
  final StudyPlanSettings settings;
  final StudyPlanProgress progress;

  // Template System (Enhanced)
  final bool isTemplate;
  final String? templateId;
  final bool allowsCustomScheduling;
  final TemplateSettings? templateSettings;

  // Creation Process (Added: 4-step creation tracking)
  final PlanCreationStep currentCreationStep;
  final bool isCreationComplete;
  final Map<String, dynamic>? creationStepData;

  // Metadata
  final List<String> tags;
  final StudyPlanPriority priority;
  final PlanOrigin origin;

  const StudyPlanModel({
    required this.id,
    required this.title,
    this.description,
    required this.type,
    this.status = StudyPlanStatus.draft,
    required this.creatorId,
    this.creatorName,
    this.creatorRole = UserRole.student,
    this.assignedUserIds = const [],
    this.classId,
    this.subjectId,
    this.scope = AssignmentScope.individual,
    this.isCollaborative = false,
    this.isMandatory = false,
    this.canBeEditedAsTemplate = false,
    this.associatedExamId,
    this.associatedClassroomId,
    this.associationType,
    required this.createdAt,
    this.startDate,
    this.endDate,
    this.estimatedDuration,
    this.hasFlexibleScheduling = false,
    this.allowsScheduleEditing = false,
    this.sections = const [],
    this.settings = const StudyPlanSettings(),
    this.progress = const StudyPlanProgress(),
    this.isTemplate = false,
    this.templateId,
    this.allowsCustomScheduling = false,
    this.templateSettings,
    this.currentCreationStep = PlanCreationStep.basicDetails,
    this.isCreationComplete = false,
    this.creationStepData,
    this.tags = const [],
    this.priority = StudyPlanPriority.normal,
    this.origin = PlanOrigin.scratch,
  });

  /// Create StudyPlanModel from JSON
  factory StudyPlanModel.fromJson(Map<String, dynamic> json) {
    return StudyPlanModel(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String?,
      type: StudyPlanType.values.firstWhere((e) => e.name == json['type']),
      status: StudyPlanStatus.values.firstWhere(
        (e) => e.name == json['status'],
        orElse: () => StudyPlanStatus.draft,
      ),
      creatorId: json['creatorId'] as String,
      creatorName: json['creatorName'] as String?,
      creatorRole: json['creatorRole'] != null
          ? UserRole.values.firstWhere(
              (e) => e.name == json['creatorRole'],
              orElse: () => UserRole.student,
            )
          : UserRole.student,
      assignedUserIds: List<String>.from(
        json['assignedUserIds'] as List? ?? [],
      ),
      classId: json['classId'] as String?,
      subjectId: json['subjectId'] as String?,
      scope: AssignmentScope.values.firstWhere(
        (e) => e.name == json['scope'],
        orElse: () => AssignmentScope.individual,
      ),
      isCollaborative: json['isCollaborative'] as bool? ?? false,
      isMandatory: json['isMandatory'] as bool? ?? false,
      canBeEditedAsTemplate: json['canBeEditedAsTemplate'] as bool? ?? false,
      associatedExamId: json['associatedExamId'] as String?,
      associatedClassroomId: json['associatedClassroomId'] as String?,
      associationType: json['associationType'] != null
          ? AssociationType.values.firstWhere(
              (e) => e.name == json['associationType'],
            )
          : null,
      createdAt: DateTime.parse(json['createdAt'] as String),
      startDate: json['startDate'] != null
          ? DateTime.parse(json['startDate'] as String)
          : null,
      endDate: json['endDate'] != null
          ? DateTime.parse(json['endDate'] as String)
          : null,
      estimatedDuration: json['estimatedDuration'] != null
          ? Duration(minutes: json['estimatedDuration'] as int)
          : null,
      hasFlexibleScheduling: json['hasFlexibleScheduling'] as bool? ?? false,
      allowsScheduleEditing: json['allowsScheduleEditing'] as bool? ?? false,
      sections:
          (json['sections'] as List?)
              ?.map((e) => StudyPlanSection.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      settings: json['settings'] != null
          ? StudyPlanSettings.fromJson(json['settings'] as Map<String, dynamic>)
          : const StudyPlanSettings(),
      progress: json['progress'] != null
          ? StudyPlanProgress.fromJson(json['progress'] as Map<String, dynamic>)
          : const StudyPlanProgress(),
      isTemplate: json['isTemplate'] as bool? ?? false,
      templateId: json['templateId'] as String?,
      allowsCustomScheduling: json['allowsCustomScheduling'] as bool? ?? false,
      templateSettings: json['templateSettings'] != null
          ? TemplateSettings.fromJson(
              json['templateSettings'] as Map<String, dynamic>,
            )
          : null,
      currentCreationStep: PlanCreationStep.values.firstWhere(
        (e) => e.name == json['currentCreationStep'],
        orElse: () => PlanCreationStep.basicDetails,
      ),
      isCreationComplete: json['isCreationComplete'] as bool? ?? false,
      creationStepData: json['creationStepData'] as Map<String, dynamic>?,
      tags: List<String>.from(json['tags'] as List? ?? []),
      priority: StudyPlanPriority.values.firstWhere(
        (e) => e.name == json['priority'],
        orElse: () => StudyPlanPriority.normal,
      ),
      origin: PlanOrigin.values.firstWhere(
        (e) => e.name == json['origin'],
        orElse: () => PlanOrigin.scratch,
      ),
    );
  }

  /// Convert StudyPlanModel to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      if (description != null) 'description': description,
      'type': type.name,
      'status': status.name,
      'creatorId': creatorId,
      if (creatorName != null) 'creatorName': creatorName,
      'creatorRole': creatorRole.name,
      'assignedUserIds': assignedUserIds,
      if (classId != null) 'classId': classId,
      if (subjectId != null) 'subjectId': subjectId,
      'scope': scope.name,
      'isCollaborative': isCollaborative,
      'isMandatory': isMandatory,
      'canBeEditedAsTemplate': canBeEditedAsTemplate,
      if (associatedExamId != null) 'associatedExamId': associatedExamId,
      if (associatedClassroomId != null)
        'associatedClassroomId': associatedClassroomId,
      if (associationType != null) 'associationType': associationType!.name,
      'createdAt': createdAt.toIso8601String(),
      if (startDate != null) 'startDate': startDate!.toIso8601String(),
      if (endDate != null) 'endDate': endDate!.toIso8601String(),
      if (estimatedDuration != null)
        'estimatedDuration': estimatedDuration!.inMinutes,
      'hasFlexibleScheduling': hasFlexibleScheduling,
      'allowsScheduleEditing': allowsScheduleEditing,
      'sections': sections.map((e) => e.toJson()).toList(),
      'settings': settings.toJson(),
      'progress': progress.toJson(),
      'isTemplate': isTemplate,
      if (templateId != null) 'templateId': templateId,
      'allowsCustomScheduling': allowsCustomScheduling,
      if (templateSettings != null)
        'templateSettings': templateSettings!.toJson(),
      'currentCreationStep': currentCreationStep.name,
      'isCreationComplete': isCreationComplete,
      if (creationStepData != null) 'creationStepData': creationStepData,
      'tags': tags,
      'priority': priority.name,
      'origin': origin.name,
    };
  }

  /// Create a copy with updated fields
  StudyPlanModel copyWith({
    String? id,
    String? title,
    String? description,
    StudyPlanType? type,
    StudyPlanStatus? status,
    String? creatorId,
    String? creatorName,
    UserRole? creatorRole,
    List<String>? assignedUserIds,
    String? classId,
    String? subjectId,
    AssignmentScope? scope,
    bool? isCollaborative,
    bool? isMandatory,
    bool? canBeEditedAsTemplate,
    String? associatedExamId,
    String? associatedClassroomId,
    AssociationType? associationType,
    DateTime? createdAt,
    DateTime? startDate,
    DateTime? endDate,
    Duration? estimatedDuration,
    bool? hasFlexibleScheduling,
    bool? allowsScheduleEditing,
    List<StudyPlanSection>? sections,
    StudyPlanSettings? settings,
    StudyPlanProgress? progress,
    bool? isTemplate,
    String? templateId,
    bool? allowsCustomScheduling,
    TemplateSettings? templateSettings,
    PlanCreationStep? currentCreationStep,
    bool? isCreationComplete,
    Map<String, dynamic>? creationStepData,
    List<String>? tags,
    StudyPlanPriority? priority,
    PlanOrigin? origin,
  }) {
    return StudyPlanModel(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      type: type ?? this.type,
      status: status ?? this.status,
      creatorId: creatorId ?? this.creatorId,
      creatorName: creatorName ?? this.creatorName,
      creatorRole: creatorRole ?? this.creatorRole,
      assignedUserIds: assignedUserIds ?? this.assignedUserIds,
      classId: classId ?? this.classId,
      subjectId: subjectId ?? this.subjectId,
      scope: scope ?? this.scope,
      isCollaborative: isCollaborative ?? this.isCollaborative,
      isMandatory: isMandatory ?? this.isMandatory,
      canBeEditedAsTemplate:
          canBeEditedAsTemplate ?? this.canBeEditedAsTemplate,
      associatedExamId: associatedExamId ?? this.associatedExamId,
      associatedClassroomId:
          associatedClassroomId ?? this.associatedClassroomId,
      associationType: associationType ?? this.associationType,
      createdAt: createdAt ?? this.createdAt,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      estimatedDuration: estimatedDuration ?? this.estimatedDuration,
      hasFlexibleScheduling:
          hasFlexibleScheduling ?? this.hasFlexibleScheduling,
      allowsScheduleEditing:
          allowsScheduleEditing ?? this.allowsScheduleEditing,
      sections: sections ?? this.sections,
      settings: settings ?? this.settings,
      progress: progress ?? this.progress,
      isTemplate: isTemplate ?? this.isTemplate,
      templateId: templateId ?? this.templateId,
      allowsCustomScheduling:
          allowsCustomScheduling ?? this.allowsCustomScheduling,
      templateSettings: templateSettings ?? this.templateSettings,
      currentCreationStep: currentCreationStep ?? this.currentCreationStep,
      isCreationComplete: isCreationComplete ?? this.isCreationComplete,
      creationStepData: creationStepData ?? this.creationStepData,
      tags: tags ?? this.tags,
      priority: priority ?? this.priority,
      origin: origin ?? this.origin,
    );
  }

  /// Get all tasks from all sections
  List<StudyPlanTask> get allTasks {
    final allTasks = <StudyPlanTask>[];
    for (final section in sections) {
      allTasks.addAll(section.allTasks);
    }
    return allTasks;
  }

  /// Check if plan is editable
  bool get isEditable {
    return status.canBeEdited;
  }

  /// Check if plan is active
  bool get isActive {
    return status == StudyPlanStatus.active;
  }

  /// Check if plan is completed
  bool get isCompleted {
    return status == StudyPlanStatus.completed;
  }

  /// Get plan category for list view
  PlanCategoryType getCategoryForUser(String userId) {
    return PlanCategoryTypeHelper.getCategoryForPlan(
      currentUserId: userId,
      planCreatorId: creatorId,
      assignedUserIds: assignedUserIds,
      classroomId: classId,
      isTemplate: isTemplate,
    );
  }

  /// Check if user can edit this plan
  bool canUserEdit(String userId) {
    if (creatorId == userId) return true;
    if (isTemplate && templateSettings?.allowTaskModification != true) {
      return false;
    }
    return assignedUserIds.contains(userId) && isEditable;
  }

  /// Check if user is assigned to this plan
  bool isUserAssigned(String userId) {
    return assignedUserIds.contains(userId) || creatorId == userId;
  }

  /// Get effective start date
  DateTime? get effectiveStartDate {
    if (startDate != null) return startDate;
    if (sections.isNotEmpty) {
      final sectionStartDates = sections
          .where((s) => s.startDate != null)
          .map((s) => s.startDate!)
          .toList();
      if (sectionStartDates.isNotEmpty) {
        sectionStartDates.sort();
        return sectionStartDates.first;
      }
    }
    return null;
  }

  /// Get effective end date
  DateTime? get effectiveEndDate {
    if (endDate != null) return endDate;
    if (sections.isNotEmpty) {
      final sectionEndDates = sections
          .where((s) => s.endDate != null)
          .map((s) => s.endDate!)
          .toList();
      if (sectionEndDates.isNotEmpty) {
        sectionEndDates.sort();
        return sectionEndDates.last;
      }
    }
    return null;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is StudyPlanModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'StudyPlanModel(id: $id, title: $title, type: ${type.displayName}, status: ${status.displayName})';
  }
}
