import 'dart:async';

import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logger/logger.dart';

import '../enums/auth_enums.dart';
import '../models/auth_error_model.dart';
import '../models/auth_state_model.dart';
import '../models/user_model.dart';
import '../services/auth_repository.dart';
import '../services/local_storage_service.dart';
import '../services/app_initialization_service.dart';
import '../../features/profile/controllers/profile_controller.dart';

/// Logger for auth providers
final _logger = Logger();

/// Provider for the authentication repository instance
final authRepositoryProvider = Provider<AuthRepository>((ref) {
  return AuthRepository();
});

/// Provider for Firebase Auth state changes stream
final authStateChangesProvider = StreamProvider<User?>((ref) {
  final authRepository = ref.read(authRepositoryProvider);
  return authRepository.authStateChanges;
});

/// Provider for current user from Firebase
final currentFirebaseUserProvider = Provider<User?>((ref) {
  final authRepository = ref.read(authRepositoryProvider);
  return authRepository.currentFirebaseUser;
});

/// Provider for current user as UserModel (basic Firebase data only)
final currentFirebaseUserModelProvider = Provider<UserModel?>((ref) {
  final authRepository = ref.read(authRepositoryProvider);
  return authRepository.currentUser;
});

/// Provider for current user with full profile data from Firestore
final currentUserProvider = FutureProvider<UserModel?>((ref) async {
  final authRepository = ref.read(authRepositoryProvider);
  final firebaseUser = authRepository.currentUser;

  if (firebaseUser == null) return null;

  try {
    // Load profile data from Firestore
    final profileRepository = ref.read(profileRepositoryProvider);
    final profile = await profileRepository.getProfileById(firebaseUser.id);

    if (profile != null) {
      // Merge Firebase auth data with Firestore profile data
      return firebaseUser.copyWith(
        fullName: profile.fullName,
        userType: profile.userType,
        primaryClassId: profile.primaryClassId,
        profileImageUrl: profile.profileImageUrl,
        grade: profile.grade,
        school: profile.school,
        studentId: profile.studentId,
        bio: profile.bio,
        subjects: profile.subjects,
        phoneNumber: profile.phoneNumber,
      );
    }

    // If no profile exists in Firestore, return basic Firebase user data
    return firebaseUser;
  } catch (e) {
    // If there's an error loading profile, return basic Firebase user data
    return firebaseUser;
  }
});

/// Main authentication state provider
final authStateProvider =
    StateNotifierProvider<AuthStateNotifier, AsyncValue<AuthStateModel>>((ref) {
      return AuthStateNotifier(ref);
    });

/// Authentication state notifier
class AuthStateNotifier extends StateNotifier<AsyncValue<AuthStateModel>> {
  AuthStateNotifier(this._ref) : super(const AsyncValue.loading()) {
    _initialize();
  }

  final Ref _ref;
  StreamSubscription<User?>? _authStateSubscription;

  /// Initialize the auth state by listening to Firebase auth changes
  void _initialize() async {
    _logger.i('Initializing authentication state');

    // First, try to load saved auth state from local storage
    try {
      final localStorageService = LocalStorageService();
      final savedUser = await localStorageService.getUserData();

      if (savedUser != null) {
        _logger.i('Found saved user data: ${savedUser.email}');
        // Set initial state with saved user data
        state = AsyncValue.data(AuthStateModel.authenticated(savedUser));

        // Load user data using the app initialization service
        final appInitService = AppInitializationService();
        await appInitService.loadUserData(savedUser);
      }
    } catch (e) {
      _logger.e('Failed to load saved auth state: $e');
      // Continue with normal initialization even if loading saved state fails
    }

    // Listen to Firebase auth state changes
    _authStateSubscription = _ref
        .read(authRepositoryProvider)
        .authStateChanges
        .listen(
          (User? firebaseUser) {
            _logger.i(
              'Auth state changed: ${firebaseUser?.email ?? 'No user'}',
            );

            if (firebaseUser != null) {
              final user = UserModel.fromFirebaseUser(firebaseUser);
              state = AsyncValue.data(AuthStateModel.authenticated(user));

              // Save user data locally when auth state changes
              _saveUserDataLocally(user);
            } else {
              state = AsyncValue.data(AuthStateModel.unauthenticated());

              // Clear saved user data when user signs out
              _clearUserDataLocally();
            }
          },
          onError: (error) {
            _logger.e('Error in auth state stream: $error');
            final authError = error is AuthErrorModel
                ? error
                : AuthErrorModel.fromException(Exception(error.toString()));
            state = AsyncValue.data(AuthStateModel.error(authError));
          },
        );
  }

  /// Sign in with email and password
  Future<void> signInWithEmailAndPassword({
    required String email,
    required String password,
  }) async {
    await AsyncValue.guard(() async {
      _logger.i('Starting sign in process for: $email');

      // Set loading state
      state = AsyncValue.data(
        AuthStateModel.loading(operation: AuthOperation.signIn),
      );

      try {
        final authRepository = _ref.read(authRepositoryProvider);
        final user = await authRepository.signInWithEmailAndPassword(
          email: email,
          password: password,
        );

        _logger.i('Sign in successful for: ${user.email}');
        // State will be updated automatically by the auth state stream

        return AuthStateModel.authenticated(user);
      } catch (e) {
        _logger.e('Sign in failed: $e');

        final authError = e is AuthErrorModel
            ? e
            : AuthErrorModel.fromException(
                Exception(e.toString()),
                operation: AuthOperation.signIn,
              );

        final errorState = AuthStateModel.error(authError);
        state = AsyncValue.data(errorState);

        throw authError;
      }
    }).then((result) {
      state = result;
    });
  }

  /// Sign up with email and password
  Future<void> signUpWithEmailAndPassword({
    required String email,
    required String password,
    String? displayName,
  }) async {
    await AsyncValue.guard(() async {
      _logger.i('Starting sign up process for: $email');

      // Set loading state
      state = AsyncValue.data(
        AuthStateModel.loading(operation: AuthOperation.signUp),
      );

      try {
        final authRepository = _ref.read(authRepositoryProvider);
        final user = await authRepository.signUpWithEmailAndPassword(
          email: email,
          password: password,
          displayName: displayName,
        );

        _logger.i('Sign up successful for: ${user.email}');
        // State will be updated automatically by the auth state stream

        return AuthStateModel.authenticated(user);
      } catch (e) {
        _logger.e('Sign up failed: $e');

        final authError = e is AuthErrorModel
            ? e
            : AuthErrorModel.fromException(
                Exception(e.toString()),
                operation: AuthOperation.signUp,
              );

        final errorState = AuthStateModel.error(authError);
        state = AsyncValue.data(errorState);

        throw authError;
      }
    }).then((result) {
      state = result;
    });
  }

  /// Sign out the current user
  Future<void> signOut() async {
    await AsyncValue.guard(() async {
      _logger.i('Starting sign out process');

      // Set loading state
      state = AsyncValue.data(
        AuthStateModel.loading(operation: AuthOperation.signOut),
      );

      try {
        final authRepository = _ref.read(authRepositoryProvider);
        await authRepository.signOut();

        // Clear local user data
        await _clearUserDataLocally();

        _logger.i('Sign out successful');
        // State will be updated automatically by the auth state stream

        return AuthStateModel.unauthenticated();
      } catch (e) {
        _logger.e('Sign out failed: $e');

        final authError = e is AuthErrorModel
            ? e
            : AuthErrorModel.fromException(
                Exception(e.toString()),
                operation: AuthOperation.signOut,
              );

        final errorState = AuthStateModel.error(authError);
        state = AsyncValue.data(errorState);

        throw authError;
      }
    }).then((result) {
      state = result;
    });
  }

  /// Send password reset email
  Future<void> sendPasswordResetEmail({required String email}) async {
    await AsyncValue.guard(() async {
      _logger.i('Sending password reset email to: $email');

      try {
        final authRepository = _ref.read(authRepositoryProvider);
        await authRepository.sendPasswordResetEmail(email: email);

        _logger.i('Password reset email sent successfully');

        // Return current state as this operation doesn't change auth state
        return state.value ?? AuthStateModel.unauthenticated();
      } catch (e) {
        _logger.e('Failed to send password reset email: $e');

        final authError = e is AuthErrorModel
            ? e
            : AuthErrorModel.fromException(
                Exception(e.toString()),
                operation: AuthOperation.resetPassword,
              );

        throw authError;
      }
    });
  }

  /// Clear any authentication errors
  void clearError() {
    state.whenData((authState) {
      if (authState.hasError) {
        state = AsyncValue.data(authState.copyWith(clearError: true));
      }
    });
  }

  /// Reload current user data
  Future<void> reloadUser() async {
    await AsyncValue.guard(() async {
      _logger.i('Reloading user data');

      try {
        final authRepository = _ref.read(authRepositoryProvider);
        final user = await authRepository.reloadUser();

        if (user != null) {
          _logger.i('User data reloaded successfully');
          return AuthStateModel.authenticated(user);
        } else {
          _logger.i('No user to reload');
          return AuthStateModel.unauthenticated();
        }
      } catch (e) {
        _logger.e('Failed to reload user data: $e');

        final authError = AuthErrorModel.fromException(
          Exception(e.toString()),
          operation: AuthOperation.refreshToken,
        );

        throw authError;
      }
    }).then((result) {
      state = result;
    });
  }

  /// Save user data locally
  Future<void> _saveUserDataLocally(UserModel user) async {
    try {
      final localStorageService = LocalStorageService();
      await localStorageService.saveUserData(user);

      final appInitService = AppInitializationService();
      await appInitService.loadUserData(user);

      _logger.i('User data saved locally for: ${user.email}');
    } catch (e) {
      _logger.e('Failed to save user data locally: $e');
    }
  }

  /// Clear user data locally
  Future<void> _clearUserDataLocally() async {
    try {
      final appInitService = AppInitializationService();
      await appInitService.clearUserData();

      _logger.i('User data cleared locally');
    } catch (e) {
      _logger.e('Failed to clear user data locally: $e');
    }
  }

  @override
  void dispose() {
    _authStateSubscription?.cancel();
    super.dispose();
  }
}

/// Provider to check if user is authenticated
final isAuthenticatedProvider = Provider<bool>((ref) {
  final authState = ref.watch(authStateProvider);
  return authState.when(
    data: (state) => state.isAuthenticated,
    loading: () => false,
    error: (_, __) => false,
  );
});

/// Provider to check if authentication is loading
final isAuthLoadingProvider = Provider<bool>((ref) {
  final authState = ref.watch(authStateProvider);
  return authState.when(
    data: (state) => state.isLoadingState,
    loading: () => true,
    error: (_, __) => false,
  );
});

/// Provider to get current authentication error
final authErrorProvider = Provider<AuthErrorModel?>((ref) {
  final authState = ref.watch(authStateProvider);
  return authState.when(
    data: (state) => state.error,
    loading: () => null,
    error: (error, _) => error is AuthErrorModel ? error : null,
  );
});
