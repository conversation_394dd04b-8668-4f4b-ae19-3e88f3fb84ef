# Scholara Student App - Development Guidelines

Welcome to the Scholara Student App development repository. This document outlines the foundational rules, structure, tools, and development practices to be followed throughout the project to ensure clean, scalable, and production-ready code.

---

## Development Philosophy

- Follow a modular, **feature-wise approach** with a clear separation of concerns.
- **Do not rush** development. Break complex features into smaller parts and use `TODO:` markers when needed.
- Code should be **error-free** — resolve all runtime and info-level issues before moving forward.
- Maintain **high readability** — avoid overly long files or deeply nested logic.
- Add meaningful **comments** to clarify complex or non-obvious logic.
- Make sure to leave no errors in the codebase.
- Make sure to leave no warnings in the codebase.
- I noticed that you are using funky and childish design patterns, the tone of our project is luxurious, minimalist and spacious. This doesn’t mean don’t use multiple colors, just use them appropriately.

---

## Development Workflow

We follow a clear, repeatable process for building features and modules in Scholara:

### 1. Feature Planning

- Define feature scope: screens, widgets, logic, end goals
- Structure folders under `features/{feature_name}`

### 2. Models & Enums

- Create all necessary models and enums
- Generate mock data if needed (using `faker` or manually)

### 3. UI/UX Implementation

- Generate modular and responsive widgets based on the prompts
- Follow the global theme strictly
- add responsive UI using `flutter_screenutil` wherever seems appropriate, don't need to add it everywhere.

### 4. State Management & Backend

- Implement business logic using Riverpod
- Connect Firestore/Auth/Storage where required
- Use `shared_preferences` and `flutter_secure_storage` appropriately
- Include validation and error handling

### 5. Logging & Analytics

- Add logs using the `logger` package
- Track screen views, errors, and user actions
- Ensure maximum observability where applicable

### 6. Testing

- Write unit tests, widget tests, and integration tests for each module
- Ask for guidance if unfamiliar with a test type

### 7. Final Cleanup

- Resolve all errors and warnings
- Remove mocks, test data, and unused files
- Ensure full adherence to project standards

---

## Project Structure

### Core Folder Structure (`lib/core/`)

| Folder       | Purpose                                                    |
| ------------ | ---------------------------------------------------------- |
| `constants/` | App-wide constants like colors, collection names, text styles, API endpoints |
| `theme/`     | Light/dark theme setup using Flutter's ThemeData           |
| `routes/`    | `go_router` setup and centralized route definitions        |
| `widgets/`   | Global reusable widgets                                    |
| `models/`    | Shared models used across multiple features                |
| `enums/`     | Shared enums (e.g., `UserRole`)                            |
| `providers/` | Global Riverpod providers (e.g., auth, preferences)        |
| `services/`  | Global services (auth, API, storage, etc.)                 |
| `utils/`     | Helpers, formatters, and extensions                        |

### Feature Structure (`lib/features/{feature_name}/`)

Each feature should have its own folder with:

- `screens/` - Screen-specific folders with main files and sections
- `controllers/` - Riverpod controllers and state management
- `repositories/` - Data access layer and API calls
- `widgets/` - Reusable widgets specific to this feature
- `models/` - Feature-specific data models (if needed)
- `services/` - Feature-specific services (if needed)
- `utils/` - Feature-specific utilities and helpers (if needed)

#### Detailed Folder Structure Guidelines

##### Screens Folder (`screens/`)

The screens folder contains **subfolders for each screen**, not individual screen files:

```
screens/
├── screen_name_1/
│   ├── screen_name_1_screen.dart    # Main screen file
│   └── sections/                    # Screen-specific sections
│       ├── header_section.dart
│       ├── content_section.dart
│       └── footer_section.dart
├── screen_name_2/
│   ├── screen_name_2_screen.dart
│   └── sections/
│       ├── info_section.dart
│       └── actions_section.dart
```

**Sections** are UI components that:

- Are specific to **one screen only**
- Represent logical parts of a screen (header, content areas, action bars)
- Help break down large screen files into manageable components
- Are **not reusable** across different screens

##### Widgets Folder (`widgets/`)

The widgets folder contains **reusable components** that can be used across multiple screens within the feature:

```
widgets/
├── feature_card.dart              # Reusable card component
├── feature_filter_bar.dart        # Reusable filter component
├── feature_list_tile.dart         # Reusable list item
└── feature_action_button.dart     # Reusable action button
```

**Widgets** are UI components that:

- Are **reusable** across multiple screens within the feature
- Can potentially be used app-wide (consider moving to `core/widgets/`)
- Include cards, tiles, buttons, form fields, etc.
- Should **not** be created just for the sake of organization

##### Key Principles

1. **Sections** = Screen-specific, non-reusable UI parts
2. **Widgets** = Reusable components within the feature
3. **Core Widgets** = Reusable components across the entire app
4. Only create files when there's a clear organizational or reusability benefit
5. Avoid over-engineering - not every UI element needs to be a separate file

---

## API & Data Management

- Use `dio` for API calls.
- Centralize API endpoints in `lib/core/constants/api_endpoints.dart`.
- Handle API errors and edge cases within repositories.
- Use `shared_preferences` for local data storage.
- Use `flutter_secure_storage` for sensitive data storage.

---

## Widget Development Rules

- Prefer creating separate **Stateless/StatefulWidgets** instead of UI methods inside other widgets.
- Use **global widgets** (from `core/widgets/`) where reuse is expected across the entire app.
- Use **feature widgets** (within `features/{feature}/widgets/`) for components reusable within that feature.
- Use **sections** (within `features/{feature}/screens/{screen}/sections/`) for screen-specific UI parts.
- Avoid deeply nested layouts — split long UIs into smaller components using the section pattern.
- Add **comments** wherever helpful, especially for business logic or conditional flows.

### Widget Organization Guidelines

1. **Core Widgets** (`core/widgets/`): App-wide reusable components

   - Global buttons, cards, form fields, navigation components
   - Used across multiple features

2. **Feature Widgets** (`features/{feature}/widgets/`): Feature-specific reusable components

   - Components used across multiple screens within the same feature
   - Feature-specific cards, tiles, filters, action buttons

3. **Screen Sections** (`features/{feature}/screens/{screen}/sections/`): Screen-specific components

   - Logical parts of a single screen (headers, content areas, footers)
   - Not reusable across different screens
   - Help break down large screen files

### Decision Matrix: Where to Place UI Components

| Component Type | Reusable Across App? | Reusable Within Feature? | Screen-Specific? | Location                                        |
| -------------- | -------------------- | ------------------------ | ---------------- | ----------------------------------------------- |
| Global Button  | ✅                   | ✅                       | ❌               | `core/widgets/`                                 |
| Feature Card   | ❌                   | ✅                       | ❌               | `features/{feature}/widgets/`                   |
| Screen Header  | ❌                   | ❌                       | ✅               | `features/{feature}/screens/{screen}/sections/` |
| Form Section   | ❌                   | ❌                       | ✅               | `features/{feature}/screens/{screen}/sections/` |

---

## Theme & Styling

- Use Flutter's `ThemeData` system to manage light and dark themes.
- Global styling should come from:
  - `AppColors`
  - `AppTypography`
- Do **not** hardcode `TextStyle` or color values.
- Layout spacing can be added inline as needed — no `LayoutGuidelines` file will be used.

---

## Libraries & Tools

### Preferred Packages

| Purpose             | Package                  |
| ------------------- | ------------------------ |
| State Management    | `riverpod`               |
| State Management    | `flutter_riverpod`       |
| Routing             | `go_router`              |
| Responsive UI       | `flutter_screenutil`     |
| Mock Data           | `faker`                  |
| Secure Storage      | `flutter_secure_storage` |
| Persistent Settings | `shared_preferences`     |
| Fonts               | `google_fonts`           |
| Logging             | `logger`                 |
| Icons               | `material_symbols_icons` |

### Avoided Tools

- Code generators like `freezed`, `json_serializable`, or `build_runner`
- Unstable or under-documented packages

---

## Adaptive UI

- Currently we are only developing for mobile but we need to keep scope for other devices
- don't confuse adaptive UI with responsive UI.
- Instead of using raw `LayoutBuilder` everywhere, we create widgets like `ResponsivePage`
- Use `Expanded` and `Flexible` widgets judiciously.
- Avoid nested `SizedBox` for layout purposes.

---

## Functional Scope (MVP)

- **Localization**: Not included in the current phase.
- **Offline-first**: Deferred to a later stage.
- **Analytics & Logging**: Must be added as early as possible. Include:
  - Error logging with stack traces
  - Page and event tracking
  - Detailed logs using `logger` package
  - as we are using firebase we can also use its products like analytics and crashlytics.

---

## Testing

After each major module is complete, testing must be implemented:

- **Unit tests** for core logic and controllers
- **Widget tests** for key screens and layouts
- **Integration tests** when applicable

If unsure about how to structure or write tests, ask for guidance in the planning thread.

---

## Cache

- Use proper methods of caching
- Use packeges like catched_network_image along with proper placeholders and error images.

---

## Mock data

- Use mock data for testing and development
- Use faker for generating mock data
- Use upload scripts to upload mock data to firebase
- Use test scripts to validate mock data
- Use clear and consistent naming conventions for mock data
- Use comments to explain the purpose of mock data

---

## Final Reminders

- Use centralized styles and configurations at all times.
- Do not hardcode values (colors, sizes, routes).
- Use `AppRoutes` and `RouteNames` for navigation.
- Add `TODO:` comments and split into parts if a screen or feature grows too complex.
- Maintain **consistent, clean, and scalable** code throughout the project.

---

**Let’s build this the right way — one clean feature at a time.**
